{"ast": null, "code": "/**\n * File utility functions for asset management\n */\n\nexport class FileUtils {\n  /**\n   * Validate file before upload\n   * @param file - File to validate\n   * @param maxSizeMB - Maximum allowed size in MB\n   * @returns Validation result\n   */\n  static validateFile(file, maxSizeMB) {\n    // Check file size\n    const maxSizeBytes = maxSizeMB * 1024 * 1024;\n    if (file.size > maxSizeBytes) {\n      return {\n        valid: false,\n        error: `File size exceeds ${maxSizeMB} MB limit`\n      };\n    }\n\n    // Check file type\n    const allowedTypes = [\n    // Images\n    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',\n    // Videos\n    'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm'];\n    if (!allowedTypes.includes(file.type)) {\n      return {\n        valid: false,\n        error: `Unsupported file type: ${file.type}`\n      };\n    }\n    return {\n      valid: true\n    };\n  }\n\n  /**\n   * Format file size for display\n   * @param bytes - File size in bytes\n   * @returns Formatted file size string\n   */\n  static formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  /**\n   * Check if file is an image\n   * @param mimeType - File MIME type\n   * @returns True if file is an image\n   */\n  static isImage(mimeType) {\n    return mimeType.startsWith('image/');\n  }\n\n  /**\n   * Check if file is a video\n   * @param mimeType - File MIME type\n   * @returns True if file is a video\n   */\n  static isVideo(mimeType) {\n    return mimeType.startsWith('video/');\n  }\n\n  /**\n   * Get file type display name\n   * @param mimeType - File MIME type\n   * @returns Display name for file type\n   */\n  static getFileTypeDisplay(mimeType) {\n    if (this.isImage(mimeType)) return 'Image';\n    if (this.isVideo(mimeType)) return 'Video';\n    return 'File';\n  }\n\n  /**\n   * Generate thumbnail URL for video files\n   * @param videoUrl - Video URL\n   * @returns Thumbnail URL (placeholder for now)\n   */\n  static getVideoThumbnail(videoUrl) {\n    // For now, return a placeholder. In production, you might want to generate\n    // actual thumbnails or use a service like AWS Lambda to create them\n    return \"/assets/video-placeholder.png\";\n  }\n}", "map": {"version": 3, "names": ["FileUtils", "validateFile", "file", "maxSizeMB", "maxSizeBytes", "size", "valid", "error", "allowedTypes", "includes", "type", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "isImage", "mimeType", "startsWith", "isVideo", "getFileTypeDisplay", "getVideoThumbnail", "videoUrl"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/utils/fileUtils.ts"], "sourcesContent": ["/**\n * File utility functions for asset management\n */\n\nexport class FileUtils {\n  /**\n   * Validate file before upload\n   * @param file - File to validate\n   * @param maxSizeMB - Maximum allowed size in MB\n   * @returns Validation result\n   */\n  static validateFile(file: File, maxSizeMB: number): { valid: boolean; error?: string } {\n    // Check file size\n    const maxSizeBytes = maxSizeMB * 1024 * 1024;\n    if (file.size > maxSizeBytes) {\n      return {\n        valid: false,\n        error: `File size exceeds ${maxSizeMB} MB limit`\n      };\n    }\n\n    // Check file type\n    const allowedTypes = [\n      // Images\n      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',\n      // Videos\n      'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm'\n    ];\n\n    if (!allowedTypes.includes(file.type)) {\n      return {\n        valid: false,\n        error: `Unsupported file type: ${file.type}`\n      };\n    }\n\n    return { valid: true };\n  }\n\n  /**\n   * Format file size for display\n   * @param bytes - File size in bytes\n   * @returns Formatted file size string\n   */\n  static formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  /**\n   * Check if file is an image\n   * @param mimeType - File MIME type\n   * @returns True if file is an image\n   */\n  static isImage(mimeType: string): boolean {\n    return mimeType.startsWith('image/');\n  }\n\n  /**\n   * Check if file is a video\n   * @param mimeType - File MIME type\n   * @returns True if file is a video\n   */\n  static isVideo(mimeType: string): boolean {\n    return mimeType.startsWith('video/');\n  }\n\n  /**\n   * Get file type display name\n   * @param mimeType - File MIME type\n   * @returns Display name for file type\n   */\n  static getFileTypeDisplay(mimeType: string): string {\n    if (this.isImage(mimeType)) return 'Image';\n    if (this.isVideo(mimeType)) return 'Video';\n    return 'File';\n  }\n\n  /**\n   * Generate thumbnail URL for video files\n   * @param videoUrl - Video URL\n   * @returns Thumbnail URL (placeholder for now)\n   */\n  static getVideoThumbnail(videoUrl: string): string {\n    // For now, return a placeholder. In production, you might want to generate\n    // actual thumbnails or use a service like AWS Lambda to create them\n    return \"/assets/video-placeholder.png\";\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,OAAO,MAAMA,SAAS,CAAC;EACrB;AACF;AACA;AACA;AACA;AACA;EACE,OAAOC,YAAYA,CAACC,IAAU,EAAEC,SAAiB,EAAsC;IACrF;IACA,MAAMC,YAAY,GAAGD,SAAS,GAAG,IAAI,GAAG,IAAI;IAC5C,IAAID,IAAI,CAACG,IAAI,GAAGD,YAAY,EAAE;MAC5B,OAAO;QACLE,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,qBAAqBJ,SAAS;MACvC,CAAC;IACH;;IAEA;IACA,MAAMK,YAAY,GAAG;IACnB;IACA,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY;IACjE;IACA,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAC9E;IAED,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACP,IAAI,CAACQ,IAAI,CAAC,EAAE;MACrC,OAAO;QACLJ,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,0BAA0BL,IAAI,CAACQ,IAAI;MAC5C,CAAC;IACH;IAEA,OAAO;MAAEJ,KAAK,EAAE;IAAK,CAAC;EACxB;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOK,cAAcA,CAACC,KAAa,EAAU;IAC3C,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOO,OAAOA,CAACC,QAAgB,EAAW;IACxC,OAAOA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC;EACtC;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOC,OAAOA,CAACF,QAAgB,EAAW;IACxC,OAAOA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC;EACtC;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOE,kBAAkBA,CAACH,QAAgB,EAAU;IAClD,IAAI,IAAI,CAACD,OAAO,CAACC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC1C,IAAI,IAAI,CAACE,OAAO,CAACF,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC1C,OAAO,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOI,iBAAiBA,CAACC,QAAgB,EAAU;IACjD;IACA;IACA,OAAO,+BAA+B;EACxC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}