{"ast": null, "code": "import HttpHelperService from \"../httpHelper.service\";\nimport { CREATE_POST, DELETE_POST, RETRIEVE_POSTS, UPLOAD_IMAGE_FILES } from \"../../constants/endPoints.constant\";\nimport { TOPIC_TYPES } from \"../../constants/application.constant\";\nclass PostsService {\n  constructor(dispatch) {\n    this._httpHelperService = void 0;\n    this.retrievePosts = async (userId, businessGroupId, businessId, locationId) => {\n      return await this._httpHelperService.get(`${RETRIEVE_POSTS}/${userId}?businessGroupId=${businessGroupId}&businessId=${businessId}&locationId=${locationId}`);\n    };\n    this.uploadImagesToServer = async formData => {\n      return await this._httpHelperService.postFormData(`${UPLOAD_IMAGE_FILES}`, formData);\n    };\n    this.createPost = async (userId, createPostData, isBulkPost = false, bulkPostId) => {\n      if (createPostData.createGooglePost.topicType === TOPIC_TYPES.Event) {\n        delete createPostData.createGooglePost.offer;\n      }\n\n      // Add bulk post information to the request\n      const requestData = {\n        ...createPostData,\n        isBulkPost,\n        bulkPostId\n      };\n      console.log(JSON.stringify(createPostData.createGooglePost));\n      return await this._httpHelperService.post(`${CREATE_POST}/${userId}`, requestData);\n    };\n    this.deletePost = async (userId, route) => {\n      return await this._httpHelperService.delete(`${DELETE_POST}/${userId}?id=${route}`);\n    };\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n}\nexport default PostsService;", "map": {"version": 3, "names": ["HttpHelperService", "CREATE_POST", "DELETE_POST", "RETRIEVE_POSTS", "UPLOAD_IMAGE_FILES", "TOPIC_TYPES", "PostsService", "constructor", "dispatch", "_httpHelperService", "retrievePosts", "userId", "businessGroupId", "businessId", "locationId", "get", "uploadImagesToServer", "formData", "postFormData", "createPost", "createPostData", "isBulkPost", "bulkPostId", "createGooglePost", "topicType", "Event", "offer", "requestData", "console", "log", "JSON", "stringify", "post", "deletePost", "route", "delete"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/posts/posts.service.tsx"], "sourcesContent": ["import { Component, Dispatch } from \"react\";\nimport HttpHelperService from \"../httpHelper.service\";\nimport {\n  CREATE_POST,\n  DELETE_POST,\n  SAVE_SCHEDULED,\n  RETRIEVE_POSTS,\n  UPLOAD_IMAGE_FILES,\n  CHECK_BULK_POST_STATUS,\n  GET_BULK_POST_DETAILS,\n  GET_POST_BY_NAME,\n} from \"../../constants/endPoints.constant\";\nimport { Action } from \"redux\";\nimport { IGoogleCreatePost } from \"../../interfaces/request/IGoogleCreatePost\";\nimport { ISelectionLocationWithPost } from \"../../screens/createSocialPost/components/submitPost.component\";\nimport { TOPIC_TYPES } from \"../../constants/application.constant\";\n\nclass PostsService {\n  _httpHelperService;\n  constructor(dispatch: Dispatch<Action>) {\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  retrievePosts = async (\n    userId: number,\n    businessGroupId: number,\n    businessId: number,\n    locationId: number\n  ) => {\n    return await this._httpHelperService.get(\n      `${RETRIEVE_POSTS}/${userId}?businessGroupId=${businessGroupId}&businessId=${businessId}&locationId=${locationId}`\n    );\n  };\n\n  uploadImagesToServer = async (formData: FormData) => {\n    return await this._httpHelperService.postFormData(\n      `${UPLOAD_IMAGE_FILES}`,\n      formData\n    );\n  };\n\n  createPost = async (\n    userId: number,\n    createPostData: ISelectionLocationWithPost,\n    isBulkPost: boolean = false,\n    bulkPostId?: string\n  ) => {\n    if (createPostData.createGooglePost.topicType === TOPIC_TYPES.Event) {\n      delete createPostData.createGooglePost.offer;\n    }\n\n    // Add bulk post information to the request\n    const requestData = {\n      ...createPostData,\n      isBulkPost,\n      bulkPostId,\n    };\n\n    console.log(JSON.stringify(createPostData.createGooglePost));\n    return await this._httpHelperService.post(\n      `${CREATE_POST}/${userId}`,\n      requestData\n    );\n  };\n\n  deletePost = async (userId: number, route: string) => {\n    return await this._httpHelperService.delete(\n      `${DELETE_POST}/${userId}?id=${route}`\n    );\n  };\n}\n\nexport default PostsService;\n"], "mappings": "AACA,OAAOA,iBAAiB,MAAM,uBAAuB;AACrD,SACEC,WAAW,EACXC,WAAW,EAEXC,cAAc,EACdC,kBAAkB,QAIb,oCAAoC;AAI3C,SAASC,WAAW,QAAQ,sCAAsC;AAElE,MAAMC,YAAY,CAAC;EAEjBC,WAAWA,CAACC,QAA0B,EAAE;IAAA,KADxCC,kBAAkB;IAAA,KAKlBC,aAAa,GAAG,OACdC,MAAc,EACdC,eAAuB,EACvBC,UAAkB,EAClBC,UAAkB,KACf;MACH,OAAO,MAAM,IAAI,CAACL,kBAAkB,CAACM,GAAG,CACtC,GAAGZ,cAAc,IAAIQ,MAAM,oBAAoBC,eAAe,eAAeC,UAAU,eAAeC,UAAU,EAClH,CAAC;IACH,CAAC;IAAA,KAEDE,oBAAoB,GAAG,MAAOC,QAAkB,IAAK;MACnD,OAAO,MAAM,IAAI,CAACR,kBAAkB,CAACS,YAAY,CAC/C,GAAGd,kBAAkB,EAAE,EACvBa,QACF,CAAC;IACH,CAAC;IAAA,KAEDE,UAAU,GAAG,OACXR,MAAc,EACdS,cAA0C,EAC1CC,UAAmB,GAAG,KAAK,EAC3BC,UAAmB,KAChB;MACH,IAAIF,cAAc,CAACG,gBAAgB,CAACC,SAAS,KAAKnB,WAAW,CAACoB,KAAK,EAAE;QACnE,OAAOL,cAAc,CAACG,gBAAgB,CAACG,KAAK;MAC9C;;MAEA;MACA,MAAMC,WAAW,GAAG;QAClB,GAAGP,cAAc;QACjBC,UAAU;QACVC;MACF,CAAC;MAEDM,OAAO,CAACC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACX,cAAc,CAACG,gBAAgB,CAAC,CAAC;MAC5D,OAAO,MAAM,IAAI,CAACd,kBAAkB,CAACuB,IAAI,CACvC,GAAG/B,WAAW,IAAIU,MAAM,EAAE,EAC1BgB,WACF,CAAC;IACH,CAAC;IAAA,KAEDM,UAAU,GAAG,OAAOtB,MAAc,EAAEuB,KAAa,KAAK;MACpD,OAAO,MAAM,IAAI,CAACzB,kBAAkB,CAAC0B,MAAM,CACzC,GAAGjC,WAAW,IAAIS,MAAM,OAAOuB,KAAK,EACtC,CAAC;IACH,CAAC;IAjDC,IAAI,CAACzB,kBAAkB,GAAG,IAAIT,iBAAiB,CAACQ,QAAQ,CAAC;EAC3D;AAiDF;AAEA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}