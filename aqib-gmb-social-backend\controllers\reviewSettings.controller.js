const ReviewSettings = require("../models/reviewSettings.models");

const welcome = async (req, res) => {
  res.status(200).json({ message: "Review Settings API is working!" });
};

// Get all reply templates for a user
const getReplyTemplates = async (req, res) => {
  try {
    const { userId } = req.params;
    const { businessId } = req.query;

    const templates = await ReviewSettings.getReplyTemplates(userId, businessId);
    
    res.status(200).json({
      message: "Reply templates fetched successfully",
      data: templates
    });
  } catch (error) {
    console.error("Error fetching reply templates:", error);
    res.status(500).json({
      message: "Failed to fetch reply templates",
      error: error.message
    });
  }
};

// Create a new reply template
const createReplyTemplate = async (req, res) => {
  try {
    const { userId } = req.params;
    const templateData = {
      ...req.body,
      userId: parseInt(userId)
    };

    const result = await ReviewSettings.createReplyTemplate(templateData);
    
    res.status(201).json({
      message: "Reply template created successfully",
      data: { id: result.insertId }
    });
  } catch (error) {
    console.error("Error creating reply template:", error);
    res.status(500).json({
      message: "Failed to create reply template",
      error: error.message
    });
  }
};

// Update a reply template
const updateReplyTemplate = async (req, res) => {
  try {
    const { userId, templateId } = req.params;
    const templateData = req.body;

    const result = await ReviewSettings.updateReplyTemplate(
      parseInt(templateId),
      templateData,
      parseInt(userId)
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        message: "Template not found or you don't have permission to update it"
      });
    }
    
    res.status(200).json({
      message: "Reply template updated successfully"
    });
  } catch (error) {
    console.error("Error updating reply template:", error);
    res.status(500).json({
      message: "Failed to update reply template",
      error: error.message
    });
  }
};

// Delete a reply template
const deleteReplyTemplate = async (req, res) => {
  try {
    const { userId, templateId } = req.params;

    const result = await ReviewSettings.deleteReplyTemplate(
      parseInt(templateId),
      parseInt(userId)
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        message: "Template not found or you don't have permission to delete it"
      });
    }
    
    res.status(200).json({
      message: "Reply template deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting reply template:", error);
    res.status(500).json({
      message: "Failed to delete reply template",
      error: error.message
    });
  }
};

// Get auto-reply settings for a business
const getAutoReplySettings = async (req, res) => {
  try {
    const { businessId } = req.params;

    const settings = await ReviewSettings.getAutoReplySettings(parseInt(businessId));
    
    res.status(200).json({
      message: "Auto-reply settings fetched successfully",
      data: settings
    });
  } catch (error) {
    console.error("Error fetching auto-reply settings:", error);
    res.status(500).json({
      message: "Failed to fetch auto-reply settings",
      error: error.message
    });
  }
};

// Update auto-reply settings
const updateAutoReplySettings = async (req, res) => {
  try {
    const { businessId } = req.params;
    const settings = req.body;

    const result = await ReviewSettings.updateAutoReplySettings(
      parseInt(businessId),
      settings
    );
    
    res.status(200).json({
      message: "Auto-reply settings updated successfully"
    });
  } catch (error) {
    console.error("Error updating auto-reply settings:", error);
    res.status(500).json({
      message: "Failed to update auto-reply settings",
      error: error.message
    });
  }
};

// Map template to businesses
const mapTemplateToBusinesses = async (req, res) => {
  try {
    const { userId, templateId } = req.params;
    const { businessIds } = req.body;

    const result = await ReviewSettings.mapTemplateToBusinesses(
      parseInt(templateId),
      businessIds,
      parseInt(userId)
    );
    
    res.status(200).json({
      message: "Template mapped to businesses successfully"
    });
  } catch (error) {
    console.error("Error mapping template to businesses:", error);
    res.status(500).json({
      message: "Failed to map template to businesses",
      error: error.message
    });
  }
};

// Get template for auto-reply (used by auto-reply system)
const getTemplateForAutoReply = async (req, res) => {
  try {
    const { businessId, starRating } = req.params;

    const template = await ReviewSettings.getTemplateForAutoReply(
      parseInt(businessId),
      parseInt(starRating)
    );
    
    res.status(200).json({
      message: "Template fetched successfully",
      data: template
    });
  } catch (error) {
    console.error("Error fetching template for auto-reply:", error);
    res.status(500).json({
      message: "Failed to fetch template for auto-reply",
      error: error.message
    });
  }
};

module.exports = {
  welcome,
  getReplyTemplates,
  createReplyTemplate,
  updateReplyTemplate,
  deleteReplyTemplate,
  getAutoReplySettings,
  updateAutoReplySettings,
  mapTemplateToBusinesses,
  getTemplateForAutoReply
};
