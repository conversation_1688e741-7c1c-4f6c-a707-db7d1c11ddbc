{"ast": null, "code": "var _ManageAssetsService;\nimport axios from \"axios\";\nclass ManageAssetsService {\n  constructor() {\n    this.baseURL = void 0;\n    this.baseURL = `${process.env.REACT_APP_BASE_URL}/manage-assets`;\n  }\n  static getInstance() {\n    if (!ManageAssetsService.instance) {\n      ManageAssetsService.instance = new ManageAssetsService();\n    }\n    return ManageAssetsService.instance;\n  }\n\n  /**\n   * Upload assets for a business\n   * @param businessId - Business ID\n   * @param files - Files to upload\n   * @returns Promise with upload result\n   */\n  async uploadAssets(businessId, files) {\n    try {\n      const formData = new FormData();\n\n      // Add all files to FormData\n      Array.from(files).forEach(file => {\n        formData.append(\"files\", file);\n      });\n      const token = localStorage.getItem(\"token\");\n      const response = await axios.post(`${this.baseURL}/upload/${businessId}`, formData, {\n        headers: {\n          Authorization: `Bearer ${token}`,\n          \"Content-Type\": \"multipart/form-data\"\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error(\"Error uploading assets:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get assets for a business with pagination\n   * @param businessId - Business ID\n   * @param page - Page number\n   * @param limit - Records per page\n   * @returns Promise with assets list\n   */\n  async getAssets(businessId, page = 1, limit = 12) {\n    try {\n      const token = localStorage.getItem(\"token\");\n      const response = await axios.get(`${this.baseURL}/business/${businessId}`, {\n        params: {\n          page,\n          limit\n        },\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error(\"Error fetching assets:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get single asset by ID\n   * @param assetId - Asset ID\n   * @returns Promise with asset data\n   */\n  async getAssetById(assetId) {\n    try {\n      const token = localStorage.getItem(\"token\");\n      const response = await axios.get(`${this.baseURL}/asset/${assetId}`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error(\"Error fetching asset:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete an asset\n   * @param assetId - Asset ID\n   * @returns Promise with delete result\n   */\n  async deleteAsset(assetId) {\n    try {\n      const token = localStorage.getItem(\"token\");\n      const response = await axios.delete(`${this.baseURL}/asset/${assetId}`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error(\"Error deleting asset:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update max upload size for a business\n   * @param businessId - Business ID\n   * @param maxSizeMB - Max size in MB\n   * @returns Promise with update result\n   */\n  async updateMaxUploadSize(businessId, maxSizeMB) {\n    try {\n      const token = localStorage.getItem(\"token\");\n      const response = await axios.put(`${this.baseURL}/business/${businessId}/max-size`, {\n        maxSizeMB\n      }, {\n        headers: {\n          Authorization: `Bearer ${token}`,\n          \"Content-Type\": \"application/json\"\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error(\"Error updating max upload size:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Validate file before upload\n   * @param file - File to validate\n   * @param maxSizeMB - Maximum allowed size in MB\n   * @returns Validation result\n   */\n  validateFile(file, maxSizeMB) {\n    // Check file size\n    const maxSizeBytes = maxSizeMB * 1024 * 1024;\n    if (file.size > maxSizeBytes) {\n      return {\n        valid: false,\n        error: `File size exceeds ${maxSizeMB} MB limit`\n      };\n    }\n\n    // Check file type\n    const allowedTypes = [\n    // Images\n    \"image/jpeg\", \"image/jpg\", \"image/png\", \"image/gif\", \"image/webp\",\n    // Videos\n    \"video/mp4\", \"video/avi\", \"video/mov\", \"video/wmv\", \"video/flv\", \"video/webm\"];\n    if (!allowedTypes.includes(file.type)) {\n      return {\n        valid: false,\n        error: `Unsupported file type: ${file.type}`\n      };\n    }\n    return {\n      valid: true\n    };\n  }\n\n  /**\n   * Format file size for display\n   * @param bytes - File size in bytes\n   * @returns Formatted file size string\n   */\n  formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n  }\n\n  /**\n   * Check if file is an image\n   * @param mimeType - File MIME type\n   * @returns True if file is an image\n   */\n  isImage(mimeType) {\n    return mimeType.startsWith(\"image/\");\n  }\n\n  /**\n   * Check if file is a video\n   * @param mimeType - File MIME type\n   * @returns True if file is a video\n   */\n  isVideo(mimeType) {\n    return mimeType.startsWith(\"video/\");\n  }\n\n  /**\n   * Get file type display name\n   * @param mimeType - File MIME type\n   * @returns Display name for file type\n   */\n  getFileTypeDisplay(mimeType) {\n    if (this.isImage(mimeType)) return \"Image\";\n    if (this.isVideo(mimeType)) return \"Video\";\n    return \"File\";\n  }\n\n  /**\n   * Generate thumbnail URL for video files\n   * @param videoUrl - Video URL\n   * @returns Thumbnail URL (placeholder for now)\n   */\n  getVideoThumbnail(videoUrl) {\n    // For now, return a placeholder. In production, you might want to generate\n    // actual thumbnails or use a service like AWS Lambda to create them\n    return \"/assets/video-placeholder.png\";\n  }\n}\n_ManageAssetsService = ManageAssetsService;\nManageAssetsService.instance = void 0;\nexport default ManageAssetsService.getInstance();", "map": {"version": 3, "names": ["axios", "ManageAssetsService", "constructor", "baseURL", "process", "env", "REACT_APP_BASE_URL", "getInstance", "instance", "uploadAssets", "businessId", "files", "formData", "FormData", "Array", "from", "for<PERSON>ach", "file", "append", "token", "localStorage", "getItem", "response", "post", "headers", "Authorization", "data", "error", "console", "getAssets", "page", "limit", "get", "params", "getAssetById", "assetId", "deleteAsset", "delete", "updateMaxUploadSize", "maxSizeMB", "put", "validateFile", "maxSizeBytes", "size", "valid", "allowedTypes", "includes", "type", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "isImage", "mimeType", "startsWith", "isVideo", "getFileTypeDisplay", "getVideoThumbnail", "videoUrl", "_ManageAssetsService"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/manageAssets/manageAssets.service.ts"], "sourcesContent": ["import axios from \"axios\";\n\nclass ManageAssetsService {\n  private static instance: ManageAssetsService;\n  private baseURL: string;\n\n  constructor() {\n    this.baseURL = `${process.env.REACT_APP_BASE_URL}/manage-assets`;\n  }\n\n  public static getInstance(): ManageAssetsService {\n    if (!ManageAssetsService.instance) {\n      ManageAssetsService.instance = new ManageAssetsService();\n    }\n    return ManageAssetsService.instance;\n  }\n\n  /**\n   * Upload assets for a business\n   * @param businessId - Business ID\n   * @param files - Files to upload\n   * @returns Promise with upload result\n   */\n  async uploadAssets(businessId: number, files: FileList): Promise<any> {\n    try {\n      const formData = new FormData();\n\n      // Add all files to FormData\n      Array.from(files).forEach((file) => {\n        formData.append(\"files\", file);\n      });\n\n      const token = localStorage.getItem(\"token\");\n      const response = await axios.post(\n        `${this.baseURL}/upload/${businessId}`,\n        formData,\n        {\n          headers: {\n            Authorization: `Bearer ${token}`,\n            \"Content-Type\": \"multipart/form-data\",\n          },\n        }\n      );\n\n      return response.data;\n    } catch (error: any) {\n      console.error(\"Error uploading assets:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get assets for a business with pagination\n   * @param businessId - Business ID\n   * @param page - Page number\n   * @param limit - Records per page\n   * @returns Promise with assets list\n   */\n  async getAssets(\n    businessId: number,\n    page: number = 1,\n    limit: number = 12\n  ): Promise<any> {\n    try {\n      const token = localStorage.getItem(\"token\");\n      const response = await axios.get(\n        `${this.baseURL}/business/${businessId}`,\n        {\n          params: { page, limit },\n          headers: {\n            Authorization: `Bearer ${token}`,\n          },\n        }\n      );\n\n      return response.data;\n    } catch (error: any) {\n      console.error(\"Error fetching assets:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get single asset by ID\n   * @param assetId - Asset ID\n   * @returns Promise with asset data\n   */\n  async getAssetById(assetId: number): Promise<any> {\n    try {\n      const token = localStorage.getItem(\"token\");\n      const response = await axios.get(`${this.baseURL}/asset/${assetId}`, {\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      return response.data;\n    } catch (error: any) {\n      console.error(\"Error fetching asset:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete an asset\n   * @param assetId - Asset ID\n   * @returns Promise with delete result\n   */\n  async deleteAsset(assetId: number): Promise<any> {\n    try {\n      const token = localStorage.getItem(\"token\");\n      const response = await axios.delete(`${this.baseURL}/asset/${assetId}`, {\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      return response.data;\n    } catch (error: any) {\n      console.error(\"Error deleting asset:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update max upload size for a business\n   * @param businessId - Business ID\n   * @param maxSizeMB - Max size in MB\n   * @returns Promise with update result\n   */\n  async updateMaxUploadSize(\n    businessId: number,\n    maxSizeMB: number\n  ): Promise<any> {\n    try {\n      const token = localStorage.getItem(\"token\");\n      const response = await axios.put(\n        `${this.baseURL}/business/${businessId}/max-size`,\n        { maxSizeMB },\n        {\n          headers: {\n            Authorization: `Bearer ${token}`,\n            \"Content-Type\": \"application/json\",\n          },\n        }\n      );\n\n      return response.data;\n    } catch (error: any) {\n      console.error(\"Error updating max upload size:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Validate file before upload\n   * @param file - File to validate\n   * @param maxSizeMB - Maximum allowed size in MB\n   * @returns Validation result\n   */\n  validateFile(\n    file: File,\n    maxSizeMB: number\n  ): { valid: boolean; error?: string } {\n    // Check file size\n    const maxSizeBytes = maxSizeMB * 1024 * 1024;\n    if (file.size > maxSizeBytes) {\n      return {\n        valid: false,\n        error: `File size exceeds ${maxSizeMB} MB limit`,\n      };\n    }\n\n    // Check file type\n    const allowedTypes = [\n      // Images\n      \"image/jpeg\",\n      \"image/jpg\",\n      \"image/png\",\n      \"image/gif\",\n      \"image/webp\",\n      // Videos\n      \"video/mp4\",\n      \"video/avi\",\n      \"video/mov\",\n      \"video/wmv\",\n      \"video/flv\",\n      \"video/webm\",\n    ];\n\n    if (!allowedTypes.includes(file.type)) {\n      return {\n        valid: false,\n        error: `Unsupported file type: ${file.type}`,\n      };\n    }\n\n    return { valid: true };\n  }\n\n  /**\n   * Format file size for display\n   * @param bytes - File size in bytes\n   * @returns Formatted file size string\n   */\n  formatFileSize(bytes: number): string {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n  }\n\n  /**\n   * Check if file is an image\n   * @param mimeType - File MIME type\n   * @returns True if file is an image\n   */\n  isImage(mimeType: string): boolean {\n    return mimeType.startsWith(\"image/\");\n  }\n\n  /**\n   * Check if file is a video\n   * @param mimeType - File MIME type\n   * @returns True if file is a video\n   */\n  isVideo(mimeType: string): boolean {\n    return mimeType.startsWith(\"video/\");\n  }\n\n  /**\n   * Get file type display name\n   * @param mimeType - File MIME type\n   * @returns Display name for file type\n   */\n  getFileTypeDisplay(mimeType: string): string {\n    if (this.isImage(mimeType)) return \"Image\";\n    if (this.isVideo(mimeType)) return \"Video\";\n    return \"File\";\n  }\n\n  /**\n   * Generate thumbnail URL for video files\n   * @param videoUrl - Video URL\n   * @returns Thumbnail URL (placeholder for now)\n   */\n  getVideoThumbnail(videoUrl: string): string {\n    // For now, return a placeholder. In production, you might want to generate\n    // actual thumbnails or use a service like AWS Lambda to create them\n    return \"/assets/video-placeholder.png\";\n  }\n}\n\nexport default ManageAssetsService.getInstance();\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,mBAAmB,CAAC;EAIxBC,WAAWA,CAAA,EAAG;IAAA,KAFNC,OAAO;IAGb,IAAI,CAACA,OAAO,GAAG,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,gBAAgB;EAClE;EAEA,OAAcC,WAAWA,CAAA,EAAwB;IAC/C,IAAI,CAACN,mBAAmB,CAACO,QAAQ,EAAE;MACjCP,mBAAmB,CAACO,QAAQ,GAAG,IAAIP,mBAAmB,CAAC,CAAC;IAC1D;IACA,OAAOA,mBAAmB,CAACO,QAAQ;EACrC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,YAAYA,CAACC,UAAkB,EAAEC,KAAe,EAAgB;IACpE,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAE/B;MACAC,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC,CAACK,OAAO,CAAEC,IAAI,IAAK;QAClCL,QAAQ,CAACM,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;MAChC,CAAC,CAAC;MAEF,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMtB,KAAK,CAACuB,IAAI,CAC/B,GAAG,IAAI,CAACpB,OAAO,WAAWO,UAAU,EAAE,EACtCE,QAAQ,EACR;QACEY,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUN,KAAK,EAAE;UAChC,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,OAAOG,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAME,SAASA,CACbnB,UAAkB,EAClBoB,IAAY,GAAG,CAAC,EAChBC,KAAa,GAAG,EAAE,EACJ;IACd,IAAI;MACF,MAAMZ,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMtB,KAAK,CAACgC,GAAG,CAC9B,GAAG,IAAI,CAAC7B,OAAO,aAAaO,UAAU,EAAE,EACxC;QACEuB,MAAM,EAAE;UAAEH,IAAI;UAAEC;QAAM,CAAC;QACvBP,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUN,KAAK;QAChC;MACF,CACF,CAAC;MAED,OAAOG,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMO,YAAYA,CAACC,OAAe,EAAgB;IAChD,IAAI;MACF,MAAMhB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMtB,KAAK,CAACgC,GAAG,CAAC,GAAG,IAAI,CAAC7B,OAAO,UAAUgC,OAAO,EAAE,EAAE;QACnEX,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUN,KAAK;QAChC;MACF,CAAC,CAAC;MAEF,OAAOG,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMS,WAAWA,CAACD,OAAe,EAAgB;IAC/C,IAAI;MACF,MAAMhB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMtB,KAAK,CAACqC,MAAM,CAAC,GAAG,IAAI,CAAClC,OAAO,UAAUgC,OAAO,EAAE,EAAE;QACtEX,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUN,KAAK;QAChC;MACF,CAAC,CAAC;MAEF,OAAOG,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMW,mBAAmBA,CACvB5B,UAAkB,EAClB6B,SAAiB,EACH;IACd,IAAI;MACF,MAAMpB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMtB,KAAK,CAACwC,GAAG,CAC9B,GAAG,IAAI,CAACrC,OAAO,aAAaO,UAAU,WAAW,EACjD;QAAE6B;MAAU,CAAC,EACb;QACEf,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUN,KAAK,EAAE;UAChC,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,OAAOG,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEc,YAAYA,CACVxB,IAAU,EACVsB,SAAiB,EACmB;IACpC;IACA,MAAMG,YAAY,GAAGH,SAAS,GAAG,IAAI,GAAG,IAAI;IAC5C,IAAItB,IAAI,CAAC0B,IAAI,GAAGD,YAAY,EAAE;MAC5B,OAAO;QACLE,KAAK,EAAE,KAAK;QACZjB,KAAK,EAAE,qBAAqBY,SAAS;MACvC,CAAC;IACH;;IAEA;IACA,MAAMM,YAAY,GAAG;IACnB;IACA,YAAY,EACZ,WAAW,EACX,WAAW,EACX,WAAW,EACX,YAAY;IACZ;IACA,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,YAAY,CACb;IAED,IAAI,CAACA,YAAY,CAACC,QAAQ,CAAC7B,IAAI,CAAC8B,IAAI,CAAC,EAAE;MACrC,OAAO;QACLH,KAAK,EAAE,KAAK;QACZjB,KAAK,EAAE,0BAA0BV,IAAI,CAAC8B,IAAI;MAC5C,CAAC;IACH;IAEA,OAAO;MAAEH,KAAK,EAAE;IAAK,CAAC;EACxB;;EAEA;AACF;AACA;AACA;AACA;EACEI,cAAcA,CAACC,KAAa,EAAU;IACpC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE;;EAEA;AACF;AACA;AACA;AACA;EACEO,OAAOA,CAACC,QAAgB,EAAW;IACjC,OAAOA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC;EACtC;;EAEA;AACF;AACA;AACA;AACA;EACEC,OAAOA,CAACF,QAAgB,EAAW;IACjC,OAAOA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC;EACtC;;EAEA;AACF;AACA;AACA;AACA;EACEE,kBAAkBA,CAACH,QAAgB,EAAU;IAC3C,IAAI,IAAI,CAACD,OAAO,CAACC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC1C,IAAI,IAAI,CAACE,OAAO,CAACF,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC1C,OAAO,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;EACEI,iBAAiBA,CAACC,QAAgB,EAAU;IAC1C;IACA;IACA,OAAO,+BAA+B;EACxC;AACF;AAACC,oBAAA,GA1PKjE,mBAAmB;AAAnBA,mBAAmB,CACRO,QAAQ;AA2PzB,eAAeP,mBAAmB,CAACM,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}