{"ast": null, "code": "import HttpHelperService from \"../httpHelper.service\";\n\n// Interfaces\n\nclass ReviewSettingsService {\n  constructor(dispatch) {\n    this._httpHelperService = void 0;\n    // Get all reply templates for a user\n    this.getReplyTemplates = async (userId, businessId) => {\n      const url = businessId ? `${TEMPLATES_ENDPOINT(userId)}?businessId=${businessId}` : TEMPLATES_ENDPOINT(userId);\n      return await this._httpHelperService.get(url);\n    };\n    // Create a new reply template\n    this.createReplyTemplate = async (userId, templateData) => {\n      return await this._httpHelperService.post(TEMPLATES_ENDPOINT(userId), templateData);\n    };\n    // Update a reply template\n    this.updateReplyTemplate = async (userId, templateId, templateData) => {\n      return await this._httpHelperService.put(TEMPLATE_BY_ID_ENDPOINT(userId, templateId), templateData);\n    };\n    // Delete a reply template\n    this.deleteReplyTemplate = async (userId, templateId) => {\n      return await this._httpHelperService.delete(TEMPLATE_BY_ID_ENDPOINT(userId, templateId));\n    };\n    // Map template to businesses\n    this.mapTemplateToBusinesses = async (userId, templateId, businessIds) => {\n      return await this._httpHelperService.post(MAP_TEMPLATE_ENDPOINT(userId, templateId), {\n        businessIds\n      });\n    };\n    // Get auto-reply settings for a business\n    this.getAutoReplySettings = async businessId => {\n      return await this._httpHelperService.get(AUTO_REPLY_ENDPOINT(businessId));\n    };\n    // Update auto-reply settings\n    this.updateAutoReplySettings = async (businessId, settings) => {\n      return await this._httpHelperService.put(AUTO_REPLY_ENDPOINT(businessId), settings);\n    };\n    // Get template for auto-reply (used by auto-reply system)\n    this.getTemplateForAutoReply = async (businessId, starRating) => {\n      return await this._httpHelperService.get(`${REVIEW_SETTINGS_BASE}/auto-reply-template/${businessId}/${starRating}`);\n    };\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n}\nexport default ReviewSettingsService;", "map": {"version": 3, "names": ["HttpHelperService", "ReviewSettingsService", "constructor", "dispatch", "_httpHelperService", "getReplyTemplates", "userId", "businessId", "url", "TEMPLATES_ENDPOINT", "get", "createReplyTemplate", "templateData", "post", "updateReplyTemplate", "templateId", "put", "TEMPLATE_BY_ID_ENDPOINT", "deleteReplyTemplate", "delete", "mapTemplateToBusinesses", "businessIds", "MAP_TEMPLATE_ENDPOINT", "getAutoReplySettings", "AUTO_REPLY_ENDPOINT", "updateAutoReplySettings", "settings", "getTemplateForAutoReply", "starRating", "REVIEW_SETTINGS_BASE"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/reviewSettings/reviewSettings.service.tsx"], "sourcesContent": ["import { Dispatch } from \"react\";\nimport HttpHelperService from \"../httpHelper.service\";\nimport { Action } from \"redux\";\nimport {\n  REVIEW_SETTINGS_TEMPLATES,\n  REVIEW_SETTINGS_TEMPLATE_BY_ID,\n  REVIEW_SETTINGS_MAP_TEMPLATE,\n  REVIEW_SETTINGS_AUTO_REPLY,\n  REVIEW_SETTINGS_AUTO_REPLY_TEMPLATE,\n} from \"../../constants/endPoints.constant\";\n\n// Interfaces\nexport interface IReplyTemplate {\n  id?: number;\n  created_by: number;\n  star_rating: number;\n  template_name: string;\n  template_content: string;\n  is_default: boolean;\n  business_id?: number;\n  business_template_active?: boolean;\n  created_at?: string;\n  updated_at?: string;\n}\n\nexport interface ICreateReplyTemplateRequest {\n  starRating: number;\n  templateName: string;\n  templateContent: string;\n  isDefault: boolean;\n  businessId?: number;\n}\n\nexport interface IUpdateReplyTemplateRequest {\n  starRating: number;\n  templateName: string;\n  templateContent: string;\n  isDefault: boolean;\n}\n\nexport interface IAutoReplySettings {\n  id?: number;\n  business_id: number;\n  is_enabled: boolean;\n  enabled_star_ratings: number[];\n  delay_minutes: number;\n  only_business_hours: boolean;\n  business_hours_start: string;\n  business_hours_end: string;\n  created_at?: string;\n  updated_at?: string;\n}\n\nexport interface IMapTemplateToBusinessesRequest {\n  businessIds: number[];\n}\n\nexport interface IReplyTemplatesResponse {\n  message: string;\n  data: IReplyTemplate[];\n}\n\nexport interface IAutoReplySettingsResponse {\n  message: string;\n  data: IAutoReplySettings | null;\n}\n\nexport interface IApiResponse {\n  message: string;\n  data?: any;\n}\n\nclass ReviewSettingsService {\n  _httpHelperService;\n\n  constructor(dispatch: Dispatch<Action>) {\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  // Get all reply templates for a user\n  getReplyTemplates = async (\n    userId: number,\n    businessId?: number\n  ): Promise<IReplyTemplatesResponse> => {\n    const url = businessId\n      ? `${TEMPLATES_ENDPOINT(userId)}?businessId=${businessId}`\n      : TEMPLATES_ENDPOINT(userId);\n    return await this._httpHelperService.get(url);\n  };\n\n  // Create a new reply template\n  createReplyTemplate = async (\n    userId: number,\n    templateData: ICreateReplyTemplateRequest\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.post(\n      TEMPLATES_ENDPOINT(userId),\n      templateData\n    );\n  };\n\n  // Update a reply template\n  updateReplyTemplate = async (\n    userId: number,\n    templateId: number,\n    templateData: IUpdateReplyTemplateRequest\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.put(\n      TEMPLATE_BY_ID_ENDPOINT(userId, templateId),\n      templateData\n    );\n  };\n\n  // Delete a reply template\n  deleteReplyTemplate = async (\n    userId: number,\n    templateId: number\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.delete(\n      TEMPLATE_BY_ID_ENDPOINT(userId, templateId)\n    );\n  };\n\n  // Map template to businesses\n  mapTemplateToBusinesses = async (\n    userId: number,\n    templateId: number,\n    businessIds: number[]\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.post(\n      MAP_TEMPLATE_ENDPOINT(userId, templateId),\n      { businessIds }\n    );\n  };\n\n  // Get auto-reply settings for a business\n  getAutoReplySettings = async (\n    businessId: number\n  ): Promise<IAutoReplySettingsResponse> => {\n    return await this._httpHelperService.get(AUTO_REPLY_ENDPOINT(businessId));\n  };\n\n  // Update auto-reply settings\n  updateAutoReplySettings = async (\n    businessId: number,\n    settings: Omit<\n      IAutoReplySettings,\n      \"id\" | \"business_id\" | \"created_at\" | \"updated_at\"\n    >\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.put(\n      AUTO_REPLY_ENDPOINT(businessId),\n      settings\n    );\n  };\n\n  // Get template for auto-reply (used by auto-reply system)\n  getTemplateForAutoReply = async (\n    businessId: number,\n    starRating: number\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.get(\n      `${REVIEW_SETTINGS_BASE}/auto-reply-template/${businessId}/${starRating}`\n    );\n  };\n}\n\nexport default ReviewSettingsService;\n"], "mappings": "AACA,OAAOA,iBAAiB,MAAM,uBAAuB;;AAUrD;;AA6DA,MAAMC,qBAAqB,CAAC;EAG1BC,WAAWA,CAACC,QAA0B,EAAE;IAAA,KAFxCC,kBAAkB;IAMlB;IAAA,KACAC,iBAAiB,GAAG,OAClBC,MAAc,EACdC,UAAmB,KACkB;MACrC,MAAMC,GAAG,GAAGD,UAAU,GAClB,GAAGE,kBAAkB,CAACH,MAAM,CAAC,eAAeC,UAAU,EAAE,GACxDE,kBAAkB,CAACH,MAAM,CAAC;MAC9B,OAAO,MAAM,IAAI,CAACF,kBAAkB,CAACM,GAAG,CAACF,GAAG,CAAC;IAC/C,CAAC;IAED;IAAA,KACAG,mBAAmB,GAAG,OACpBL,MAAc,EACdM,YAAyC,KACf;MAC1B,OAAO,MAAM,IAAI,CAACR,kBAAkB,CAACS,IAAI,CACvCJ,kBAAkB,CAACH,MAAM,CAAC,EAC1BM,YACF,CAAC;IACH,CAAC;IAED;IAAA,KACAE,mBAAmB,GAAG,OACpBR,MAAc,EACdS,UAAkB,EAClBH,YAAyC,KACf;MAC1B,OAAO,MAAM,IAAI,CAACR,kBAAkB,CAACY,GAAG,CACtCC,uBAAuB,CAACX,MAAM,EAAES,UAAU,CAAC,EAC3CH,YACF,CAAC;IACH,CAAC;IAED;IAAA,KACAM,mBAAmB,GAAG,OACpBZ,MAAc,EACdS,UAAkB,KACQ;MAC1B,OAAO,MAAM,IAAI,CAACX,kBAAkB,CAACe,MAAM,CACzCF,uBAAuB,CAACX,MAAM,EAAES,UAAU,CAC5C,CAAC;IACH,CAAC;IAED;IAAA,KACAK,uBAAuB,GAAG,OACxBd,MAAc,EACdS,UAAkB,EAClBM,WAAqB,KACK;MAC1B,OAAO,MAAM,IAAI,CAACjB,kBAAkB,CAACS,IAAI,CACvCS,qBAAqB,CAAChB,MAAM,EAAES,UAAU,CAAC,EACzC;QAAEM;MAAY,CAChB,CAAC;IACH,CAAC;IAED;IAAA,KACAE,oBAAoB,GAAG,MACrBhB,UAAkB,IACsB;MACxC,OAAO,MAAM,IAAI,CAACH,kBAAkB,CAACM,GAAG,CAACc,mBAAmB,CAACjB,UAAU,CAAC,CAAC;IAC3E,CAAC;IAED;IAAA,KACAkB,uBAAuB,GAAG,OACxBlB,UAAkB,EAClBmB,QAGC,KACyB;MAC1B,OAAO,MAAM,IAAI,CAACtB,kBAAkB,CAACY,GAAG,CACtCQ,mBAAmB,CAACjB,UAAU,CAAC,EAC/BmB,QACF,CAAC;IACH,CAAC;IAED;IAAA,KACAC,uBAAuB,GAAG,OACxBpB,UAAkB,EAClBqB,UAAkB,KACQ;MAC1B,OAAO,MAAM,IAAI,CAACxB,kBAAkB,CAACM,GAAG,CACtC,GAAGmB,oBAAoB,wBAAwBtB,UAAU,IAAIqB,UAAU,EACzE,CAAC;IACH,CAAC;IAxFC,IAAI,CAACxB,kBAAkB,GAAG,IAAIJ,iBAAiB,CAACG,QAAQ,CAAC;EAC3D;AAwFF;AAEA,eAAeF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}