{"ast": null, "code": "import { Component } from \"react\";\nimport moment from \"moment\";\nclass HelperService extends Component {\n  constructor(...args) {\n    super(...args);\n    this.isValidateEmail = async text => {\n      try {\n        let reg = /^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w\\w+)+$/;\n        if (reg.test(text) === false) {\n          console.log(\"In-Valid Email in Application Helper Service\");\n          return false;\n        } else {\n          console.log(\"Valid Email in Application Helper Service\");\n          return true;\n        }\n      } catch (error) {\n        return false;\n      }\n    };\n    this.getUserDateTimeFormat = timestamp => {\n      return moment(new Date(timestamp)).format(\"DD-MM-YYYY hh:mm A\"); // 12H clock (AM/PM)\n    };\n    this.getExpandedDateTimeFormat = timestamp => {\n      return moment(new Date(timestamp)).format(\"MMMM DD, YYYY hh:mm A\"); // 12H clock (AM/PM)\n    };\n    this.getFormatedDate = (date = new Date(), format = \"YYYY/MM/DD\") => moment(date).format(format);\n    this.getExpandedFormatedDate = (date = new Date(), format = \"DD-MMM-YYYY\") => moment(date).format(format);\n  }\n}\nexport default HelperService;", "map": {"version": 3, "names": ["Component", "moment", "HelperService", "constructor", "args", "isValidateEmail", "text", "reg", "test", "console", "log", "error", "getUserDateTimeFormat", "timestamp", "Date", "format", "getExpandedDateTimeFormat", "getFormatedDate", "date", "getExpandedFormatedDate"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/helperService.tsx"], "sourcesContent": ["import { Component } from \"react\";\nimport moment from \"moment\";\n\nclass HelperService extends Component {\n  isValidateEmail = async (text: string) => {\n    try {\n      let reg = /^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w\\w+)+$/;\n      if (reg.test(text) === false) {\n        console.log(\"In-Valid Email in Application Helper Service\");\n        return false;\n      } else {\n        console.log(\"Valid Email in Application Helper Service\");\n        return true;\n      }\n    } catch (error) {\n      return false;\n    }\n  };\n\n  getUserDateTimeFormat = (timestamp: any) => {\n    return moment(new Date(timestamp)).format(\"DD-MM-YYYY hh:mm A\"); // 12H clock (AM/PM)\n  };\n\n  getExpandedDateTimeFormat = (timestamp: any) => {\n    return moment(new Date(timestamp)).format(\"MMMM DD, YYYY hh:mm A\"); // 12H clock (AM/PM)\n  };\n\n  getFormatedDate = (date = new Date(), format = \"YYYY/MM/DD\") =>\n    moment(date).format(format);\n\n  getExpandedFormatedDate = (date = new Date(), format = \"DD-MMM-YYYY\") =>\n    moment(date).format(format);\n}\n\nexport default HelperService;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,MAAM,MAAM,QAAQ;AAE3B,MAAMC,aAAa,SAASF,SAAS,CAAC;EAAAG,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA,KACpCC,eAAe,GAAG,MAAOC,IAAY,IAAK;MACxC,IAAI;QACF,IAAIC,GAAG,GAAG,6CAA6C;QACvD,IAAIA,GAAG,CAACC,IAAI,CAACF,IAAI,CAAC,KAAK,KAAK,EAAE;UAC5BG,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3D,OAAO,KAAK;QACd,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;UACxD,OAAO,IAAI;QACb;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd,OAAO,KAAK;MACd;IACF,CAAC;IAAA,KAEDC,qBAAqB,GAAIC,SAAc,IAAK;MAC1C,OAAOZ,MAAM,CAAC,IAAIa,IAAI,CAACD,SAAS,CAAC,CAAC,CAACE,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC;IACnE,CAAC;IAAA,KAEDC,yBAAyB,GAAIH,SAAc,IAAK;MAC9C,OAAOZ,MAAM,CAAC,IAAIa,IAAI,CAACD,SAAS,CAAC,CAAC,CAACE,MAAM,CAAC,uBAAuB,CAAC,CAAC,CAAC;IACtE,CAAC;IAAA,KAEDE,eAAe,GAAG,CAACC,IAAI,GAAG,IAAIJ,IAAI,CAAC,CAAC,EAAEC,MAAM,GAAG,YAAY,KACzDd,MAAM,CAACiB,IAAI,CAAC,CAACH,MAAM,CAACA,MAAM,CAAC;IAAA,KAE7BI,uBAAuB,GAAG,CAACD,IAAI,GAAG,IAAIJ,IAAI,CAAC,CAAC,EAAEC,MAAM,GAAG,aAAa,KAClEd,MAAM,CAACiB,IAAI,CAAC,CAACH,MAAM,CAACA,MAAM,CAAC;EAAA;AAC/B;AAEA,eAAeb,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}