{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\screens\\\\businessManagement\\\\callback\\\\callback.screen.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport Box from \"@mui/material/Box\";\nimport Button from \"@mui/material/Button\";\nimport Typography from \"@mui/material/Typography\";\nimport \"../manageBusiness/manageBusiness.screen.style.css\";\nimport ErrorOutlineIcon from \"@mui/icons-material/ErrorOutline\";\nimport googleLogo from \"../../../assets/common/GoogleBusiness.jpg\";\nimport applicationLogo from \"../../../assets/common/Logo.png\";\nimport { useNavigate, useSearchParams } from \"react-router-dom\";\nimport { ThreeCircles } from \"react-loader-spinner\";\nimport AuthService from \"../../../services/auth/auth.service\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GMBCallback = ({\n  title\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    userInfo\n  } = useSelector(state => state.authReducer);\n  const _authService = new AuthService(dispatch);\n  const [searchParams] = useSearchParams();\n  const [authStatus, setAuthStatus] = useState(null);\n  const navigate = useNavigate();\n  const [countdown, setCountdown] = useState(0); // Initially null (no countdown running)\n  const [isRunning, setIsRunning] = useState(false); // Tracks if timer is active\n\n  useEffect(() => {\n    let interval;\n    let timeout;\n    if (isRunning && countdown > 0) {\n      interval = setInterval(() => {\n        setCountdown(prev => prev - 1);\n      }, 1000);\n    } else if (isRunning && countdown === 0) {\n      clearInterval(interval);\n      navigate(\"/home\");\n    }\n    return () => {\n      clearInterval(interval);\n      clearTimeout(timeout);\n    };\n  }, [countdown, isRunning, navigate]);\n  useEffect(() => {\n    gmbCallBack(searchParams.get(\"code\"), searchParams.get(\"state\"));\n  }, [searchParams]);\n  const startCountdown = () => {\n    setCountdown(5); // Set countdown to 5 seconds\n    setIsRunning(true); // Start countdown\n  };\n  const gmbCallBack = async (code, state) => {\n    try {\n      const result = await _authService.gmbcallback(code, state, userInfo.id);\n      if (result.data) {\n        startCountdown();\n      }\n      setAuthStatus(result.data);\n      return;\n    } catch (error) {\n      console.error(error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      backgroundColor: \"white\",\n      padding: 4,\n      borderRadius: 2,\n      boxShadow: 3,\n      width: \"400px\",\n      margin: \"auto\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        alt: \"AQIB Softech\",\n        src: applicationLogo,\n        style: {\n          height: 50\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"+\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        alt: \"AQIB Softech\",\n        src: googleLogo,\n        style: {\n          height: 50\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), authStatus != null && authStatus ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          marginTop: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(ErrorOutlineIcon, {\n          color: \"success\",\n          sx: {\n            fontSize: 40\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"success\",\n          sx: {\n            marginLeft: 1\n          },\n          children: \"Authentication Success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"success\",\n        sx: {\n          marginLeft: 1\n        },\n        children: [\"Redirecting you in \", countdown, \" Seconds\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 9\n    }, this) : authStatus === null ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        alignItems: \"center\",\n        margin: 10\n      },\n      children: /*#__PURE__*/_jsxDEV(ThreeCircles, {\n        visible: true,\n        height: \"100\",\n        width: \"100\",\n        color: \"#467fea\",\n        ariaLabel: \"three-circles-loading\",\n        wrapperStyle: {},\n        wrapperClass: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        alignItems: \"center\",\n        marginTop: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(ErrorOutlineIcon, {\n        color: \"error\",\n        sx: {\n          fontSize: 40\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"error\",\n        sx: {\n          marginLeft: 1\n        },\n        children: \"Authentication Failure\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      color: \"secondary\",\n      sx: {\n        marginTop: 3\n      },\n      onClick: () => navigate(\"/home\"),\n      children: \"Go To Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s(GMBCallback, \"MaFHC93ti/iqj+DO/PUeTHbE+EQ=\", false, function () {\n  return [useDispatch, useSelector, useSearchParams, useNavigate];\n});\n_c = GMBCallback;\nexport default GMBCallback;\nvar _c;\n$RefreshReg$(_c, \"GMBCallback\");", "map": {"version": 3, "names": ["useEffect", "useState", "Box", "<PERSON><PERSON>", "Typography", "ErrorOutlineIcon", "googleLogo", "applicationLogo", "useNavigate", "useSearchParams", "ThreeCircles", "AuthService", "useDispatch", "useSelector", "jsxDEV", "_jsxDEV", "GMBCallback", "title", "_s", "dispatch", "userInfo", "state", "authReducer", "_authService", "searchParams", "authStatus", "setAuthStatus", "navigate", "countdown", "setCountdown", "isRunning", "setIsRunning", "interval", "timeout", "setInterval", "prev", "clearInterval", "clearTimeout", "gmbCallBack", "get", "startCountdown", "code", "result", "gmbcallback", "id", "data", "error", "console", "sx", "display", "flexDirection", "alignItems", "justifyContent", "backgroundColor", "padding", "borderRadius", "boxShadow", "width", "margin", "children", "gap", "alt", "src", "style", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "marginTop", "color", "fontSize", "marginLeft", "visible", "aria<PERSON><PERSON><PERSON>", "wrapperStyle", "wrapperClass", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/screens/businessManagement/callback/callback.screen.tsx"], "sourcesContent": ["import { FunctionComponent, useEffect, useState } from \"react\";\nimport PageProps from \"../../../models/PageProps.interface\";\nimport Box from \"@mui/material/Box\";\nimport Button from \"@mui/material/Button\";\nimport Typography from \"@mui/material/Typography\";\nimport \"../manageBusiness/manageBusiness.screen.style.css\";\nimport ErrorOutlineIcon from \"@mui/icons-material/ErrorOutline\";\nimport googleLogo from \"../../../assets/common/GoogleBusiness.jpg\";\nimport applicationLogo from \"../../../assets/common/Logo.png\";\nimport { useNavigate, useSearchParams } from \"react-router-dom\";\nimport {\n  FallingLines,\n  RotatingLines,\n  ThreeCircles,\n} from \"react-loader-spinner\";\nimport AuthService from \"../../../services/auth/auth.service\";\nimport { useDispatch, useSelector } from \"react-redux\";\n\ntype IDeleteRecord = {\n  isShow: boolean;\n  data: any;\n  businessId: number;\n};\n\nconst GMBCallback: FunctionComponent<PageProps> = ({ title }) => {\n  const dispatch = useDispatch();\n  const { userInfo } = useSelector((state: any) => state.authReducer);\n  const _authService = new AuthService(dispatch);\n  const [searchParams] = useSearchParams();\n  const [authStatus, setAuthStatus] = useState(null);\n  const navigate = useNavigate();\n\n  const [countdown, setCountdown] = useState<number>(0); // Initially null (no countdown running)\n  const [isRunning, setIsRunning] = useState(false); // Tracks if timer is active\n\n  useEffect(() => {\n    let interval: any;\n    let timeout: any;\n\n    if (isRunning && countdown > 0) {\n      interval = setInterval(() => {\n        setCountdown((prev) => prev - 1);\n      }, 1000);\n    } else if (isRunning && countdown === 0) {\n      clearInterval(interval);\n      navigate(\"/home\");\n    }\n\n    return () => {\n      clearInterval(interval);\n      clearTimeout(timeout);\n    };\n  }, [countdown, isRunning, navigate]);\n\n  useEffect(() => {\n    gmbCallBack(searchParams.get(\"code\"), searchParams.get(\"state\"));\n  }, [searchParams]);\n\n  const startCountdown = () => {\n    setCountdown(5); // Set countdown to 5 seconds\n    setIsRunning(true); // Start countdown\n  };\n\n  const gmbCallBack = async (code: any, state: any) => {\n    try {\n      const result = await _authService.gmbcallback(code, state, userInfo.id);\n      if (result.data) {\n        startCountdown();\n      }\n      setAuthStatus(result.data);\n      return;\n    } catch (error) {\n      console.error(error);\n    }\n  };\n\n  return (\n    <Box\n      sx={{\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        backgroundColor: \"white\",\n        padding: 4,\n        borderRadius: 2,\n        boxShadow: 3,\n        width: \"400px\",\n        margin: \"auto\",\n      }}\n    >\n      <Box sx={{ display: \"flex\", alignItems: \"center\", gap: 1 }}>\n        <img alt=\"AQIB Softech\" src={applicationLogo} style={{ height: 50 }} />\n        <Typography variant=\"h6\">+</Typography>\n        <img alt=\"AQIB Softech\" src={googleLogo} style={{ height: 50 }} />\n      </Box>\n      {authStatus != null && authStatus ? (\n        <Box>\n          <Box sx={{ display: \"flex\", alignItems: \"center\", marginTop: 2 }}>\n            <ErrorOutlineIcon color=\"success\" sx={{ fontSize: 40 }} />\n            <Typography variant=\"body1\" color=\"success\" sx={{ marginLeft: 1 }}>\n              Authentication Success\n            </Typography>\n          </Box>\n          <Typography variant=\"body1\" color=\"success\" sx={{ marginLeft: 1 }}>\n            Redirecting you in {countdown} Seconds\n          </Typography>\n        </Box>\n      ) : authStatus === null ? (\n        <Box sx={{ display: \"flex\", alignItems: \"center\", margin: 10 }}>\n          <ThreeCircles\n            visible={true}\n            height=\"100\"\n            width=\"100\"\n            color=\"#467fea\"\n            ariaLabel=\"three-circles-loading\"\n            wrapperStyle={{}}\n            wrapperClass=\"\"\n          />\n        </Box>\n      ) : (\n        <Box sx={{ display: \"flex\", alignItems: \"center\", marginTop: 2 }}>\n          <ErrorOutlineIcon color=\"error\" sx={{ fontSize: 40 }} />\n          <Typography variant=\"body1\" color=\"error\" sx={{ marginLeft: 1 }}>\n            Authentication Failure\n          </Typography>\n        </Box>\n      )}\n\n      <Button\n        variant=\"contained\"\n        color=\"secondary\"\n        sx={{ marginTop: 3 }}\n        onClick={() => navigate(\"/home\")}\n      >\n        Go To Dashboard\n      </Button>\n    </Box>\n  );\n};\n\nexport default GMBCallback;\n"], "mappings": ";;AAAA,SAA4BA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE9D,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAO,mDAAmD;AAC1D,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,UAAU,MAAM,2CAA2C;AAClE,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,SAASC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAC/D,SAGEC,YAAY,QACP,sBAAsB;AAC7B,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQvD,MAAMC,WAAyC,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC/D,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ;EAAS,CAAC,GAAGP,WAAW,CAAEQ,KAAU,IAAKA,KAAK,CAACC,WAAW,CAAC;EACnE,MAAMC,YAAY,GAAG,IAAIZ,WAAW,CAACQ,QAAQ,CAAC;EAC9C,MAAM,CAACK,YAAY,CAAC,GAAGf,eAAe,CAAC,CAAC;EACxC,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM0B,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAS,CAAC,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEnDD,SAAS,CAAC,MAAM;IACd,IAAIgC,QAAa;IACjB,IAAIC,OAAY;IAEhB,IAAIH,SAAS,IAAIF,SAAS,GAAG,CAAC,EAAE;MAC9BI,QAAQ,GAAGE,WAAW,CAAC,MAAM;QAC3BL,YAAY,CAAEM,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;MAClC,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM,IAAIL,SAAS,IAAIF,SAAS,KAAK,CAAC,EAAE;MACvCQ,aAAa,CAACJ,QAAQ,CAAC;MACvBL,QAAQ,CAAC,OAAO,CAAC;IACnB;IAEA,OAAO,MAAM;MACXS,aAAa,CAACJ,QAAQ,CAAC;MACvBK,YAAY,CAACJ,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACL,SAAS,EAAEE,SAAS,EAAEH,QAAQ,CAAC,CAAC;EAEpC3B,SAAS,CAAC,MAAM;IACdsC,WAAW,CAACd,YAAY,CAACe,GAAG,CAAC,MAAM,CAAC,EAAEf,YAAY,CAACe,GAAG,CAAC,OAAO,CAAC,CAAC;EAClE,CAAC,EAAE,CAACf,YAAY,CAAC,CAAC;EAElB,MAAMgB,cAAc,GAAGA,CAAA,KAAM;IAC3BX,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACjBE,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;EACtB,CAAC;EAED,MAAMO,WAAW,GAAG,MAAAA,CAAOG,IAAS,EAAEpB,KAAU,KAAK;IACnD,IAAI;MACF,MAAMqB,MAAM,GAAG,MAAMnB,YAAY,CAACoB,WAAW,CAACF,IAAI,EAAEpB,KAAK,EAAED,QAAQ,CAACwB,EAAE,CAAC;MACvE,IAAIF,MAAM,CAACG,IAAI,EAAE;QACfL,cAAc,CAAC,CAAC;MAClB;MACAd,aAAa,CAACgB,MAAM,CAACG,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB;EACF,CAAC;EAED,oBACE/B,OAAA,CAACb,GAAG;IACF8C,EAAE,EAAE;MACFC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,eAAe,EAAE,OAAO;MACxBC,OAAO,EAAE,CAAC;MACVC,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,CAAC;MACZC,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE;IACV,CAAE;IAAAC,QAAA,gBAEF5C,OAAA,CAACb,GAAG;MAAC8C,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEE,UAAU,EAAE,QAAQ;QAAES,GAAG,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACzD5C,OAAA;QAAK8C,GAAG,EAAC,cAAc;QAACC,GAAG,EAAEvD,eAAgB;QAACwD,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAG;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvErD,OAAA,CAACX,UAAU;QAACiE,OAAO,EAAC,IAAI;QAAAV,QAAA,EAAC;MAAC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACvCrD,OAAA;QAAK8C,GAAG,EAAC,cAAc;QAACC,GAAG,EAAExD,UAAW;QAACyD,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAG;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,EACL3C,UAAU,IAAI,IAAI,IAAIA,UAAU,gBAC/BV,OAAA,CAACb,GAAG;MAAAyD,QAAA,gBACF5C,OAAA,CAACb,GAAG;QAAC8C,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEmB,SAAS,EAAE;QAAE,CAAE;QAAAX,QAAA,gBAC/D5C,OAAA,CAACV,gBAAgB;UAACkE,KAAK,EAAC,SAAS;UAACvB,EAAE,EAAE;YAAEwB,QAAQ,EAAE;UAAG;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DrD,OAAA,CAACX,UAAU;UAACiE,OAAO,EAAC,OAAO;UAACE,KAAK,EAAC,SAAS;UAACvB,EAAE,EAAE;YAAEyB,UAAU,EAAE;UAAE,CAAE;UAAAd,QAAA,EAAC;QAEnE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNrD,OAAA,CAACX,UAAU;QAACiE,OAAO,EAAC,OAAO;QAACE,KAAK,EAAC,SAAS;QAACvB,EAAE,EAAE;UAAEyB,UAAU,EAAE;QAAE,CAAE;QAAAd,QAAA,GAAC,qBAC9C,EAAC/B,SAAS,EAAC,UAChC;MAAA;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,GACJ3C,UAAU,KAAK,IAAI,gBACrBV,OAAA,CAACb,GAAG;MAAC8C,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEE,UAAU,EAAE,QAAQ;QAAEO,MAAM,EAAE;MAAG,CAAE;MAAAC,QAAA,eAC7D5C,OAAA,CAACL,YAAY;QACXgE,OAAO,EAAE,IAAK;QACdV,MAAM,EAAC,KAAK;QACZP,KAAK,EAAC,KAAK;QACXc,KAAK,EAAC,SAAS;QACfI,SAAS,EAAC,uBAAuB;QACjCC,YAAY,EAAE,CAAC,CAAE;QACjBC,YAAY,EAAC;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,gBAENrD,OAAA,CAACb,GAAG;MAAC8C,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEE,UAAU,EAAE,QAAQ;QAAEmB,SAAS,EAAE;MAAE,CAAE;MAAAX,QAAA,gBAC/D5C,OAAA,CAACV,gBAAgB;QAACkE,KAAK,EAAC,OAAO;QAACvB,EAAE,EAAE;UAAEwB,QAAQ,EAAE;QAAG;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxDrD,OAAA,CAACX,UAAU;QAACiE,OAAO,EAAC,OAAO;QAACE,KAAK,EAAC,OAAO;QAACvB,EAAE,EAAE;UAAEyB,UAAU,EAAE;QAAE,CAAE;QAAAd,QAAA,EAAC;MAEjE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAEDrD,OAAA,CAACZ,MAAM;MACLkE,OAAO,EAAC,WAAW;MACnBE,KAAK,EAAC,WAAW;MACjBvB,EAAE,EAAE;QAAEsB,SAAS,EAAE;MAAE,CAAE;MACrBQ,OAAO,EAAEA,CAAA,KAAMnD,QAAQ,CAAC,OAAO,CAAE;MAAAgC,QAAA,EAClC;IAED;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAClD,EAAA,CAnHIF,WAAyC;EAAA,QAC5BJ,WAAW,EACPC,WAAW,EAETJ,eAAe,EAErBD,WAAW;AAAA;AAAAuE,EAAA,GANxB/D,WAAyC;AAqH/C,eAAeA,WAAW;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}