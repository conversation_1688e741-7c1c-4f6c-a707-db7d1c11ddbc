{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\genericDrawer\\\\genericDrawer.component.tsx\";\nimport { Box, Drawer } from \"@mui/material\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GenericDrawer = props => {\n  return /*#__PURE__*/_jsxDEV(Drawer, {\n    anchor: \"right\",\n    open: props.isShow,\n    ModalProps: {\n      // onBackdropClick: () => props.callback(),\n      // setOpenAddEdit({ isShow: false, data: null, userId: 0 }),\n    },\n    sx: {\n      \"& .MuiDrawer-paper\": {\n        minWidth: \"400px\",\n        maxWidth: \"400px\",\n        // Set the max width\n        width: \"100%\" // Ensure the drawer does not exceed the max width\n      },\n      zIndex: theme => {\n        return theme.zIndex.drawer + 1;\n      }\n    }\n    // PaperProps={{ style: { width: window.innerWidth * 0.25 } }}\n    ,\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      className: \"height100\",\n      children: props.component\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = GenericDrawer;\nexport default GenericDrawer;\nvar _c;\n$RefreshReg$(_c, \"GenericDrawer\");", "map": {"version": 3, "names": ["Box", "Drawer", "jsxDEV", "_jsxDEV", "GenericDrawer", "props", "anchor", "open", "isShow", "ModalProps", "sx", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "width", "zIndex", "theme", "drawer", "children", "className", "component", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/genericDrawer/genericDrawer.component.tsx"], "sourcesContent": ["import Avatar from \"@mui/material/Avatar\";\nimport { Box, Drawer, Grid, Grid2, Stack } from \"@mui/material\";\n\nconst GenericDrawer = (props: {\n  component: React.ReactNode;\n  isShow: boolean;\n  callback: () => undefined | null | void;\n}) => {\n  return (\n    <Drawer\n      anchor={\"right\"}\n      open={props.isShow}\n      ModalProps={\n        {\n          // onBackdropClick: () => props.callback(),\n          // setOpenAddEdit({ isShow: false, data: null, userId: 0 }),\n        }\n      }\n      sx={{\n        \"& .MuiDrawer-paper\": {\n          minWidth: \"400px\",\n          maxWidth: \"400px\", // Set the max width\n          width: \"100%\", // Ensure the drawer does not exceed the max width\n        },\n        zIndex: (theme) => {\n          return theme.zIndex.drawer + 1;\n        },\n      }}\n      // PaperProps={{ style: { width: window.innerWidth * 0.25 } }}\n    >\n      <Box className=\"height100\">{props.component}</Box>\n    </Drawer>\n  );\n};\n\nexport default GenericDrawer;\n"], "mappings": ";AACA,SAASA,GAAG,EAAEC,MAAM,QAA4B,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,aAAa,GAAIC,KAItB,IAAK;EACJ,oBACEF,OAAA,CAACF,MAAM;IACLK,MAAM,EAAE,OAAQ;IAChBC,IAAI,EAAEF,KAAK,CAACG,MAAO;IACnBC,UAAU,EACR;MACE;MACA;IAAA,CAEH;IACDC,EAAE,EAAE;MACF,oBAAoB,EAAE;QACpBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,OAAO;QAAE;QACnBC,KAAK,EAAE,MAAM,CAAE;MACjB,CAAC;MACDC,MAAM,EAAGC,KAAK,IAAK;QACjB,OAAOA,KAAK,CAACD,MAAM,CAACE,MAAM,GAAG,CAAC;MAChC;IACF;IACA;IAAA;IAAAC,QAAA,eAEAd,OAAA,CAACH,GAAG;MAACkB,SAAS,EAAC,WAAW;MAAAD,QAAA,EAAEZ,KAAK,CAACc;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5C,CAAC;AAEb,CAAC;AAACC,EAAA,GA9BIpB,aAAa;AAgCnB,eAAeA,aAAa;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}