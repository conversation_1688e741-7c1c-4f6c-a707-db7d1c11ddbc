require('dotenv').config({ path: '.env.development' });
const pool = require('../config/db');

async function testDatabaseConnection() {
  console.log('🔗 Testing database connection...');
  console.log(`📍 Host: ${process.env.APP_DB_HOST}`);
  console.log(`📍 Database: ${process.env.APP_DB_NAME}`);
  console.log(`📍 User: ${process.env.APP_DB_USER}`);
  
  try {
    // Test basic connection
    const result = await pool.query('SELECT 1 as test');
    console.log('✅ Database connection established successfully!');
    
    // Test basic query
    console.log('🧪 Testing basic query...');
    console.log('✅ Basic query successful:', result[0]);
    
    // Check database version
    console.log('🔍 Checking database version...');
    const versionResult = await pool.query('SELECT VERSION() as version');
    console.log('✅ MySQL Version:', versionResult[0].version);
    
    // Check if posts table exists
    console.log('🔍 Checking if gmb_posts table exists...');
    const tableCheck = await pool.query(`
      SELECT COUNT(*) as table_exists 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = 'gmb_posts'
    `, [process.env.APP_DB_NAME]);
    
    if (tableCheck[0].table_exists > 0) {
      console.log('✅ gmb_posts table already exists');
      
      // Get table info
      const tableInfo = await pool.query('DESCRIBE gmb_posts');
      console.log('📋 Table structure:');
      tableInfo.forEach(column => {
        console.log(`   - ${column.Field}: ${column.Type} ${column.Null === 'NO' ? 'NOT NULL' : 'NULL'}`);
      });
      
      // Get row count
      const countResult = await pool.query('SELECT COUNT(*) as row_count FROM gmb_posts');
      console.log(`📊 Current rows in gmb_posts: ${countResult[0].row_count}`);
    } else {
      console.log('⚠️ gmb_posts table does not exist');
      console.log('💡 Run: npm run setup:posts-table to create it');
    }
    
    console.log('\n🎉 Database connection test completed successfully!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('🔧 Please check your .env.development file and database credentials');
    process.exit(1);
  }
}

testDatabaseConnection();
