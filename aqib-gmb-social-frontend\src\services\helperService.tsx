import { Component } from "react";
import moment from "moment";

class HelperService extends Component {
  isValidateEmail = async (text: string) => {
    try {
      let reg = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w\w+)+$/;
      if (reg.test(text) === false) {
        console.log("In-Valid Email in Application Helper Service");
        return false;
      } else {
        console.log("Valid Email in Application Helper Service");
        return true;
      }
    } catch (error) {
      return false;
    }
  };

  getUserDateTimeFormat = (timestamp: any) => {
    return moment(new Date(timestamp)).format("DD-MM-YYYY hh:mm A"); // 12H clock (AM/PM)
  };

  getExpandedDateTimeFormat = (timestamp: any) => {
    return moment(new Date(timestamp)).format("MMMM DD, YYYY hh:mm A"); // 12H clock (AM/PM)
  };

  getFormatedDate = (date = new Date(), format = "YYYY/MM/DD") =>
    moment(date).format(format);

  getExpandedFormatedDate = (date = new Date(), format = "DD-MMM-YYYY") =>
    moment(date).format(format);
}

export default HelperService;
