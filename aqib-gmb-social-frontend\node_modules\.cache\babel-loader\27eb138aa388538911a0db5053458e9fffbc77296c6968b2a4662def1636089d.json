{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\screens\\\\signIn\\\\signIn.screen.tsx\",\n  _s = $RefreshSig$();\nimport * as React from \"react\";\nimport { useContext, useEffect, useState } from \"react\";\n//Widgets\nimport Box from \"@mui/material/Box\";\nimport Grid from \"@mui/material/Grid\";\nimport TextField from \"@mui/material/TextField\";\nimport { InputAdornment } from \"@mui/material\";\nimport FormControlLabel from \"@mui/material/FormControlLabel\";\nimport Checkbox from \"@mui/material/Checkbox\";\nimport Button from \"@mui/material/Button\";\nimport { FormHelperText, Typography } from \"@mui/material\";\nimport Visibility from \"@mui/icons-material/Visibility\";\nimport VisibilityOff from \"@mui/icons-material/VisibilityOff\";\nimport IconButton from \"@mui/material/IconButton\";\n//Icons\nimport MailOutlinedIcon from \"@mui/icons-material/MailOutlined\";\nimport VpnKeyOutlinedIcon from \"@mui/icons-material/VpnKeyOutlined\";\n\n//Css Import\nimport \"../signIn/signIn.screen.style.css\";\nimport * as yup from \"yup\";\nimport { Formik } from \"formik\";\nimport { useDispatch } from \"react-redux\";\nimport { authInitiate } from \"../../actions/auth.actions\";\nimport { LoadingContext } from \"../../context/loading.context\";\nimport { ToastContext } from \"../../context/toast.context\";\nimport { ToastSeverity } from \"../../constants/toastSeverity.constant\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SignIn = ({\n  title\n}) => {\n  _s();\n  const [showPassword, setShowPassword] = React.useState(false);\n  const {\n    setToastConfig,\n    setOpen\n  } = useContext(ToastContext);\n  const handleClickShowPassword = () => setShowPassword(show => !show);\n  const handleMouseDownPassword = event => {\n    event.preventDefault();\n  };\n  const handleMouseUpPassword = event => {\n    event.preventDefault();\n  };\n  const {\n    setLoading\n  } = useContext(LoadingContext);\n  const dispatch = useDispatch();\n  const loginUser = (payload, setLoading) => dispatch(authInitiate(payload, setLoading));\n  const [rememberMe, setRememberMe] = useState(false);\n  const InitialValues = {\n    email: \"\",\n    password: \"\"\n  };\n  const [loginCreds, setLoginCreds] = useState(InitialValues);\n  useEffect(() => {\n    document.title = title;\n    const savedRememberMe = localStorage.getItem(\"rememberMe\") === \"true\";\n    const savedCredentials = localStorage.getItem(\"savedCreds\");\n    if (savedCredentials && savedRememberMe) {\n      setLoginCreds(JSON.parse(savedCredentials));\n    }\n  }, []);\n  const SignInSchema = yup.object().shape({\n    email: yup.string().email(\"Please enter a valid email\").required(\"This field is required\"),\n    password: yup.string().required(\"Password is required\")\n  });\n  const _handleSignIn = async (values, formikHelpers) => {\n    try {\n      if (rememberMe) {\n        localStorage.setItem(\"savedCreds\", JSON.stringify(values));\n        localStorage.setItem(\"rememberMe\", \"true\");\n      } else {\n        localStorage.removeItem(\"savedCreds\");\n        localStorage.removeItem(\"rememberMe\");\n      }\n      const isValid = await SignInSchema.isValid(values);\n      console.log(isValid);\n      if (isValid) {\n        formikHelpers.setSubmitting(true);\n        loginUser(values, setLoading);\n      }\n    } catch (error) {\n      console.log(error);\n      setToastConfig(ToastSeverity.Error, error.response.data.error, true);\n    } finally {\n      formikHelpers.setSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Formik, {\n    initialValues: {\n      ...loginCreds\n    },\n    validationSchema: SignInSchema,\n    onSubmit: (values, formikHelpers) => _handleSignIn(values, formikHelpers),\n    enableReinitialize: true,\n    children: ({\n      values,\n      errors,\n      touched,\n      handleChange,\n      handleBlur,\n      handleSubmit,\n      isSubmitting,\n      isValid\n      /* and other goodies */\n    }) => /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"height100\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"height100\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          className: \"height100\",\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            className: \"height100 marT0\",\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              lg: 6,\n              className: \"pad0\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                className: \"accountLeft\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  alt: \"MyLocoBiz - Login\",\n                  className: \"width100\",\n                  src: require(\"../../assets/login/loginLeftSlider.png\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              lg: 6,\n              className: \"pad0\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                className: \"accountRight\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  className: \"accountTopPart\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    className: \"commonTitle welcomeTitle\",\n                    children: \"Welcome to\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    className: \"accountLogo\",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      alt: \"MyLocoBiz - Logo\",\n                      className: \"width100\",\n                      src: require(\"../../assets/common/Logo.png\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 150,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  className: \"accountBodyPart\",\n                  children: /*#__PURE__*/_jsxDEV(Grid, {\n                    container: true,\n                    spacing: 2,\n                    children: [/*#__PURE__*/_jsxDEV(Grid, {\n                      item: true,\n                      xs: 12,\n                      md: 12,\n                      lg: 12,\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        className: \"commonInput\",\n                        children: [/*#__PURE__*/_jsxDEV(MailOutlinedIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 184,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                          id: \"email\",\n                          className: \"width100\",\n                          label: \"Email\",\n                          type: \"email\",\n                          variant: \"filled\",\n                          onChange: handleChange,\n                          value: values.email\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 185,\n                          columnNumber: 29\n                        }, this), errors.email && touched.email && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                          className: \"errorMessage\",\n                          children: errors.email\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 195,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 183,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 159,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                      item: true,\n                      xs: 12,\n                      md: 12,\n                      lg: 12,\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        className: \"commonInput\",\n                        children: [/*#__PURE__*/_jsxDEV(VpnKeyOutlinedIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 203,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                          id: \"password\",\n                          className: \"width100\",\n                          label: \"Password\",\n                          type: showPassword ? \"text\" : \"password\",\n                          variant: \"filled\",\n                          onChange: handleChange,\n                          value: values.password,\n                          InputProps: {\n                            disableUnderline: true,\n                            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                              position: \"end\",\n                              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                                onClick: handleClickShowPassword,\n                                edge: \"end\",\n                                children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 221,\n                                  columnNumber: 41\n                                }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 223,\n                                  columnNumber: 41\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 216,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 215,\n                              columnNumber: 35\n                            }, this)\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 204,\n                          columnNumber: 29\n                        }, this), errors.password && touched.password && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                          className: \"errorMessage\",\n                          children: errors.password\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 231,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 202,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                      item: true,\n                      xs: 6,\n                      md: 6,\n                      lg: 6,\n                      children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                        control: /*#__PURE__*/_jsxDEV(Checkbox, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 239,\n                          columnNumber: 38\n                        }, this),\n                        label: \"Remember Me\",\n                        checked: rememberMe,\n                        onChange: () => setRememberMe(!rememberMe)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 238,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                      item: true,\n                      xs: 12,\n                      md: 12,\n                      lg: 12,\n                      children: /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"contained\",\n                        className: \"primaryFillBtn width100\",\n                        type: \"submit\",\n                        children: \"Login\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 251,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n};\n_s(SignIn, \"HFKY9BUxaY09WyIoL3Oy6L2LVzE=\", false, function () {\n  return [useDispatch];\n});\n_c = SignIn;\nexport default SignIn;\nvar _c;\n$RefreshReg$(_c, \"SignIn\");", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useState", "Box", "Grid", "TextField", "InputAdornment", "FormControlLabel", "Checkbox", "<PERSON><PERSON>", "FormHelperText", "Typography", "Visibility", "VisibilityOff", "IconButton", "MailOutlinedIcon", "VpnKeyOutlinedIcon", "yup", "<PERSON><PERSON>", "useDispatch", "authInitiate", "LoadingContext", "ToastContext", "ToastSeverity", "jsxDEV", "_jsxDEV", "SignIn", "title", "_s", "showPassword", "setShowPassword", "setToastConfig", "<PERSON><PERSON><PERSON>", "handleClickShowPassword", "show", "handleMouseDownPassword", "event", "preventDefault", "handleMouseUpPassword", "setLoading", "dispatch", "loginUser", "payload", "rememberMe", "setRememberMe", "InitialValues", "email", "password", "loginCreds", "setLogin<PERSON>reds", "document", "savedRememberMe", "localStorage", "getItem", "savedCredentials", "JSON", "parse", "SignInSchema", "object", "shape", "string", "required", "_handleSignIn", "values", "formikHelpers", "setItem", "stringify", "removeItem", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "setSubmitting", "error", "Error", "response", "data", "initialValues", "validationSchema", "onSubmit", "enableReinitialize", "children", "errors", "touched", "handleChange", "handleBlur", "handleSubmit", "isSubmitting", "className", "container", "spacing", "item", "xs", "md", "lg", "alt", "src", "require", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "label", "type", "variant", "onChange", "value", "InputProps", "disableUnderline", "endAdornment", "position", "onClick", "edge", "control", "checked", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/screens/signIn/signIn.screen.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { FunctionComponent, useContext, useEffect, useState } from \"react\";\nimport PageProps from \"../../models/PageProps.interface\";\n\n//Widgets\nimport Box from \"@mui/material/Box\";\nimport Grid from \"@mui/material/Grid\";\nimport TextField from \"@mui/material/TextField\";\nimport { InputAdornment } from \"@mui/material\";\nimport FormControlLabel from \"@mui/material/FormControlLabel\";\nimport Checkbox from \"@mui/material/Checkbox\";\nimport Link from \"@mui/material/Link\";\nimport Button from \"@mui/material/Button\";\nimport { FormHelperText, Typography } from \"@mui/material\";\n\nimport FormControl from \"@mui/material/FormControl\";\nimport Visibility from \"@mui/icons-material/Visibility\";\nimport VisibilityOff from \"@mui/icons-material/VisibilityOff\";\nimport IconButton from \"@mui/material/IconButton\";\nimport FilledInput from \"@mui/material/FilledInput\";\nimport InputLabel from \"@mui/material/InputLabel\";\n\n//Icons\nimport MailOutlinedIcon from \"@mui/icons-material/MailOutlined\";\nimport VpnKeyOutlinedIcon from \"@mui/icons-material/VpnKeyOutlined\";\n\n//Css Import\nimport \"../signIn/signIn.screen.style.css\";\nimport * as yup from \"yup\";\nimport { Formik } from \"formik\";\nimport { ILoginModel } from \"../../interfaces/request/ILoginModel\";\nimport { useDispatch } from \"react-redux\";\nimport { authInitiate } from \"../../actions/auth.actions\";\nimport { ILoginRequestModel } from \"../../interfaces/ILoginRequestModel\";\nimport { LoadingContext } from \"../../context/loading.context\";\nimport { ToastContext } from \"../../context/toast.context\";\nimport { ToastSeverity } from \"../../constants/toastSeverity.constant\";\nimport { MessageConstants } from \"../../constants/message.constant\";\n\nconst SignIn: FunctionComponent<PageProps> = ({ title }) => {\n  const [showPassword, setShowPassword] = React.useState(false);\n  const { setToastConfig, setOpen } = useContext(ToastContext);\n  const handleClickShowPassword = () => setShowPassword((show) => !show);\n\n  const handleMouseDownPassword = (\n    event: React.MouseEvent<HTMLButtonElement>\n  ) => {\n    event.preventDefault();\n  };\n\n  const handleMouseUpPassword = (\n    event: React.MouseEvent<HTMLButtonElement>\n  ) => {\n    event.preventDefault();\n  };\n\n  const { setLoading } = useContext(LoadingContext);\n  const dispatch = useDispatch();\n  const loginUser = (\n    payload: ILoginModel,\n    setLoading: (loading: boolean) => void\n  ) => dispatch<any>(authInitiate(payload, setLoading));\n  const [rememberMe, setRememberMe] = useState(false);\n  const InitialValues: ILoginRequestModel = {\n    email: \"\",\n    password: \"\",\n  };\n\n  const [loginCreds, setLoginCreds] =\n    useState<ILoginRequestModel>(InitialValues);\n\n  useEffect(() => {\n    document.title = title;\n    const savedRememberMe = localStorage.getItem(\"rememberMe\") === \"true\";\n    const savedCredentials = localStorage.getItem(\"savedCreds\");\n    if (savedCredentials && savedRememberMe) {\n      setLoginCreds(JSON.parse(savedCredentials));\n    }\n  }, []);\n\n  const SignInSchema = yup.object().shape({\n    email: yup\n      .string()\n      .email(\"Please enter a valid email\")\n      .required(\"This field is required\"),\n    password: yup.string().required(\"Password is required\"),\n  });\n\n  const _handleSignIn = async (values: ILoginModel, formikHelpers: any) => {\n    try {\n      if (rememberMe) {\n        localStorage.setItem(\"savedCreds\", JSON.stringify(values));\n        localStorage.setItem(\"rememberMe\", \"true\");\n      } else {\n        localStorage.removeItem(\"savedCreds\");\n        localStorage.removeItem(\"rememberMe\");\n      }\n      const isValid = await SignInSchema.isValid(values);\n      console.log(isValid);\n      if (isValid) {\n        formikHelpers.setSubmitting(true);\n        loginUser(values, setLoading);\n      }\n    } catch (error: any) {\n      console.log(error);\n      setToastConfig(ToastSeverity.Error, error.response.data.error, true);\n    } finally {\n      formikHelpers.setSubmitting(false);\n    }\n  };\n\n  return (\n    <Formik\n      initialValues={{ ...loginCreds }}\n      validationSchema={SignInSchema}\n      onSubmit={(values, formikHelpers) => _handleSignIn(values, formikHelpers)}\n      enableReinitialize\n    >\n      {({\n        values,\n        errors,\n        touched,\n        handleChange,\n        handleBlur,\n        handleSubmit,\n        isSubmitting,\n        isValid,\n        /* and other goodies */\n      }) => (\n        <form onSubmit={handleSubmit} className=\"height100\">\n          <div className=\"height100\">\n            <Box className=\"height100\">\n              <Grid container spacing={2} className=\"height100 marT0\">\n                <Grid item xs={12} md={6} lg={6} className=\"pad0\">\n                  <Box className=\"accountLeft\">\n                    <img\n                      alt=\"MyLocoBiz - Login\"\n                      className=\"width100\"\n                      src={require(\"../../assets/login/loginLeftSlider.png\")}\n                    />\n                  </Box>\n                </Grid>\n                <Grid item xs={12} md={6} lg={6} className=\"pad0\">\n                  <Box className=\"accountRight\">\n                    <Box className=\"accountTopPart\">\n                      <Typography className=\"commonTitle welcomeTitle\">\n                        Welcome to\n                      </Typography>\n                      <Box className=\"accountLogo\">\n                        <img\n                          alt=\"MyLocoBiz - Logo\"\n                          className=\"width100\"\n                          src={require(\"../../assets/common/Logo.png\")}\n                        />\n                      </Box>\n                    </Box>\n                    <Box className=\"accountBodyPart\">\n                      <Grid container spacing={2}>\n                        <Grid item xs={12} md={12} lg={12}>\n                          {/* <Box className=\"commonInput\">\n                            <TextField\n                              id=\"email\"\n                              className=\"width100\"\n                              label=\"Email\"\n                              type=\"email\"\n                              variant=\"filled\"\n                              onChange={handleChange}\n                              value={values.email}\n                              InputProps={{\n                                startAdornment: (\n                                  <InputAdornment position=\"start\">\n                                    <EmailIcon />\n                                  </InputAdornment>\n                                ),\n                              }}\n                            />\n                            {errors.email && touched.email && (\n                              <FormHelperText className=\"errorMessage\">\n                                {errors.email}\n                              </FormHelperText>\n                            )}\n                          </Box> */}\n                          <Box className=\"commonInput\">\n                            <MailOutlinedIcon />\n                            <TextField\n                              id=\"email\"\n                              className=\"width100\"\n                              label=\"Email\"\n                              type=\"email\"\n                              variant=\"filled\"\n                              onChange={handleChange}\n                              value={values.email}\n                            />\n                            {errors.email && touched.email && (\n                              <FormHelperText className=\"errorMessage\">\n                                {errors.email}\n                              </FormHelperText>\n                            )}\n                          </Box>\n                        </Grid>\n                        <Grid item xs={12} md={12} lg={12}>\n                          <Box className=\"commonInput\">\n                            <VpnKeyOutlinedIcon />\n                            <TextField\n                              id=\"password\"\n                              className=\"width100\"\n                              label=\"Password\"\n                              type={showPassword ? \"text\" : \"password\"}\n                              variant=\"filled\"\n                              onChange={handleChange}\n                              value={values.password}\n                              InputProps={{\n                                disableUnderline: true,\n                                endAdornment: (\n                                  <InputAdornment position=\"end\">\n                                    <IconButton\n                                      onClick={handleClickShowPassword}\n                                      edge=\"end\"\n                                    >\n                                      {showPassword ? (\n                                        <VisibilityOff />\n                                      ) : (\n                                        <Visibility />\n                                      )}\n                                    </IconButton>\n                                  </InputAdornment>\n                                ),\n                              }}\n                            />\n                            {errors.password && touched.password && (\n                              <FormHelperText className=\"errorMessage\">\n                                {errors.password}\n                              </FormHelperText>\n                            )}\n                          </Box>\n                        </Grid>\n                        <Grid item xs={6} md={6} lg={6}>\n                          <FormControlLabel\n                            control={<Checkbox />}\n                            label=\"Remember Me\"\n                            checked={rememberMe}\n                            onChange={() => setRememberMe(!rememberMe)}\n                          />\n                        </Grid>\n                        {/* <Grid item xs={6} md={6} lg={6}>\n                          <Link href=\"/forgot-password\" className=\"floatR lh42\">\n                            Forgot Password\n                          </Link>\n                        </Grid> */}\n                        <Grid item xs={12} md={12} lg={12}>\n                          <Button\n                            variant=\"contained\"\n                            className=\"primaryFillBtn width100\"\n                            type=\"submit\"\n                          >\n                            Login\n                          </Button>\n                        </Grid>\n                      </Grid>\n                    </Box>\n                  </Box>\n                </Grid>\n              </Grid>\n            </Box>\n          </div>\n        </form>\n      )}\n    </Formik>\n  );\n};\n\nexport default SignIn;\n"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAA4BC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAG1E;AACA,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,QAAQ,MAAM,wBAAwB;AAE7C,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,cAAc,EAAEC,UAAU,QAAQ,eAAe;AAG1D,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,OAAOC,UAAU,MAAM,0BAA0B;AAIjD;AACA,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,kBAAkB,MAAM,oCAAoC;;AAEnE;AACA,OAAO,mCAAmC;AAC1C,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SAASC,MAAM,QAAQ,QAAQ;AAE/B,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,YAAY,QAAQ,4BAA4B;AAEzD,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,aAAa,QAAQ,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGvE,MAAMC,MAAoC,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/B,KAAK,CAACG,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM;IAAE6B,cAAc;IAAEC;EAAQ,CAAC,GAAGhC,UAAU,CAACsB,YAAY,CAAC;EAC5D,MAAMW,uBAAuB,GAAGA,CAAA,KAAMH,eAAe,CAAEI,IAAI,IAAK,CAACA,IAAI,CAAC;EAEtE,MAAMC,uBAAuB,GAC3BC,KAA0C,IACvC;IACHA,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB,CAAC;EAED,MAAMC,qBAAqB,GACzBF,KAA0C,IACvC;IACHA,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB,CAAC;EAED,MAAM;IAAEE;EAAW,CAAC,GAAGvC,UAAU,CAACqB,cAAc,CAAC;EACjD,MAAMmB,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,SAAS,GAAGA,CAChBC,OAAoB,EACpBH,UAAsC,KACnCC,QAAQ,CAAMpB,YAAY,CAACsB,OAAO,EAAEH,UAAU,CAAC,CAAC;EACrD,MAAM,CAACI,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM2C,aAAiC,GAAG;IACxCC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC;EAED,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAC/B/C,QAAQ,CAAqB2C,aAAa,CAAC;EAE7C5C,SAAS,CAAC,MAAM;IACdiD,QAAQ,CAACvB,KAAK,GAAGA,KAAK;IACtB,MAAMwB,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,KAAK,MAAM;IACrE,MAAMC,gBAAgB,GAAGF,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAC3D,IAAIC,gBAAgB,IAAIH,eAAe,EAAE;MACvCF,aAAa,CAACM,IAAI,CAACC,KAAK,CAACF,gBAAgB,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,YAAY,GAAGxC,GAAG,CAACyC,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IACtCb,KAAK,EAAE7B,GAAG,CACP2C,MAAM,CAAC,CAAC,CACRd,KAAK,CAAC,4BAA4B,CAAC,CACnCe,QAAQ,CAAC,wBAAwB,CAAC;IACrCd,QAAQ,EAAE9B,GAAG,CAAC2C,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,sBAAsB;EACxD,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAG,MAAAA,CAAOC,MAAmB,EAAEC,aAAkB,KAAK;IACvE,IAAI;MACF,IAAIrB,UAAU,EAAE;QACdS,YAAY,CAACa,OAAO,CAAC,YAAY,EAAEV,IAAI,CAACW,SAAS,CAACH,MAAM,CAAC,CAAC;QAC1DX,YAAY,CAACa,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC;MAC5C,CAAC,MAAM;QACLb,YAAY,CAACe,UAAU,CAAC,YAAY,CAAC;QACrCf,YAAY,CAACe,UAAU,CAAC,YAAY,CAAC;MACvC;MACA,MAAMC,OAAO,GAAG,MAAMX,YAAY,CAACW,OAAO,CAACL,MAAM,CAAC;MAClDM,OAAO,CAACC,GAAG,CAACF,OAAO,CAAC;MACpB,IAAIA,OAAO,EAAE;QACXJ,aAAa,CAACO,aAAa,CAAC,IAAI,CAAC;QACjC9B,SAAS,CAACsB,MAAM,EAAExB,UAAU,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOiC,KAAU,EAAE;MACnBH,OAAO,CAACC,GAAG,CAACE,KAAK,CAAC;MAClBzC,cAAc,CAACR,aAAa,CAACkD,KAAK,EAAED,KAAK,CAACE,QAAQ,CAACC,IAAI,CAACH,KAAK,EAAE,IAAI,CAAC;IACtE,CAAC,SAAS;MACRR,aAAa,CAACO,aAAa,CAAC,KAAK,CAAC;IACpC;EACF,CAAC;EAED,oBACE9C,OAAA,CAACP,MAAM;IACL0D,aAAa,EAAE;MAAE,GAAG5B;IAAW,CAAE;IACjC6B,gBAAgB,EAAEpB,YAAa;IAC/BqB,QAAQ,EAAEA,CAACf,MAAM,EAAEC,aAAa,KAAKF,aAAa,CAACC,MAAM,EAAEC,aAAa,CAAE;IAC1Ee,kBAAkB;IAAAC,QAAA,EAEjBA,CAAC;MACAjB,MAAM;MACNkB,MAAM;MACNC,OAAO;MACPC,YAAY;MACZC,UAAU;MACVC,YAAY;MACZC,YAAY;MACZlB;MACA;IACF,CAAC,kBACC3C,OAAA;MAAMqD,QAAQ,EAAEO,YAAa;MAACE,SAAS,EAAC,WAAW;MAAAP,QAAA,eACjDvD,OAAA;QAAK8D,SAAS,EAAC,WAAW;QAAAP,QAAA,eACxBvD,OAAA,CAACtB,GAAG;UAACoF,SAAS,EAAC,WAAW;UAAAP,QAAA,eACxBvD,OAAA,CAACrB,IAAI;YAACoF,SAAS;YAACC,OAAO,EAAE,CAAE;YAACF,SAAS,EAAC,iBAAiB;YAAAP,QAAA,gBACrDvD,OAAA,CAACrB,IAAI;cAACsF,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAACN,SAAS,EAAC,MAAM;cAAAP,QAAA,eAC/CvD,OAAA,CAACtB,GAAG;gBAACoF,SAAS,EAAC,aAAa;gBAAAP,QAAA,eAC1BvD,OAAA;kBACEqE,GAAG,EAAC,mBAAmB;kBACvBP,SAAS,EAAC,UAAU;kBACpBQ,GAAG,EAAEC,OAAO,CAAC,wCAAwC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACP3E,OAAA,CAACrB,IAAI;cAACsF,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAACN,SAAS,EAAC,MAAM;cAAAP,QAAA,eAC/CvD,OAAA,CAACtB,GAAG;gBAACoF,SAAS,EAAC,cAAc;gBAAAP,QAAA,gBAC3BvD,OAAA,CAACtB,GAAG;kBAACoF,SAAS,EAAC,gBAAgB;kBAAAP,QAAA,gBAC7BvD,OAAA,CAACd,UAAU;oBAAC4E,SAAS,EAAC,0BAA0B;oBAAAP,QAAA,EAAC;kBAEjD;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb3E,OAAA,CAACtB,GAAG;oBAACoF,SAAS,EAAC,aAAa;oBAAAP,QAAA,eAC1BvD,OAAA;sBACEqE,GAAG,EAAC,kBAAkB;sBACtBP,SAAS,EAAC,UAAU;sBACpBQ,GAAG,EAAEC,OAAO,CAAC,8BAA8B;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3E,OAAA,CAACtB,GAAG;kBAACoF,SAAS,EAAC,iBAAiB;kBAAAP,QAAA,eAC9BvD,OAAA,CAACrB,IAAI;oBAACoF,SAAS;oBAACC,OAAO,EAAE,CAAE;oBAAAT,QAAA,gBACzBvD,OAAA,CAACrB,IAAI;sBAACsF,IAAI;sBAACC,EAAE,EAAE,EAAG;sBAACC,EAAE,EAAE,EAAG;sBAACC,EAAE,EAAE,EAAG;sBAAAb,QAAA,eAwBhCvD,OAAA,CAACtB,GAAG;wBAACoF,SAAS,EAAC,aAAa;wBAAAP,QAAA,gBAC1BvD,OAAA,CAACV,gBAAgB;0BAAAkF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACpB3E,OAAA,CAACpB,SAAS;0BACRgG,EAAE,EAAC,OAAO;0BACVd,SAAS,EAAC,UAAU;0BACpBe,KAAK,EAAC,OAAO;0BACbC,IAAI,EAAC,OAAO;0BACZC,OAAO,EAAC,QAAQ;0BAChBC,QAAQ,EAAEtB,YAAa;0BACvBuB,KAAK,EAAE3C,MAAM,CAACjB;wBAAM;0BAAAmD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrB,CAAC,EACDnB,MAAM,CAACnC,KAAK,IAAIoC,OAAO,CAACpC,KAAK,iBAC5BrB,OAAA,CAACf,cAAc;0BAAC6E,SAAS,EAAC,cAAc;0BAAAP,QAAA,EACrCC,MAAM,CAACnC;wBAAK;0BAAAmD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CACjB;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACP3E,OAAA,CAACrB,IAAI;sBAACsF,IAAI;sBAACC,EAAE,EAAE,EAAG;sBAACC,EAAE,EAAE,EAAG;sBAACC,EAAE,EAAE,EAAG;sBAAAb,QAAA,eAChCvD,OAAA,CAACtB,GAAG;wBAACoF,SAAS,EAAC,aAAa;wBAAAP,QAAA,gBAC1BvD,OAAA,CAACT,kBAAkB;0BAAAiF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACtB3E,OAAA,CAACpB,SAAS;0BACRgG,EAAE,EAAC,UAAU;0BACbd,SAAS,EAAC,UAAU;0BACpBe,KAAK,EAAC,UAAU;0BAChBC,IAAI,EAAE1E,YAAY,GAAG,MAAM,GAAG,UAAW;0BACzC2E,OAAO,EAAC,QAAQ;0BAChBC,QAAQ,EAAEtB,YAAa;0BACvBuB,KAAK,EAAE3C,MAAM,CAAChB,QAAS;0BACvB4D,UAAU,EAAE;4BACVC,gBAAgB,EAAE,IAAI;4BACtBC,YAAY,eACVpF,OAAA,CAACnB,cAAc;8BAACwG,QAAQ,EAAC,KAAK;8BAAA9B,QAAA,eAC5BvD,OAAA,CAACX,UAAU;gCACTiG,OAAO,EAAE9E,uBAAwB;gCACjC+E,IAAI,EAAC,KAAK;gCAAAhC,QAAA,EAETnD,YAAY,gBACXJ,OAAA,CAACZ,aAAa;kCAAAoF,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,gBAEjB3E,OAAA,CAACb,UAAU;kCAAAqF,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BACd;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACS;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC;0BAEpB;wBAAE;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,EACDnB,MAAM,CAAClC,QAAQ,IAAImC,OAAO,CAACnC,QAAQ,iBAClCtB,OAAA,CAACf,cAAc;0BAAC6E,SAAS,EAAC,cAAc;0BAAAP,QAAA,EACrCC,MAAM,CAAClC;wBAAQ;0BAAAkD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CACjB;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACP3E,OAAA,CAACrB,IAAI;sBAACsF,IAAI;sBAACC,EAAE,EAAE,CAAE;sBAACC,EAAE,EAAE,CAAE;sBAACC,EAAE,EAAE,CAAE;sBAAAb,QAAA,eAC7BvD,OAAA,CAAClB,gBAAgB;wBACf0G,OAAO,eAAExF,OAAA,CAACjB,QAAQ;0BAAAyF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACtBE,KAAK,EAAC,aAAa;wBACnBY,OAAO,EAAEvE,UAAW;wBACpB8D,QAAQ,EAAEA,CAAA,KAAM7D,aAAa,CAAC,CAACD,UAAU;sBAAE;wBAAAsD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAMP3E,OAAA,CAACrB,IAAI;sBAACsF,IAAI;sBAACC,EAAE,EAAE,EAAG;sBAACC,EAAE,EAAE,EAAG;sBAACC,EAAE,EAAE,EAAG;sBAAAb,QAAA,eAChCvD,OAAA,CAAChB,MAAM;wBACL+F,OAAO,EAAC,WAAW;wBACnBjB,SAAS,EAAC,yBAAyB;wBACnCgB,IAAI,EAAC,QAAQ;wBAAAvB,QAAA,EACd;sBAED;wBAAAiB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EACP;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACxE,EAAA,CAtOIF,MAAoC;EAAA,QAkBvBP,WAAW;AAAA;AAAAgG,EAAA,GAlBxBzF,MAAoC;AAwO1C,eAAeA,MAAM;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}