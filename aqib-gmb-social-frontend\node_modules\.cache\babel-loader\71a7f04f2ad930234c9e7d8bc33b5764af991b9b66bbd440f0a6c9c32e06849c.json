{"ast": null, "code": "/**\n * LOGIN\n */\nexport const AUTH_REQUESTED = \"AUTH_REQUESTED\";\nexport const AUTH_SUCCESS = \"AUTH_SUCCESS\";\nexport const AUTH_ERROR = \"AUTH_ERROR\";\nexport const AUTH_LOGOUT = \"AUTH_LOGOUT\";\nexport const AUTH_UNAUTHORIZED = \"AUTH_UNAUTHORIZED\";\n\n/**\n * MENU\n */\nexport const UPDATE_MENU_STATE = \"UPDATE_MENU_STATE\";\nexport const TOGGLED_MENU_ITEM = \"TOGGLED_MENU_ITEM\";", "map": {"version": 3, "names": ["AUTH_REQUESTED", "AUTH_SUCCESS", "AUTH_ERROR", "AUTH_LOGOUT", "AUTH_UNAUTHORIZED", "UPDATE_MENU_STATE", "TOGGLED_MENU_ITEM"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/constants/reducer.constant.tsx"], "sourcesContent": ["/**\n * LOGIN\n */\nexport const AUTH_REQUESTED = \"AUTH_REQUESTED\";\nexport const AUTH_SUCCESS = \"AUTH_SUCCESS\";\nexport const AUTH_ERROR = \"AUTH_ERROR\";\nexport const AUTH_LOGOUT = \"AUTH_LOGOUT\";\nexport const AUTH_UNAUTHORIZED = \"AUTH_UNAUTHORIZED\";\n\n/**\n * MENU\n */\nexport const UPDATE_MENU_STATE = \"UPDATE_MENU_STATE\";\nexport const TOGGLED_MENU_ITEM = \"TOGGLED_MENU_ITEM\";\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,MAAMA,cAAc,GAAG,gBAAgB;AAC9C,OAAO,MAAMC,YAAY,GAAG,cAAc;AAC1C,OAAO,MAAMC,UAAU,GAAG,YAAY;AACtC,OAAO,MAAMC,WAAW,GAAG,aAAa;AACxC,OAAO,MAAMC,iBAAiB,GAAG,mBAAmB;;AAEpD;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAG,mBAAmB;AACpD,OAAO,MAAMC,iBAAiB,GAAG,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}