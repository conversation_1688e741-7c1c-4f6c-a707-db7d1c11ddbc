{"ast": null, "code": "import HttpHelperService from \"../httpHelper.service\";\nimport { CREATE_REVIEW_TAGS, GET_ALL_TAGS, LIST_OF_REVIEWS, LIST_OF_REVIEWS_EXTENDED, REFRESH_REVIEWS, REPLY_TO_REVIEW, REPLY_WITH_AI, UPDATE_TAGS_TO_REVIEW } from \"../../constants/endPoints.constant\";\nclass ReviewService {\n  constructor(dispatch) {\n    this._httpHelperService = void 0;\n    this.getReviews = async (userId, locationId) => {\n      return await this._httpHelperService.get(LIST_OF_REVIEWS(userId), {\n        \"x-gmb-location-id\": locationId\n      });\n    };\n    this.getReviewsExtended = async (userId, locationId, reviewRequestModel) => {\n      return await this._httpHelperService.post(LIST_OF_REVIEWS_EXTENDED(userId), reviewRequestModel, {\n        \"x-gmb-location-id\": locationId\n      });\n    };\n    this.getReviewReplyFromAI = async (comment, rating) => {\n      return await this._httpHelperService.post(REPLY_WITH_AI, {\n        comment,\n        rating\n      });\n    };\n    this.createReviewTags = async (tagName, userId) => {\n      return await this._httpHelperService.post(CREATE_REVIEW_TAGS, {\n        tagName,\n        createdBy: userId\n      });\n    };\n    this.getAllTags = async () => {\n      return await this._httpHelperService.get(GET_ALL_TAGS);\n    };\n    this.updateTagsToReview = async request => {\n      return await this._httpHelperService.post(UPDATE_TAGS_TO_REVIEW, request);\n    };\n    this.refreshReviews = async headerObject => {\n      return await this._httpHelperService.get(REFRESH_REVIEWS, headerObject);\n    };\n    this.replyToReview = async (headers, reqBody) => {\n      return await this._httpHelperService.post(REPLY_TO_REVIEW, reqBody, headers);\n    };\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n}\nexport default ReviewService;", "map": {"version": 3, "names": ["HttpHelperService", "CREATE_REVIEW_TAGS", "GET_ALL_TAGS", "LIST_OF_REVIEWS", "LIST_OF_REVIEWS_EXTENDED", "REFRESH_REVIEWS", "REPLY_TO_REVIEW", "REPLY_WITH_AI", "UPDATE_TAGS_TO_REVIEW", "ReviewService", "constructor", "dispatch", "_httpHelperService", "getReviews", "userId", "locationId", "get", "getReviewsExtended", "reviewRequestModel", "post", "getReviewReplyFromAI", "comment", "rating", "createReviewTags", "tagName", "created<PERSON>y", "getAllTags", "updateTagsToReview", "request", "refreshReviews", "headerObject", "replyToReview", "headers", "reqBody"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/review/review.service.tsx"], "sourcesContent": ["import { Component, Dispatch } from \"react\";\nimport HttpHelperService from \"../httpHelper.service\";\nimport {\n  CREATE_REVIEW_TAGS,\n  GET_ALL_TAGS,\n  LIST_OF_REVIEWS,\n  LIST_OF_REVIEWS_EXTENDED,\n  REFRESH_REVIEWS,\n  REPLY_TO_REVIEW,\n  REPLY_WITH_AI,\n  UPDATE_TAGS_TO_REVIEW,\n} from \"../../constants/endPoints.constant\";\nimport { Action } from \"redux\";\nimport { IUpdateTagToReviewRequestModel } from \"../../interfaces/request/IUpdateTagToReviewRequestModel\";\nimport { IReviewsListRequestModel } from \"../../interfaces/request/IReviewsListRequestModel\";\n\nclass ReviewService {\n  _httpHelperService;\n  constructor(dispatch: Dispatch<Action>) {\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  getReviews = async (userId: number, locationId: string) => {\n    return await this._httpHelperService.get(LIST_OF_REVIEWS(userId), {\n      \"x-gmb-location-id\": locationId,\n    });\n  };\n\n  getReviewsExtended = async (\n    userId: number,\n    locationId: string,\n    reviewRequestModel: IReviewsListRequestModel\n  ) => {\n    return await this._httpHelperService.post(\n      LIST_OF_REVIEWS_EXTENDED(userId),\n      reviewRequestModel,\n      {\n        \"x-gmb-location-id\": locationId,\n      }\n    );\n  };\n\n  getReviewReplyFromAI = async (comment: string, rating: number) => {\n    return await this._httpHelperService.post(REPLY_WITH_AI, {\n      comment,\n      rating,\n    });\n  };\n\n  createReviewTags = async (tagName: string, userId: number) => {\n    return await this._httpHelperService.post(CREATE_REVIEW_TAGS, {\n      tagName,\n      createdBy: userId,\n    });\n  };\n\n  getAllTags = async () => {\n    return await this._httpHelperService.get(GET_ALL_TAGS);\n  };\n\n  updateTagsToReview = async (request: IUpdateTagToReviewRequestModel) => {\n    return await this._httpHelperService.post(UPDATE_TAGS_TO_REVIEW, request);\n  };\n\n  refreshReviews = async (headerObject: any) => {\n    return await this._httpHelperService.get(REFRESH_REVIEWS, headerObject);\n  };\n\n  replyToReview = async (headers: any, reqBody: any) => {\n    return await this._httpHelperService.post(\n      REPLY_TO_REVIEW,\n      reqBody,\n      headers\n    );\n  };\n}\n\nexport default ReviewService;\n"], "mappings": "AACA,OAAOA,iBAAiB,MAAM,uBAAuB;AACrD,SACEC,kBAAkB,EAClBC,YAAY,EACZC,eAAe,EACfC,wBAAwB,EACxBC,eAAe,EACfC,eAAe,EACfC,aAAa,EACbC,qBAAqB,QAChB,oCAAoC;AAK3C,MAAMC,aAAa,CAAC;EAElBC,WAAWA,CAACC,QAA0B,EAAE;IAAA,KADxCC,kBAAkB;IAAA,KAKlBC,UAAU,GAAG,OAAOC,MAAc,EAAEC,UAAkB,KAAK;MACzD,OAAO,MAAM,IAAI,CAACH,kBAAkB,CAACI,GAAG,CAACb,eAAe,CAACW,MAAM,CAAC,EAAE;QAChE,mBAAmB,EAAEC;MACvB,CAAC,CAAC;IACJ,CAAC;IAAA,KAEDE,kBAAkB,GAAG,OACnBH,MAAc,EACdC,UAAkB,EAClBG,kBAA4C,KACzC;MACH,OAAO,MAAM,IAAI,CAACN,kBAAkB,CAACO,IAAI,CACvCf,wBAAwB,CAACU,MAAM,CAAC,EAChCI,kBAAkB,EAClB;QACE,mBAAmB,EAAEH;MACvB,CACF,CAAC;IACH,CAAC;IAAA,KAEDK,oBAAoB,GAAG,OAAOC,OAAe,EAAEC,MAAc,KAAK;MAChE,OAAO,MAAM,IAAI,CAACV,kBAAkB,CAACO,IAAI,CAACZ,aAAa,EAAE;QACvDc,OAAO;QACPC;MACF,CAAC,CAAC;IACJ,CAAC;IAAA,KAEDC,gBAAgB,GAAG,OAAOC,OAAe,EAAEV,MAAc,KAAK;MAC5D,OAAO,MAAM,IAAI,CAACF,kBAAkB,CAACO,IAAI,CAAClB,kBAAkB,EAAE;QAC5DuB,OAAO;QACPC,SAAS,EAAEX;MACb,CAAC,CAAC;IACJ,CAAC;IAAA,KAEDY,UAAU,GAAG,YAAY;MACvB,OAAO,MAAM,IAAI,CAACd,kBAAkB,CAACI,GAAG,CAACd,YAAY,CAAC;IACxD,CAAC;IAAA,KAEDyB,kBAAkB,GAAG,MAAOC,OAAuC,IAAK;MACtE,OAAO,MAAM,IAAI,CAAChB,kBAAkB,CAACO,IAAI,CAACX,qBAAqB,EAAEoB,OAAO,CAAC;IAC3E,CAAC;IAAA,KAEDC,cAAc,GAAG,MAAOC,YAAiB,IAAK;MAC5C,OAAO,MAAM,IAAI,CAAClB,kBAAkB,CAACI,GAAG,CAACX,eAAe,EAAEyB,YAAY,CAAC;IACzE,CAAC;IAAA,KAEDC,aAAa,GAAG,OAAOC,OAAY,EAAEC,OAAY,KAAK;MACpD,OAAO,MAAM,IAAI,CAACrB,kBAAkB,CAACO,IAAI,CACvCb,eAAe,EACf2B,OAAO,EACPD,OACF,CAAC;IACH,CAAC;IAvDC,IAAI,CAACpB,kBAAkB,GAAG,IAAIZ,iBAAiB,CAACW,QAAQ,CAAC;EAC3D;AAuDF;AAEA,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}