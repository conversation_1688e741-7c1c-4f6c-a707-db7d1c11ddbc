[{"C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\theme.tsx": "4", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\store\\index.tsx": "5", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\toast.context.tsx": "6", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\loading.context.tsx": "7", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\auth.actions.tsx": "8", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\toastSeverity.constant.tsx": "9", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\forgotPassword\\forgotPassword.screen.tsx": "10", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\signIn\\signIn.screen.tsx": "11", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboard\\dashboard.screen.tsx": "12", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\qanda\\qanda.screen.tsx": "13", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\help\\help.screen.tsx": "14", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\analytics\\analytics.screen.tsx": "15", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\createSocialPost.screen.tsx": "16", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\dashboardV2.screen.tsx": "17", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\businessCategory.screen.tsx": "18", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\servicesDemo.screen.tsx": "19", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\demo.screen.tsx": "20", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\geoGrid\\geoGrid.screen.tsx": "21", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\roles\\roles.screen.tsx": "22", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\users\\users.screen.tsx": "23", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\manageBusiness\\manageBusiness.screen.tsx": "24", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\businessSummary\\businessSummary.screen.tsx": "25", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\localBusiness\\localBusiness.screen.tsx": "26", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\callback\\callback.screen.tsx": "27", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\manageReviews\\manageReviews.screen.tsx": "28", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\userPreferences.reducer.tsx": "29", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\auth.reducer.tsx": "30", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\PostListing.screen.tsx": "31", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\loader\\loader.component.tsx": "32", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\unAuthorized\\notFoundPage.component.tsx": "33", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\notFoundPage\\notFoundPage.component.tsx": "34", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\endPoints.constant.tsx": "35", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\reducer.constant.tsx": "36", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\httpHelper.service.tsx": "37", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\helperService.tsx": "38", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\dbConstant.constant.tsx": "39", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\message.constant.tsx": "40", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\leftMenu\\leftMenu.component.tsx": "41", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\ApplicationHelperService.tsx": "42", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\websiteClicksChart.tsx": "43", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\platformBreakdownChart.tsx": "44", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\registeredEmployees.charts.tsx": "45", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\activeJobs.charts.tsx": "46", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\pie.charts.tsx": "47", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\homeChartCard\\homeChartCard.component.tsx": "48", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\qanda\\qanda.service.tsx": "49", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\business\\business.service.tsx": "50", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\location\\location.service.tsx": "51", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyQuestionAnswer\\replyQuestionAnswer.component.tsx": "52", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\genericDrawer\\genericDrawer.component.tsx": "53", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\revenueChartDashboard\\revenueChartDashboard.component.tsx": "54", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\application.constant.tsx": "55", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\locationChips\\locationChips.component.tsx": "56", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\dateFilter\\dateFilter.component.tsx": "57", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\locationMetrics\\locationMetrics.service.tsx": "58", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\InfoCard.screen.tsx": "59", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\submitPost.component.tsx": "60", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\searchBreakdown.tsx": "61", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\businessProfileInteractionsChart.tsx": "62", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\posts\\posts.service.tsx": "63", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addBusinessCategory.component.tsx": "64", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\servicesDisplay.component.tsx": "65", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addServices.component.tsx": "66", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\BusinessHours.tsx": "67", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\AddSpecialHours.tsx": "68", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editBusinessName\\editBusinessName.component.tsx": "69", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\FromTheBusiness.tsx": "70", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\AddChildren.tsx": "71", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\MoreAccessibility.tsx": "72", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\Amenities.tsx": "73", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ServiceOptions.tsx": "74", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\CrowdComponent.tsx": "75", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ParkingComponent.tsx": "76", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PaymentsComponent.tsx": "77", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PlanningComponent.tsx": "78", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridControls.component.tsx": "79", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridMap.component.tsx": "80", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\geoGrid\\geoGrid.service.tsx": "81", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridSettings.component.tsx": "82", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\roles\\roles.service.tsx": "83", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\user\\user.service.tsx": "84", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmModel\\confirmModel.component.tsx": "85", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditUser\\addEditUser.component.tsx": "86", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\auth\\auth.service.tsx": "87", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\alertDialog\\alertDialog.component.tsx": "88", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\noRowsFound\\noRowsFound.component.tsx": "89", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\logosPhotosDisplay\\logsPhotosDisplay.component.tsx": "90", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\categoryDisplay\\categoryDisplay.component.tsx": "91", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\LinearProgressWithLabel\\LinearProgressWithLabel.component.tsx": "92", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditBusiness\\addEditBusiness.component.tsx": "93", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceItemsDisplay\\serviceItemsDisplay.component.tsx": "94", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\regularHoursTable\\regularHoursTable.component.tsx": "95", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceAreaList\\serviceAreaList.component.tsx": "96", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\iconOnAvailability\\iconOnAvailability.component.tsx": "97", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatarWIthName\\userAvatarWIthName.component.tsx": "98", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyReviews\\replyReviews.component.tsx": "99", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPost.component.tsx": "100", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createTags\\createTags.component.tsx": "101", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\ratingsStar\\ratingsStar.component.tsx": "102", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\review\\review.service.tsx": "103", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\postCard\\postCard.screen.tsx": "104", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\updatesSection\\updatesSection.screen.tsx": "105", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\userPreferences.actions.tsx": "106", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\menuListItemNested\\menuListItemNested.component.tsx": "107", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\exportButton\\exportButton.component.tsx": "108", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\header\\header.component.tsx": "109", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\mediaGallery\\mediaGallery.component.tsx": "110", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatar\\userAvatar.component.tsx": "111", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editElements\\editElements.component.tsx": "112", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\colorPalette\\colorPalette.component.tsx": "113", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard1\\testimonialCard1.component.tsx": "114", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard3\\testimonialCard3.component.tsx": "115", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard2\\testimonialCard2.component.tsx": "116", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard4\\testimonialCard4.component.tsx": "117", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard5\\testimonialCard5.component.tsx": "118", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard6\\testimonialCard6.component.tsx": "119", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\excelExport.service.ts": "120", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\preferences.context.tsx": "121", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\googleMaps.utils.ts": "122", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\reviewSettings.screen.tsx": "123", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\reviewSettings\\reviewSettings.service.tsx": "124", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\autoReplySettings.component.tsx": "125", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\createEditTemplate.component.tsx": "126", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\manageAssets.screen.tsx": "127", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\manageAssets\\manageAssets.service.ts": "128", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileGallery\\fileGallery.component.tsx": "129", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileUpload\\fileUpload.component.tsx": "130", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileViewer\\fileViewer.component.tsx": "131", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmDelete\\confirmDelete.component.tsx": "132", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\fileUtils.ts": "133", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\confirmDelete.component.tsx": "134", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileUpload.component.tsx": "135", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileViewer.component.tsx": "136", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileGallery.component.tsx": "137"}, {"size": 1322, "mtime": 1748969368507, "results": "138", "hashOfConfig": "139"}, {"size": 425, "mtime": 1748969368711, "results": "140", "hashOfConfig": "139"}, {"size": 12429, "mtime": 1748969367924, "results": "141", "hashOfConfig": "139"}, {"size": 1924, "mtime": 1748969177420, "results": "142", "hashOfConfig": "139"}, {"size": 1216, "mtime": 1748969371143, "results": "143", "hashOfConfig": "139"}, {"size": 438, "mtime": 1748969368495, "results": "144", "hashOfConfig": "139"}, {"size": 152, "mtime": 1748969368485, "results": "145", "hashOfConfig": "139"}, {"size": 2954, "mtime": 1748969367929, "results": "146", "hashOfConfig": "139"}, {"size": 112, "mtime": 1748969368479, "results": "147", "hashOfConfig": "139"}, {"size": 2586, "mtime": 1748969370932, "results": "148", "hashOfConfig": "139"}, {"size": 10740, "mtime": 1748969371025, "results": "149", "hashOfConfig": "139"}, {"size": 13928, "mtime": 1748969370904, "results": "150", "hashOfConfig": "139"}, {"size": 21620, "mtime": 1748969371012, "results": "151", "hashOfConfig": "139"}, {"size": 220, "mtime": 1748969370943, "results": "152", "hashOfConfig": "139"}, {"size": 72602, "mtime": 1748969179368, "results": "153", "hashOfConfig": "139"}, {"size": 71962, "mtime": 1749024365349, "results": "154", "hashOfConfig": "139"}, {"size": 23584, "mtime": 1748969370918, "results": "155", "hashOfConfig": "139"}, {"size": 4224, "mtime": 1748969368724, "results": "156", "hashOfConfig": "139"}, {"size": 2557, "mtime": 1748969370870, "results": "157", "hashOfConfig": "139"}, {"size": 92285, "mtime": 1748969370866, "results": "158", "hashOfConfig": "139"}, {"size": 12587, "mtime": 1748969370938, "results": "159", "hashOfConfig": "139"}, {"size": 37116, "mtime": 1748969179991, "results": "160", "hashOfConfig": "139"}, {"size": 22457, "mtime": 1748969180011, "results": "161", "hashOfConfig": "139"}, {"size": 25334, "mtime": 1748969370888, "results": "162", "hashOfConfig": "139"}, {"size": 56467, "mtime": 1748969179480, "results": "163", "hashOfConfig": "139"}, {"size": 30332, "mtime": 1748969179542, "results": "164", "hashOfConfig": "139"}, {"size": 4452, "mtime": 1748969370879, "results": "165", "hashOfConfig": "139"}, {"size": 43075, "mtime": 1748969179942, "results": "166", "hashOfConfig": "139"}, {"size": 951, "mtime": 1748969368707, "results": "167", "hashOfConfig": "139"}, {"size": 1730, "mtime": 1748969368697, "results": "168", "hashOfConfig": "139"}, {"size": 18225, "mtime": 1748969179861, "results": "169", "hashOfConfig": "139"}, {"size": 347, "mtime": 1748969178541, "results": "170", "hashOfConfig": "139"}, {"size": 1818, "mtime": 1748969368442, "results": "171", "hashOfConfig": "139"}, {"size": 1814, "mtime": 1748969368320, "results": "172", "hashOfConfig": "139"}, {"size": 5670, "mtime": 1749024223912, "results": "173", "hashOfConfig": "139"}, {"size": 370, "mtime": 1748969368473, "results": "174", "hashOfConfig": "139"}, {"size": 8994, "mtime": 1748969371088, "results": "175", "hashOfConfig": "139"}, {"size": 1053, "mtime": 1748969371080, "results": "176", "hashOfConfig": "139"}, {"size": 307, "mtime": 1748969368464, "results": "177", "hashOfConfig": "139"}, {"size": 1953, "mtime": 1748969178987, "results": "178", "hashOfConfig": "139"}, {"size": 21450, "mtime": 1748969368205, "results": "179", "hashOfConfig": "139"}, {"size": 3091, "mtime": 1748969371041, "results": "180", "hashOfConfig": "139"}, {"size": 3899, "mtime": 1748969179700, "results": "181", "hashOfConfig": "139"}, {"size": 4028, "mtime": 1748969179684, "results": "182", "hashOfConfig": "139"}, {"size": 1918, "mtime": 1748969367988, "results": "183", "hashOfConfig": "139"}, {"size": 1129, "mtime": 1748969367971, "results": "184", "hashOfConfig": "139"}, {"size": 977, "mtime": 1748969367982, "results": "185", "hashOfConfig": "139"}, {"size": 2779, "mtime": 1748969178435, "results": "186", "hashOfConfig": "139"}, {"size": 1388, "mtime": 1748969371120, "results": "187", "hashOfConfig": "139"}, {"size": 2038, "mtime": 1748969371058, "results": "188", "hashOfConfig": "139"}, {"size": 1631, "mtime": 1748969371101, "results": "189", "hashOfConfig": "139"}, {"size": 4825, "mtime": 1748969368417, "results": "190", "hashOfConfig": "139"}, {"size": 971, "mtime": 1748969368089, "results": "191", "hashOfConfig": "139"}, {"size": 4015, "mtime": 1748969178808, "results": "192", "hashOfConfig": "139"}, {"size": 2046, "mtime": 1748969368459, "results": "193", "hashOfConfig": "139"}, {"size": 3524, "mtime": 1748969368215, "results": "194", "hashOfConfig": "139"}, {"size": 6531, "mtime": 1748969178236, "results": "195", "hashOfConfig": "139"}, {"size": 1027, "mtime": 1748969371107, "results": "196", "hashOfConfig": "139"}, {"size": 3176, "mtime": 1748969179606, "results": "197", "hashOfConfig": "139"}, {"size": 42714, "mtime": 1749023567819, "results": "198", "hashOfConfig": "139"}, {"size": 7778, "mtime": 1748969370923, "results": "199", "hashOfConfig": "139"}, {"size": 4054, "mtime": 1748969179667, "results": "200", "hashOfConfig": "139"}, {"size": 2579, "mtime": 1749024262624, "results": "201", "hashOfConfig": "139"}, {"size": 1521, "mtime": 1748969370847, "results": "202", "hashOfConfig": "139"}, {"size": 7403, "mtime": 1748969370859, "results": "203", "hashOfConfig": "139"}, {"size": 8129, "mtime": 1748969370854, "results": "204", "hashOfConfig": "139"}, {"size": 12230, "mtime": 1748969368737, "results": "205", "hashOfConfig": "139"}, {"size": 9701, "mtime": 1748969368730, "results": "206", "hashOfConfig": "139"}, {"size": 7411, "mtime": 1748969368059, "results": "207", "hashOfConfig": "139"}, {"size": 7645, "mtime": 1748969370963, "results": "208", "hashOfConfig": "139"}, {"size": 7764, "mtime": 1748969370948, "results": "209", "hashOfConfig": "139"}, {"size": 8438, "mtime": 1748969370968, "results": "210", "hashOfConfig": "139"}, {"size": 9418, "mtime": 1748969370953, "results": "211", "hashOfConfig": "139"}, {"size": 8390, "mtime": 1748969370988, "results": "212", "hashOfConfig": "139"}, {"size": 7694, "mtime": 1748969370958, "results": "213", "hashOfConfig": "139"}, {"size": 8842, "mtime": 1748969370973, "results": "214", "hashOfConfig": "139"}, {"size": 8910, "mtime": 1748969370978, "results": "215", "hashOfConfig": "139"}, {"size": 9753, "mtime": 1748969370983, "results": "216", "hashOfConfig": "139"}, {"size": 16883, "mtime": 1748969368096, "results": "217", "hashOfConfig": "139"}, {"size": 8642, "mtime": 1748969368161, "results": "218", "hashOfConfig": "139"}, {"size": 3621, "mtime": 1748969371074, "results": "219", "hashOfConfig": "139"}, {"size": 6848, "mtime": 1748969368169, "results": "220", "hashOfConfig": "139"}, {"size": 723, "mtime": 1748969371134, "results": "221", "hashOfConfig": "139"}, {"size": 2655, "mtime": 1748969180196, "results": "222", "hashOfConfig": "139"}, {"size": 2179, "mtime": 1748969177971, "results": "223", "hashOfConfig": "139"}, {"size": 62073, "mtime": 1748969177860, "results": "224", "hashOfConfig": "139"}, {"size": 847, "mtime": 1748969371051, "results": "225", "hashOfConfig": "139"}, {"size": 3505, "mtime": 1748969177878, "results": "226", "hashOfConfig": "139"}, {"size": 825, "mtime": 1748969178665, "results": "227", "hashOfConfig": "139"}, {"size": 4702, "mtime": 1748969368221, "results": "228", "hashOfConfig": "139"}, {"size": 3568, "mtime": 1748969367959, "results": "229", "hashOfConfig": "139"}, {"size": 1109, "mtime": 1748969367946, "results": "230", "hashOfConfig": "139"}, {"size": 9176, "mtime": 1748969177843, "results": "231", "hashOfConfig": "139"}, {"size": 2262, "mtime": 1748969368435, "results": "232", "hashOfConfig": "139"}, {"size": 3305, "mtime": 1748969178736, "results": "233", "hashOfConfig": "139"}, {"size": 1607, "mtime": 1748969368429, "results": "234", "hashOfConfig": "139"}, {"size": 583, "mtime": 1748969368196, "results": "235", "hashOfConfig": "139"}, {"size": 463, "mtime": 1748969368454, "results": "236", "hashOfConfig": "139"}, {"size": 6596, "mtime": 1748969178782, "results": "237", "hashOfConfig": "139"}, {"size": 14893, "mtime": 1748969368007, "results": "238", "hashOfConfig": "139"}, {"size": 8516, "mtime": 1748969178205, "results": "239", "hashOfConfig": "139"}, {"size": 1147, "mtime": 1748969368411, "results": "240", "hashOfConfig": "139"}, {"size": 2167, "mtime": 1748969371126, "results": "241", "hashOfConfig": "139"}, {"size": 12145, "mtime": 1748970904670, "results": "242", "hashOfConfig": "139"}, {"size": 3329, "mtime": 1748969371000, "results": "243", "hashOfConfig": "139"}, {"size": 485, "mtime": 1748969367939, "results": "244", "hashOfConfig": "139"}, {"size": 5996, "mtime": 1748969368238, "results": "245", "hashOfConfig": "139"}, {"size": 2617, "mtime": 1748969368072, "results": "246", "hashOfConfig": "139"}, {"size": 3305, "mtime": 1748969368191, "results": "247", "hashOfConfig": "139"}, {"size": 2809, "mtime": 1748969368226, "results": "248", "hashOfConfig": "139"}, {"size": 1348, "mtime": 1748969368448, "results": "249", "hashOfConfig": "139"}, {"size": 5636, "mtime": 1748969368063, "results": "250", "hashOfConfig": "139"}, {"size": 9514, "mtime": 1748969367995, "results": "251", "hashOfConfig": "139"}, {"size": 2827, "mtime": 1748969368012, "results": "252", "hashOfConfig": "139"}, {"size": 4537, "mtime": 1748969368026, "results": "253", "hashOfConfig": "139"}, {"size": 2097, "mtime": 1748969368019, "results": "254", "hashOfConfig": "139"}, {"size": 3140, "mtime": 1748969368038, "results": "255", "hashOfConfig": "139"}, {"size": 3321, "mtime": 1748969368045, "results": "256", "hashOfConfig": "139"}, {"size": 3000, "mtime": 1748969368053, "results": "257", "hashOfConfig": "139"}, {"size": 13705, "mtime": 1748969371067, "results": "258", "hashOfConfig": "139"}, {"size": 163, "mtime": 1748969368490, "results": "259", "hashOfConfig": "139"}, {"size": 4815, "mtime": 1748969371157, "results": "260", "hashOfConfig": "139"}, {"size": 16593, "mtime": 1748842579937, "results": "261", "hashOfConfig": "139"}, {"size": 4220, "mtime": 1748804384121, "results": "262", "hashOfConfig": "139"}, {"size": 15590, "mtime": 1748806340914, "results": "263", "hashOfConfig": "139"}, {"size": 11543, "mtime": 1748843169061, "results": "264", "hashOfConfig": "139"}, {"size": 15944, "mtime": 1748941966068, "results": "265", "hashOfConfig": "139"}, {"size": 3056, "mtime": 1748931206364, "results": "266", "hashOfConfig": "139"}, {"size": 8554, "mtime": 1748931169711, "results": "267", "hashOfConfig": "139"}, {"size": 10503, "mtime": 1748931116654, "results": "268", "hashOfConfig": "139"}, {"size": 8369, "mtime": 1748931329681, "results": "269", "hashOfConfig": "139"}, {"size": 2576, "mtime": 1748887662915, "results": "270", "hashOfConfig": "139"}, {"size": 2526, "mtime": 1748931004554, "results": "271", "hashOfConfig": "139"}, {"size": 1469, "mtime": 1748934891273, "results": "272", "hashOfConfig": "139"}, {"size": 7042, "mtime": 1748934815415, "results": "273", "hashOfConfig": "139"}, {"size": 7395, "mtime": 1748937395633, "results": "274", "hashOfConfig": "139"}, {"size": 8292, "mtime": 1748937372532, "results": "275", "hashOfConfig": "139"}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qj6s6v", {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\App.tsx", ["687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\theme.tsx", ["706"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\store\\index.tsx", ["707"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\toast.context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\loading.context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\auth.actions.tsx", ["708", "709", "710", "711", "712", "713"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\toastSeverity.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\forgotPassword\\forgotPassword.screen.tsx", ["714"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\signIn\\signIn.screen.tsx", ["715", "716", "717", "718", "719", "720", "721", "722", "723"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboard\\dashboard.screen.tsx", ["724"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\qanda\\qanda.screen.tsx", ["725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\help\\help.screen.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\analytics\\analytics.screen.tsx", ["738", "739", "740", "741"], ["742"], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\createSocialPost.screen.tsx", ["743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\dashboardV2.screen.tsx", ["763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\businessCategory.screen.tsx", ["785"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\servicesDemo.screen.tsx", ["786", "787", "788"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\demo.screen.tsx", ["789", "790", "791", "792", "793", "794"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\geoGrid\\geoGrid.screen.tsx", ["795", "796", "797", "798", "799"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\roles\\roles.screen.tsx", ["800", "801", "802", "803", "804", "805", "806", "807", "808", "809"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\users\\users.screen.tsx", ["810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\manageBusiness\\manageBusiness.screen.tsx", ["823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\businessSummary\\businessSummary.screen.tsx", ["836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\localBusiness\\localBusiness.screen.tsx", ["853", "854", "855", "856", "857", "858", "859", "860", "861", "862"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\callback\\callback.screen.tsx", ["863", "864", "865", "866"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\manageReviews\\manageReviews.screen.tsx", ["867", "868", "869", "870", "871", "872", "873", "874", "875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\userPreferences.reducer.tsx", ["896", "897", "898", "899", "900", "901", "902"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\auth.reducer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\PostListing.screen.tsx", ["903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\loader\\loader.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\unAuthorized\\notFoundPage.component.tsx", ["918"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\notFoundPage\\notFoundPage.component.tsx", ["919"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\endPoints.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\reducer.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\httpHelper.service.tsx", ["920", "921"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\helperService.tsx", ["922", "923"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\dbConstant.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\message.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\leftMenu\\leftMenu.component.tsx", ["924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\ApplicationHelperService.tsx", ["944", "945"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\websiteClicksChart.tsx", ["946", "947"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\platformBreakdownChart.tsx", ["948"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\registeredEmployees.charts.tsx", ["949", "950", "951"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\activeJobs.charts.tsx", ["952"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\pie.charts.tsx", ["953", "954"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\homeChartCard\\homeChartCard.component.tsx", ["955", "956"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\qanda\\qanda.service.tsx", ["957"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\business\\business.service.tsx", ["958"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\location\\location.service.tsx", ["959"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyQuestionAnswer\\replyQuestionAnswer.component.tsx", ["960", "961", "962"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\genericDrawer\\genericDrawer.component.tsx", ["963", "964", "965", "966"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\revenueChartDashboard\\revenueChartDashboard.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\application.constant.tsx", ["967"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\locationChips\\locationChips.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\dateFilter\\dateFilter.component.tsx", ["968"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\locationMetrics\\locationMetrics.service.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\InfoCard.screen.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\submitPost.component.tsx", ["969", "970", "971", "972", "973", "974", "975", "976", "977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\searchBreakdown.tsx", ["1002", "1003", "1004", "1005", "1006"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\businessProfileInteractionsChart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\posts\\posts.service.tsx", ["1007", "1008", "1009"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addBusinessCategory.component.tsx", ["1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\servicesDisplay.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addServices.component.tsx", ["1030"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\BusinessHours.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\AddSpecialHours.tsx", ["1031", "1032", "1033", "1034", "1035"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editBusinessName\\editBusinessName.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\FromTheBusiness.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\AddChildren.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\MoreAccessibility.tsx", ["1036"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\Amenities.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ServiceOptions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\CrowdComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ParkingComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PaymentsComponent.tsx", ["1037"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PlanningComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridControls.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridMap.component.tsx", ["1038", "1039"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\geoGrid\\geoGrid.service.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridSettings.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\roles\\roles.service.tsx", ["1040", "1041"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\user\\user.service.tsx", ["1042", "1043", "1044"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmModel\\confirmModel.component.tsx", ["1045", "1046"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditUser\\addEditUser.component.tsx", ["1047", "1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\auth\\auth.service.tsx", ["1059", "1060"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\alertDialog\\alertDialog.component.tsx", ["1061"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\noRowsFound\\noRowsFound.component.tsx", ["1062", "1063", "1064"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\logosPhotosDisplay\\logsPhotosDisplay.component.tsx", ["1065", "1066", "1067"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\categoryDisplay\\categoryDisplay.component.tsx", ["1068", "1069", "1070", "1071"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\LinearProgressWithLabel\\LinearProgressWithLabel.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditBusiness\\addEditBusiness.component.tsx", ["1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceItemsDisplay\\serviceItemsDisplay.component.tsx", ["1083", "1084", "1085", "1086", "1087"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\regularHoursTable\\regularHoursTable.component.tsx", ["1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceAreaList\\serviceAreaList.component.tsx", ["1099", "1100", "1101"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\iconOnAvailability\\iconOnAvailability.component.tsx", ["1102", "1103", "1104"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatarWIthName\\userAvatarWIthName.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyReviews\\replyReviews.component.tsx", ["1105", "1106", "1107", "1108"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPost.component.tsx", ["1109", "1110", "1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createTags\\createTags.component.tsx", ["1122", "1123", "1124", "1125", "1126", "1127"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\ratingsStar\\ratingsStar.component.tsx", ["1128", "1129", "1130"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\review\\review.service.tsx", ["1131"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\postCard\\postCard.screen.tsx", ["1132", "1133", "1134"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\updatesSection\\updatesSection.screen.tsx", ["1135", "1136"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\userPreferences.actions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\menuListItemNested\\menuListItemNested.component.tsx", ["1137", "1138", "1139"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\exportButton\\exportButton.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\header\\header.component.tsx", ["1140", "1141", "1142", "1143", "1144", "1145", "1146", "1147", "1148", "1149"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\mediaGallery\\mediaGallery.component.tsx", ["1150", "1151"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatar\\userAvatar.component.tsx", ["1152"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editElements\\editElements.component.tsx", ["1153", "1154", "1155"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\colorPalette\\colorPalette.component.tsx", ["1156", "1157", "1158"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard1\\testimonialCard1.component.tsx", ["1159"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard3\\testimonialCard3.component.tsx", ["1160", "1161", "1162", "1163"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard2\\testimonialCard2.component.tsx", ["1164", "1165", "1166"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard4\\testimonialCard4.component.tsx", ["1167", "1168", "1169", "1170"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard5\\testimonialCard5.component.tsx", ["1171", "1172", "1173", "1174", "1175"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard6\\testimonialCard6.component.tsx", ["1176", "1177", "1178", "1179"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\excelExport.service.ts", ["1180"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\preferences.context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\googleMaps.utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\reviewSettings.screen.tsx", ["1181", "1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\reviewSettings\\reviewSettings.service.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\autoReplySettings.component.tsx", ["1194", "1195"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\createEditTemplate.component.tsx", ["1196", "1197"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\manageAssets.screen.tsx", ["1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\manageAssets\\manageAssets.service.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileGallery\\fileGallery.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileUpload\\fileUpload.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\fileViewer\\fileViewer.component.tsx", ["1206"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmDelete\\confirmDelete.component.tsx", ["1207"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\fileUtils.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\confirmDelete.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileUpload.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileViewer.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\manageAssets\\components\\fileGallery.component.tsx", [], [], {"ruleId": "1208", "severity": 1, "message": "1209", "line": 3, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 3, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1212", "line": 9, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 9, "endColumn": 12}, {"ruleId": "1208", "severity": 1, "message": "1213", "line": 11, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1214", "line": 12, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 12, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1215", "line": 13, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 13, "endColumn": 22}, {"ruleId": "1208", "severity": 1, "message": "1216", "line": 16, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 16, "endColumn": 28}, {"ruleId": "1208", "severity": 1, "message": "1217", "line": 19, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 19, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1218", "line": 61, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 61, "endColumn": 24}, {"ruleId": "1208", "severity": 1, "message": "1219", "line": 61, "column": 26, "nodeType": "1210", "messageId": "1211", "endLine": 61, "endColumn": 43}, {"ruleId": "1220", "severity": 1, "message": "1221", "line": 77, "column": 5, "nodeType": "1222", "endLine": 77, "endColumn": 14, "suggestions": "1223"}, {"ruleId": "1220", "severity": 1, "message": "1224", "line": 85, "column": 5, "nodeType": "1222", "endLine": 85, "endColumn": 14, "suggestions": "1225"}, {"ruleId": "1208", "severity": 1, "message": "1226", "line": 96, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 96, "endColumn": 26}, {"ruleId": "1220", "severity": 1, "message": "1227", "line": 100, "column": 5, "nodeType": "1222", "endLine": 100, "endColumn": 21, "suggestions": "1228"}, {"ruleId": "1220", "severity": 1, "message": "1229", "line": 128, "column": 6, "nodeType": "1222", "endLine": 128, "endColumn": 25, "suggestions": "1230"}, {"ruleId": "1220", "severity": 1, "message": "1231", "line": 139, "column": 6, "nodeType": "1222", "endLine": 139, "endColumn": 22, "suggestions": "1232"}, {"ruleId": "1220", "severity": 1, "message": "1233", "line": 163, "column": 5, "nodeType": "1222", "endLine": 163, "endColumn": 11, "suggestions": "1234"}, {"ruleId": "1220", "severity": 1, "message": "1233", "line": 170, "column": 5, "nodeType": "1222", "endLine": 170, "endColumn": 11, "suggestions": "1235"}, {"ruleId": "1220", "severity": 1, "message": "1236", "line": 177, "column": 5, "nodeType": "1222", "endLine": 177, "endColumn": 18, "suggestions": "1237"}, {"ruleId": "1220", "severity": 1, "message": "1238", "line": 189, "column": 5, "nodeType": "1222", "endLine": 189, "endColumn": 33, "suggestions": "1239"}, {"ruleId": "1208", "severity": 1, "message": "1240", "line": 3, "column": 24, "nodeType": "1210", "messageId": "1211", "endLine": 3, "endColumn": 38}, {"ruleId": "1208", "severity": 1, "message": "1241", "line": 6, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 6, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1242", "line": 1, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1209", "line": 4, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1243", "line": 4, "column": 22, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 31}, {"ruleId": "1208", "severity": 1, "message": "1244", "line": 12, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 12, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1245", "line": 14, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 14, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1246", "line": 18, "column": 7, "nodeType": "1210", "messageId": "1211", "endLine": 18, "endColumn": 32}, {"ruleId": "1208", "severity": 1, "message": "1247", "line": 11, "column": 13, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1248", "line": 12, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 12, "endColumn": 12}, {"ruleId": "1208", "severity": 1, "message": "1249", "line": 16, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 16, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1250", "line": 20, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 20, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1251", "line": 21, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 21, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1252", "line": 38, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 38, "endColumn": 26}, {"ruleId": "1208", "severity": 1, "message": "1253", "line": 42, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 42, "endColumn": 34}, {"ruleId": "1208", "severity": 1, "message": "1254", "line": 45, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 45, "endColumn": 32}, {"ruleId": "1208", "severity": 1, "message": "1255", "line": 51, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 51, "endColumn": 30}, {"ruleId": "1220", "severity": 1, "message": "1256", "line": 79, "column": 6, "nodeType": "1222", "endLine": 79, "endColumn": 8, "suggestions": "1257"}, {"ruleId": "1208", "severity": 1, "message": "1258", "line": 27, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 27, "endColumn": 29}, {"ruleId": "1208", "severity": 1, "message": "1259", "line": 17, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 17, "endColumn": 10}, {"ruleId": "1208", "severity": 1, "message": "1260", "line": 18, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 18, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1261", "line": 64, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 64, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1262", "line": 64, "column": 21, "nodeType": "1210", "messageId": "1211", "endLine": 64, "endColumn": 33}, {"ruleId": "1208", "severity": 1, "message": "1263", "line": 67, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 67, "endColumn": 25}, {"ruleId": "1208", "severity": 1, "message": "1246", "line": 69, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 69, "endColumn": 34}, {"ruleId": "1208", "severity": 1, "message": "1253", "line": 71, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 71, "endColumn": 34}, {"ruleId": "1208", "severity": 1, "message": "1264", "line": 73, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 73, "endColumn": 34}, {"ruleId": "1208", "severity": 1, "message": "1265", "line": 81, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 81, "endColumn": 25}, {"ruleId": "1208", "severity": 1, "message": "1266", "line": 95, "column": 25, "nodeType": "1210", "messageId": "1211", "endLine": 95, "endColumn": 41}, {"ruleId": "1267", "severity": 1, "message": "1268", "line": 134, "column": 56, "nodeType": "1269", "messageId": "1270", "endLine": 134, "endColumn": 58}, {"ruleId": "1267", "severity": 1, "message": "1268", "line": 139, "column": 57, "nodeType": "1269", "messageId": "1270", "endLine": 139, "endColumn": 59}, {"ruleId": "1220", "severity": 1, "message": "1271", "line": 146, "column": 6, "nodeType": "1222", "endLine": 146, "endColumn": 8, "suggestions": "1272"}, {"ruleId": "1208", "severity": 1, "message": "1259", "line": 7, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 7, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1273", "line": 13, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 13, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1274", "line": 140, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 140, "endColumn": 30}, {"ruleId": "1267", "severity": 1, "message": "1268", "line": 306, "column": 56, "nodeType": "1269", "messageId": "1270", "endLine": 306, "endColumn": 58}, {"ruleId": "1220", "severity": 1, "message": "1275", "line": 151, "column": 6, "nodeType": "1222", "endLine": 151, "endColumn": 8, "suggestions": "1276", "suppressions": "1277"}, {"ruleId": "1208", "severity": 1, "message": "1278", "line": 8, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 8, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1279", "line": 37, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 37, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1280", "line": 39, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 39, "endColumn": 22}, {"ruleId": "1208", "severity": 1, "message": "1281", "line": 61, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 61, "endColumn": 23}, {"ruleId": "1208", "severity": 1, "message": "1282", "line": 99, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 99, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1283", "line": 100, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 100, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1284", "line": 234, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 234, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1285", "line": 234, "column": 16, "nodeType": "1210", "messageId": "1211", "endLine": 234, "endColumn": 23}, {"ruleId": "1208", "severity": 1, "message": "1286", "line": 235, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 235, "endColumn": 26}, {"ruleId": "1208", "severity": 1, "message": "1287", "line": 235, "column": 28, "nodeType": "1210", "messageId": "1211", "endLine": 235, "endColumn": 47}, {"ruleId": "1220", "severity": 1, "message": "1288", "line": 308, "column": 6, "nodeType": "1222", "endLine": 308, "endColumn": 8, "suggestions": "1289"}, {"ruleId": "1208", "severity": 1, "message": "1290", "line": 310, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 310, "endColumn": 26}, {"ruleId": "1208", "severity": 1, "message": "1291", "line": 312, "column": 11, "nodeType": "1210", "messageId": "1211", "endLine": 312, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1292", "line": 582, "column": 11, "nodeType": "1210", "messageId": "1211", "endLine": 582, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1293", "line": 887, "column": 11, "nodeType": "1210", "messageId": "1211", "endLine": 887, "endColumn": 27}, {"ruleId": "1267", "severity": 1, "message": "1294", "line": 1412, "column": 71, "nodeType": "1269", "messageId": "1270", "endLine": 1412, "endColumn": 73}, {"ruleId": "1267", "severity": 1, "message": "1268", "line": 1427, "column": 42, "nodeType": "1269", "messageId": "1270", "endLine": 1427, "endColumn": 44}, {"ruleId": "1295", "severity": 1, "message": "1296", "line": 1658, "column": 19, "nodeType": "1297", "endLine": 1669, "endColumn": 21}, {"ruleId": "1295", "severity": 1, "message": "1296", "line": 1716, "column": 17, "nodeType": "1297", "endLine": 1729, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1298", "line": 1835, "column": 17, "nodeType": "1210", "messageId": "1211", "endLine": 1835, "endColumn": 24}, {"ruleId": "1208", "severity": 1, "message": "1299", "line": 9, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 9, "endColumn": 34}, {"ruleId": "1208", "severity": 1, "message": "1300", "line": 10, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 10, "endColumn": 25}, {"ruleId": "1208", "severity": 1, "message": "1301", "line": 11, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1302", "line": 14, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 14, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1303", "line": 15, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 15, "endColumn": 23}, {"ruleId": "1208", "severity": 1, "message": "1304", "line": 16, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 16, "endColumn": 26}, {"ruleId": "1208", "severity": 1, "message": "1305", "line": 17, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 17, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1306", "line": 18, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 18, "endColumn": 27}, {"ruleId": "1208", "severity": 1, "message": "1307", "line": 19, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 19, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1308", "line": 21, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 21, "endColumn": 30}, {"ruleId": "1208", "severity": 1, "message": "1309", "line": 22, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 22, "endColumn": 32}, {"ruleId": "1208", "severity": 1, "message": "1273", "line": 26, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 26, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1310", "line": 31, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 31, "endColumn": 24}, {"ruleId": "1208", "severity": 1, "message": "1311", "line": 31, "column": 26, "nodeType": "1210", "messageId": "1211", "endLine": 31, "endColumn": 31}, {"ruleId": "1208", "severity": 1, "message": "1213", "line": 40, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 40, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1312", "line": 41, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 41, "endColumn": 33}, {"ruleId": "1208", "severity": 1, "message": "1313", "line": 43, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 43, "endColumn": 25}, {"ruleId": "1208", "severity": 1, "message": "1314", "line": 66, "column": 21, "nodeType": "1210", "messageId": "1211", "endLine": 66, "endColumn": 29}, {"ruleId": "1208", "severity": 1, "message": "1253", "line": 72, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 72, "endColumn": 34}, {"ruleId": "1220", "severity": 1, "message": "1315", "line": 116, "column": 6, "nodeType": "1222", "endLine": 116, "endColumn": 8, "suggestions": "1316"}, {"ruleId": "1220", "severity": 1, "message": "1317", "line": 125, "column": 6, "nodeType": "1222", "endLine": 125, "endColumn": 26, "suggestions": "1318"}, {"ruleId": "1220", "severity": 1, "message": "1319", "line": 138, "column": 6, "nodeType": "1222", "endLine": 138, "endColumn": 45, "suggestions": "1320"}, {"ruleId": "1208", "severity": 1, "message": "1321", "line": 9, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 9, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1321", "line": 9, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 9, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1322", "line": 10, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 10, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1323", "line": 13, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 13, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1324", "line": 25, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 25, "endColumn": 23}, {"ruleId": "1208", "severity": 1, "message": "1325", "line": 33, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 33, "endColumn": 23}, {"ruleId": "1208", "severity": 1, "message": "1326", "line": 33, "column": 33, "nodeType": "1210", "messageId": "1211", "endLine": 33, "endColumn": 41}, {"ruleId": "1208", "severity": 1, "message": "1327", "line": 34, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 34, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1328", "line": 36, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 36, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1329", "line": 37, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 37, "endColumn": 22}, {"ruleId": "1208", "severity": 1, "message": "1330", "line": 1, "column": 38, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 49}, {"ruleId": "1208", "severity": 1, "message": "1331", "line": 4, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 12}, {"ruleId": "1208", "severity": 1, "message": "1332", "line": 67, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 67, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1333", "line": 67, "column": 21, "nodeType": "1210", "messageId": "1211", "endLine": 67, "endColumn": 33}, {"ruleId": "1220", "severity": 1, "message": "1334", "line": 79, "column": 6, "nodeType": "1222", "endLine": 79, "endColumn": 19, "suggestions": "1335"}, {"ruleId": "1208", "severity": 1, "message": "1336", "line": 14, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 14, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1337", "line": 25, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 25, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1338", "line": 26, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 26, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1339", "line": 31, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 31, "endColumn": 25}, {"ruleId": "1208", "severity": 1, "message": "1260", "line": 47, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 47, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1217", "line": 53, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 53, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1253", "line": 70, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 70, "endColumn": 34}, {"ruleId": "1220", "severity": 1, "message": "1340", "line": 104, "column": 6, "nodeType": "1222", "endLine": 104, "endColumn": 8, "suggestions": "1341"}, {"ruleId": "1220", "severity": 1, "message": "1342", "line": 108, "column": 6, "nodeType": "1222", "endLine": 108, "endColumn": 8, "suggestions": "1343"}, {"ruleId": "1208", "severity": 1, "message": "1292", "line": 223, "column": 13, "nodeType": "1210", "messageId": "1211", "endLine": 223, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1344", "line": 20, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 20, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1345", "line": 21, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 21, "endColumn": 28}, {"ruleId": "1208", "severity": 1, "message": "1346", "line": 34, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 34, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1260", "line": 49, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 49, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1347", "line": 49, "column": 18, "nodeType": "1210", "messageId": "1211", "endLine": 49, "endColumn": 22}, {"ruleId": "1208", "severity": 1, "message": "1348", "line": 49, "column": 31, "nodeType": "1210", "messageId": "1211", "endLine": 49, "endColumn": 36}, {"ruleId": "1208", "severity": 1, "message": "1349", "line": 53, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 53, "endColumn": 23}, {"ruleId": "1208", "severity": 1, "message": "1253", "line": 91, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 91, "endColumn": 34}, {"ruleId": "1208", "severity": 1, "message": "1350", "line": 96, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 96, "endColumn": 20}, {"ruleId": "1220", "severity": 1, "message": "1351", "line": 120, "column": 6, "nodeType": "1222", "endLine": 120, "endColumn": 23, "suggestions": "1352"}, {"ruleId": "1208", "severity": 1, "message": "1353", "line": 217, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 217, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1354", "line": 219, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 219, "endColumn": 25}, {"ruleId": "1208", "severity": 1, "message": "1355", "line": 224, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 224, "endColumn": 32}, {"ruleId": "1208", "severity": 1, "message": "1260", "line": 18, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 18, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1356", "line": 21, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 21, "endColumn": 25}, {"ruleId": "1208", "severity": 1, "message": "1357", "line": 22, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 22, "endColumn": 30}, {"ruleId": "1208", "severity": 1, "message": "1358", "line": 30, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 30, "endColumn": 29}, {"ruleId": "1208", "severity": 1, "message": "1359", "line": 44, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 44, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1345", "line": 51, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 51, "endColumn": 28}, {"ruleId": "1208", "severity": 1, "message": "1360", "line": 59, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 59, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1361", "line": 61, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 61, "endColumn": 29}, {"ruleId": "1208", "severity": 1, "message": "1362", "line": 62, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 62, "endColumn": 23}, {"ruleId": "1208", "severity": 1, "message": "1253", "line": 86, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 86, "endColumn": 34}, {"ruleId": "1208", "severity": 1, "message": "1363", "line": 98, "column": 23, "nodeType": "1210", "messageId": "1211", "endLine": 98, "endColumn": 37}, {"ruleId": "1208", "severity": 1, "message": "1350", "line": 102, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 102, "endColumn": 20}, {"ruleId": "1220", "severity": 1, "message": "1364", "line": 108, "column": 6, "nodeType": "1222", "endLine": 108, "endColumn": 8, "suggestions": "1365"}, {"ruleId": "1208", "severity": 1, "message": "1366", "line": 17, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 17, "endColumn": 12}, {"ruleId": "1208", "severity": 1, "message": "1367", "line": 18, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 18, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1368", "line": 19, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 19, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1369", "line": 29, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 29, "endColumn": 23}, {"ruleId": "1208", "severity": 1, "message": "1370", "line": 40, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 40, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1371", "line": 45, "column": 23, "nodeType": "1210", "messageId": "1211", "endLine": 45, "endColumn": 34}, {"ruleId": "1208", "severity": 1, "message": "1362", "line": 56, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 56, "endColumn": 23}, {"ruleId": "1208", "severity": 1, "message": "1372", "line": 57, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 57, "endColumn": 26}, {"ruleId": "1208", "severity": 1, "message": "1373", "line": 64, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 64, "endColumn": 27}, {"ruleId": "1208", "severity": 1, "message": "1374", "line": 70, "column": 7, "nodeType": "1210", "messageId": "1211", "endLine": 70, "endColumn": 12}, {"ruleId": "1208", "severity": 1, "message": "1375", "line": 72, "column": 11, "nodeType": "1210", "messageId": "1211", "endLine": 72, "endColumn": 26}, {"ruleId": "1208", "severity": 1, "message": "1253", "line": 87, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 87, "endColumn": 34}, {"ruleId": "1208", "severity": 1, "message": "1376", "line": 107, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 107, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1377", "line": 107, "column": 20, "nodeType": "1210", "messageId": "1211", "endLine": 107, "endColumn": 31}, {"ruleId": "1220", "severity": 1, "message": "1378", "line": 121, "column": 6, "nodeType": "1222", "endLine": 121, "endColumn": 8, "suggestions": "1379"}, {"ruleId": "1220", "severity": 1, "message": "1380", "line": 223, "column": 6, "nodeType": "1222", "endLine": 223, "endColumn": 23, "suggestions": "1381"}, {"ruleId": "1295", "severity": 1, "message": "1296", "line": 678, "column": 31, "nodeType": "1297", "endLine": 682, "endColumn": 33}, {"ruleId": "1208", "severity": 1, "message": "1260", "line": 20, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 20, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1382", "line": 67, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 67, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1383", "line": 97, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 97, "endColumn": 26}, {"ruleId": "1208", "severity": 1, "message": "1384", "line": 97, "column": 28, "nodeType": "1210", "messageId": "1211", "endLine": 97, "endColumn": 47}, {"ruleId": "1208", "severity": 1, "message": "1253", "line": 102, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 102, "endColumn": 34}, {"ruleId": "1208", "severity": 1, "message": "1314", "line": 106, "column": 21, "nodeType": "1210", "messageId": "1211", "endLine": 106, "endColumn": 29}, {"ruleId": "1208", "severity": 1, "message": "1363", "line": 119, "column": 23, "nodeType": "1210", "messageId": "1211", "endLine": 119, "endColumn": 37}, {"ruleId": "1220", "severity": 1, "message": "1256", "line": 133, "column": 6, "nodeType": "1222", "endLine": 133, "endColumn": 8, "suggestions": "1385"}, {"ruleId": "1220", "severity": 1, "message": "1386", "line": 137, "column": 6, "nodeType": "1222", "endLine": 137, "endColumn": 23, "suggestions": "1387"}, {"ruleId": "1220", "severity": 1, "message": "1388", "line": 146, "column": 6, "nodeType": "1222", "endLine": 146, "endColumn": 8, "suggestions": "1389"}, {"ruleId": "1208", "severity": 1, "message": "1390", "line": 12, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 12, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1391", "line": 13, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 13, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1392", "line": 19, "column": 6, "nodeType": "1210", "messageId": "1211", "endLine": 19, "endColumn": 19}, {"ruleId": "1220", "severity": 1, "message": "1393", "line": 57, "column": 6, "nodeType": "1222", "endLine": 57, "endColumn": 20, "suggestions": "1394"}, {"ruleId": "1208", "severity": 1, "message": "1395", "line": 27, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 27, "endColumn": 22}, {"ruleId": "1208", "severity": 1, "message": "1338", "line": 28, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 28, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1396", "line": 32, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 32, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1397", "line": 41, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 41, "endColumn": 23}, {"ruleId": "1208", "severity": 1, "message": "1398", "line": 53, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 53, "endColumn": 35}, {"ruleId": "1208", "severity": 1, "message": "1399", "line": 57, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 57, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1400", "line": 80, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 80, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1401", "line": 84, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 84, "endColumn": 12}, {"ruleId": "1208", "severity": 1, "message": "1402", "line": 90, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 90, "endColumn": 26}, {"ruleId": "1208", "severity": 1, "message": "1321", "line": 91, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 91, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1403", "line": 92, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 92, "endColumn": 24}, {"ruleId": "1208", "severity": 1, "message": "1404", "line": 106, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 106, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1405", "line": 107, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 107, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1406", "line": 108, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 108, "endColumn": 23}, {"ruleId": "1208", "severity": 1, "message": "1407", "line": 109, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 109, "endColumn": 22}, {"ruleId": "1208", "severity": 1, "message": "1263", "line": 114, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 114, "endColumn": 25}, {"ruleId": "1208", "severity": 1, "message": "1253", "line": 118, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 118, "endColumn": 34}, {"ruleId": "1208", "severity": 1, "message": "1265", "line": 123, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 123, "endColumn": 25}, {"ruleId": "1208", "severity": 1, "message": "1408", "line": 128, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 128, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1266", "line": 139, "column": 25, "nodeType": "1210", "messageId": "1211", "endLine": 139, "endColumn": 41}, {"ruleId": "1208", "severity": 1, "message": "1350", "line": 141, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 141, "endColumn": 20}, {"ruleId": "1267", "severity": 1, "message": "1268", "line": 179, "column": 56, "nodeType": "1269", "messageId": "1270", "endLine": 179, "endColumn": 58}, {"ruleId": "1267", "severity": 1, "message": "1268", "line": 184, "column": 57, "nodeType": "1269", "messageId": "1270", "endLine": 184, "endColumn": 59}, {"ruleId": "1220", "severity": 1, "message": "1409", "line": 192, "column": 6, "nodeType": "1222", "endLine": 192, "endColumn": 8, "suggestions": "1410"}, {"ruleId": "1208", "severity": 1, "message": "1411", "line": 207, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 207, "endColumn": 20}, {"ruleId": "1267", "severity": 1, "message": "1294", "line": 217, "column": 36, "nodeType": "1269", "messageId": "1270", "endLine": 217, "endColumn": 38}, {"ruleId": "1208", "severity": 1, "message": "1412", "line": 278, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 278, "endColumn": 20}, {"ruleId": "1267", "severity": 1, "message": "1268", "line": 390, "column": 60, "nodeType": "1269", "messageId": "1270", "endLine": 390, "endColumn": 62}, {"ruleId": "1208", "severity": 1, "message": "1413", "line": 499, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 499, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1414", "line": 2, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 2, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1415", "line": 3, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 3, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1416", "line": 4, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1417", "line": 5, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 5, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1418", "line": 6, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 6, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1419", "line": 10, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 10, "endColumn": 39}, {"ruleId": "1208", "severity": 1, "message": "1420", "line": 11, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 36}, {"ruleId": "1208", "severity": 1, "message": "1421", "line": 7, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 7, "endColumn": 7}, {"ruleId": "1208", "severity": 1, "message": "1422", "line": 8, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 8, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1382", "line": 11, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 12}, {"ruleId": "1208", "severity": 1, "message": "1311", "line": 14, "column": 19, "nodeType": "1210", "messageId": "1211", "endLine": 14, "endColumn": 24}, {"ruleId": "1208", "severity": 1, "message": "1398", "line": 28, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 28, "endColumn": 28}, {"ruleId": "1208", "severity": 1, "message": "1260", "line": 32, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 32, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1423", "line": 36, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 36, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1424", "line": 37, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 37, "endColumn": 8}, {"ruleId": "1208", "severity": 1, "message": "1425", "line": 51, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 51, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1426", "line": 57, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 57, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1266", "line": 78, "column": 25, "nodeType": "1210", "messageId": "1211", "endLine": 78, "endColumn": 41}, {"ruleId": "1208", "severity": 1, "message": "1253", "line": 83, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 83, "endColumn": 34}, {"ruleId": "1220", "severity": 1, "message": "1427", "line": 118, "column": 6, "nodeType": "1222", "endLine": 118, "endColumn": 8, "suggestions": "1428"}, {"ruleId": "1208", "severity": 1, "message": "1429", "line": 179, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 179, "endColumn": 18}, {"ruleId": "1267", "severity": 1, "message": "1294", "line": 368, "column": 65, "nodeType": "1269", "messageId": "1270", "endLine": 368, "endColumn": 67}, {"ruleId": "1220", "severity": 1, "message": "1430", "line": 28, "column": 6, "nodeType": "1222", "endLine": 28, "endColumn": 16, "suggestions": "1431"}, {"ruleId": "1220", "severity": 1, "message": "1430", "line": 28, "column": 6, "nodeType": "1222", "endLine": 28, "endColumn": 16, "suggestions": "1432"}, {"ruleId": "1208", "severity": 1, "message": "1433", "line": 2, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 2, "endColumn": 19}, {"ruleId": "1267", "severity": 1, "message": "1294", "line": 29, "column": 45, "nodeType": "1269", "messageId": "1270", "endLine": 29, "endColumn": 47}, {"ruleId": "1434", "severity": 1, "message": "1435", "line": 7, "column": 24, "nodeType": "1436", "messageId": "1437", "endLine": 7, "endColumn": 25, "suggestions": "1438"}, {"ruleId": "1434", "severity": 1, "message": "1435", "line": 7, "column": 40, "nodeType": "1436", "messageId": "1437", "endLine": 7, "endColumn": 41, "suggestions": "1439"}, {"ruleId": "1208", "severity": 1, "message": "1440", "line": 6, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 6, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1441", "line": 9, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 9, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1259", "line": 10, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 10, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1442", "line": 12, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 12, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1213", "line": 14, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 14, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1443", "line": 15, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 15, "endColumn": 12}, {"ruleId": "1208", "severity": 1, "message": "1444", "line": 16, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 16, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1445", "line": 22, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 22, "endColumn": 26}, {"ruleId": "1208", "severity": 1, "message": "1446", "line": 28, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 28, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1447", "line": 31, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 31, "endColumn": 28}, {"ruleId": "1208", "severity": 1, "message": "1448", "line": 32, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 32, "endColumn": 34}, {"ruleId": "1208", "severity": 1, "message": "1449", "line": 33, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 33, "endColumn": 22}, {"ruleId": "1208", "severity": 1, "message": "1450", "line": 41, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 41, "endColumn": 26}, {"ruleId": "1208", "severity": 1, "message": "1451", "line": 92, "column": 7, "nodeType": "1210", "messageId": "1211", "endLine": 92, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1452", "line": 156, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 156, "endColumn": 14}, {"ruleId": "1220", "severity": 1, "message": "1453", "line": 169, "column": 6, "nodeType": "1222", "endLine": 169, "endColumn": 8, "suggestions": "1454"}, {"ruleId": "1208", "severity": 1, "message": "1455", "line": 171, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 171, "endColumn": 28}, {"ruleId": "1208", "severity": 1, "message": "1456", "line": 184, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 184, "endColumn": 41}, {"ruleId": "1208", "severity": 1, "message": "1457", "line": 340, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 340, "endColumn": 39}, {"ruleId": "1208", "severity": 1, "message": "1458", "line": 351, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 351, "endColumn": 44}, {"ruleId": "1434", "severity": 1, "message": "1435", "line": 9, "column": 24, "nodeType": "1436", "messageId": "1437", "endLine": 9, "endColumn": 25, "suggestions": "1459"}, {"ruleId": "1434", "severity": 1, "message": "1435", "line": 9, "column": 40, "nodeType": "1436", "messageId": "1437", "endLine": 9, "endColumn": 41, "suggestions": "1460"}, {"ruleId": "1208", "severity": 1, "message": "1461", "line": 1, "column": 17, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 26}, {"ruleId": "1208", "severity": 1, "message": "1462", "line": 1, "column": 28, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 35}, {"ruleId": "1208", "severity": 1, "message": "1259", "line": 4, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1421", "line": 4, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1422", "line": 4, "column": 16, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 27}, {"ruleId": "1208", "severity": 1, "message": "1463", "line": 8, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 8, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1464", "line": 2, "column": 16, "nodeType": "1210", "messageId": "1211", "endLine": 2, "endColumn": 25}, {"ruleId": "1208", "severity": 1, "message": "1465", "line": 2, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 2, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1464", "line": 2, "column": 15, "nodeType": "1210", "messageId": "1211", "endLine": 2, "endColumn": 24}, {"ruleId": "1208", "severity": 1, "message": "1421", "line": 3, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 3, "endColumn": 7}, {"ruleId": "1208", "severity": 1, "message": "1422", "line": 4, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1433", "line": 1, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1433", "line": 1, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1433", "line": 1, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1466", "line": 11, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1253", "line": 32, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 32, "endColumn": 34}, {"ruleId": "1208", "severity": 1, "message": "1292", "line": 42, "column": 11, "nodeType": "1210", "messageId": "1211", "endLine": 42, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1396", "line": 1, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1347", "line": 2, "column": 23, "nodeType": "1210", "messageId": "1211", "endLine": 2, "endColumn": 27}, {"ruleId": "1208", "severity": 1, "message": "1311", "line": 2, "column": 29, "nodeType": "1210", "messageId": "1211", "endLine": 2, "endColumn": 34}, {"ruleId": "1208", "severity": 1, "message": "1348", "line": 2, "column": 36, "nodeType": "1210", "messageId": "1211", "endLine": 2, "endColumn": 41}, {"ruleId": "1208", "severity": 1, "message": "1467", "line": 1, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 15}, {"ruleId": "1220", "severity": 1, "message": "1468", "line": 63, "column": 6, "nodeType": "1222", "endLine": 63, "endColumn": 8, "suggestions": "1469"}, {"ruleId": "1208", "severity": 1, "message": "1336", "line": 4, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 8}, {"ruleId": "1208", "severity": 1, "message": "1470", "line": 8, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 8, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1322", "line": 25, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 25, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1471", "line": 26, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 26, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1321", "line": 27, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 27, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1440", "line": 28, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 28, "endColumn": 10}, {"ruleId": "1208", "severity": 1, "message": "1472", "line": 48, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 48, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1473", "line": 50, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 50, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1421", "line": 52, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 52, "endColumn": 12}, {"ruleId": "1208", "severity": 1, "message": "1422", "line": 53, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 53, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1382", "line": 54, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 54, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1347", "line": 54, "column": 21, "nodeType": "1210", "messageId": "1211", "endLine": 54, "endColumn": 25}, {"ruleId": "1208", "severity": 1, "message": "1474", "line": 55, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 55, "endColumn": 22}, {"ruleId": "1208", "severity": 1, "message": "1475", "line": 55, "column": 24, "nodeType": "1210", "messageId": "1211", "endLine": 55, "endColumn": 39}, {"ruleId": "1208", "severity": 1, "message": "1476", "line": 56, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 56, "endColumn": 31}, {"ruleId": "1208", "severity": 1, "message": "1403", "line": 57, "column": 26, "nodeType": "1210", "messageId": "1211", "endLine": 57, "endColumn": 40}, {"ruleId": "1208", "severity": 1, "message": "1477", "line": 107, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 107, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1478", "line": 108, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 108, "endColumn": 23}, {"ruleId": "1208", "severity": 1, "message": "1479", "line": 109, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 109, "endColumn": 25}, {"ruleId": "1208", "severity": 1, "message": "1480", "line": 111, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 111, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1481", "line": 112, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 112, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1482", "line": 117, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 117, "endColumn": 29}, {"ruleId": "1208", "severity": 1, "message": "1483", "line": 125, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 125, "endColumn": 32}, {"ruleId": "1208", "severity": 1, "message": "1484", "line": 131, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 131, "endColumn": 22}, {"ruleId": "1208", "severity": 1, "message": "1485", "line": 133, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 133, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1486", "line": 142, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 142, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1266", "line": 177, "column": 25, "nodeType": "1210", "messageId": "1211", "endLine": 177, "endColumn": 41}, {"ruleId": "1220", "severity": 1, "message": "1487", "line": 222, "column": 6, "nodeType": "1222", "endLine": 222, "endColumn": 8, "suggestions": "1488"}, {"ruleId": "1220", "severity": 1, "message": "1489", "line": 244, "column": 6, "nodeType": "1222", "endLine": 244, "endColumn": 68, "suggestions": "1490"}, {"ruleId": "1267", "severity": 1, "message": "1268", "line": 426, "column": 64, "nodeType": "1269", "messageId": "1270", "endLine": 426, "endColumn": 66}, {"ruleId": "1267", "severity": 1, "message": "1268", "line": 432, "column": 66, "nodeType": "1269", "messageId": "1270", "endLine": 432, "endColumn": 68}, {"ruleId": "1267", "severity": 1, "message": "1294", "line": 450, "column": 50, "nodeType": "1269", "messageId": "1270", "endLine": 450, "endColumn": 52}, {"ruleId": "1267", "severity": 1, "message": "1268", "line": 743, "column": 61, "nodeType": "1269", "messageId": "1270", "endLine": 743, "endColumn": 63}, {"ruleId": "1208", "severity": 1, "message": "1209", "line": 1, "column": 46, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 56}, {"ruleId": "1208", "severity": 1, "message": "1470", "line": 7, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 7, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1491", "line": 19, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 19, "endColumn": 24}, {"ruleId": "1220", "severity": 1, "message": "1492", "line": 58, "column": 6, "nodeType": "1222", "endLine": 58, "endColumn": 39, "suggestions": "1493"}, {"ruleId": "1220", "severity": 1, "message": "1494", "line": 137, "column": 6, "nodeType": "1222", "endLine": 137, "endColumn": 36, "suggestions": "1495"}, {"ruleId": "1208", "severity": 1, "message": "1433", "line": 1, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1496", "line": 6, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 6, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1497", "line": 14, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 14, "endColumn": 27}, {"ruleId": "1208", "severity": 1, "message": "1498", "line": 4, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1213", "line": 5, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 5, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1441", "line": 6, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 6, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1499", "line": 7, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 7, "endColumn": 6}, {"ruleId": "1208", "severity": 1, "message": "1321", "line": 8, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 8, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1471", "line": 9, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 9, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1440", "line": 10, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 10, "endColumn": 10}, {"ruleId": "1208", "severity": 1, "message": "1366", "line": 11, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 7}, {"ruleId": "1208", "severity": 1, "message": "1367", "line": 12, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 12, "endColumn": 11}, {"ruleId": "1208", "severity": 1, "message": "1470", "line": 13, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 13, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1259", "line": 14, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 14, "endColumn": 10}, {"ruleId": "1208", "severity": 1, "message": "1500", "line": 17, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 17, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1473", "line": 19, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 19, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1501", "line": 20, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 20, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1502", "line": 21, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 21, "endColumn": 24}, {"ruleId": "1208", "severity": 1, "message": "1503", "line": 22, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 22, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1504", "line": 23, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 23, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1505", "line": 23, "column": 18, "nodeType": "1210", "messageId": "1211", "endLine": 23, "endColumn": 22}, {"ruleId": "1208", "severity": 1, "message": "1247", "line": 24, "column": 13, "nodeType": "1210", "messageId": "1211", "endLine": 24, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1506", "line": 33, "column": 11, "nodeType": "1210", "messageId": "1211", "endLine": 33, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1507", "line": 4, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1214", "line": 10, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 10, "endColumn": 12}, {"ruleId": "1208", "severity": 1, "message": "1424", "line": 14, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 14, "endColumn": 8}, {"ruleId": "1208", "severity": 1, "message": "1508", "line": 21, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 21, "endColumn": 22}, {"ruleId": "1208", "severity": 1, "message": "1509", "line": 22, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 22, "endColumn": 30}, {"ruleId": "1208", "severity": 1, "message": "1510", "line": 22, "column": 32, "nodeType": "1210", "messageId": "1211", "endLine": 22, "endColumn": 42}, {"ruleId": "1208", "severity": 1, "message": "1347", "line": 15, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 15, "endColumn": 7}, {"ruleId": "1208", "severity": 1, "message": "1259", "line": 15, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 15, "endColumn": 10}, {"ruleId": "1208", "severity": 1, "message": "1461", "line": 1, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 36}, {"ruleId": "1208", "severity": 1, "message": "1511", "line": 13, "column": 13, "nodeType": "1210", "messageId": "1211", "endLine": 13, "endColumn": 23}, {"ruleId": "1208", "severity": 1, "message": "1433", "line": 1, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1512", "line": 4, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1433", "line": 1, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1512", "line": 10, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 10, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1513", "line": 11, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1514", "line": 1, "column": 17, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 25}, {"ruleId": "1208", "severity": 1, "message": "1515", "line": 10, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 10, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1516", "line": 8, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 8, "endColumn": 28}, {"ruleId": "1208", "severity": 1, "message": "1347", "line": 29, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 29, "endColumn": 12}, {"ruleId": "1208", "severity": 1, "message": "1517", "line": 52, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 52, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1518", "line": 57, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 57, "endColumn": 28}, {"ruleId": "1208", "severity": 1, "message": "1519", "line": 58, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 58, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1520", "line": 59, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 59, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1521", "line": 73, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 73, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1522", "line": 73, "column": 18, "nodeType": "1210", "messageId": "1211", "endLine": 73, "endColumn": 27}, {"ruleId": "1208", "severity": 1, "message": "1253", "line": 76, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 76, "endColumn": 34}, {"ruleId": "1267", "severity": 1, "message": "1294", "line": 143, "column": 53, "nodeType": "1269", "messageId": "1270", "endLine": 143, "endColumn": 55}, {"ruleId": "1220", "severity": 1, "message": "1523", "line": 152, "column": 6, "nodeType": "1222", "endLine": 152, "endColumn": 8, "suggestions": "1524"}, {"ruleId": "1220", "severity": 1, "message": "1342", "line": 373, "column": 6, "nodeType": "1222", "endLine": 373, "endColumn": 8, "suggestions": "1525"}, {"ruleId": "1208", "severity": 1, "message": "1433", "line": 1, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1526", "line": 6, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 6, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1527", "line": 29, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 29, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1396", "line": 1, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1461", "line": 2, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 2, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1514", "line": 2, "column": 21, "nodeType": "1210", "messageId": "1211", "endLine": 2, "endColumn": 29}, {"ruleId": "1208", "severity": 1, "message": "1422", "line": 5, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 5, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1396", "line": 8, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 8, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1528", "line": 10, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 10, "endColumn": 8}, {"ruleId": "1208", "severity": 1, "message": "1366", "line": 9, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 9, "endColumn": 7}, {"ruleId": "1208", "severity": 1, "message": "1367", "line": 10, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 10, "endColumn": 11}, {"ruleId": "1208", "severity": 1, "message": "1470", "line": 11, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1259", "line": 12, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 12, "endColumn": 10}, {"ruleId": "1208", "severity": 1, "message": "1529", "line": 12, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 12, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1530", "line": 12, "column": 18, "nodeType": "1210", "messageId": "1211", "endLine": 12, "endColumn": 35}, {"ruleId": "1208", "severity": 1, "message": "1444", "line": 13, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 13, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1531", "line": 14, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 14, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1251", "line": 16, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 16, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1249", "line": 17, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 17, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1532", "line": 20, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 20, "endColumn": 24}, {"ruleId": "1208", "severity": 1, "message": "1533", "line": 21, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 21, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1534", "line": 45, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 45, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1266", "line": 59, "column": 25, "nodeType": "1210", "messageId": "1211", "endLine": 59, "endColumn": 41}, {"ruleId": "1220", "severity": 1, "message": "1535", "line": 81, "column": 6, "nodeType": "1222", "endLine": 81, "endColumn": 8, "suggestions": "1536"}, {"ruleId": "1208", "severity": 1, "message": "1366", "line": 9, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 9, "endColumn": 7}, {"ruleId": "1208", "severity": 1, "message": "1367", "line": 10, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 10, "endColumn": 11}, {"ruleId": "1208", "severity": 1, "message": "1470", "line": 11, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 15}, {"ruleId": "1208", "severity": 1, "message": "1259", "line": 12, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 12, "endColumn": 10}, {"ruleId": "1537", "severity": 1, "message": "1538", "line": 37, "column": 61, "nodeType": "1539", "messageId": "1540", "endLine": 37, "endColumn": 63}, {"ruleId": "1208", "severity": 1, "message": "1441", "line": 5, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 5, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1396", "line": 13, "column": 16, "nodeType": "1210", "messageId": "1211", "endLine": 13, "endColumn": 22}, {"ruleId": "1208", "severity": 1, "message": "1541", "line": 13, "column": 24, "nodeType": "1210", "messageId": "1211", "endLine": 13, "endColumn": 40}, {"ruleId": "1208", "severity": 1, "message": "1528", "line": 13, "column": 42, "nodeType": "1210", "messageId": "1211", "endLine": 13, "endColumn": 47}, {"ruleId": "1208", "severity": 1, "message": "1362", "line": 14, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 14, "endColumn": 23}, {"ruleId": "1208", "severity": 1, "message": "1542", "line": 15, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 15, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1543", "line": 17, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 17, "endColumn": 29}, {"ruleId": "1208", "severity": 1, "message": "1544", "line": 18, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 18, "endColumn": 26}, {"ruleId": "1208", "severity": 1, "message": "1545", "line": 19, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 19, "endColumn": 29}, {"ruleId": "1208", "severity": 1, "message": "1546", "line": 20, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 20, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1547", "line": 21, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 21, "endColumn": 25}, {"ruleId": "1208", "severity": 1, "message": "1213", "line": 8, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 8, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1548", "line": 10, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 10, "endColumn": 26}, {"ruleId": "1208", "severity": 1, "message": "1549", "line": 22, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 22, "endColumn": 22}, {"ruleId": "1208", "severity": 1, "message": "1396", "line": 1, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1461", "line": 2, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 2, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1514", "line": 2, "column": 21, "nodeType": "1210", "messageId": "1211", "endLine": 2, "endColumn": 29}, {"ruleId": "1208", "severity": 1, "message": "1550", "line": 2, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 2, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1461", "line": 4, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 12}, {"ruleId": "1208", "severity": 1, "message": "1253", "line": 38, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 38, "endColumn": 34}, {"ruleId": "1208", "severity": 1, "message": "1292", "line": 49, "column": 11, "nodeType": "1210", "messageId": "1211", "endLine": 49, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1529", "line": 7, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 7, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1444", "line": 8, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 8, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1251", "line": 9, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 9, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1249", "line": 10, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 10, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1551", "line": 12, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 12, "endColumn": 29}, {"ruleId": "1208", "severity": 1, "message": "1397", "line": 13, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 13, "endColumn": 23}, {"ruleId": "1208", "severity": 1, "message": "1552", "line": 19, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 19, "endColumn": 24}, {"ruleId": "1208", "severity": 1, "message": "1553", "line": 20, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 20, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1554", "line": 21, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 21, "endColumn": 21}, {"ruleId": "1208", "severity": 1, "message": "1331", "line": 21, "column": 23, "nodeType": "1210", "messageId": "1211", "endLine": 21, "endColumn": 32}, {"ruleId": "1208", "severity": 1, "message": "1259", "line": 21, "column": 41, "nodeType": "1210", "messageId": "1211", "endLine": 21, "endColumn": 48}, {"ruleId": "1208", "severity": 1, "message": "1555", "line": 22, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 22, "endColumn": 27}, {"ruleId": "1220", "severity": 1, "message": "1556", "line": 56, "column": 6, "nodeType": "1222", "endLine": 56, "endColumn": 8, "suggestions": "1557"}, {"ruleId": "1208", "severity": 1, "message": "1253", "line": 32, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 32, "endColumn": 34}, {"ruleId": "1220", "severity": 1, "message": "1558", "line": 57, "column": 6, "nodeType": "1222", "endLine": 57, "endColumn": 8, "suggestions": "1559"}, {"ruleId": "1208", "severity": 1, "message": "1560", "line": 64, "column": 15, "nodeType": "1210", "messageId": "1211", "endLine": 64, "endColumn": 57}, {"ruleId": "1267", "severity": 1, "message": "1294", "line": 85, "column": 38, "nodeType": "1269", "messageId": "1270", "endLine": 85, "endColumn": 40}, {"ruleId": "1267", "severity": 1, "message": "1294", "line": 94, "column": 67, "nodeType": "1269", "messageId": "1270", "endLine": 94, "endColumn": 69}, {"ruleId": "1208", "severity": 1, "message": "1561", "line": 113, "column": 13, "nodeType": "1210", "messageId": "1211", "endLine": 113, "endColumn": 72}, {"ruleId": "1208", "severity": 1, "message": "1396", "line": 1, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 14}, {"ruleId": "1220", "severity": 1, "message": "1562", "line": 15, "column": 6, "nodeType": "1222", "endLine": 15, "endColumn": 8, "suggestions": "1563"}, {"ruleId": "1564", "severity": 1, "message": "1565", "line": 22, "column": 9, "nodeType": "1566", "messageId": "1567", "endLine": 38, "endColumn": 10}, {"ruleId": "1208", "severity": 1, "message": "1433", "line": 1, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1503", "line": 10, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 10, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1253", "line": 36, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 36, "endColumn": 34}, {"ruleId": "1295", "severity": 1, "message": "1296", "line": 126, "column": 11, "nodeType": "1297", "endLine": 137, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1568", "line": 33, "column": 5, "nodeType": "1210", "messageId": "1211", "endLine": 33, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1569", "line": 69, "column": 11, "nodeType": "1210", "messageId": "1211", "endLine": 69, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1570", "line": 2, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 2, "endColumn": 24}, {"ruleId": "1208", "severity": 1, "message": "1499", "line": 9, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 9, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1571", "line": 26, "column": 30, "nodeType": "1210", "messageId": "1211", "endLine": 26, "endColumn": 44}, {"ruleId": "1208", "severity": 1, "message": "1321", "line": 3, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 3, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1447", "line": 4, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 28}, {"ruleId": "1208", "severity": 1, "message": "1213", "line": 5, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 5, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1443", "line": 7, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 7, "endColumn": 12}, {"ruleId": "1208", "severity": 1, "message": "1444", "line": 8, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 8, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1572", "line": 11, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1573", "line": 16, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 16, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1574", "line": 18, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 18, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1412", "line": 19, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 19, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1481", "line": 22, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 22, "endColumn": 20}, {"ruleId": "1208", "severity": 1, "message": "1371", "line": 11, "column": 23, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 34}, {"ruleId": "1220", "severity": 1, "message": "1575", "line": 43, "column": 6, "nodeType": "1222", "endLine": 43, "endColumn": 8, "suggestions": "1576"}, {"ruleId": "1220", "severity": 1, "message": "1577", "line": 37, "column": 6, "nodeType": "1222", "endLine": 37, "endColumn": 8, "suggestions": "1578"}, {"ruleId": "1208", "severity": 1, "message": "1579", "line": 22, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 22, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1580", "line": 22, "column": 20, "nodeType": "1210", "messageId": "1211", "endLine": 22, "endColumn": 31}, {"ruleId": "1220", "severity": 1, "message": "1581", "line": 41, "column": 6, "nodeType": "1222", "endLine": 41, "endColumn": 26, "suggestions": "1582"}, {"ruleId": "1208", "severity": 1, "message": "1396", "line": 1, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 14}, {"ruleId": "1208", "severity": 1, "message": "1347", "line": 3, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 3, "endColumn": 31}, {"ruleId": "1220", "severity": 1, "message": "1581", "line": 136, "column": 6, "nodeType": "1222", "endLine": 136, "endColumn": 26, "suggestions": "1583"}, {"ruleId": "1208", "severity": 1, "message": "1584", "line": 4, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1396", "line": 5, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 5, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1585", "line": 6, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 6, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1584", "line": 11, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1586", "line": 12, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 12, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1396", "line": 2, "column": 27, "nodeType": "1210", "messageId": "1211", "endLine": 2, "endColumn": 33}, {"ruleId": "1208", "severity": 1, "message": "1584", "line": 4, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 4, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1586", "line": 5, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 5, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1396", "line": 5, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 5, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1585", "line": 6, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 6, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1584", "line": 11, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1586", "line": 12, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 12, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1396", "line": 5, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 5, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1585", "line": 6, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 6, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1584", "line": 11, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1586", "line": 12, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 12, "endColumn": 16}, {"ruleId": "1587", "severity": 1, "message": "1588", "line": 50, "column": 15, "nodeType": "1297", "endLine": 52, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1396", "line": 5, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 5, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1585", "line": 6, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 6, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1584", "line": 11, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 11, "endColumn": 13}, {"ruleId": "1208", "severity": 1, "message": "1586", "line": 12, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 12, "endColumn": 16}, {"ruleId": "1208", "severity": 1, "message": "1589", "line": 35, "column": 7, "nodeType": "1210", "messageId": "1211", "endLine": 35, "endColumn": 25}, {"ruleId": "1208", "severity": 1, "message": "1368", "line": 15, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 15, "endColumn": 9}, {"ruleId": "1208", "severity": 1, "message": "1590", "line": 16, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 16, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1214", "line": 17, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 17, "endColumn": 12}, {"ruleId": "1208", "severity": 1, "message": "1259", "line": 22, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 22, "endColumn": 10}, {"ruleId": "1208", "severity": 1, "message": "1591", "line": 28, "column": 15, "nodeType": "1210", "messageId": "1211", "endLine": 28, "endColumn": 27}, {"ruleId": "1208", "severity": 1, "message": "1586", "line": 29, "column": 11, "nodeType": "1210", "messageId": "1211", "endLine": 29, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1305", "line": 30, "column": 15, "nodeType": "1210", "messageId": "1211", "endLine": 30, "endColumn": 27}, {"ruleId": "1208", "severity": 1, "message": "1592", "line": 41, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 41, "endColumn": 30}, {"ruleId": "1208", "severity": 1, "message": "1593", "line": 100, "column": 10, "nodeType": "1210", "messageId": "1211", "endLine": 100, "endColumn": 29}, {"ruleId": "1220", "severity": 1, "message": "1594", "line": 116, "column": 6, "nodeType": "1222", "endLine": 116, "endColumn": 16, "suggestions": "1595"}, {"ruleId": "1220", "severity": 1, "message": "1596", "line": 123, "column": 6, "nodeType": "1222", "endLine": 123, "endColumn": 34, "suggestions": "1597"}, {"ruleId": "1208", "severity": 1, "message": "1598", "line": 252, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 252, "endColumn": 35}, {"ruleId": "1208", "severity": 1, "message": "1599", "line": 257, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 257, "endColumn": 27}, {"ruleId": "1208", "severity": 1, "message": "1461", "line": 1, "column": 29, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 38}, {"ruleId": "1208", "severity": 1, "message": "1514", "line": 1, "column": 40, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 48}, {"ruleId": "1208", "severity": 1, "message": "1461", "line": 1, "column": 29, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 38}, {"ruleId": "1208", "severity": 1, "message": "1514", "line": 1, "column": 40, "nodeType": "1210", "messageId": "1211", "endLine": 1, "endColumn": 48}, {"ruleId": "1208", "severity": 1, "message": "1401", "line": 16, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 16, "endColumn": 7}, {"ruleId": "1208", "severity": 1, "message": "1541", "line": 24, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 24, "endColumn": 19}, {"ruleId": "1208", "severity": 1, "message": "1337", "line": 38, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 38, "endColumn": 18}, {"ruleId": "1208", "severity": 1, "message": "1600", "line": 39, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 39, "endColumn": 22}, {"ruleId": "1208", "severity": 1, "message": "1601", "line": 40, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 40, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1602", "line": 41, "column": 8, "nodeType": "1210", "messageId": "1211", "endLine": 41, "endColumn": 24}, {"ruleId": "1220", "severity": 1, "message": "1594", "line": 117, "column": 6, "nodeType": "1222", "endLine": 117, "endColumn": 8, "suggestions": "1603"}, {"ruleId": "1220", "severity": 1, "message": "1604", "line": 124, "column": 6, "nodeType": "1222", "endLine": 124, "endColumn": 26, "suggestions": "1605"}, {"ruleId": "1208", "severity": 1, "message": "1606", "line": 52, "column": 9, "nodeType": "1210", "messageId": "1211", "endLine": 52, "endColumn": 17}, {"ruleId": "1208", "severity": 1, "message": "1499", "line": 9, "column": 3, "nodeType": "1210", "messageId": "1211", "endLine": 9, "endColumn": 6}, "@typescript-eslint/no-unused-vars", "'useContext' is defined but never used.", "Identifier", "unusedVar", "'logo' is defined but never used.", "'Button' is defined but never used.", "'TextField' is defined but never used.", "'ThreeCircles' is defined but never used.", "'PreferencesContext' is defined but never used.", "'theme' is defined but never used.", "'appToastConfig' is assigned a value but never used.", "'setAppToastConfig' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has an unnecessary dependency: 'loading'. Either exclude it or remove the dependency array.", "ArrayExpression", ["1607"], "React Hook useMemo has a missing dependency: 'setLoading'. Either include it or remove the dependency array.", ["1608"], "'setActiveMenuItem' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'activeMenuItem'. Either exclude it or remove the dependency array.", ["1609"], "React Hook useEffect has missing dependencies: 'location.pathname', 'loginMessage', and 'navigate'. Either include them or remove the dependency array.", ["1610"], "React Hook useEffect has a missing dependency: 'dispatchSessionExpired'. Either include it or remove the dependency array.", ["1611"], "React Hook useCallback has an unnecessary dependency: 'open'. Either exclude it or remove the dependency array.", ["1612"], ["1613"], "React Hook useCallback has an unnecessary dependency: 'toastConfig'. Either exclude it or remove the dependency array.", ["1614"], "React Hook useMemo has missing dependencies: 'setOpen', 'setToastConfig', and 'setToastMessage'. Either include them or remove the dependency array.", ["1615"], "'PaletteOptions' is defined but never used.", "'lookupReducer' is defined but never used.", "'LOGIN' is defined but never used.", "'createRef' is defined but never used.", "'axios' is defined but never used.", "'AnyAction' is defined but never used.", "'_applicationHelperService' is assigned a value but never used.", "'yup' is defined but never used.", "'Link' is defined but never used.", "'FormControl' is defined but never used.", "'FilledInput' is defined but never used.", "'InputLabel' is defined but never used.", "'MessageConstants' is defined but never used.", "'setOpen' is assigned a value but never used.", "'handleMouseDownPassword' is assigned a value but never used.", "'handleMouseUpPassword' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'title'. Either include it or remove the dependency array.", ["1616"], "'RevenueChartDashboard' is defined but never used.", "'Divider' is defined but never used.", "'Drawer' is defined but never used.", "'openqanda' is assigned a value but never used.", "'setOpenqanda' is assigned a value but never used.", "'_locationService' is assigned a value but never used.", "'businessGroupsOnBusiness' is assigned a value but never used.", "'paginationModel' is assigned a value but never used.", "'setInitialValues' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchLocationsPaginated', 'getBusiness', and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1617"], "'HomeChartCard' is defined but never used.", "'originalLocationData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchLocations', 'getBusiness', and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1618"], ["1619"], "'PageProps' is defined but never used.", "'ILoginModel' is defined but never used.", "'authInitiate' is defined but never used.", "'CalendarToday' is defined but never used.", "'ScheduleLater' is defined but never used.", "'GenericDrawer' is defined but never used.", "'date' is assigned a value but never used.", "'setDate' is assigned a value but never used.", "'scheduleForLater' is assigned a value but never used.", "'setScheduleForLater' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'location.state' and 'title'. Either include them or remove the dependency array.", ["1620"], "'checkFormValidity' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'response' is assigned a value but never used.", "'formatDayJsToISO' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'element' is assigned a value but never used.", "'RegisteredEmployeesChart' is defined but never used.", "'ActiveJobsChart' is defined but never used.", "'PieChart' is defined but never used.", "'GroupIcon' is defined but never used.", "'WorkHistoryIcon' is defined but never used.", "'EventAvailableIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'EditLocationAltIcon' is defined but never used.", "'HelpIcon' is defined but never used.", "'ArrowUpwardRoundedIcon' is defined but never used.", "'ArrowDownwardRoundedIcon' is defined but never used.", "'FormHelperText' is defined but never used.", "'Grid2' is defined but never used.", "'BusinessInteractionsChart' is defined but never used.", "'SearchQueriesList' is defined but never used.", "'rbAccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLocations'. Either include it or remove the dependency array.", ["1621"], "React Hook useEffect has a missing dependency: 'locationList'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedAccountId' needs the current value of 'locationList'.", ["1622"], "React Hook useEffect has missing dependencies: 'fetchAnalyticsData', 'locationList', and 'selectedAccountId'. Either include them or remove the dependency array.", ["1623"], "'IconButton' is defined but never used.", "'Dialog' is defined but never used.", "'AddIcon' is defined but never used.", "'ServicesDisplay' is defined but never used.", "'Accessibility' is defined but never used.", "'FlagIcon' is defined but never used.", "'ChatIcon' is defined but never used.", "'LanguageIcon' is defined but never used.", "'LocationOnIcon' is defined but never used.", "'useCallback' is defined but never used.", "'Container' is defined but never used.", "'activeTab' is assigned a value but never used.", "'setActiveTab' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadSavedConfigurations'. Either include it or remove the dependency array.", ["1624"], "'Modal' is defined but never used.", "'DeleteIcon' is defined but never used.", "'SendIcon' is defined but never used.", "'DeleteOutlineIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo.roleId'. Either include them or remove the dependency array.", ["1625"], "React Hook useEffect has a missing dependency: 'getRolesList'. Either include it or remove the dependency array.", ["1626"], "'SearchOffIcon' is defined but never used.", "'TableRowsRoundedIcon' is defined but never used.", "'IUsersListResponse' is defined but never used.", "'Grid' is defined but never used.", "'Stack' is defined but never used.", "'TablePagination' is defined but never used.", "'searchText' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsersPaginated'. Either include it or remove the dependency array.", ["1627"], "'rowsPerPage' is assigned a value but never used.", "'handleChangePage' is assigned a value but never used.", "'handleChangeRowsPerPage' is assigned a value but never used.", "'BlockOutlinedIcon' is defined but never used.", "'CheckCircleRoundedIcon' is defined but never used.", "'IBusinessListResponseModel' is defined but never used.", "'FormGroup' is defined but never used.", "'VerifiedIcon' is defined but never used.", "'PauseCircleFilledIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'setAlertConfig' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchBusinessPaginated', 'title', and 'userInfo'. Either include them or remove the dependency array.", ["1628"], "'List' is defined but never used.", "'ListItem' is defined but never used.", "'Switch' is defined but never used.", "'businessPreview' is defined but never used.", "'LockOpenIcon' is defined but never used.", "'useSelector' is defined but never used.", "'CancelOutlinedIcon' is defined but never used.", "'CampaignRoundedIcon' is defined but never used.", "'label' is assigned a value but never used.", "'StatusCardProps' is defined but never used.", "'progress' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId', 'businessId', 'getLocationSummary', and 'locationId'. Either include them or remove the dependency array.", ["1629"], "React Hook useEffect has a missing dependency: 'performMissingInformationOperation'. Either include it or remove the dependency array.", ["1630"], "'CardMedia' is defined but never used.", "'showConfirmPopup' is assigned a value but never used.", "'setShowConfirmPopup' is assigned a value but never used.", ["1631"], "React Hook useEffect has a missing dependency: 'fetchLocationsPaginated'. Either include it or remove the dependency array.", ["1632"], "React Hook useEffect has missing dependencies: 'getBusiness' and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1633"], "'FallingLines' is defined but never used.", "'RotatingLines' is defined but never used.", "'IDeleteRecord' is defined but never used.", "React Hook useEffect has a missing dependency: 'gmbCallBack'. Either include it or remove the dependency array.", ["1634"], "'StarBorderIcon' is defined but never used.", "'Avatar' is defined but never used.", "'StarRoundedIcon' is defined but never used.", "'ILocationListRequestModel' is defined but never used.", "'STARRATINGMAP' is defined but never used.", "'UserAvatar' is defined but never used.", "'Chip' is defined but never used.", "'SearchOutlinedIcon' is defined but never used.", "'InputAdornment' is defined but never used.", "'newestIcon' is assigned a value but never used.", "'oldestIcon' is assigned a value but never used.", "'highRatingIcon' is assigned a value but never used.", "'lowRatingIcon' is assigned a value but never used.", "'showScroll' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchLocationsPaginated', 'getAllTags', 'getBusiness', and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1635"], "'scrollToTop' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'AUTH_REQUESTED' is defined but never used.", "'AUTH_SUCCESS' is defined but never used.", "'AUTH_LOGOUT' is defined but never used.", "'AUTH_ERROR' is defined but never used.", "'AUTH_UNAUTHORIZED' is defined but never used.", "'IRoleBasedAccessResponseModel' is defined but never used.", "'ILoggedInUserResponseModel' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'Pagination' is defined but never used.", "'Paper' is defined but never used.", "'OutlinedInput' is defined but never used.", "'getIn' is defined but never used.", "React Hook useEffect has missing dependencies: 'getBusiness', 'getBusinessGroups', and 'getLocationsList'. Either include them or remove the dependency array.", ["1636"], "'MenuProps' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'logoutUser'. Either include it or remove the dependency array.", ["1637"], ["1638"], "'Component' is defined but never used.", "no-useless-escape", "Unnecessary escape character: \\..", "Literal", "unnecessaryEscape", ["1639", "1640"], ["1641", "1642"], "'Toolbar' is defined but never used.", "'Typography' is defined but never used.", "'MenuIcon' is defined but never used.", "'Menu' is defined but never used.", "'MenuItem' is defined but never used.", "'ManageAccountsIcon' is defined but never used.", "'Collapse' is defined but never used.", "'SettingsOutlinedIcon' is defined but never used.", "'ArrowForwardIosRoundedIcon' is defined but never used.", "'ListAltSharp' is defined but never used.", "'MapsUgcRoundedIcon' is defined but never used.", "'AppBar' is assigned a value but never used.", "'theme' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isMobile', 'menuOpened', 'openLeftMenu', 'rbAccess', and 'userInfo'. Either include them or remove the dependency array.", ["1643"], "'handleMenuItemClick' is assigned a value but never used.", "'AnalyticsRoutes' is assigned a value but never used.", "'GeoGridRoutes' is assigned a value but never used.", "'ManageAssetsRoutes' is assigned a value but never used.", ["1644", "1645"], ["1646", "1647"], "'useEffect' is defined but never used.", "'useMemo' is defined but never used.", "'MONTHS' is assigned a value but never used.", "'PolarArea' is defined but never used.", "'Bar' is defined but never used.", "'ReviewService' is defined but never used.", "'title' is defined but never used.", "React Hook useEffect has missing dependencies: 'onDateChange' and 'selectedDuration'. Either include them or remove the dependency array. If 'onDateChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1648"], "'ListItemText' is defined but never used.", "'AppBar' is defined but never used.", "'RoleType' is defined but never used.", "'CloseIcon' is defined but never used.", "'ArrowBackIos' is defined but never used.", "'ArrowForwardIos' is defined but never used.", "'LinearProgressWithLabel' is defined but never used.", "'open' is assigned a value but never used.", "'selectedValue' is assigned a value but never used.", "'selectedOptions' is assigned a value but never used.", "'handleOpen' is assigned a value but never used.", "'handleClose' is assigned a value but never used.", "'handleDropdownChange' is assigned a value but never used.", "'handleMultiSelectChange' is assigned a value but never used.", "'currentIndex' is assigned a value but never used.", "'handleNext' is assigned a value but never used.", "'handlePrev' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getBusiness' and 'getLocationsList'. Either include them or remove the dependency array.", ["1649"], "React Hook useEffect has a missing dependency: 'initialValues'. Either include it or remove the dependency array.", ["1650"], "'LoadingContext' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSearchKeywords'. Either include it or remove the dependency array.", ["1651"], "React Hook useEffect has a missing dependency: 'fetchMoreData'. Either include it or remove the dependency array.", ["1652"], "'SAVE_SCHEDULED' is defined but never used.", "'IGoogleCreatePost' is defined but never used.", "'DialogContent' is defined but never used.", "'Box' is defined but never used.", "'ListItemButton' is defined but never used.", "'ArrowBackIcon' is defined but never used.", "'ChevronRightIcon' is defined but never used.", "'MoreVertIcon' is defined but never used.", "'Formik' is defined but never used.", "'Form' is defined but never used.", "'Category' is defined but never used.", "'DialogTitle' is defined but never used.", "'AdapterDayjs' is defined but never used.", "'LocalizationProvider' is defined but never used.", "'DatePicker' is defined but never used.", "'ZoomInIcon' is defined but never used.", "'LIST_OF_LOCATIONS' is defined but never used.", "'LIST_OF_ROLE' is defined but never used.", "'useState' is defined but never used.", "'useNavigate' is defined but never used.", "'IUserResponseModel' is defined but never used.", "'IUser' is defined but never used.", "'IAlertDialogConfig' is defined but never used.", "'logOut' is defined but never used.", "'AlertDialog' is defined but never used.", "'isEdit' is assigned a value but never used.", "'setIsEdit' is assigned a value but never used.", "React Hook useEffect has missing dependencies: '_userService', 'getBusiness', 'getBusinessGroups', 'getLocationsList', and 'props.editData'. Either include them or remove the dependency array.", ["1653"], ["1654"], "'LIST_OF_BUSINESS' is defined but never used.", "'navigate' is assigned a value but never used.", "'Badge' is defined but never used.", "'Select' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'IRole' is defined but never used.", "'IBusinessGroup' is defined but never used.", "'ILocation' is defined but never used.", "'usersList' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["1655"], "array-callback-return", "Array.prototype.map() expects a value to be returned at the end of arrow function.", "ArrowFunctionExpression", "expectedAtEnd", "'CircularProgress' is defined but never used.", "'MediaGallery' is defined but never used.", "'MISSING_INFORMATION' is defined but never used.", "'IconOnAvailability' is defined but never used.", "'LocationOnRoundedIcon' is defined but never used.", "'MovieIcon' is defined but never used.", "'AccountCircleIcon' is defined but never used.", "'NearMeOutlinedIcon' is defined but never used.", "'handleOpenMap' is assigned a value but never used.", "'FunctionComponent' is defined but never used.", "'ThumbUpAltRoundedIcon' is defined but never used.", "'FeedbackTemplate' is defined but never used.", "'FeedbackCard' is defined but never used.", "'CssBaseline' is defined but never used.", "'ImageBackgroundCard' is defined but never used.", "React Hook useEffect has missing dependencies: 'props.review.review', 'props.review.reviewerName', 'props.review.reviewerProfilePic', and 'props.review.starRating'. Either include them or remove the dependency array. If 'setPostTemplateConfig' needs the current value of 'props.review.review', you can also switch to useReducer instead of useState and read 'props.review.review' in the reducer.", ["1656"], "React Hook useEffect has a missing dependency: 'getAllTags'. Either include it or remove the dependency array.", ["1657"], "'createTag' is assigned a value but never used.", "'updateTagsToReviewResponse' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'size'. Either include it or remove the dependency array.", ["1658"], "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "'navigationType' is assigned a value but never used.", "'userInfo' is assigned a value but never used.", "'isValidElement' is defined but never used.", "'activeMenuItem' is assigned a value but never used.", "'LogoutIcon' is defined but never used.", "'logoutUser' is assigned a value but never used.", "'openSubMenu' is assigned a value but never used.", "React Hook useEffect has missing dependencies: '_locationService', 'props.mediaItems', and 'setLoading'. Either include them or remove the dependency array.", ["1659"], "React Hook useEffect has a missing dependency: 'props.profileImage'. Either include it or remove the dependency array.", ["1660"], "'fontType' is assigned a value but never used.", "'setFontType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'props'. Either include it or remove the dependency array. However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the useEffect call and refer to those specific props inside useEffect.", ["1661"], ["1662"], "'ref' is defined but never used.", "'Rating' is defined but never used.", "'StarIcon' is defined but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'availableLocations' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", "'SettingsIcon' is defined but never used.", "'ICreateReplyTemplateRequest' is defined but never used.", "'openAutoReplyDrawer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadBusinesses'. Either include it or remove the dependency array.", ["1663"], "React Hook useEffect has missing dependencies: 'loadAutoReplySettings' and 'loadTemplates'. Either include them or remove the dependency array.", ["1664"], "'handleAutoReplyDrawerClose' is assigned a value but never used.", "'getStarRatingColor' is assigned a value but never used.", "'VisibilityIcon' is defined but never used.", "'ImageIcon' is defined but never used.", "'VideoLibraryIcon' is defined but never used.", ["1665"], "React Hook useEffect has a missing dependency: 'loadAssets'. Either include it or remove the dependency array.", ["1666"], "'isMobile' is assigned a value but never used.", {"desc": "1667", "fix": "1668"}, {"desc": "1669", "fix": "1670"}, {"desc": "1667", "fix": "1671"}, {"desc": "1672", "fix": "1673"}, {"desc": "1674", "fix": "1675"}, {"desc": "1667", "fix": "1676"}, {"desc": "1667", "fix": "1677"}, {"desc": "1667", "fix": "1678"}, {"desc": "1679", "fix": "1680"}, {"desc": "1681", "fix": "1682"}, {"desc": "1683", "fix": "1684"}, {"desc": "1685", "fix": "1686"}, {"kind": "1687", "justification": "1688"}, {"desc": "1689", "fix": "1690"}, {"desc": "1691", "fix": "1692"}, {"desc": "1693", "fix": "1694"}, {"desc": "1695", "fix": "1696"}, {"desc": "1697", "fix": "1698"}, {"desc": "1699", "fix": "1700"}, {"desc": "1701", "fix": "1702"}, {"desc": "1703", "fix": "1704"}, {"desc": "1705", "fix": "1706"}, {"desc": "1707", "fix": "1708"}, {"desc": "1709", "fix": "1710"}, {"desc": "1681", "fix": "1711"}, {"desc": "1712", "fix": "1713"}, {"desc": "1714", "fix": "1715"}, {"desc": "1716", "fix": "1717"}, {"desc": "1718", "fix": "1719"}, {"desc": "1720", "fix": "1721"}, {"desc": "1722", "fix": "1723"}, {"desc": "1722", "fix": "1724"}, {"messageId": "1725", "fix": "1726", "desc": "1727"}, {"messageId": "1728", "fix": "1729", "desc": "1730"}, {"messageId": "1725", "fix": "1731", "desc": "1727"}, {"messageId": "1728", "fix": "1732", "desc": "1730"}, {"desc": "1733", "fix": "1734"}, {"messageId": "1725", "fix": "1735", "desc": "1727"}, {"messageId": "1728", "fix": "1736", "desc": "1730"}, {"messageId": "1725", "fix": "1737", "desc": "1727"}, {"messageId": "1728", "fix": "1738", "desc": "1730"}, {"desc": "1739", "fix": "1740"}, {"desc": "1741", "fix": "1742"}, {"desc": "1743", "fix": "1744"}, {"desc": "1745", "fix": "1746"}, {"desc": "1747", "fix": "1748"}, {"desc": "1749", "fix": "1750"}, {"desc": "1701", "fix": "1751"}, {"desc": "1752", "fix": "1753"}, {"desc": "1754", "fix": "1755"}, {"desc": "1756", "fix": "1757"}, {"desc": "1758", "fix": "1759"}, {"desc": "1760", "fix": "1761"}, {"desc": "1762", "fix": "1763"}, {"desc": "1764", "fix": "1765"}, {"desc": "1764", "fix": "1766"}, {"desc": "1767", "fix": "1768"}, {"desc": "1769", "fix": "1770"}, {"desc": "1771", "fix": "1772"}, {"desc": "1773", "fix": "1774"}, "Update the dependencies array to be: []", {"range": "1775", "text": "1776"}, "Update the dependencies array to be: [loading, setLoading]", {"range": "1777", "text": "1778"}, {"range": "1779", "text": "1776"}, "Update the dependencies array to be: [userInfo, success, location.pathname, navigate, loginMessage]", {"range": "1780", "text": "1781"}, "Update the dependencies array to be: [dispatchSessionExpired, isUnAuthorised]", {"range": "1782", "text": "1783"}, {"range": "1784", "text": "1776"}, {"range": "1785", "text": "1776"}, {"range": "1786", "text": "1776"}, "Update the dependencies array to be: [open, setOpen, message, setToastMessage, toastConfig, setToastConfig]", {"range": "1787", "text": "1788"}, "Update the dependencies array to be: [title]", {"range": "1789", "text": "1790"}, "Update the dependencies array to be: [fetchLocationsPaginated, getBusiness, getBusinessGroups]", {"range": "1791", "text": "1792"}, "Update the dependencies array to be: [fetchLocations, getBusiness, getBusinessGroups]", {"range": "1793", "text": "1794"}, "directive", "", "Update the dependencies array to be: [location.state, title]", {"range": "1795", "text": "1796"}, "Update the dependencies array to be: [fetchLocations]", {"range": "1797", "text": "1798"}, "Update the dependencies array to be: [locationList, selectedLocationId]", {"range": "1799", "text": "1800"}, "Update the dependencies array to be: [selectedLocationId, selectedDateRange, selectedAccountId, fetchAnalyticsData, locationList]", {"range": "1801", "text": "1802"}, "Update the dependencies array to be: [loadSavedConfigurations, title, user]", {"range": "1803", "text": "1804"}, "Update the dependencies array to be: [navigate, userInfo.roleId]", {"range": "1805", "text": "1806"}, "Update the dependencies array to be: [getRolesList]", {"range": "1807", "text": "1808"}, "Update the dependencies array to be: [fetchUsersPaginated, paginationModel]", {"range": "1809", "text": "1810"}, "Update the dependencies array to be: [fetchBusinessPaginated, title, userInfo]", {"range": "1811", "text": "1812"}, "Update the dependencies array to be: [accountId, businessId, getLocationSummary, locationId]", {"range": "1813", "text": "1814"}, "Update the dependencies array to be: [locationSummary, performMissingInformationOperation]", {"range": "1815", "text": "1816"}, {"range": "1817", "text": "1790"}, "Update the dependencies array to be: [fetchLocationsPaginated, paginationModel]", {"range": "1818", "text": "1819"}, "Update the dependencies array to be: [getBusiness, getBusinessGroups]", {"range": "1820", "text": "1821"}, "Update the dependencies array to be: [gmbCallBack, searchParams]", {"range": "1822", "text": "1823"}, "Update the dependencies array to be: [fetchLocationsPaginated, getAllTags, getBusiness, getBusinessGroups]", {"range": "1824", "text": "1825"}, "Update the dependencies array to be: [getBusiness, getBusinessGroups, getLocationsList]", {"range": "1826", "text": "1827"}, "Update the dependencies array to be: [logoutUser, navigate]", {"range": "1828", "text": "1829"}, {"range": "1830", "text": "1829"}, "removeEscape", {"range": "1831", "text": "1688"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1832", "text": "1833"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1834", "text": "1688"}, {"range": "1835", "text": "1833"}, "Update the dependencies array to be: [isMobile, menuOpened, openLeftMenu, rbAccess, userInfo]", {"range": "1836", "text": "1837"}, {"range": "1838", "text": "1688"}, {"range": "1839", "text": "1833"}, {"range": "1840", "text": "1688"}, {"range": "1841", "text": "1833"}, "Update the dependencies array to be: [onDateChange, selectedDuration]", {"range": "1842", "text": "1843"}, "Update the dependencies array to be: [getBusiness, getLocationsList]", {"range": "1844", "text": "1845"}, "Update the dependencies array to be: [initialValues.locationId, initialValues.accountId, locations, initialValues]", {"range": "1846", "text": "1847"}, "Update the dependencies array to be: [accountId, locationId, from, to, fetchSearchKeywords]", {"range": "1848", "text": "1849"}, "Update the dependencies array to be: [nextPageToken, loading, open, fetchMoreData]", {"range": "1850", "text": "1851"}, "Update the dependencies array to be: [_userService, getBusiness, getBusinessGroups, getLocationsList, props.editData]", {"range": "1852", "text": "1853"}, {"range": "1854", "text": "1808"}, "Update the dependencies array to be: [fetchUsers]", {"range": "1855", "text": "1856"}, "Update the dependencies array to be: [props.review.review, props.review.reviewerName, props.review.reviewerProfilePic, props.review.starRating]", {"range": "1857", "text": "1858"}, "Update the dependencies array to be: [getAllTags]", {"range": "1859", "text": "1860"}, "Update the dependencies array to be: [size]", {"range": "1861", "text": "1862"}, "Update the dependencies array to be: [_locationService, props.mediaItems, setLoading]", {"range": "1863", "text": "1864"}, "Update the dependencies array to be: [props.profileImage]", {"range": "1865", "text": "1866"}, "Update the dependencies array to be: [postTemplateConfig, props]", {"range": "1867", "text": "1868"}, {"range": "1869", "text": "1868"}, "Update the dependencies array to be: [loadBusinesses, userInfo]", {"range": "1870", "text": "1871"}, "Update the dependencies array to be: [loadAutoReplySettings, loadTemplates, selectedBusiness, userInfo]", {"range": "1872", "text": "1873"}, "Update the dependencies array to be: [loadBusinesses]", {"range": "1874", "text": "1875"}, "Update the dependencies array to be: [loadAssets, selectedBusinessId]", {"range": "1876", "text": "1877"}, [3723, 3732], "[]", [3836, 3845], "[loading, setLoading]", [4126, 4142], [4801, 4820], "[userInfo, success, location.pathname, navigate, loginMessage]", [5072, 5088], "[dispatchSessionExpired, isUnAuthorised]", [5603, 5609], [5734, 5740], [5923, 5936], [6115, 6143], "[open, setOpen, message, setToastMessage, toastConfig, setToastConfig]", [2979, 2981], "[title]", [5326, 5328], "[fetchLocationsPaginated, getBusiness, getBusinessGroups]", [5424, 5426], "[fetchLocations, getBusiness, getBusinessGroups]", [9381, 9383], "[location.state, title]", [4843, 4845], "[fetchLocations]", [5049, 5069], "[locationList, selectedLocationId]", [5446, 5485], "[selectedLocationId, selectedDateRange, selectedAccountId, fetchAnalyticsData, locationList]", [2227, 2240], "[loadSavedConfigurations, title, user]", [3559, 3561], "[navigate, userInfo.roleId]", [3614, 3616], "[getRolesList]", [5200, 5217], "[fetchUsersPaginated, paginationModel]", [5187, 5189], "[fetchBusinessPaginated, title, userInfo]", [5063, 5065], "[accountId, businessId, getLocationSummary, locationId]", [8294, 8311], "[locationSummary, performMissingInformationOperation]", [5560, 5562], [5626, 5643], "[fetchLocationsPaginated, paginationModel]", [5820, 5822], "[getBusiness, getBusinessGroups]", [1934, 1948], "[gmbCallBack, searchParams]", [7854, 7856], "[fetchLocationsPaginated, getAllTags, getBusiness, getBusinessGroups]", [4079, 4081], "[getBusiness, getBusinessGroups, getLocationsList]", [904, 914], "[logo<PERSON><PERSON><PERSON>, navigate]", [905, 915], [184, 185], [184, 184], "\\", [200, 201], [200, 200], [6518, 6520], "[isMobile, menuOpened, openLeftMenu, rbAccess, userInfo]", [288, 289], [288, 288], [304, 305], [304, 304], [1857, 1859], "[onDate<PERSON><PERSON>e, selectedDuration]", [6903, 6905], "[getBusiness, getLocationsList]", [7653, 7715], "[initialValues.locationId, initialValues.accountId, locations, initialValues]", [1519, 1552], "[accountId, locationId, from, to, fetchSearchKeywords]", [3743, 3773], "[nextPageToken, loading, open, fetchMoreData]", [5857, 5859], "[_userService, getBusiness, getBusinessGroups, getLocationsList, props.editData]", [12623, 12625], [3571, 3573], "[fetchUsers]", [2956, 2958], "[props.review.review, props.review.reviewerName, props.review.reviewerProfilePic, props.review.starRating]", [2469, 2471], "[getAllTags]", [497, 499], "[size]", [1388, 1390], "[_locationService, props.mediaItems, setLoading]", [1123, 1125], "[props.profileImage]", [1210, 1230], "[postTemplateConfig, props]", [6340, 6360], [3585, 3595], "[loadBusinesses, userInfo]", [3728, 3756], "[loadAutoReplySettings, loadTemplates, selectedBusiness, userInfo]", [3619, 3621], "[loadBusinesses]", [3750, 3770], "[loadAssets, selectedBusinessId]"]