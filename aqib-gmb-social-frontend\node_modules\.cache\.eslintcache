[{"C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\theme.tsx": "4", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\store\\index.tsx": "5", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\toast.context.tsx": "6", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\loading.context.tsx": "7", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\auth.actions.tsx": "8", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\toastSeverity.constant.tsx": "9", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\forgotPassword\\forgotPassword.screen.tsx": "10", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\signIn\\signIn.screen.tsx": "11", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboard\\dashboard.screen.tsx": "12", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\qanda\\qanda.screen.tsx": "13", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\help\\help.screen.tsx": "14", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\analytics\\analytics.screen.tsx": "15", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\createSocialPost.screen.tsx": "16", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\dashboardV2.screen.tsx": "17", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\businessCategory.screen.tsx": "18", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\servicesDemo.screen.tsx": "19", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\demo.screen.tsx": "20", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\geoGrid\\geoGrid.screen.tsx": "21", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\roles\\roles.screen.tsx": "22", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\users\\users.screen.tsx": "23", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\manageBusiness\\manageBusiness.screen.tsx": "24", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\businessSummary\\businessSummary.screen.tsx": "25", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\localBusiness\\localBusiness.screen.tsx": "26", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\callback\\callback.screen.tsx": "27", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\manageReviews\\manageReviews.screen.tsx": "28", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\userPreferences.reducer.tsx": "29", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\auth.reducer.tsx": "30", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\PostListing.screen.tsx": "31", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\loader\\loader.component.tsx": "32", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\unAuthorized\\notFoundPage.component.tsx": "33", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\notFoundPage\\notFoundPage.component.tsx": "34", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\endPoints.constant.tsx": "35", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\reducer.constant.tsx": "36", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\httpHelper.service.tsx": "37", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\helperService.tsx": "38", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\dbConstant.constant.tsx": "39", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\message.constant.tsx": "40", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\leftMenu\\leftMenu.component.tsx": "41", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\ApplicationHelperService.tsx": "42", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\websiteClicksChart.tsx": "43", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\platformBreakdownChart.tsx": "44", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\registeredEmployees.charts.tsx": "45", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\activeJobs.charts.tsx": "46", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\pie.charts.tsx": "47", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\homeChartCard\\homeChartCard.component.tsx": "48", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\qanda\\qanda.service.tsx": "49", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\business\\business.service.tsx": "50", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\location\\location.service.tsx": "51", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyQuestionAnswer\\replyQuestionAnswer.component.tsx": "52", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\genericDrawer\\genericDrawer.component.tsx": "53", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\revenueChartDashboard\\revenueChartDashboard.component.tsx": "54", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\application.constant.tsx": "55", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\locationChips\\locationChips.component.tsx": "56", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\dateFilter\\dateFilter.component.tsx": "57", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\locationMetrics\\locationMetrics.service.tsx": "58", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\InfoCard.screen.tsx": "59", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\submitPost.component.tsx": "60", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\searchBreakdown.tsx": "61", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\businessProfileInteractionsChart.tsx": "62", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\posts\\posts.service.tsx": "63", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addBusinessCategory.component.tsx": "64", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\servicesDisplay.component.tsx": "65", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addServices.component.tsx": "66", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\BusinessHours.tsx": "67", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\AddSpecialHours.tsx": "68", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editBusinessName\\editBusinessName.component.tsx": "69", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\FromTheBusiness.tsx": "70", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\AddChildren.tsx": "71", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\MoreAccessibility.tsx": "72", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\Amenities.tsx": "73", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ServiceOptions.tsx": "74", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\CrowdComponent.tsx": "75", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ParkingComponent.tsx": "76", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PaymentsComponent.tsx": "77", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PlanningComponent.tsx": "78", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridControls.component.tsx": "79", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridMap.component.tsx": "80", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\geoGrid\\geoGrid.service.tsx": "81", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridSettings.component.tsx": "82", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\roles\\roles.service.tsx": "83", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\user\\user.service.tsx": "84", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmModel\\confirmModel.component.tsx": "85", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditUser\\addEditUser.component.tsx": "86", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\auth\\auth.service.tsx": "87", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\alertDialog\\alertDialog.component.tsx": "88", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\noRowsFound\\noRowsFound.component.tsx": "89", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\logosPhotosDisplay\\logsPhotosDisplay.component.tsx": "90", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\categoryDisplay\\categoryDisplay.component.tsx": "91", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\LinearProgressWithLabel\\LinearProgressWithLabel.component.tsx": "92", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditBusiness\\addEditBusiness.component.tsx": "93", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceItemsDisplay\\serviceItemsDisplay.component.tsx": "94", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\regularHoursTable\\regularHoursTable.component.tsx": "95", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceAreaList\\serviceAreaList.component.tsx": "96", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\iconOnAvailability\\iconOnAvailability.component.tsx": "97", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatarWIthName\\userAvatarWIthName.component.tsx": "98", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyReviews\\replyReviews.component.tsx": "99", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPost.component.tsx": "100", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createTags\\createTags.component.tsx": "101", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\ratingsStar\\ratingsStar.component.tsx": "102", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\review\\review.service.tsx": "103", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\postCard\\postCard.screen.tsx": "104", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\updatesSection\\updatesSection.screen.tsx": "105", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\userPreferences.actions.tsx": "106", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\menuListItemNested\\menuListItemNested.component.tsx": "107", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\exportButton\\exportButton.component.tsx": "108", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\header\\header.component.tsx": "109", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\mediaGallery\\mediaGallery.component.tsx": "110", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatar\\userAvatar.component.tsx": "111", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editElements\\editElements.component.tsx": "112", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\colorPalette\\colorPalette.component.tsx": "113", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard1\\testimonialCard1.component.tsx": "114", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard3\\testimonialCard3.component.tsx": "115", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard2\\testimonialCard2.component.tsx": "116", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard4\\testimonialCard4.component.tsx": "117", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard5\\testimonialCard5.component.tsx": "118", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard6\\testimonialCard6.component.tsx": "119", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\excelExport.service.ts": "120", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\preferences.context.tsx": "121", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\googleMaps.utils.ts": "122", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\reviewSettings.screen.tsx": "123", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\reviewSettings\\reviewSettings.service.tsx": "124", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\autoReplySettings.component.tsx": "125", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\createEditTemplate.component.tsx": "126"}, {"size": 1322, "mtime": 1735557527698, "results": "127", "hashOfConfig": "128"}, {"size": 425, "mtime": 1732825514676, "results": "129", "hashOfConfig": "128"}, {"size": 12171, "mtime": 1748802958619, "results": "130", "hashOfConfig": "128"}, {"size": 1924, "mtime": 1748447423371, "results": "131", "hashOfConfig": "128"}, {"size": 1261, "mtime": 1745417383983, "results": "132", "hashOfConfig": "128"}, {"size": 457, "mtime": 1734026524277, "results": "133", "hashOfConfig": "128"}, {"size": 157, "mtime": 1701682230464, "results": "134", "hashOfConfig": "128"}, {"size": 3060, "mtime": 1745228037979, "results": "135", "hashOfConfig": "128"}, {"size": 118, "mtime": 1734026414980, "results": "136", "hashOfConfig": "128"}, {"size": 2661, "mtime": 1743422802376, "results": "137", "hashOfConfig": "128"}, {"size": 11012, "mtime": 1745224565621, "results": "138", "hashOfConfig": "128"}, {"size": 14227, "mtime": 1745153279478, "results": "139", "hashOfConfig": "128"}, {"size": 22162, "mtime": 1746354630774, "results": "140", "hashOfConfig": "128"}, {"size": 228, "mtime": 1732915981749, "results": "141", "hashOfConfig": "128"}, {"size": 72602, "mtime": 1748504384752, "results": "142", "hashOfConfig": "128"}, {"size": 71530, "mtime": 1748273135277, "results": "143", "hashOfConfig": "128"}, {"size": 23584, "mtime": 1748081008306, "results": "144", "hashOfConfig": "128"}, {"size": 4224, "mtime": 1747927064178, "results": "145", "hashOfConfig": "128"}, {"size": 2557, "mtime": 1747926509581, "results": "146", "hashOfConfig": "128"}, {"size": 92285, "mtime": 1748096498620, "results": "147", "hashOfConfig": "128"}, {"size": 12587, "mtime": 1748631248512, "results": "148", "hashOfConfig": "128"}, {"size": 37116, "mtime": 1748283097919, "results": "149", "hashOfConfig": "128"}, {"size": 22457, "mtime": 1748268107744, "results": "150", "hashOfConfig": "128"}, {"size": 25334, "mtime": 1748622303774, "results": "151", "hashOfConfig": "128"}, {"size": 56467, "mtime": 1748103534373, "results": "152", "hashOfConfig": "128"}, {"size": 30332, "mtime": 1748091334232, "results": "153", "hashOfConfig": "128"}, {"size": 4594, "mtime": 1739004178483, "results": "154", "hashOfConfig": "128"}, {"size": 43075, "mtime": 1748262014290, "results": "155", "hashOfConfig": "128"}, {"size": 991, "mtime": 1745432980413, "results": "156", "hashOfConfig": "128"}, {"size": 1802, "mtime": 1745154549344, "results": "157", "hashOfConfig": "128"}, {"size": 18225, "mtime": 1748096373981, "results": "158", "hashOfConfig": "128"}, {"size": 347, "mtime": 1748514901624, "results": "159", "hashOfConfig": "128"}, {"size": 1880, "mtime": 1746361169966, "results": "160", "hashOfConfig": "128"}, {"size": 1876, "mtime": 1746360898380, "results": "161", "hashOfConfig": "128"}, {"size": 4903, "mtime": 1748804295440, "results": "162", "hashOfConfig": "128"}, {"size": 384, "mtime": 1745418826460, "results": "163", "hashOfConfig": "128"}, {"size": 9311, "mtime": 1743515029706, "results": "164", "hashOfConfig": "128"}, {"size": 1088, "mtime": 1732882009247, "results": "165", "hashOfConfig": "128"}, {"size": 327, "mtime": 1737044880500, "results": "166", "hashOfConfig": "128"}, {"size": 1953, "mtime": 1747674177440, "results": "167", "hashOfConfig": "128"}, {"size": 20503, "mtime": 1748802930589, "results": "168", "hashOfConfig": "128"}, {"size": 3187, "mtime": 1745654081462, "results": "169", "hashOfConfig": "128"}, {"size": 3899, "mtime": 1748504225839, "results": "170", "hashOfConfig": "128"}, {"size": 4028, "mtime": 1748447576248, "results": "171", "hashOfConfig": "128"}, {"size": 1997, "mtime": 1741184787031, "results": "172", "hashOfConfig": "128"}, {"size": 1170, "mtime": 1745152642022, "results": "173", "hashOfConfig": "128"}, {"size": 1016, "mtime": 1741184738204, "results": "174", "hashOfConfig": "128"}, {"size": 2779, "mtime": 1748275515999, "results": "175", "hashOfConfig": "128"}, {"size": 1440, "mtime": 1741157415799, "results": "176", "hashOfConfig": "128"}, {"size": 2104, "mtime": 1741175030313, "results": "177", "hashOfConfig": "128"}, {"size": 1687, "mtime": 1745173540907, "results": "178", "hashOfConfig": "128"}, {"size": 4969, "mtime": 1741157515080, "results": "179", "hashOfConfig": "128"}, {"size": 1007, "mtime": 1743912188289, "results": "180", "hashOfConfig": "128"}, {"size": 4015, "mtime": 1748504257458, "results": "181", "hashOfConfig": "128"}, {"size": 2108, "mtime": 1743964958327, "results": "182", "hashOfConfig": "128"}, {"size": 3524, "mtime": 1748452767055, "results": "183", "hashOfConfig": "128"}, {"size": 6531, "mtime": 1747674884828, "results": "184", "hashOfConfig": "128"}, {"size": 1066, "mtime": 1745130929150, "results": "185", "hashOfConfig": "128"}, {"size": 3176, "mtime": 1748356415373, "results": "186", "hashOfConfig": "128"}, {"size": 42711, "mtime": 1748268104980, "results": "187", "hashOfConfig": "128"}, {"size": 8043, "mtime": 1745778157886, "results": "188", "hashOfConfig": "128"}, {"size": 4054, "mtime": 1748275903493, "results": "189", "hashOfConfig": "128"}, {"size": 1840, "mtime": 1743959659084, "results": "190", "hashOfConfig": "128"}, {"size": 1521, "mtime": 1748447423366, "results": "191", "hashOfConfig": "128"}, {"size": 7403, "mtime": 1747927040597, "results": "192", "hashOfConfig": "128"}, {"size": 8129, "mtime": 1747926970546, "results": "193", "hashOfConfig": "128"}, {"size": 12230, "mtime": 1748096498612, "results": "194", "hashOfConfig": "128"}, {"size": 9701, "mtime": 1748096498609, "results": "195", "hashOfConfig": "128"}, {"size": 7411, "mtime": 1747983900839, "results": "196", "hashOfConfig": "128"}, {"size": 7645, "mtime": 1748096498642, "results": "197", "hashOfConfig": "128"}, {"size": 7764, "mtime": 1748096498625, "results": "198", "hashOfConfig": "128"}, {"size": 8438, "mtime": 1748096498645, "results": "199", "hashOfConfig": "128"}, {"size": 9418, "mtime": 1748096498628, "results": "200", "hashOfConfig": "128"}, {"size": 8390, "mtime": 1748096498661, "results": "201", "hashOfConfig": "128"}, {"size": 7694, "mtime": 1748096498633, "results": "202", "hashOfConfig": "128"}, {"size": 8842, "mtime": 1748096498648, "results": "203", "hashOfConfig": "128"}, {"size": 8910, "mtime": 1748096498650, "results": "204", "hashOfConfig": "128"}, {"size": 9753, "mtime": 1748096498658, "results": "205", "hashOfConfig": "128"}, {"size": 16883, "mtime": 1748629558056, "results": "206", "hashOfConfig": "128"}, {"size": 8642, "mtime": 1748631223321, "results": "207", "hashOfConfig": "128"}, {"size": 3621, "mtime": 1748627420404, "results": "208", "hashOfConfig": "128"}, {"size": 6848, "mtime": 1748626503234, "results": "209", "hashOfConfig": "128"}, {"size": 751, "mtime": 1739004316101, "results": "210", "hashOfConfig": "128"}, {"size": 2655, "mtime": 1747673914361, "results": "211", "hashOfConfig": "128"}, {"size": 2179, "mtime": 1748282005004, "results": "212", "hashOfConfig": "128"}, {"size": 62073, "mtime": 1748268099200, "results": "213", "hashOfConfig": "128"}, {"size": 881, "mtime": 1739736032570, "results": "214", "hashOfConfig": "128"}, {"size": 3505, "mtime": 1747674887530, "results": "215", "hashOfConfig": "128"}, {"size": 825, "mtime": 1748091399276, "results": "216", "hashOfConfig": "128"}, {"size": 4855, "mtime": 1745166216216, "results": "217", "hashOfConfig": "128"}, {"size": 3676, "mtime": 1745152642016, "results": "218", "hashOfConfig": "128"}, {"size": 1151, "mtime": 1745646436581, "results": "219", "hashOfConfig": "128"}, {"size": 9176, "mtime": 1747683781666, "results": "220", "hashOfConfig": "128"}, {"size": 2338, "mtime": 1745655282096, "results": "221", "hashOfConfig": "128"}, {"size": 3305, "mtime": 1748103773906, "results": "222", "hashOfConfig": "128"}, {"size": 1668, "mtime": 1745167749805, "results": "223", "hashOfConfig": "128"}, {"size": 607, "mtime": 1743920400746, "results": "224", "hashOfConfig": "128"}, {"size": 481, "mtime": 1737047216070, "results": "225", "hashOfConfig": "128"}, {"size": 6596, "mtime": 1748262291113, "results": "226", "hashOfConfig": "128"}, {"size": 15273, "mtime": 1745778157866, "results": "227", "hashOfConfig": "128"}, {"size": 8516, "mtime": 1748275747346, "results": "228", "hashOfConfig": "128"}, {"size": 1191, "mtime": 1746359987224, "results": "229", "hashOfConfig": "128"}, {"size": 2245, "mtime": 1746356711188, "results": "230", "hashOfConfig": "128"}, {"size": 12541, "mtime": 1744000300023, "results": "231", "hashOfConfig": "128"}, {"size": 3452, "mtime": 1743915304875, "results": "232", "hashOfConfig": "128"}, {"size": 506, "mtime": 1745432977071, "results": "233", "hashOfConfig": "128"}, {"size": 6166, "mtime": 1745223603623, "results": "234", "hashOfConfig": "128"}, {"size": 2617, "mtime": 1748496622200, "results": "235", "hashOfConfig": "128"}, {"size": 3400, "mtime": 1743780827843, "results": "236", "hashOfConfig": "128"}, {"size": 2900, "mtime": 1743926009788, "results": "237", "hashOfConfig": "128"}, {"size": 1394, "mtime": 1743924141624, "results": "238", "hashOfConfig": "128"}, {"size": 5825, "mtime": 1743778841267, "results": "239", "hashOfConfig": "128"}, {"size": 9763, "mtime": 1739741482429, "results": "240", "hashOfConfig": "128"}, {"size": 2929, "mtime": 1741701510631, "results": "241", "hashOfConfig": "128"}, {"size": 4699, "mtime": 1741701603500, "results": "242", "hashOfConfig": "128"}, {"size": 2178, "mtime": 1741701600352, "results": "243", "hashOfConfig": "128"}, {"size": 3248, "mtime": 1741701606568, "results": "244", "hashOfConfig": "128"}, {"size": 3430, "mtime": 1741701609669, "results": "245", "hashOfConfig": "128"}, {"size": 3100, "mtime": 1743924130802, "results": "246", "hashOfConfig": "128"}, {"size": 13705, "mtime": 1748504200123, "results": "247", "hashOfConfig": "128"}, {"size": 168, "mtime": 1701682230489, "results": "248", "hashOfConfig": "128"}, {"size": 4815, "mtime": 1748628188151, "results": "249", "hashOfConfig": "128"}, {"size": 16593, "mtime": 1748842579937, "results": "250", "hashOfConfig": "128"}, {"size": 4220, "mtime": 1748804384121, "results": "251", "hashOfConfig": "128"}, {"size": 15590, "mtime": 1748806340914, "results": "252", "hashOfConfig": "128"}, {"size": 11543, "mtime": 1748843169061, "results": "253", "hashOfConfig": "128"}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qj6s6v", {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\App.tsx", ["632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\theme.tsx", ["651"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\store\\index.tsx", ["652"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\toast.context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\loading.context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\auth.actions.tsx", ["653", "654", "655", "656", "657", "658"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\toastSeverity.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\forgotPassword\\forgotPassword.screen.tsx", ["659"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\signIn\\signIn.screen.tsx", ["660", "661", "662", "663", "664", "665", "666", "667", "668"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboard\\dashboard.screen.tsx", ["669"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\qanda\\qanda.screen.tsx", ["670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\help\\help.screen.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\analytics\\analytics.screen.tsx", ["683", "684", "685", "686"], ["687"], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\createSocialPost.screen.tsx", ["688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\dashboardV2.screen.tsx", ["708", "709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\businessCategory.screen.tsx", ["730"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\servicesDemo.screen.tsx", ["731", "732", "733"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\demo.screen.tsx", ["734", "735", "736", "737", "738", "739"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\geoGrid\\geoGrid.screen.tsx", ["740", "741", "742", "743", "744"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\roles\\roles.screen.tsx", ["745", "746", "747", "748", "749", "750", "751", "752", "753", "754"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\users\\users.screen.tsx", ["755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\manageBusiness\\manageBusiness.screen.tsx", ["768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\businessSummary\\businessSummary.screen.tsx", ["781", "782", "783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\localBusiness\\localBusiness.screen.tsx", ["798", "799", "800", "801", "802", "803", "804", "805", "806", "807"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\callback\\callback.screen.tsx", ["808", "809", "810", "811"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\manageReviews\\manageReviews.screen.tsx", ["812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839", "840"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\userPreferences.reducer.tsx", ["841", "842", "843", "844", "845", "846", "847"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\auth.reducer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\PostListing.screen.tsx", ["848", "849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\loader\\loader.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\unAuthorized\\notFoundPage.component.tsx", ["863"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\notFoundPage\\notFoundPage.component.tsx", ["864"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\endPoints.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\reducer.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\httpHelper.service.tsx", ["865", "866"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\helperService.tsx", ["867", "868"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\dbConstant.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\message.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\leftMenu\\leftMenu.component.tsx", ["869", "870", "871", "872", "873", "874", "875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\ApplicationHelperService.tsx", ["888", "889"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\websiteClicksChart.tsx", ["890", "891"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\platformBreakdownChart.tsx", ["892"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\registeredEmployees.charts.tsx", ["893", "894", "895"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\activeJobs.charts.tsx", ["896"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\pie.charts.tsx", ["897", "898"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\homeChartCard\\homeChartCard.component.tsx", ["899", "900"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\qanda\\qanda.service.tsx", ["901"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\business\\business.service.tsx", ["902"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\location\\location.service.tsx", ["903"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyQuestionAnswer\\replyQuestionAnswer.component.tsx", ["904", "905", "906"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\genericDrawer\\genericDrawer.component.tsx", ["907", "908", "909", "910"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\revenueChartDashboard\\revenueChartDashboard.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\application.constant.tsx", ["911"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\locationChips\\locationChips.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\dateFilter\\dateFilter.component.tsx", ["912"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\locationMetrics\\locationMetrics.service.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\InfoCard.screen.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\submitPost.component.tsx", ["913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\searchBreakdown.tsx", ["946", "947", "948", "949", "950"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\businessProfileInteractionsChart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\posts\\posts.service.tsx", ["951", "952", "953"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addBusinessCategory.component.tsx", ["954", "955", "956", "957", "958", "959", "960", "961", "962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\servicesDisplay.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addServices.component.tsx", ["974"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\BusinessHours.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\AddSpecialHours.tsx", ["975", "976", "977", "978", "979"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editBusinessName\\editBusinessName.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\FromTheBusiness.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\AddChildren.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\MoreAccessibility.tsx", ["980"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\Amenities.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ServiceOptions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\CrowdComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ParkingComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PaymentsComponent.tsx", ["981"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PlanningComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridControls.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridMap.component.tsx", ["982", "983"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\geoGrid\\geoGrid.service.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridSettings.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\roles\\roles.service.tsx", ["984", "985"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\user\\user.service.tsx", ["986", "987", "988"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmModel\\confirmModel.component.tsx", ["989", "990"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditUser\\addEditUser.component.tsx", ["991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\auth\\auth.service.tsx", ["1003", "1004"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\alertDialog\\alertDialog.component.tsx", ["1005"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\noRowsFound\\noRowsFound.component.tsx", ["1006", "1007", "1008"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\logosPhotosDisplay\\logsPhotosDisplay.component.tsx", ["1009", "1010", "1011"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\categoryDisplay\\categoryDisplay.component.tsx", ["1012", "1013", "1014", "1015"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\LinearProgressWithLabel\\LinearProgressWithLabel.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditBusiness\\addEditBusiness.component.tsx", ["1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceItemsDisplay\\serviceItemsDisplay.component.tsx", ["1027", "1028", "1029", "1030", "1031"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\regularHoursTable\\regularHoursTable.component.tsx", ["1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceAreaList\\serviceAreaList.component.tsx", ["1043", "1044", "1045"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\iconOnAvailability\\iconOnAvailability.component.tsx", ["1046", "1047", "1048"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatarWIthName\\userAvatarWIthName.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyReviews\\replyReviews.component.tsx", ["1049", "1050", "1051", "1052"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPost.component.tsx", ["1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createTags\\createTags.component.tsx", ["1066", "1067", "1068", "1069", "1070", "1071"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\ratingsStar\\ratingsStar.component.tsx", ["1072", "1073", "1074"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\review\\review.service.tsx", ["1075"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\postCard\\postCard.screen.tsx", ["1076", "1077", "1078", "1079", "1080", "1081"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\updatesSection\\updatesSection.screen.tsx", ["1082", "1083"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\userPreferences.actions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\menuListItemNested\\menuListItemNested.component.tsx", ["1084", "1085", "1086"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\exportButton\\exportButton.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\header\\header.component.tsx", ["1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\mediaGallery\\mediaGallery.component.tsx", ["1097", "1098"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatar\\userAvatar.component.tsx", ["1099"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editElements\\editElements.component.tsx", ["1100", "1101", "1102"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\colorPalette\\colorPalette.component.tsx", ["1103", "1104", "1105"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard1\\testimonialCard1.component.tsx", ["1106"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard3\\testimonialCard3.component.tsx", ["1107", "1108", "1109", "1110"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard2\\testimonialCard2.component.tsx", ["1111", "1112", "1113"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard4\\testimonialCard4.component.tsx", ["1114", "1115", "1116", "1117"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard5\\testimonialCard5.component.tsx", ["1118", "1119", "1120", "1121", "1122"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard6\\testimonialCard6.component.tsx", ["1123", "1124", "1125", "1126"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\excelExport.service.ts", ["1127"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\preferences.context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\googleMaps.utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\reviewSettings.screen.tsx", ["1128", "1129", "1130", "1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\reviewSettings\\reviewSettings.service.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\autoReplySettings.component.tsx", ["1141", "1142"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\reviewSettings\\components\\createEditTemplate.component.tsx", ["1143", "1144"], [], {"ruleId": "1145", "severity": 1, "message": "1146", "line": 3, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 3, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1149", "line": 9, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 9, "endColumn": 12}, {"ruleId": "1145", "severity": 1, "message": "1150", "line": 11, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1151", "line": 12, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 12, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1152", "line": 13, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 13, "endColumn": 22}, {"ruleId": "1145", "severity": 1, "message": "1153", "line": 16, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 16, "endColumn": 28}, {"ruleId": "1145", "severity": 1, "message": "1154", "line": 19, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 19, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1155", "line": 60, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 60, "endColumn": 24}, {"ruleId": "1145", "severity": 1, "message": "1156", "line": 60, "column": 26, "nodeType": "1147", "messageId": "1148", "endLine": 60, "endColumn": 43}, {"ruleId": "1157", "severity": 1, "message": "1158", "line": 76, "column": 5, "nodeType": "1159", "endLine": 76, "endColumn": 14, "suggestions": "1160"}, {"ruleId": "1157", "severity": 1, "message": "1161", "line": 84, "column": 5, "nodeType": "1159", "endLine": 84, "endColumn": 14, "suggestions": "1162"}, {"ruleId": "1145", "severity": 1, "message": "1163", "line": 95, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 95, "endColumn": 26}, {"ruleId": "1157", "severity": 1, "message": "1164", "line": 99, "column": 5, "nodeType": "1159", "endLine": 99, "endColumn": 21, "suggestions": "1165"}, {"ruleId": "1157", "severity": 1, "message": "1166", "line": 127, "column": 6, "nodeType": "1159", "endLine": 127, "endColumn": 25, "suggestions": "1167"}, {"ruleId": "1157", "severity": 1, "message": "1168", "line": 138, "column": 6, "nodeType": "1159", "endLine": 138, "endColumn": 22, "suggestions": "1169"}, {"ruleId": "1157", "severity": 1, "message": "1170", "line": 162, "column": 5, "nodeType": "1159", "endLine": 162, "endColumn": 11, "suggestions": "1171"}, {"ruleId": "1157", "severity": 1, "message": "1170", "line": 169, "column": 5, "nodeType": "1159", "endLine": 169, "endColumn": 11, "suggestions": "1172"}, {"ruleId": "1157", "severity": 1, "message": "1173", "line": 176, "column": 5, "nodeType": "1159", "endLine": 176, "endColumn": 18, "suggestions": "1174"}, {"ruleId": "1157", "severity": 1, "message": "1175", "line": 188, "column": 5, "nodeType": "1159", "endLine": 188, "endColumn": 33, "suggestions": "1176"}, {"ruleId": "1145", "severity": 1, "message": "1177", "line": 3, "column": 24, "nodeType": "1147", "messageId": "1148", "endLine": 3, "endColumn": 38}, {"ruleId": "1145", "severity": 1, "message": "1178", "line": 6, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 6, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1179", "line": 1, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1146", "line": 4, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1180", "line": 4, "column": 22, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 31}, {"ruleId": "1145", "severity": 1, "message": "1181", "line": 12, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 12, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1182", "line": 14, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 14, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1183", "line": 18, "column": 7, "nodeType": "1147", "messageId": "1148", "endLine": 18, "endColumn": 32}, {"ruleId": "1145", "severity": 1, "message": "1184", "line": 11, "column": 13, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1185", "line": 12, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 12, "endColumn": 12}, {"ruleId": "1145", "severity": 1, "message": "1186", "line": 16, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 16, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1187", "line": 20, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 20, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1188", "line": 21, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 21, "endColumn": 18}, {"ruleId": "1145", "severity": 1, "message": "1189", "line": 38, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 38, "endColumn": 26}, {"ruleId": "1145", "severity": 1, "message": "1190", "line": 42, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 42, "endColumn": 34}, {"ruleId": "1145", "severity": 1, "message": "1191", "line": 45, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 45, "endColumn": 32}, {"ruleId": "1145", "severity": 1, "message": "1192", "line": 51, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 51, "endColumn": 30}, {"ruleId": "1157", "severity": 1, "message": "1193", "line": 79, "column": 6, "nodeType": "1159", "endLine": 79, "endColumn": 8, "suggestions": "1194"}, {"ruleId": "1145", "severity": 1, "message": "1195", "line": 27, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 27, "endColumn": 29}, {"ruleId": "1145", "severity": 1, "message": "1196", "line": 17, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 17, "endColumn": 10}, {"ruleId": "1145", "severity": 1, "message": "1197", "line": 18, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 18, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1198", "line": 64, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 64, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1199", "line": 64, "column": 21, "nodeType": "1147", "messageId": "1148", "endLine": 64, "endColumn": 33}, {"ruleId": "1145", "severity": 1, "message": "1200", "line": 67, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 67, "endColumn": 25}, {"ruleId": "1145", "severity": 1, "message": "1183", "line": 69, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 69, "endColumn": 34}, {"ruleId": "1145", "severity": 1, "message": "1190", "line": 71, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 71, "endColumn": 34}, {"ruleId": "1145", "severity": 1, "message": "1201", "line": 73, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 73, "endColumn": 34}, {"ruleId": "1145", "severity": 1, "message": "1202", "line": 81, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 81, "endColumn": 25}, {"ruleId": "1145", "severity": 1, "message": "1203", "line": 95, "column": 25, "nodeType": "1147", "messageId": "1148", "endLine": 95, "endColumn": 41}, {"ruleId": "1204", "severity": 1, "message": "1205", "line": 134, "column": 56, "nodeType": "1206", "messageId": "1207", "endLine": 134, "endColumn": 58}, {"ruleId": "1204", "severity": 1, "message": "1205", "line": 139, "column": 57, "nodeType": "1206", "messageId": "1207", "endLine": 139, "endColumn": 59}, {"ruleId": "1157", "severity": 1, "message": "1208", "line": 146, "column": 6, "nodeType": "1159", "endLine": 146, "endColumn": 8, "suggestions": "1209"}, {"ruleId": "1145", "severity": 1, "message": "1196", "line": 7, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 7, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1210", "line": 13, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 13, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1211", "line": 140, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 140, "endColumn": 30}, {"ruleId": "1204", "severity": 1, "message": "1205", "line": 306, "column": 56, "nodeType": "1206", "messageId": "1207", "endLine": 306, "endColumn": 58}, {"ruleId": "1157", "severity": 1, "message": "1212", "line": 151, "column": 6, "nodeType": "1159", "endLine": 151, "endColumn": 8, "suggestions": "1213", "suppressions": "1214"}, {"ruleId": "1145", "severity": 1, "message": "1215", "line": 8, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 8, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1216", "line": 37, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 37, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1217", "line": 39, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 39, "endColumn": 22}, {"ruleId": "1145", "severity": 1, "message": "1218", "line": 61, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 61, "endColumn": 23}, {"ruleId": "1145", "severity": 1, "message": "1219", "line": 99, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 99, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1220", "line": 100, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 100, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1221", "line": 234, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 234, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1222", "line": 234, "column": 16, "nodeType": "1147", "messageId": "1148", "endLine": 234, "endColumn": 23}, {"ruleId": "1145", "severity": 1, "message": "1223", "line": 235, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 235, "endColumn": 26}, {"ruleId": "1145", "severity": 1, "message": "1224", "line": 235, "column": 28, "nodeType": "1147", "messageId": "1148", "endLine": 235, "endColumn": 47}, {"ruleId": "1157", "severity": 1, "message": "1225", "line": 303, "column": 6, "nodeType": "1159", "endLine": 303, "endColumn": 8, "suggestions": "1226"}, {"ruleId": "1145", "severity": 1, "message": "1227", "line": 305, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 305, "endColumn": 26}, {"ruleId": "1145", "severity": 1, "message": "1228", "line": 307, "column": 11, "nodeType": "1147", "messageId": "1148", "endLine": 307, "endColumn": 18}, {"ruleId": "1145", "severity": 1, "message": "1229", "line": 577, "column": 11, "nodeType": "1147", "messageId": "1148", "endLine": 577, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1230", "line": 882, "column": 11, "nodeType": "1147", "messageId": "1148", "endLine": 882, "endColumn": 27}, {"ruleId": "1204", "severity": 1, "message": "1231", "line": 1407, "column": 71, "nodeType": "1206", "messageId": "1207", "endLine": 1407, "endColumn": 73}, {"ruleId": "1204", "severity": 1, "message": "1205", "line": 1422, "column": 42, "nodeType": "1206", "messageId": "1207", "endLine": 1422, "endColumn": 44}, {"ruleId": "1232", "severity": 1, "message": "1233", "line": 1653, "column": 19, "nodeType": "1234", "endLine": 1664, "endColumn": 21}, {"ruleId": "1232", "severity": 1, "message": "1233", "line": 1711, "column": 17, "nodeType": "1234", "endLine": 1724, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1235", "line": 1825, "column": 17, "nodeType": "1147", "messageId": "1148", "endLine": 1825, "endColumn": 24}, {"ruleId": "1145", "severity": 1, "message": "1236", "line": 9, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 9, "endColumn": 34}, {"ruleId": "1145", "severity": 1, "message": "1237", "line": 10, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 10, "endColumn": 25}, {"ruleId": "1145", "severity": 1, "message": "1238", "line": 11, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 18}, {"ruleId": "1145", "severity": 1, "message": "1239", "line": 14, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 14, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1240", "line": 15, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 15, "endColumn": 23}, {"ruleId": "1145", "severity": 1, "message": "1241", "line": 16, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 16, "endColumn": 26}, {"ruleId": "1145", "severity": 1, "message": "1242", "line": 17, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 17, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1243", "line": 18, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 18, "endColumn": 27}, {"ruleId": "1145", "severity": 1, "message": "1244", "line": 19, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 19, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1245", "line": 21, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 21, "endColumn": 30}, {"ruleId": "1145", "severity": 1, "message": "1246", "line": 22, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 22, "endColumn": 32}, {"ruleId": "1145", "severity": 1, "message": "1210", "line": 26, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 26, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1247", "line": 31, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 31, "endColumn": 24}, {"ruleId": "1145", "severity": 1, "message": "1248", "line": 31, "column": 26, "nodeType": "1147", "messageId": "1148", "endLine": 31, "endColumn": 31}, {"ruleId": "1145", "severity": 1, "message": "1150", "line": 40, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 40, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1249", "line": 41, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 41, "endColumn": 33}, {"ruleId": "1145", "severity": 1, "message": "1250", "line": 43, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 43, "endColumn": 25}, {"ruleId": "1145", "severity": 1, "message": "1251", "line": 66, "column": 21, "nodeType": "1147", "messageId": "1148", "endLine": 66, "endColumn": 29}, {"ruleId": "1145", "severity": 1, "message": "1190", "line": 72, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 72, "endColumn": 34}, {"ruleId": "1157", "severity": 1, "message": "1252", "line": 116, "column": 6, "nodeType": "1159", "endLine": 116, "endColumn": 8, "suggestions": "1253"}, {"ruleId": "1157", "severity": 1, "message": "1254", "line": 125, "column": 6, "nodeType": "1159", "endLine": 125, "endColumn": 26, "suggestions": "1255"}, {"ruleId": "1157", "severity": 1, "message": "1256", "line": 138, "column": 6, "nodeType": "1159", "endLine": 138, "endColumn": 45, "suggestions": "1257"}, {"ruleId": "1145", "severity": 1, "message": "1258", "line": 9, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 9, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1258", "line": 9, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 9, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1259", "line": 10, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 10, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1260", "line": 13, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 13, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1261", "line": 25, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 25, "endColumn": 23}, {"ruleId": "1145", "severity": 1, "message": "1262", "line": 33, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 33, "endColumn": 23}, {"ruleId": "1145", "severity": 1, "message": "1263", "line": 33, "column": 33, "nodeType": "1147", "messageId": "1148", "endLine": 33, "endColumn": 41}, {"ruleId": "1145", "severity": 1, "message": "1264", "line": 34, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 34, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1265", "line": 36, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 36, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1266", "line": 37, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 37, "endColumn": 22}, {"ruleId": "1145", "severity": 1, "message": "1267", "line": 1, "column": 38, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 49}, {"ruleId": "1145", "severity": 1, "message": "1268", "line": 4, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 12}, {"ruleId": "1145", "severity": 1, "message": "1269", "line": 67, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 67, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1270", "line": 67, "column": 21, "nodeType": "1147", "messageId": "1148", "endLine": 67, "endColumn": 33}, {"ruleId": "1157", "severity": 1, "message": "1271", "line": 79, "column": 6, "nodeType": "1159", "endLine": 79, "endColumn": 19, "suggestions": "1272"}, {"ruleId": "1145", "severity": 1, "message": "1273", "line": 14, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 14, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1274", "line": 25, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 25, "endColumn": 18}, {"ruleId": "1145", "severity": 1, "message": "1275", "line": 26, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 26, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1276", "line": 31, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 31, "endColumn": 25}, {"ruleId": "1145", "severity": 1, "message": "1197", "line": 47, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 47, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1154", "line": 53, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 53, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1190", "line": 70, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 70, "endColumn": 34}, {"ruleId": "1157", "severity": 1, "message": "1277", "line": 104, "column": 6, "nodeType": "1159", "endLine": 104, "endColumn": 8, "suggestions": "1278"}, {"ruleId": "1157", "severity": 1, "message": "1279", "line": 108, "column": 6, "nodeType": "1159", "endLine": 108, "endColumn": 8, "suggestions": "1280"}, {"ruleId": "1145", "severity": 1, "message": "1229", "line": 223, "column": 13, "nodeType": "1147", "messageId": "1148", "endLine": 223, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1281", "line": 20, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 20, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1282", "line": 21, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 21, "endColumn": 28}, {"ruleId": "1145", "severity": 1, "message": "1283", "line": 34, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 34, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1197", "line": 49, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 49, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1284", "line": 49, "column": 18, "nodeType": "1147", "messageId": "1148", "endLine": 49, "endColumn": 22}, {"ruleId": "1145", "severity": 1, "message": "1285", "line": 49, "column": 31, "nodeType": "1147", "messageId": "1148", "endLine": 49, "endColumn": 36}, {"ruleId": "1145", "severity": 1, "message": "1286", "line": 53, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 53, "endColumn": 23}, {"ruleId": "1145", "severity": 1, "message": "1190", "line": 91, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 91, "endColumn": 34}, {"ruleId": "1145", "severity": 1, "message": "1287", "line": 96, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 96, "endColumn": 20}, {"ruleId": "1157", "severity": 1, "message": "1288", "line": 120, "column": 6, "nodeType": "1159", "endLine": 120, "endColumn": 23, "suggestions": "1289"}, {"ruleId": "1145", "severity": 1, "message": "1290", "line": 217, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 217, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1291", "line": 219, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 219, "endColumn": 25}, {"ruleId": "1145", "severity": 1, "message": "1292", "line": 224, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 224, "endColumn": 32}, {"ruleId": "1145", "severity": 1, "message": "1197", "line": 18, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 18, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1293", "line": 21, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 21, "endColumn": 25}, {"ruleId": "1145", "severity": 1, "message": "1294", "line": 22, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 22, "endColumn": 30}, {"ruleId": "1145", "severity": 1, "message": "1295", "line": 30, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 30, "endColumn": 29}, {"ruleId": "1145", "severity": 1, "message": "1296", "line": 44, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 44, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1282", "line": 51, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 51, "endColumn": 28}, {"ruleId": "1145", "severity": 1, "message": "1297", "line": 59, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 59, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1298", "line": 61, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 61, "endColumn": 29}, {"ruleId": "1145", "severity": 1, "message": "1299", "line": 62, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 62, "endColumn": 23}, {"ruleId": "1145", "severity": 1, "message": "1190", "line": 86, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 86, "endColumn": 34}, {"ruleId": "1145", "severity": 1, "message": "1300", "line": 98, "column": 23, "nodeType": "1147", "messageId": "1148", "endLine": 98, "endColumn": 37}, {"ruleId": "1145", "severity": 1, "message": "1287", "line": 102, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 102, "endColumn": 20}, {"ruleId": "1157", "severity": 1, "message": "1301", "line": 108, "column": 6, "nodeType": "1159", "endLine": 108, "endColumn": 8, "suggestions": "1302"}, {"ruleId": "1145", "severity": 1, "message": "1303", "line": 17, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 17, "endColumn": 12}, {"ruleId": "1145", "severity": 1, "message": "1304", "line": 18, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 18, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1305", "line": 19, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 19, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1306", "line": 29, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 29, "endColumn": 23}, {"ruleId": "1145", "severity": 1, "message": "1307", "line": 40, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 40, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1308", "line": 45, "column": 23, "nodeType": "1147", "messageId": "1148", "endLine": 45, "endColumn": 34}, {"ruleId": "1145", "severity": 1, "message": "1299", "line": 56, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 56, "endColumn": 23}, {"ruleId": "1145", "severity": 1, "message": "1309", "line": 57, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 57, "endColumn": 26}, {"ruleId": "1145", "severity": 1, "message": "1310", "line": 64, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 64, "endColumn": 27}, {"ruleId": "1145", "severity": 1, "message": "1311", "line": 70, "column": 7, "nodeType": "1147", "messageId": "1148", "endLine": 70, "endColumn": 12}, {"ruleId": "1145", "severity": 1, "message": "1312", "line": 72, "column": 11, "nodeType": "1147", "messageId": "1148", "endLine": 72, "endColumn": 26}, {"ruleId": "1145", "severity": 1, "message": "1190", "line": 87, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 87, "endColumn": 34}, {"ruleId": "1145", "severity": 1, "message": "1313", "line": 107, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 107, "endColumn": 18}, {"ruleId": "1145", "severity": 1, "message": "1314", "line": 107, "column": 20, "nodeType": "1147", "messageId": "1148", "endLine": 107, "endColumn": 31}, {"ruleId": "1157", "severity": 1, "message": "1315", "line": 121, "column": 6, "nodeType": "1159", "endLine": 121, "endColumn": 8, "suggestions": "1316"}, {"ruleId": "1157", "severity": 1, "message": "1317", "line": 223, "column": 6, "nodeType": "1159", "endLine": 223, "endColumn": 23, "suggestions": "1318"}, {"ruleId": "1232", "severity": 1, "message": "1233", "line": 678, "column": 31, "nodeType": "1234", "endLine": 682, "endColumn": 33}, {"ruleId": "1145", "severity": 1, "message": "1197", "line": 20, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 20, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1319", "line": 67, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 67, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1320", "line": 97, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 97, "endColumn": 26}, {"ruleId": "1145", "severity": 1, "message": "1321", "line": 97, "column": 28, "nodeType": "1147", "messageId": "1148", "endLine": 97, "endColumn": 47}, {"ruleId": "1145", "severity": 1, "message": "1190", "line": 102, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 102, "endColumn": 34}, {"ruleId": "1145", "severity": 1, "message": "1251", "line": 106, "column": 21, "nodeType": "1147", "messageId": "1148", "endLine": 106, "endColumn": 29}, {"ruleId": "1145", "severity": 1, "message": "1300", "line": 119, "column": 23, "nodeType": "1147", "messageId": "1148", "endLine": 119, "endColumn": 37}, {"ruleId": "1157", "severity": 1, "message": "1193", "line": 133, "column": 6, "nodeType": "1159", "endLine": 133, "endColumn": 8, "suggestions": "1322"}, {"ruleId": "1157", "severity": 1, "message": "1323", "line": 137, "column": 6, "nodeType": "1159", "endLine": 137, "endColumn": 23, "suggestions": "1324"}, {"ruleId": "1157", "severity": 1, "message": "1325", "line": 146, "column": 6, "nodeType": "1159", "endLine": 146, "endColumn": 8, "suggestions": "1326"}, {"ruleId": "1145", "severity": 1, "message": "1327", "line": 12, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 12, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1328", "line": 13, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 13, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1329", "line": 19, "column": 6, "nodeType": "1147", "messageId": "1148", "endLine": 19, "endColumn": 19}, {"ruleId": "1157", "severity": 1, "message": "1330", "line": 57, "column": 6, "nodeType": "1159", "endLine": 57, "endColumn": 20, "suggestions": "1331"}, {"ruleId": "1145", "severity": 1, "message": "1332", "line": 27, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 27, "endColumn": 22}, {"ruleId": "1145", "severity": 1, "message": "1275", "line": 28, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 28, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1333", "line": 32, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 32, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1334", "line": 41, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 41, "endColumn": 23}, {"ruleId": "1145", "severity": 1, "message": "1335", "line": 53, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 53, "endColumn": 35}, {"ruleId": "1145", "severity": 1, "message": "1336", "line": 57, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 57, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1337", "line": 80, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 80, "endColumn": 18}, {"ruleId": "1145", "severity": 1, "message": "1338", "line": 84, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 84, "endColumn": 12}, {"ruleId": "1145", "severity": 1, "message": "1339", "line": 90, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 90, "endColumn": 26}, {"ruleId": "1145", "severity": 1, "message": "1258", "line": 91, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 91, "endColumn": 18}, {"ruleId": "1145", "severity": 1, "message": "1340", "line": 92, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 92, "endColumn": 24}, {"ruleId": "1145", "severity": 1, "message": "1341", "line": 106, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 106, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1342", "line": 107, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 107, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1343", "line": 108, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 108, "endColumn": 23}, {"ruleId": "1145", "severity": 1, "message": "1344", "line": 109, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 109, "endColumn": 22}, {"ruleId": "1145", "severity": 1, "message": "1200", "line": 114, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 114, "endColumn": 25}, {"ruleId": "1145", "severity": 1, "message": "1190", "line": 118, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 118, "endColumn": 34}, {"ruleId": "1145", "severity": 1, "message": "1202", "line": 123, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 123, "endColumn": 25}, {"ruleId": "1145", "severity": 1, "message": "1345", "line": 128, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 128, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1203", "line": 139, "column": 25, "nodeType": "1147", "messageId": "1148", "endLine": 139, "endColumn": 41}, {"ruleId": "1145", "severity": 1, "message": "1287", "line": 141, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 141, "endColumn": 20}, {"ruleId": "1204", "severity": 1, "message": "1205", "line": 179, "column": 56, "nodeType": "1206", "messageId": "1207", "endLine": 179, "endColumn": 58}, {"ruleId": "1204", "severity": 1, "message": "1205", "line": 184, "column": 57, "nodeType": "1206", "messageId": "1207", "endLine": 184, "endColumn": 59}, {"ruleId": "1157", "severity": 1, "message": "1346", "line": 192, "column": 6, "nodeType": "1159", "endLine": 192, "endColumn": 8, "suggestions": "1347"}, {"ruleId": "1145", "severity": 1, "message": "1348", "line": 207, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 207, "endColumn": 20}, {"ruleId": "1204", "severity": 1, "message": "1231", "line": 217, "column": 36, "nodeType": "1206", "messageId": "1207", "endLine": 217, "endColumn": 38}, {"ruleId": "1145", "severity": 1, "message": "1349", "line": 278, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 278, "endColumn": 20}, {"ruleId": "1204", "severity": 1, "message": "1205", "line": 390, "column": 60, "nodeType": "1206", "messageId": "1207", "endLine": 390, "endColumn": 62}, {"ruleId": "1145", "severity": 1, "message": "1350", "line": 499, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 499, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1351", "line": 2, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 2, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1352", "line": 3, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 3, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1353", "line": 4, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1354", "line": 5, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 5, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1355", "line": 6, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 6, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1356", "line": 10, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 10, "endColumn": 39}, {"ruleId": "1145", "severity": 1, "message": "1357", "line": 11, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 36}, {"ruleId": "1145", "severity": 1, "message": "1358", "line": 7, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 7, "endColumn": 7}, {"ruleId": "1145", "severity": 1, "message": "1359", "line": 8, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 8, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1319", "line": 11, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 12}, {"ruleId": "1145", "severity": 1, "message": "1248", "line": 14, "column": 19, "nodeType": "1147", "messageId": "1148", "endLine": 14, "endColumn": 24}, {"ruleId": "1145", "severity": 1, "message": "1335", "line": 28, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 28, "endColumn": 28}, {"ruleId": "1145", "severity": 1, "message": "1197", "line": 32, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 32, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1360", "line": 36, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 36, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1361", "line": 37, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 37, "endColumn": 8}, {"ruleId": "1145", "severity": 1, "message": "1362", "line": 51, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 51, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1363", "line": 57, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 57, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1203", "line": 78, "column": 25, "nodeType": "1147", "messageId": "1148", "endLine": 78, "endColumn": 41}, {"ruleId": "1145", "severity": 1, "message": "1190", "line": 83, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 83, "endColumn": 34}, {"ruleId": "1157", "severity": 1, "message": "1364", "line": 118, "column": 6, "nodeType": "1159", "endLine": 118, "endColumn": 8, "suggestions": "1365"}, {"ruleId": "1145", "severity": 1, "message": "1366", "line": 179, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 179, "endColumn": 18}, {"ruleId": "1204", "severity": 1, "message": "1231", "line": 368, "column": 65, "nodeType": "1206", "messageId": "1207", "endLine": 368, "endColumn": 67}, {"ruleId": "1157", "severity": 1, "message": "1367", "line": 28, "column": 6, "nodeType": "1159", "endLine": 28, "endColumn": 16, "suggestions": "1368"}, {"ruleId": "1157", "severity": 1, "message": "1367", "line": 28, "column": 6, "nodeType": "1159", "endLine": 28, "endColumn": 16, "suggestions": "1369"}, {"ruleId": "1145", "severity": 1, "message": "1370", "line": 2, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 2, "endColumn": 19}, {"ruleId": "1204", "severity": 1, "message": "1231", "line": 29, "column": 45, "nodeType": "1206", "messageId": "1207", "endLine": 29, "endColumn": 47}, {"ruleId": "1371", "severity": 1, "message": "1372", "line": 7, "column": 24, "nodeType": "1373", "messageId": "1374", "endLine": 7, "endColumn": 25, "suggestions": "1375"}, {"ruleId": "1371", "severity": 1, "message": "1372", "line": 7, "column": 40, "nodeType": "1373", "messageId": "1374", "endLine": 7, "endColumn": 41, "suggestions": "1376"}, {"ruleId": "1145", "severity": 1, "message": "1377", "line": 6, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 6, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1378", "line": 9, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 9, "endColumn": 18}, {"ruleId": "1145", "severity": 1, "message": "1196", "line": 10, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 10, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1379", "line": 12, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 12, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1150", "line": 14, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 14, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1380", "line": 15, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 15, "endColumn": 12}, {"ruleId": "1145", "severity": 1, "message": "1381", "line": 16, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 16, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1382", "line": 22, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 22, "endColumn": 26}, {"ruleId": "1145", "severity": 1, "message": "1383", "line": 28, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 28, "endColumn": 18}, {"ruleId": "1145", "severity": 1, "message": "1384", "line": 31, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 31, "endColumn": 28}, {"ruleId": "1145", "severity": 1, "message": "1385", "line": 32, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 32, "endColumn": 34}, {"ruleId": "1145", "severity": 1, "message": "1386", "line": 33, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 33, "endColumn": 22}, {"ruleId": "1145", "severity": 1, "message": "1387", "line": 41, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 41, "endColumn": 26}, {"ruleId": "1145", "severity": 1, "message": "1388", "line": 91, "column": 7, "nodeType": "1147", "messageId": "1148", "endLine": 91, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1389", "line": 155, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 155, "endColumn": 14}, {"ruleId": "1157", "severity": 1, "message": "1390", "line": 168, "column": 6, "nodeType": "1159", "endLine": 168, "endColumn": 8, "suggestions": "1391"}, {"ruleId": "1145", "severity": 1, "message": "1392", "line": 170, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 170, "endColumn": 28}, {"ruleId": "1145", "severity": 1, "message": "1393", "line": 183, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 183, "endColumn": 41}, {"ruleId": "1145", "severity": 1, "message": "1394", "line": 339, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 339, "endColumn": 39}, {"ruleId": "1371", "severity": 1, "message": "1372", "line": 9, "column": 24, "nodeType": "1373", "messageId": "1374", "endLine": 9, "endColumn": 25, "suggestions": "1395"}, {"ruleId": "1371", "severity": 1, "message": "1372", "line": 9, "column": 40, "nodeType": "1373", "messageId": "1374", "endLine": 9, "endColumn": 41, "suggestions": "1396"}, {"ruleId": "1145", "severity": 1, "message": "1397", "line": 1, "column": 17, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 26}, {"ruleId": "1145", "severity": 1, "message": "1398", "line": 1, "column": 28, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 35}, {"ruleId": "1145", "severity": 1, "message": "1196", "line": 4, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1358", "line": 4, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1359", "line": 4, "column": 16, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 27}, {"ruleId": "1145", "severity": 1, "message": "1399", "line": 8, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 8, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1400", "line": 2, "column": 16, "nodeType": "1147", "messageId": "1148", "endLine": 2, "endColumn": 25}, {"ruleId": "1145", "severity": 1, "message": "1401", "line": 2, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 2, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1400", "line": 2, "column": 15, "nodeType": "1147", "messageId": "1148", "endLine": 2, "endColumn": 24}, {"ruleId": "1145", "severity": 1, "message": "1358", "line": 3, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 3, "endColumn": 7}, {"ruleId": "1145", "severity": 1, "message": "1359", "line": 4, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1370", "line": 1, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1370", "line": 1, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1370", "line": 1, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1402", "line": 11, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1190", "line": 32, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 32, "endColumn": 34}, {"ruleId": "1145", "severity": 1, "message": "1229", "line": 42, "column": 11, "nodeType": "1147", "messageId": "1148", "endLine": 42, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1333", "line": 1, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1284", "line": 2, "column": 23, "nodeType": "1147", "messageId": "1148", "endLine": 2, "endColumn": 27}, {"ruleId": "1145", "severity": 1, "message": "1248", "line": 2, "column": 29, "nodeType": "1147", "messageId": "1148", "endLine": 2, "endColumn": 34}, {"ruleId": "1145", "severity": 1, "message": "1285", "line": 2, "column": 36, "nodeType": "1147", "messageId": "1148", "endLine": 2, "endColumn": 41}, {"ruleId": "1145", "severity": 1, "message": "1403", "line": 1, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 15}, {"ruleId": "1157", "severity": 1, "message": "1404", "line": 63, "column": 6, "nodeType": "1159", "endLine": 63, "endColumn": 8, "suggestions": "1405"}, {"ruleId": "1145", "severity": 1, "message": "1273", "line": 4, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 8}, {"ruleId": "1145", "severity": 1, "message": "1406", "line": 8, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 8, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1259", "line": 25, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 25, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1407", "line": 26, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 26, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1258", "line": 27, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 27, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1377", "line": 28, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 28, "endColumn": 10}, {"ruleId": "1145", "severity": 1, "message": "1408", "line": 48, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 48, "endColumn": 18}, {"ruleId": "1145", "severity": 1, "message": "1409", "line": 50, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 50, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1358", "line": 52, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 52, "endColumn": 12}, {"ruleId": "1145", "severity": 1, "message": "1359", "line": 53, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 53, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1319", "line": 54, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 54, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1284", "line": 54, "column": 21, "nodeType": "1147", "messageId": "1148", "endLine": 54, "endColumn": 25}, {"ruleId": "1145", "severity": 1, "message": "1410", "line": 55, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 55, "endColumn": 22}, {"ruleId": "1145", "severity": 1, "message": "1411", "line": 55, "column": 24, "nodeType": "1147", "messageId": "1148", "endLine": 55, "endColumn": 39}, {"ruleId": "1145", "severity": 1, "message": "1412", "line": 56, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 56, "endColumn": 31}, {"ruleId": "1145", "severity": 1, "message": "1340", "line": 57, "column": 26, "nodeType": "1147", "messageId": "1148", "endLine": 57, "endColumn": 40}, {"ruleId": "1145", "severity": 1, "message": "1413", "line": 107, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 107, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1414", "line": 108, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 108, "endColumn": 23}, {"ruleId": "1145", "severity": 1, "message": "1415", "line": 109, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 109, "endColumn": 25}, {"ruleId": "1145", "severity": 1, "message": "1416", "line": 111, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 111, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1417", "line": 112, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 112, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1418", "line": 117, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 117, "endColumn": 29}, {"ruleId": "1145", "severity": 1, "message": "1419", "line": 125, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 125, "endColumn": 32}, {"ruleId": "1145", "severity": 1, "message": "1420", "line": 131, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 131, "endColumn": 22}, {"ruleId": "1145", "severity": 1, "message": "1421", "line": 133, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 133, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1422", "line": 142, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 142, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1203", "line": 177, "column": 25, "nodeType": "1147", "messageId": "1148", "endLine": 177, "endColumn": 41}, {"ruleId": "1157", "severity": 1, "message": "1423", "line": 222, "column": 6, "nodeType": "1159", "endLine": 222, "endColumn": 8, "suggestions": "1424"}, {"ruleId": "1157", "severity": 1, "message": "1425", "line": 244, "column": 6, "nodeType": "1159", "endLine": 244, "endColumn": 68, "suggestions": "1426"}, {"ruleId": "1204", "severity": 1, "message": "1205", "line": 426, "column": 64, "nodeType": "1206", "messageId": "1207", "endLine": 426, "endColumn": 66}, {"ruleId": "1204", "severity": 1, "message": "1205", "line": 432, "column": 66, "nodeType": "1206", "messageId": "1207", "endLine": 432, "endColumn": 68}, {"ruleId": "1204", "severity": 1, "message": "1231", "line": 450, "column": 50, "nodeType": "1206", "messageId": "1207", "endLine": 450, "endColumn": 52}, {"ruleId": "1204", "severity": 1, "message": "1205", "line": 743, "column": 61, "nodeType": "1206", "messageId": "1207", "endLine": 743, "endColumn": 63}, {"ruleId": "1145", "severity": 1, "message": "1146", "line": 1, "column": 46, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 56}, {"ruleId": "1145", "severity": 1, "message": "1406", "line": 7, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 7, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1427", "line": 19, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 19, "endColumn": 24}, {"ruleId": "1157", "severity": 1, "message": "1428", "line": 58, "column": 6, "nodeType": "1159", "endLine": 58, "endColumn": 39, "suggestions": "1429"}, {"ruleId": "1157", "severity": 1, "message": "1430", "line": 137, "column": 6, "nodeType": "1159", "endLine": 137, "endColumn": 36, "suggestions": "1431"}, {"ruleId": "1145", "severity": 1, "message": "1370", "line": 1, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1432", "line": 6, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 6, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1433", "line": 11, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 27}, {"ruleId": "1145", "severity": 1, "message": "1434", "line": 4, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1150", "line": 5, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 5, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1378", "line": 6, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 6, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1435", "line": 7, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 7, "endColumn": 6}, {"ruleId": "1145", "severity": 1, "message": "1258", "line": 8, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 8, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1407", "line": 9, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 9, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1377", "line": 10, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 10, "endColumn": 10}, {"ruleId": "1145", "severity": 1, "message": "1303", "line": 11, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 7}, {"ruleId": "1145", "severity": 1, "message": "1304", "line": 12, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 12, "endColumn": 11}, {"ruleId": "1145", "severity": 1, "message": "1406", "line": 13, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 13, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1196", "line": 14, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 14, "endColumn": 10}, {"ruleId": "1145", "severity": 1, "message": "1436", "line": 17, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 17, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1409", "line": 19, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 19, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1437", "line": 20, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 20, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1438", "line": 21, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 21, "endColumn": 24}, {"ruleId": "1145", "severity": 1, "message": "1439", "line": 22, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 22, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1440", "line": 23, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 23, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1441", "line": 23, "column": 18, "nodeType": "1147", "messageId": "1148", "endLine": 23, "endColumn": 22}, {"ruleId": "1145", "severity": 1, "message": "1184", "line": 24, "column": 13, "nodeType": "1147", "messageId": "1148", "endLine": 24, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1442", "line": 33, "column": 11, "nodeType": "1147", "messageId": "1148", "endLine": 33, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1443", "line": 4, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1151", "line": 10, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 10, "endColumn": 12}, {"ruleId": "1145", "severity": 1, "message": "1361", "line": 14, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 14, "endColumn": 8}, {"ruleId": "1145", "severity": 1, "message": "1444", "line": 21, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 21, "endColumn": 22}, {"ruleId": "1145", "severity": 1, "message": "1445", "line": 22, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 22, "endColumn": 30}, {"ruleId": "1145", "severity": 1, "message": "1446", "line": 22, "column": 32, "nodeType": "1147", "messageId": "1148", "endLine": 22, "endColumn": 42}, {"ruleId": "1145", "severity": 1, "message": "1284", "line": 15, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 15, "endColumn": 7}, {"ruleId": "1145", "severity": 1, "message": "1196", "line": 15, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 15, "endColumn": 10}, {"ruleId": "1145", "severity": 1, "message": "1397", "line": 1, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 36}, {"ruleId": "1145", "severity": 1, "message": "1447", "line": 13, "column": 13, "nodeType": "1147", "messageId": "1148", "endLine": 13, "endColumn": 23}, {"ruleId": "1145", "severity": 1, "message": "1370", "line": 1, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1448", "line": 4, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1370", "line": 1, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1448", "line": 10, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 10, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1449", "line": 11, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1450", "line": 1, "column": 17, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 25}, {"ruleId": "1145", "severity": 1, "message": "1451", "line": 10, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 10, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1452", "line": 8, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 8, "endColumn": 28}, {"ruleId": "1145", "severity": 1, "message": "1284", "line": 29, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 29, "endColumn": 12}, {"ruleId": "1145", "severity": 1, "message": "1453", "line": 52, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 52, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1454", "line": 57, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 57, "endColumn": 28}, {"ruleId": "1145", "severity": 1, "message": "1455", "line": 58, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 58, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1456", "line": 59, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 59, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1457", "line": 73, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 73, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1458", "line": 73, "column": 18, "nodeType": "1147", "messageId": "1148", "endLine": 73, "endColumn": 27}, {"ruleId": "1145", "severity": 1, "message": "1190", "line": 76, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 76, "endColumn": 34}, {"ruleId": "1204", "severity": 1, "message": "1231", "line": 143, "column": 53, "nodeType": "1206", "messageId": "1207", "endLine": 143, "endColumn": 55}, {"ruleId": "1157", "severity": 1, "message": "1459", "line": 152, "column": 6, "nodeType": "1159", "endLine": 152, "endColumn": 8, "suggestions": "1460"}, {"ruleId": "1157", "severity": 1, "message": "1279", "line": 373, "column": 6, "nodeType": "1159", "endLine": 373, "endColumn": 8, "suggestions": "1461"}, {"ruleId": "1145", "severity": 1, "message": "1370", "line": 1, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1462", "line": 6, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 6, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1463", "line": 29, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 29, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1333", "line": 1, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1397", "line": 2, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 2, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1450", "line": 2, "column": 21, "nodeType": "1147", "messageId": "1148", "endLine": 2, "endColumn": 29}, {"ruleId": "1145", "severity": 1, "message": "1359", "line": 5, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 5, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1333", "line": 8, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 8, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1464", "line": 10, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 10, "endColumn": 8}, {"ruleId": "1145", "severity": 1, "message": "1303", "line": 9, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 9, "endColumn": 7}, {"ruleId": "1145", "severity": 1, "message": "1304", "line": 10, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 10, "endColumn": 11}, {"ruleId": "1145", "severity": 1, "message": "1406", "line": 11, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1196", "line": 12, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 12, "endColumn": 10}, {"ruleId": "1145", "severity": 1, "message": "1465", "line": 12, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 12, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1466", "line": 12, "column": 18, "nodeType": "1147", "messageId": "1148", "endLine": 12, "endColumn": 35}, {"ruleId": "1145", "severity": 1, "message": "1381", "line": 13, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 13, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1467", "line": 14, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 14, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1188", "line": 16, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 16, "endColumn": 18}, {"ruleId": "1145", "severity": 1, "message": "1186", "line": 17, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 17, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1468", "line": 20, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 20, "endColumn": 24}, {"ruleId": "1145", "severity": 1, "message": "1469", "line": 21, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 21, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1470", "line": 45, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 45, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1203", "line": 59, "column": 25, "nodeType": "1147", "messageId": "1148", "endLine": 59, "endColumn": 41}, {"ruleId": "1157", "severity": 1, "message": "1471", "line": 81, "column": 6, "nodeType": "1159", "endLine": 81, "endColumn": 8, "suggestions": "1472"}, {"ruleId": "1145", "severity": 1, "message": "1303", "line": 9, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 9, "endColumn": 7}, {"ruleId": "1145", "severity": 1, "message": "1304", "line": 10, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 10, "endColumn": 11}, {"ruleId": "1145", "severity": 1, "message": "1406", "line": 11, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 15}, {"ruleId": "1145", "severity": 1, "message": "1196", "line": 12, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 12, "endColumn": 10}, {"ruleId": "1473", "severity": 1, "message": "1474", "line": 37, "column": 61, "nodeType": "1475", "messageId": "1476", "endLine": 37, "endColumn": 63}, {"ruleId": "1145", "severity": 1, "message": "1378", "line": 5, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 5, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1333", "line": 13, "column": 16, "nodeType": "1147", "messageId": "1148", "endLine": 13, "endColumn": 22}, {"ruleId": "1145", "severity": 1, "message": "1477", "line": 13, "column": 24, "nodeType": "1147", "messageId": "1148", "endLine": 13, "endColumn": 40}, {"ruleId": "1145", "severity": 1, "message": "1464", "line": 13, "column": 42, "nodeType": "1147", "messageId": "1148", "endLine": 13, "endColumn": 47}, {"ruleId": "1145", "severity": 1, "message": "1299", "line": 14, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 14, "endColumn": 23}, {"ruleId": "1145", "severity": 1, "message": "1478", "line": 15, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 15, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1479", "line": 17, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 17, "endColumn": 29}, {"ruleId": "1145", "severity": 1, "message": "1480", "line": 18, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 18, "endColumn": 26}, {"ruleId": "1145", "severity": 1, "message": "1481", "line": 19, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 19, "endColumn": 29}, {"ruleId": "1145", "severity": 1, "message": "1482", "line": 20, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 20, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1483", "line": 21, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 21, "endColumn": 25}, {"ruleId": "1145", "severity": 1, "message": "1150", "line": 8, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 8, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1484", "line": 10, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 10, "endColumn": 26}, {"ruleId": "1145", "severity": 1, "message": "1485", "line": 22, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 22, "endColumn": 22}, {"ruleId": "1145", "severity": 1, "message": "1333", "line": 1, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1397", "line": 2, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 2, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1450", "line": 2, "column": 21, "nodeType": "1147", "messageId": "1148", "endLine": 2, "endColumn": 29}, {"ruleId": "1145", "severity": 1, "message": "1486", "line": 2, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 2, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1397", "line": 4, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 12}, {"ruleId": "1145", "severity": 1, "message": "1190", "line": 38, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 38, "endColumn": 34}, {"ruleId": "1145", "severity": 1, "message": "1229", "line": 49, "column": 11, "nodeType": "1147", "messageId": "1148", "endLine": 49, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1465", "line": 7, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 7, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1381", "line": 8, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 8, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1188", "line": 9, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 9, "endColumn": 18}, {"ruleId": "1145", "severity": 1, "message": "1186", "line": 10, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 10, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1487", "line": 12, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 12, "endColumn": 29}, {"ruleId": "1145", "severity": 1, "message": "1334", "line": 13, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 13, "endColumn": 23}, {"ruleId": "1145", "severity": 1, "message": "1488", "line": 19, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 19, "endColumn": 24}, {"ruleId": "1145", "severity": 1, "message": "1489", "line": 20, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 20, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1490", "line": 21, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 21, "endColumn": 21}, {"ruleId": "1145", "severity": 1, "message": "1268", "line": 21, "column": 23, "nodeType": "1147", "messageId": "1148", "endLine": 21, "endColumn": 32}, {"ruleId": "1145", "severity": 1, "message": "1196", "line": 21, "column": 41, "nodeType": "1147", "messageId": "1148", "endLine": 21, "endColumn": 48}, {"ruleId": "1145", "severity": 1, "message": "1491", "line": 22, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 22, "endColumn": 27}, {"ruleId": "1157", "severity": 1, "message": "1492", "line": 56, "column": 6, "nodeType": "1159", "endLine": 56, "endColumn": 8, "suggestions": "1493"}, {"ruleId": "1145", "severity": 1, "message": "1190", "line": 32, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 32, "endColumn": 34}, {"ruleId": "1157", "severity": 1, "message": "1494", "line": 57, "column": 6, "nodeType": "1159", "endLine": 57, "endColumn": 8, "suggestions": "1495"}, {"ruleId": "1145", "severity": 1, "message": "1496", "line": 64, "column": 15, "nodeType": "1147", "messageId": "1148", "endLine": 64, "endColumn": 57}, {"ruleId": "1204", "severity": 1, "message": "1231", "line": 85, "column": 38, "nodeType": "1206", "messageId": "1207", "endLine": 85, "endColumn": 40}, {"ruleId": "1204", "severity": 1, "message": "1231", "line": 94, "column": 67, "nodeType": "1206", "messageId": "1207", "endLine": 94, "endColumn": 69}, {"ruleId": "1145", "severity": 1, "message": "1497", "line": 113, "column": 13, "nodeType": "1147", "messageId": "1148", "endLine": 113, "endColumn": 72}, {"ruleId": "1145", "severity": 1, "message": "1333", "line": 1, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 14}, {"ruleId": "1157", "severity": 1, "message": "1498", "line": 15, "column": 6, "nodeType": "1159", "endLine": 15, "endColumn": 8, "suggestions": "1499"}, {"ruleId": "1500", "severity": 1, "message": "1501", "line": 22, "column": 9, "nodeType": "1502", "messageId": "1503", "endLine": 38, "endColumn": 10}, {"ruleId": "1145", "severity": 1, "message": "1370", "line": 1, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1504", "line": 8, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 8, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1439", "line": 10, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 10, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1433", "line": 27, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 27, "endColumn": 27}, {"ruleId": "1145", "severity": 1, "message": "1463", "line": 30, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 30, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1190", "line": 36, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 36, "endColumn": 34}, {"ruleId": "1232", "severity": 1, "message": "1233", "line": 126, "column": 11, "nodeType": "1234", "endLine": 137, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1505", "line": 33, "column": 5, "nodeType": "1147", "messageId": "1148", "endLine": 33, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1506", "line": 69, "column": 11, "nodeType": "1147", "messageId": "1148", "endLine": 69, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1507", "line": 2, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 2, "endColumn": 24}, {"ruleId": "1145", "severity": 1, "message": "1435", "line": 9, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 9, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1508", "line": 26, "column": 30, "nodeType": "1147", "messageId": "1148", "endLine": 26, "endColumn": 44}, {"ruleId": "1145", "severity": 1, "message": "1258", "line": 3, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 3, "endColumn": 18}, {"ruleId": "1145", "severity": 1, "message": "1384", "line": 4, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 28}, {"ruleId": "1145", "severity": 1, "message": "1150", "line": 5, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 5, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1380", "line": 7, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 7, "endColumn": 12}, {"ruleId": "1145", "severity": 1, "message": "1381", "line": 8, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 8, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1509", "line": 11, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 18}, {"ruleId": "1145", "severity": 1, "message": "1510", "line": 16, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 16, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1511", "line": 18, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 18, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1349", "line": 19, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 19, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1417", "line": 22, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 22, "endColumn": 20}, {"ruleId": "1145", "severity": 1, "message": "1308", "line": 11, "column": 23, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 34}, {"ruleId": "1157", "severity": 1, "message": "1512", "line": 43, "column": 6, "nodeType": "1159", "endLine": 43, "endColumn": 8, "suggestions": "1513"}, {"ruleId": "1157", "severity": 1, "message": "1514", "line": 37, "column": 6, "nodeType": "1159", "endLine": 37, "endColumn": 8, "suggestions": "1515"}, {"ruleId": "1145", "severity": 1, "message": "1516", "line": 22, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 22, "endColumn": 18}, {"ruleId": "1145", "severity": 1, "message": "1517", "line": 22, "column": 20, "nodeType": "1147", "messageId": "1148", "endLine": 22, "endColumn": 31}, {"ruleId": "1157", "severity": 1, "message": "1518", "line": 41, "column": 6, "nodeType": "1159", "endLine": 41, "endColumn": 26, "suggestions": "1519"}, {"ruleId": "1145", "severity": 1, "message": "1333", "line": 1, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 14}, {"ruleId": "1145", "severity": 1, "message": "1284", "line": 3, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 3, "endColumn": 31}, {"ruleId": "1157", "severity": 1, "message": "1518", "line": 136, "column": 6, "nodeType": "1159", "endLine": 136, "endColumn": 26, "suggestions": "1520"}, {"ruleId": "1145", "severity": 1, "message": "1521", "line": 4, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1333", "line": 5, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 5, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1522", "line": 6, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 6, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1521", "line": 11, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1523", "line": 12, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 12, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1333", "line": 2, "column": 27, "nodeType": "1147", "messageId": "1148", "endLine": 2, "endColumn": 33}, {"ruleId": "1145", "severity": 1, "message": "1521", "line": 4, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 4, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1523", "line": 5, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 5, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1333", "line": 5, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 5, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1522", "line": 6, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 6, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1521", "line": 11, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1523", "line": 12, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 12, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1333", "line": 5, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 5, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1522", "line": 6, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 6, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1521", "line": 11, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1523", "line": 12, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 12, "endColumn": 16}, {"ruleId": "1524", "severity": 1, "message": "1525", "line": 50, "column": 15, "nodeType": "1234", "endLine": 52, "endColumn": 17}, {"ruleId": "1145", "severity": 1, "message": "1333", "line": 5, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 5, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1522", "line": 6, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 6, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1521", "line": 11, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 11, "endColumn": 13}, {"ruleId": "1145", "severity": 1, "message": "1523", "line": 12, "column": 8, "nodeType": "1147", "messageId": "1148", "endLine": 12, "endColumn": 16}, {"ruleId": "1145", "severity": 1, "message": "1526", "line": 35, "column": 7, "nodeType": "1147", "messageId": "1148", "endLine": 35, "endColumn": 25}, {"ruleId": "1145", "severity": 1, "message": "1305", "line": 15, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 15, "endColumn": 9}, {"ruleId": "1145", "severity": 1, "message": "1527", "line": 16, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 16, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1151", "line": 17, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 17, "endColumn": 12}, {"ruleId": "1145", "severity": 1, "message": "1196", "line": 22, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 22, "endColumn": 10}, {"ruleId": "1145", "severity": 1, "message": "1528", "line": 28, "column": 15, "nodeType": "1147", "messageId": "1148", "endLine": 28, "endColumn": 27}, {"ruleId": "1145", "severity": 1, "message": "1523", "line": 29, "column": 11, "nodeType": "1147", "messageId": "1148", "endLine": 29, "endColumn": 19}, {"ruleId": "1145", "severity": 1, "message": "1242", "line": 30, "column": 15, "nodeType": "1147", "messageId": "1148", "endLine": 30, "endColumn": 27}, {"ruleId": "1145", "severity": 1, "message": "1529", "line": 41, "column": 3, "nodeType": "1147", "messageId": "1148", "endLine": 41, "endColumn": 30}, {"ruleId": "1145", "severity": 1, "message": "1530", "line": 100, "column": 10, "nodeType": "1147", "messageId": "1148", "endLine": 100, "endColumn": 29}, {"ruleId": "1157", "severity": 1, "message": "1531", "line": 116, "column": 6, "nodeType": "1159", "endLine": 116, "endColumn": 16, "suggestions": "1532"}, {"ruleId": "1157", "severity": 1, "message": "1533", "line": 123, "column": 6, "nodeType": "1159", "endLine": 123, "endColumn": 34, "suggestions": "1534"}, {"ruleId": "1145", "severity": 1, "message": "1535", "line": 252, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 252, "endColumn": 35}, {"ruleId": "1145", "severity": 1, "message": "1536", "line": 257, "column": 9, "nodeType": "1147", "messageId": "1148", "endLine": 257, "endColumn": 27}, {"ruleId": "1145", "severity": 1, "message": "1397", "line": 1, "column": 29, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 38}, {"ruleId": "1145", "severity": 1, "message": "1450", "line": 1, "column": 40, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 48}, {"ruleId": "1145", "severity": 1, "message": "1397", "line": 1, "column": 29, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 38}, {"ruleId": "1145", "severity": 1, "message": "1450", "line": 1, "column": 40, "nodeType": "1147", "messageId": "1148", "endLine": 1, "endColumn": 48}, "@typescript-eslint/no-unused-vars", "'useContext' is defined but never used.", "Identifier", "unusedVar", "'logo' is defined but never used.", "'Button' is defined but never used.", "'TextField' is defined but never used.", "'ThreeCircles' is defined but never used.", "'PreferencesContext' is defined but never used.", "'theme' is defined but never used.", "'appToastConfig' is assigned a value but never used.", "'setAppToastConfig' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has an unnecessary dependency: 'loading'. Either exclude it or remove the dependency array.", "ArrayExpression", ["1537"], "React Hook useMemo has a missing dependency: 'setLoading'. Either include it or remove the dependency array.", ["1538"], "'setActiveMenuItem' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'activeMenuItem'. Either exclude it or remove the dependency array.", ["1539"], "React Hook useEffect has missing dependencies: 'location.pathname', 'loginMessage', and 'navigate'. Either include them or remove the dependency array.", ["1540"], "React Hook useEffect has a missing dependency: 'dispatchSessionExpired'. Either include it or remove the dependency array.", ["1541"], "React Hook useCallback has an unnecessary dependency: 'open'. Either exclude it or remove the dependency array.", ["1542"], ["1543"], "React Hook useCallback has an unnecessary dependency: 'toastConfig'. Either exclude it or remove the dependency array.", ["1544"], "React Hook useMemo has missing dependencies: 'setOpen', 'setToastConfig', and 'setToastMessage'. Either include them or remove the dependency array.", ["1545"], "'PaletteOptions' is defined but never used.", "'lookupReducer' is defined but never used.", "'LOGIN' is defined but never used.", "'createRef' is defined but never used.", "'axios' is defined but never used.", "'AnyAction' is defined but never used.", "'_applicationHelperService' is assigned a value but never used.", "'yup' is defined but never used.", "'Link' is defined but never used.", "'FormControl' is defined but never used.", "'FilledInput' is defined but never used.", "'InputLabel' is defined but never used.", "'MessageConstants' is defined but never used.", "'setOpen' is assigned a value but never used.", "'handleMouseDownPassword' is assigned a value but never used.", "'handleMouseUpPassword' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'title'. Either include it or remove the dependency array.", ["1546"], "'RevenueChartDashboard' is defined but never used.", "'Divider' is defined but never used.", "'Drawer' is defined but never used.", "'openqanda' is assigned a value but never used.", "'setOpenqanda' is assigned a value but never used.", "'_locationService' is assigned a value but never used.", "'businessGroupsOnBusiness' is assigned a value but never used.", "'paginationModel' is assigned a value but never used.", "'setInitialValues' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchLocationsPaginated', 'getBusiness', and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1547"], "'HomeChartCard' is defined but never used.", "'originalLocationData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchLocations', 'getBusiness', and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1548"], ["1549"], "'PageProps' is defined but never used.", "'ILoginModel' is defined but never used.", "'authInitiate' is defined but never used.", "'CalendarToday' is defined but never used.", "'ScheduleLater' is defined but never used.", "'GenericDrawer' is defined but never used.", "'date' is assigned a value but never used.", "'setDate' is assigned a value but never used.", "'scheduleForLater' is assigned a value but never used.", "'setScheduleForLater' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'location.state' and 'title'. Either include them or remove the dependency array.", ["1550"], "'checkFormValidity' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'response' is assigned a value but never used.", "'formatDayJsToISO' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'element' is assigned a value but never used.", "'RegisteredEmployeesChart' is defined but never used.", "'ActiveJobsChart' is defined but never used.", "'PieChart' is defined but never used.", "'GroupIcon' is defined but never used.", "'WorkHistoryIcon' is defined but never used.", "'EventAvailableIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'EditLocationAltIcon' is defined but never used.", "'HelpIcon' is defined but never used.", "'ArrowUpwardRoundedIcon' is defined but never used.", "'ArrowDownwardRoundedIcon' is defined but never used.", "'FormHelperText' is defined but never used.", "'Grid2' is defined but never used.", "'BusinessInteractionsChart' is defined but never used.", "'SearchQueriesList' is defined but never used.", "'rbAccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLocations'. Either include it or remove the dependency array.", ["1551"], "React Hook useEffect has a missing dependency: 'locationList'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedAccountId' needs the current value of 'locationList'.", ["1552"], "React Hook useEffect has missing dependencies: 'fetchAnalyticsData', 'locationList', and 'selectedAccountId'. Either include them or remove the dependency array.", ["1553"], "'IconButton' is defined but never used.", "'Dialog' is defined but never used.", "'AddIcon' is defined but never used.", "'ServicesDisplay' is defined but never used.", "'Accessibility' is defined but never used.", "'FlagIcon' is defined but never used.", "'ChatIcon' is defined but never used.", "'LanguageIcon' is defined but never used.", "'LocationOnIcon' is defined but never used.", "'useCallback' is defined but never used.", "'Container' is defined but never used.", "'activeTab' is assigned a value but never used.", "'setActiveTab' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadSavedConfigurations'. Either include it or remove the dependency array.", ["1554"], "'Modal' is defined but never used.", "'DeleteIcon' is defined but never used.", "'SendIcon' is defined but never used.", "'DeleteOutlineIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo.roleId'. Either include them or remove the dependency array.", ["1555"], "React Hook useEffect has a missing dependency: 'getRolesList'. Either include it or remove the dependency array.", ["1556"], "'SearchOffIcon' is defined but never used.", "'TableRowsRoundedIcon' is defined but never used.", "'IUsersListResponse' is defined but never used.", "'Grid' is defined but never used.", "'Stack' is defined but never used.", "'TablePagination' is defined but never used.", "'searchText' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsersPaginated'. Either include it or remove the dependency array.", ["1557"], "'rowsPerPage' is assigned a value but never used.", "'handleChangePage' is assigned a value but never used.", "'handleChangeRowsPerPage' is assigned a value but never used.", "'BlockOutlinedIcon' is defined but never used.", "'CheckCircleRoundedIcon' is defined but never used.", "'IBusinessListResponseModel' is defined but never used.", "'FormGroup' is defined but never used.", "'VerifiedIcon' is defined but never used.", "'PauseCircleFilledIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'setAlertConfig' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchBusinessPaginated', 'title', and 'userInfo'. Either include them or remove the dependency array.", ["1558"], "'List' is defined but never used.", "'ListItem' is defined but never used.", "'Switch' is defined but never used.", "'businessPreview' is defined but never used.", "'LockOpenIcon' is defined but never used.", "'useSelector' is defined but never used.", "'CancelOutlinedIcon' is defined but never used.", "'CampaignRoundedIcon' is defined but never used.", "'label' is assigned a value but never used.", "'StatusCardProps' is defined but never used.", "'progress' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId', 'businessId', 'getLocationSummary', and 'locationId'. Either include them or remove the dependency array.", ["1559"], "React Hook useEffect has a missing dependency: 'performMissingInformationOperation'. Either include it or remove the dependency array.", ["1560"], "'CardMedia' is defined but never used.", "'showConfirmPopup' is assigned a value but never used.", "'setShowConfirmPopup' is assigned a value but never used.", ["1561"], "React Hook useEffect has a missing dependency: 'fetchLocationsPaginated'. Either include it or remove the dependency array.", ["1562"], "React Hook useEffect has missing dependencies: 'getBusiness' and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1563"], "'FallingLines' is defined but never used.", "'RotatingLines' is defined but never used.", "'IDeleteRecord' is defined but never used.", "React Hook useEffect has a missing dependency: 'gmbCallBack'. Either include it or remove the dependency array.", ["1564"], "'StarBorderIcon' is defined but never used.", "'Avatar' is defined but never used.", "'StarRoundedIcon' is defined but never used.", "'ILocationListRequestModel' is defined but never used.", "'STARRATINGMAP' is defined but never used.", "'UserAvatar' is defined but never used.", "'Chip' is defined but never used.", "'SearchOutlinedIcon' is defined but never used.", "'InputAdornment' is defined but never used.", "'newestIcon' is assigned a value but never used.", "'oldestIcon' is assigned a value but never used.", "'highRatingIcon' is assigned a value but never used.", "'lowRatingIcon' is assigned a value but never used.", "'showScroll' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchLocationsPaginated', 'getAllTags', 'getBusiness', and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1565"], "'scrollToTop' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'AUTH_REQUESTED' is defined but never used.", "'AUTH_SUCCESS' is defined but never used.", "'AUTH_LOGOUT' is defined but never used.", "'AUTH_ERROR' is defined but never used.", "'AUTH_UNAUTHORIZED' is defined but never used.", "'IRoleBasedAccessResponseModel' is defined but never used.", "'ILoggedInUserResponseModel' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'Pagination' is defined but never used.", "'Paper' is defined but never used.", "'OutlinedInput' is defined but never used.", "'getIn' is defined but never used.", "React Hook useEffect has missing dependencies: 'getBusiness', 'getBusinessGroups', and 'getLocationsList'. Either include them or remove the dependency array.", ["1566"], "'MenuProps' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'logoutUser'. Either include it or remove the dependency array.", ["1567"], ["1568"], "'Component' is defined but never used.", "no-useless-escape", "Unnecessary escape character: \\..", "Literal", "unnecessaryEscape", ["1569", "1570"], ["1571", "1572"], "'Toolbar' is defined but never used.", "'Typography' is defined but never used.", "'MenuIcon' is defined but never used.", "'Menu' is defined but never used.", "'MenuItem' is defined but never used.", "'ManageAccountsIcon' is defined but never used.", "'Collapse' is defined but never used.", "'SettingsOutlinedIcon' is defined but never used.", "'ArrowForwardIosRoundedIcon' is defined but never used.", "'ListAltSharp' is defined but never used.", "'MapsUgcRoundedIcon' is defined but never used.", "'AppBar' is assigned a value but never used.", "'theme' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isMobile', 'menuOpened', 'openLeftMenu', 'rbAccess', and 'userInfo'. Either include them or remove the dependency array.", ["1573"], "'handleMenuItemClick' is assigned a value but never used.", "'AnalyticsRoutes' is assigned a value but never used.", "'GeoGridRoutes' is assigned a value but never used.", ["1574", "1575"], ["1576", "1577"], "'useEffect' is defined but never used.", "'useMemo' is defined but never used.", "'MONTHS' is assigned a value but never used.", "'PolarArea' is defined but never used.", "'Bar' is defined but never used.", "'ReviewService' is defined but never used.", "'title' is defined but never used.", "React Hook useEffect has missing dependencies: 'onDateChange' and 'selectedDuration'. Either include them or remove the dependency array. If 'onDateChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1578"], "'ListItemText' is defined but never used.", "'AppBar' is defined but never used.", "'RoleType' is defined but never used.", "'CloseIcon' is defined but never used.", "'ArrowBackIos' is defined but never used.", "'ArrowForwardIos' is defined but never used.", "'LinearProgressWithLabel' is defined but never used.", "'open' is assigned a value but never used.", "'selectedValue' is assigned a value but never used.", "'selectedOptions' is assigned a value but never used.", "'handleOpen' is assigned a value but never used.", "'handleClose' is assigned a value but never used.", "'handleDropdownChange' is assigned a value but never used.", "'handleMultiSelectChange' is assigned a value but never used.", "'currentIndex' is assigned a value but never used.", "'handleNext' is assigned a value but never used.", "'handlePrev' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getBusiness' and 'getLocationsList'. Either include them or remove the dependency array.", ["1579"], "React Hook useEffect has a missing dependency: 'initialValues'. Either include it or remove the dependency array.", ["1580"], "'LoadingContext' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSearchKeywords'. Either include it or remove the dependency array.", ["1581"], "React Hook useEffect has a missing dependency: 'fetchMoreData'. Either include it or remove the dependency array.", ["1582"], "'SAVE_SCHEDULED' is defined but never used.", "'IGoogleCreatePost' is defined but never used.", "'DialogContent' is defined but never used.", "'Box' is defined but never used.", "'ListItemButton' is defined but never used.", "'ArrowBackIcon' is defined but never used.", "'ChevronRightIcon' is defined but never used.", "'MoreVertIcon' is defined but never used.", "'Formik' is defined but never used.", "'Form' is defined but never used.", "'Category' is defined but never used.", "'DialogTitle' is defined but never used.", "'AdapterDayjs' is defined but never used.", "'LocalizationProvider' is defined but never used.", "'DatePicker' is defined but never used.", "'ZoomInIcon' is defined but never used.", "'LIST_OF_LOCATIONS' is defined but never used.", "'LIST_OF_ROLE' is defined but never used.", "'useState' is defined but never used.", "'useNavigate' is defined but never used.", "'IUserResponseModel' is defined but never used.", "'IUser' is defined but never used.", "'IAlertDialogConfig' is defined but never used.", "'logOut' is defined but never used.", "'AlertDialog' is defined but never used.", "'isEdit' is assigned a value but never used.", "'setIsEdit' is assigned a value but never used.", "React Hook useEffect has missing dependencies: '_userService', 'getBusiness', 'getBusinessGroups', 'getLocationsList', and 'props.editData'. Either include them or remove the dependency array.", ["1583"], ["1584"], "'LIST_OF_BUSINESS' is defined but never used.", "'navigate' is assigned a value but never used.", "'Badge' is defined but never used.", "'Select' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'IRole' is defined but never used.", "'IBusinessGroup' is defined but never used.", "'ILocation' is defined but never used.", "'usersList' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["1585"], "array-callback-return", "Array.prototype.map() expects a value to be returned at the end of arrow function.", "ArrowFunctionExpression", "expectedAtEnd", "'CircularProgress' is defined but never used.", "'MediaGallery' is defined but never used.", "'MISSING_INFORMATION' is defined but never used.", "'IconOnAvailability' is defined but never used.", "'LocationOnRoundedIcon' is defined but never used.", "'MovieIcon' is defined but never used.", "'AccountCircleIcon' is defined but never used.", "'NearMeOutlinedIcon' is defined but never used.", "'handleOpenMap' is assigned a value but never used.", "'FunctionComponent' is defined but never used.", "'ThumbUpAltRoundedIcon' is defined but never used.", "'FeedbackTemplate' is defined but never used.", "'FeedbackCard' is defined but never used.", "'CssBaseline' is defined but never used.", "'ImageBackgroundCard' is defined but never used.", "React Hook useEffect has missing dependencies: 'props.review.review', 'props.review.reviewerName', 'props.review.reviewerProfilePic', and 'props.review.starRating'. Either include them or remove the dependency array. If 'setPostTemplateConfig' needs the current value of 'props.review.review', you can also switch to useReducer instead of useState and read 'props.review.review' in the reducer.", ["1586"], "React Hook useEffect has a missing dependency: 'getAllTags'. Either include it or remove the dependency array.", ["1587"], "'createTag' is assigned a value but never used.", "'updateTagsToReviewResponse' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'size'. Either include it or remove the dependency array.", ["1588"], "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "'EditIcon' is defined but never used.", "'navigationType' is assigned a value but never used.", "'userInfo' is assigned a value but never used.", "'isValidElement' is defined but never used.", "'activeMenuItem' is assigned a value but never used.", "'LogoutIcon' is defined but never used.", "'logoutUser' is assigned a value but never used.", "'openSubMenu' is assigned a value but never used.", "React Hook useEffect has missing dependencies: '_locationService', 'props.mediaItems', and 'setLoading'. Either include them or remove the dependency array.", ["1589"], "React Hook useEffect has a missing dependency: 'props.profileImage'. Either include it or remove the dependency array.", ["1590"], "'fontType' is assigned a value but never used.", "'setFontType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'props'. Either include it or remove the dependency array. However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the useEffect call and refer to those specific props inside useEffect.", ["1591"], ["1592"], "'ref' is defined but never used.", "'Rating' is defined but never used.", "'StarIcon' is defined but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'availableLocations' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", "'SettingsIcon' is defined but never used.", "'ICreateReplyTemplateRequest' is defined but never used.", "'openAutoReplyDrawer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadBusinesses'. Either include it or remove the dependency array.", ["1593"], "React Hook useEffect has missing dependencies: 'loadAutoReplySettings' and 'loadTemplates'. Either include them or remove the dependency array.", ["1594"], "'handleAutoReplyDrawerClose' is assigned a value but never used.", "'getStarRatingColor' is assigned a value but never used.", {"desc": "1595", "fix": "1596"}, {"desc": "1597", "fix": "1598"}, {"desc": "1595", "fix": "1599"}, {"desc": "1600", "fix": "1601"}, {"desc": "1602", "fix": "1603"}, {"desc": "1595", "fix": "1604"}, {"desc": "1595", "fix": "1605"}, {"desc": "1595", "fix": "1606"}, {"desc": "1607", "fix": "1608"}, {"desc": "1609", "fix": "1610"}, {"desc": "1611", "fix": "1612"}, {"desc": "1613", "fix": "1614"}, {"kind": "1615", "justification": "1616"}, {"desc": "1617", "fix": "1618"}, {"desc": "1619", "fix": "1620"}, {"desc": "1621", "fix": "1622"}, {"desc": "1623", "fix": "1624"}, {"desc": "1625", "fix": "1626"}, {"desc": "1627", "fix": "1628"}, {"desc": "1629", "fix": "1630"}, {"desc": "1631", "fix": "1632"}, {"desc": "1633", "fix": "1634"}, {"desc": "1635", "fix": "1636"}, {"desc": "1637", "fix": "1638"}, {"desc": "1609", "fix": "1639"}, {"desc": "1640", "fix": "1641"}, {"desc": "1642", "fix": "1643"}, {"desc": "1644", "fix": "1645"}, {"desc": "1646", "fix": "1647"}, {"desc": "1648", "fix": "1649"}, {"desc": "1650", "fix": "1651"}, {"desc": "1650", "fix": "1652"}, {"messageId": "1653", "fix": "1654", "desc": "1655"}, {"messageId": "1656", "fix": "1657", "desc": "1658"}, {"messageId": "1653", "fix": "1659", "desc": "1655"}, {"messageId": "1656", "fix": "1660", "desc": "1658"}, {"desc": "1661", "fix": "1662"}, {"messageId": "1653", "fix": "1663", "desc": "1655"}, {"messageId": "1656", "fix": "1664", "desc": "1658"}, {"messageId": "1653", "fix": "1665", "desc": "1655"}, {"messageId": "1656", "fix": "1666", "desc": "1658"}, {"desc": "1667", "fix": "1668"}, {"desc": "1669", "fix": "1670"}, {"desc": "1671", "fix": "1672"}, {"desc": "1673", "fix": "1674"}, {"desc": "1675", "fix": "1676"}, {"desc": "1677", "fix": "1678"}, {"desc": "1629", "fix": "1679"}, {"desc": "1680", "fix": "1681"}, {"desc": "1682", "fix": "1683"}, {"desc": "1684", "fix": "1685"}, {"desc": "1686", "fix": "1687"}, {"desc": "1688", "fix": "1689"}, {"desc": "1690", "fix": "1691"}, {"desc": "1692", "fix": "1693"}, {"desc": "1692", "fix": "1694"}, {"desc": "1695", "fix": "1696"}, {"desc": "1697", "fix": "1698"}, "Update the dependencies array to be: []", {"range": "1699", "text": "1700"}, "Update the dependencies array to be: [loading, setLoading]", {"range": "1701", "text": "1702"}, {"range": "1703", "text": "1700"}, "Update the dependencies array to be: [userInfo, success, location.pathname, navigate, loginMessage]", {"range": "1704", "text": "1705"}, "Update the dependencies array to be: [dispatchSessionExpired, isUnAuthorised]", {"range": "1706", "text": "1707"}, {"range": "1708", "text": "1700"}, {"range": "1709", "text": "1700"}, {"range": "1710", "text": "1700"}, "Update the dependencies array to be: [open, setOpen, message, setToastMessage, toastConfig, setToastConfig]", {"range": "1711", "text": "1712"}, "Update the dependencies array to be: [title]", {"range": "1713", "text": "1714"}, "Update the dependencies array to be: [fetchLocationsPaginated, getBusiness, getBusinessGroups]", {"range": "1715", "text": "1716"}, "Update the dependencies array to be: [fetchLocations, getBusiness, getBusinessGroups]", {"range": "1717", "text": "1718"}, "directive", "", "Update the dependencies array to be: [location.state, title]", {"range": "1719", "text": "1720"}, "Update the dependencies array to be: [fetchLocations]", {"range": "1721", "text": "1722"}, "Update the dependencies array to be: [locationList, selectedLocationId]", {"range": "1723", "text": "1724"}, "Update the dependencies array to be: [selectedLocationId, selectedDateRange, selectedAccountId, fetchAnalyticsData, locationList]", {"range": "1725", "text": "1726"}, "Update the dependencies array to be: [loadSavedConfigurations, title, user]", {"range": "1727", "text": "1728"}, "Update the dependencies array to be: [navigate, userInfo.roleId]", {"range": "1729", "text": "1730"}, "Update the dependencies array to be: [getRolesList]", {"range": "1731", "text": "1732"}, "Update the dependencies array to be: [fetchUsersPaginated, paginationModel]", {"range": "1733", "text": "1734"}, "Update the dependencies array to be: [fetchBusinessPaginated, title, userInfo]", {"range": "1735", "text": "1736"}, "Update the dependencies array to be: [accountId, businessId, getLocationSummary, locationId]", {"range": "1737", "text": "1738"}, "Update the dependencies array to be: [locationSummary, performMissingInformationOperation]", {"range": "1739", "text": "1740"}, {"range": "1741", "text": "1714"}, "Update the dependencies array to be: [fetchLocationsPaginated, paginationModel]", {"range": "1742", "text": "1743"}, "Update the dependencies array to be: [getBusiness, getBusinessGroups]", {"range": "1744", "text": "1745"}, "Update the dependencies array to be: [gmbCallBack, searchParams]", {"range": "1746", "text": "1747"}, "Update the dependencies array to be: [fetchLocationsPaginated, getAllTags, getBusiness, getBusinessGroups]", {"range": "1748", "text": "1749"}, "Update the dependencies array to be: [getBusiness, getBusinessGroups, getLocationsList]", {"range": "1750", "text": "1751"}, "Update the dependencies array to be: [logoutUser, navigate]", {"range": "1752", "text": "1753"}, {"range": "1754", "text": "1753"}, "removeEscape", {"range": "1755", "text": "1616"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1756", "text": "1757"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1758", "text": "1616"}, {"range": "1759", "text": "1757"}, "Update the dependencies array to be: [isMobile, menuOpened, openLeftMenu, rbAccess, userInfo]", {"range": "1760", "text": "1761"}, {"range": "1762", "text": "1616"}, {"range": "1763", "text": "1757"}, {"range": "1764", "text": "1616"}, {"range": "1765", "text": "1757"}, "Update the dependencies array to be: [onDateChange, selectedDuration]", {"range": "1766", "text": "1767"}, "Update the dependencies array to be: [getBusiness, getLocationsList]", {"range": "1768", "text": "1769"}, "Update the dependencies array to be: [initialValues.locationId, initialValues.accountId, locations, initialValues]", {"range": "1770", "text": "1771"}, "Update the dependencies array to be: [accountId, locationId, from, to, fetchSearchKeywords]", {"range": "1772", "text": "1773"}, "Update the dependencies array to be: [nextPageToken, loading, open, fetchMoreData]", {"range": "1774", "text": "1775"}, "Update the dependencies array to be: [_userService, getBusiness, getBusinessGroups, getLocationsList, props.editData]", {"range": "1776", "text": "1777"}, {"range": "1778", "text": "1732"}, "Update the dependencies array to be: [fetchUsers]", {"range": "1779", "text": "1780"}, "Update the dependencies array to be: [props.review.review, props.review.reviewerName, props.review.reviewerProfilePic, props.review.starRating]", {"range": "1781", "text": "1782"}, "Update the dependencies array to be: [getAllTags]", {"range": "1783", "text": "1784"}, "Update the dependencies array to be: [size]", {"range": "1785", "text": "1786"}, "Update the dependencies array to be: [_locationService, props.mediaItems, setLoading]", {"range": "1787", "text": "1788"}, "Update the dependencies array to be: [props.profileImage]", {"range": "1789", "text": "1790"}, "Update the dependencies array to be: [postTemplateConfig, props]", {"range": "1791", "text": "1792"}, {"range": "1793", "text": "1792"}, "Update the dependencies array to be: [loadBusinesses, userInfo]", {"range": "1794", "text": "1795"}, "Update the dependencies array to be: [loadAutoReplySettings, loadTemplates, selectedBusiness, userInfo]", {"range": "1796", "text": "1797"}, [3651, 3660], "[]", [3764, 3773], "[loading, setLoading]", [4054, 4070], [4729, 4748], "[userInfo, success, location.pathname, navigate, loginMessage]", [5000, 5016], "[dispatchSessionExpired, isUnAuthorised]", [5531, 5537], [5662, 5668], [5851, 5864], [6043, 6071], "[open, setOpen, message, setToastMessage, toastConfig, setToastConfig]", [3057, 3059], "[title]", [5471, 5473], "[fetchLocationsPaginated, getBusiness, getBusinessGroups]", [5424, 5426], "[fetchLocations, getBusiness, getBusinessGroups]", [9218, 9220], "[location.state, title]", [4843, 4845], "[fetchLocations]", [5049, 5069], "[locationList, selectedLocationId]", [5446, 5485], "[selectedLocationId, selectedDateRange, selectedAccountId, fetchAnalyticsData, locationList]", [2227, 2240], "[loadSavedConfigurations, title, user]", [3559, 3561], "[navigate, userInfo.roleId]", [3614, 3616], "[getRolesList]", [5200, 5217], "[fetchUsersPaginated, paginationModel]", [5187, 5189], "[fetchBusinessPaginated, title, userInfo]", [5063, 5065], "[accountId, businessId, getLocationSummary, locationId]", [8294, 8311], "[locationSummary, performMissingInformationOperation]", [5560, 5562], [5626, 5643], "[fetchLocationsPaginated, paginationModel]", [5820, 5822], "[getBusiness, getBusinessGroups]", [1990, 2004], "[gmbCallBack, searchParams]", [7854, 7856], "[fetchLocationsPaginated, getAllTags, getBusiness, getBusinessGroups]", [4079, 4081], "[getBusiness, getBusinessGroups, getLocationsList]", [931, 941], "[logo<PERSON><PERSON><PERSON>, navigate]", [932, 942], [190, 191], [190, 190], "\\", [206, 207], [206, 206], [6454, 6456], "[isMobile, menuOpened, openLeftMenu, rbAccess, userInfo]", [296, 297], [296, 296], [312, 313], [312, 312], [1857, 1859], "[onDate<PERSON><PERSON>e, selectedDuration]", [6900, 6902], "[getBusiness, getLocationsList]", [7650, 7712], "[initialValues.locationId, initialValues.accountId, locations, initialValues]", [1576, 1609], "[accountId, locationId, from, to, fetchSearchKeywords]", [3879, 3909], "[nextPageToken, loading, open, fetchMoreData]", [5857, 5859], "[_userService, getBusiness, getBusinessGroups, getLocationsList, props.editData]", [12623, 12625], [3571, 3573], "[fetchUsers]", [3011, 3013], "[props.review.review, props.review.reviewerName, props.review.reviewerProfilePic, props.review.starRating]", [2469, 2471], "[getAllTags]", [511, 513], "[size]", [1430, 1432], "[_locationService, props.mediaItems, setLoading]", [1159, 1161], "[props.profileImage]", [1250, 1270], "[postTemplateConfig, props]", [6475, 6495], [3585, 3595], "[loadBusinesses, userInfo]", [3728, 3756], "[loadAutoReplySettings, loadTemplates, selectedBusiness, userInfo]"]