require('dotenv').config({ path: '.env.development' });
const pool = require('../config/db');
const fs = require('fs');
const path = require('path');

async function setupPostsTable() {
  console.log('🚀 Setting up GMB Posts Table...');
  console.log(`📅 Timestamp: ${new Date().toISOString()}`);
  console.log();
  
  try {
    // Test database connection
    console.log('🔗 Connecting to database...');
    await pool.query('SELECT 1');
    console.log('✅ Database connection established!');
    console.log();
    
    // Check if table already exists
    console.log('🔍 Checking if gmb_posts table exists...');
    const tableCheck = await pool.query(`
      SELECT COUNT(*) as table_exists 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = 'gmb_posts'
    `, [process.env.APP_DB_NAME]);
    
    if (tableCheck[0].table_exists > 0) {
      console.log('⚠️ gmb_posts table already exists');
      console.log('🔍 Checking table structure...');
      
      // Get current table structure
      const tableInfo = await pool.query('DESCRIBE gmb_posts');
      console.log('📋 Current table structure:');
      tableInfo.forEach(column => {
        console.log(`   - ${column.Field}: ${column.Type} ${column.Null === 'NO' ? 'NOT NULL' : 'NULL'}`);
      });
      
      // Get row count
      const countResult = await pool.query('SELECT COUNT(*) as row_count FROM gmb_posts');
      console.log(`📊 Current rows: ${countResult[0].row_count}`);
      console.log();
      console.log('✅ Table verification completed!');
      console.log('💡 If you need to recreate the table, drop it first: DROP TABLE gmb_posts;');
      return;
    }
    
    // Read and execute SQL file
    console.log('🏗️ Creating gmb_posts table...');
    const sqlFilePath = path.join(__dirname, 'create_posts_table.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split SQL content by semicolons and execute each statement
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of statements) {
      if (statement.trim()) {
        await pool.query(statement);
      }
    }
    
    console.log('✅ gmb_posts table created successfully!');
    console.log();
    
    // Verify table creation
    console.log('🔍 Verifying table creation...');
    const verifyResult = await pool.query(`
      SELECT COUNT(*) as table_exists 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = 'gmb_posts'
    `, [process.env.APP_DB_NAME]);
    
    if (verifyResult[0].table_exists > 0) {
      console.log('✅ Table verification successful!');
      
      // Show table structure
      const tableInfo = await pool.query('DESCRIBE gmb_posts');
      console.log('📋 Table structure:');
      tableInfo.forEach(column => {
        console.log(`   - ${column.Field}: ${column.Type} ${column.Null === 'NO' ? 'NOT NULL' : 'NULL'}`);
      });
      
      // Show indexes
      console.log('📊 Table indexes:');
      const indexInfo = await pool.query('SHOW INDEX FROM gmb_posts');
      const uniqueIndexes = [...new Set(indexInfo.map(idx => idx.Key_name))];
      uniqueIndexes.forEach(indexName => {
        console.log(`   - ${indexName}`);
      });
      
    } else {
      throw new Error('Table creation verification failed');
    }
    
    console.log();
    console.log('🎉 GMB Posts table setup completed successfully!');
    console.log();
    console.log('🎯 Next steps:');
    console.log('   1. Start the backend server: npm start');
    console.log('   2. Test the posts API endpoints');
    console.log('   3. Create posts through the frontend');
    console.log();
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    console.error('🔧 Error details:', error);
    process.exit(1);
  }
}

setupPostsTable();
