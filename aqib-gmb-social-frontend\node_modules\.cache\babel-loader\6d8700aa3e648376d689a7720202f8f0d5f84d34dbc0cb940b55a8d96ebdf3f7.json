{"ast": null, "code": "import HttpHelperService from \"../httpHelper.service\";\nimport { PERFORMANCE_LOCATIONMETRICS, PERFORMANCE_SEARCHKEYWORDS } from \"../../constants/endPoints.constant\";\nclass LocationMetricsService {\n  constructor(dispatch) {\n    this._httpHelperService = void 0;\n    this.getLocationMetrics = async (requestBody, headerObject) => {\n      return await this._httpHelperService.post(PERFORMANCE_LOCATIONMETRICS, requestBody, headerObject);\n    };\n    this.getSearchkeywords = async (requestBody, headerObject) => {\n      return await this._httpHelperService.post(PERFORMANCE_SEARCHKEYWORDS, requestBody, headerObject);\n    };\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n}\nexport default LocationMetricsService;", "map": {"version": 3, "names": ["HttpHelperService", "PERFORMANCE_LOCATIONMETRICS", "PERFORMANCE_SEARCHKEYWORDS", "LocationMetricsService", "constructor", "dispatch", "_httpHelperService", "getLocationMetrics", "requestBody", "headerObject", "post", "getSearchkeywords"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/locationMetrics/locationMetrics.service.tsx"], "sourcesContent": ["import { Dispatch } from \"react\";\nimport HttpHelperService from \"../httpHelper.service\";\nimport {\n  PERFORMANCE_LOCATIONMETRICS,\n  PERFORMANCE_SEARCHKEYWORDS,\n} from \"../../constants/endPoints.constant\";\nimport { Action } from \"redux\";\nimport { ILocationMetricsRequestModel } from \"../../interfaces/request/ILocationMetricsRequestModel\";\n\nclass LocationMetricsService {\n  _httpHelperService;\n  constructor(dispatch: Dispatch<Action>) {\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  getLocationMetrics = async (\n    requestBody: ILocationMetricsRequestModel,\n    headerObject: any\n  ) => {\n    return await this._httpHelperService.post(\n      PERFORMANCE_LOCATIONMETRICS,\n      requestBody,\n      headerObject\n    );\n  };\n\n  getSearchkeywords = async (\n    requestBody: ILocationMetricsRequestModel,\n    headerObject: any\n  ) => {\n    return await this._httpHelperService.post(\n      PERFORMANCE_SEARCHKEYWORDS,\n      requestBody,\n      headerObject\n    );\n  };\n}\n\nexport default LocationMetricsService;\n"], "mappings": "AACA,OAAOA,iBAAiB,MAAM,uBAAuB;AACrD,SACEC,2BAA2B,EAC3BC,0BAA0B,QACrB,oCAAoC;AAI3C,MAAMC,sBAAsB,CAAC;EAE3BC,WAAWA,CAACC,QAA0B,EAAE;IAAA,KADxCC,kBAAkB;IAAA,KAKlBC,kBAAkB,GAAG,OACnBC,WAAyC,EACzCC,YAAiB,KACd;MACH,OAAO,MAAM,IAAI,CAACH,kBAAkB,CAACI,IAAI,CACvCT,2BAA2B,EAC3BO,WAAW,EACXC,YACF,CAAC;IACH,CAAC;IAAA,KAEDE,iBAAiB,GAAG,OAClBH,WAAyC,EACzCC,YAAiB,KACd;MACH,OAAO,MAAM,IAAI,CAACH,kBAAkB,CAACI,IAAI,CACvCR,0BAA0B,EAC1BM,WAAW,EACXC,YACF,CAAC;IACH,CAAC;IAvBC,IAAI,CAACH,kBAAkB,GAAG,IAAIN,iBAAiB,CAACK,QAAQ,CAAC;EAC3D;AAuBF;AAEA,eAAeF,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}