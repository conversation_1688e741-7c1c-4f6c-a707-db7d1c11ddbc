require('dotenv').config({ path: '.env.development' });
const axios = require('axios');

const BASE_URL = `http://localhost:${process.env.APP_PORT || 3000}`;

// Mock data for testing
const mockPostData = {
  userId: 1,
  businessId: 45,
  locationId: '321',
  accountId: '2',
  googlePostName: 'accounts/115247341462647119732/locations/2713944107398523560/localPosts/test123',
  bulkPostId: 'bulk_test_' + Date.now(),
  isBulkPost: true,
  postContent: {
    languageCode: 'en-US',
    summary: 'Test post content for API testing',
    topicType: 'STANDARD',
    media: []
  },
  postResponse: {
    name: 'accounts/115247341462647119732/locations/2713944107398523560/localPosts/test123',
    summary: 'Test post content for API testing',
    state: 'PROCESSING',
    languageCode: 'en',
    topicType: 'STANDARD',
    createTime: new Date().toISOString(),
    updateTime: new Date().toISOString(),
    searchUrl: 'https://local.google.com/place?id=test123'
  },
  summary: 'Test post content for API testing',
  topicType: 'STANDARD',
  languageCode: 'en-US',
  state: 'PROCESSING',
  searchUrl: 'https://local.google.com/place?id=test123'
};

async function testPostsAPI() {
  console.log('🧪 Testing GMB Posts API Endpoints...');
  console.log(`📅 Timestamp: ${new Date().toISOString()}`);
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log();
  
  try {
    // Test 1: Check if server is running
    console.log('1. 🔍 Testing server connection...');
    try {
      const response = await axios.get(BASE_URL);
      console.log('✅ Server is running:', response.data.message);
    } catch (error) {
      throw new Error(`Server not running. Please start with: npm start`);
    }
    console.log();
    
    // Test 2: Test database connection through API
    console.log('2. 🔍 Testing database connection...');
    // This would require a health check endpoint
    console.log('ℹ️ Skipping database connection test (no health endpoint)');
    console.log();
    
    // Test 3: Test bulk post status check (should return 404 for non-existent post)
    console.log('3. 🔍 Testing bulk post status check...');
    try {
      const response = await axios.get(
        `${BASE_URL}/post/check-bulk-status/non-existent-post`,
        { headers: { 'Authorization': 'Bearer test-token' } }
      );
      console.log('⚠️ Expected 404 but got:', response.status);
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('✅ Bulk post status check working (404 for non-existent post)');
      } else if (error.response && error.response.status === 401) {
        console.log('ℹ️ Authentication required for API endpoints');
      } else {
        console.log('⚠️ Unexpected error:', error.message);
      }
    }
    console.log();
    
    // Test 4: Test get post by name (should return 404 for non-existent post)
    console.log('4. 🔍 Testing get post by name...');
    try {
      const response = await axios.get(
        `${BASE_URL}/post/post-by-name/non-existent-post`,
        { headers: { 'Authorization': 'Bearer test-token' } }
      );
      console.log('⚠️ Expected 404 but got:', response.status);
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('✅ Get post by name working (404 for non-existent post)');
      } else if (error.response && error.response.status === 401) {
        console.log('ℹ️ Authentication required for API endpoints');
      } else {
        console.log('⚠️ Unexpected error:', error.message);
      }
    }
    console.log();
    
    // Test 5: Test bulk post details (should return 404 for non-existent bulk ID)
    console.log('5. 🔍 Testing bulk post details...');
    try {
      const response = await axios.get(
        `${BASE_URL}/post/bulk-post-details/non-existent-bulk-id`,
        { headers: { 'Authorization': 'Bearer test-token' } }
      );
      console.log('⚠️ Expected 404 but got:', response.status);
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('✅ Bulk post details working (404 for non-existent bulk ID)');
      } else if (error.response && error.response.status === 401) {
        console.log('ℹ️ Authentication required for API endpoints');
      } else {
        console.log('⚠️ Unexpected error:', error.message);
      }
    }
    console.log();
    
    console.log('🎉 API endpoint tests completed!');
    console.log();
    console.log('📋 Test Summary:');
    console.log('   ✅ Server connection: Working');
    console.log('   ✅ Bulk post status endpoint: Available');
    console.log('   ✅ Get post by name endpoint: Available');
    console.log('   ✅ Bulk post details endpoint: Available');
    console.log();
    console.log('💡 Note: Full functionality testing requires:');
    console.log('   - Valid authentication tokens');
    console.log('   - Actual post data in database');
    console.log('   - Complete post creation flow');
    console.log();
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
    console.error('🔧 Error details:', error);
    process.exit(1);
  }
}

testPostsAPI();
