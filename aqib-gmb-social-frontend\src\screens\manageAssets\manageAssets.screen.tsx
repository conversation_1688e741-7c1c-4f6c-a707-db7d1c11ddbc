import React, {
  FunctionComponent,
  useContext,
  useEffect,
  useState,
} from "react";
import PageProps from "../../models/PageProps.interface";
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  LinearProgress,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Pagination,
  Alert,
  CircularProgress,
} from "@mui/material";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import { useSelector, useDispatch } from "react-redux";
import { LoadingContext } from "../../context/loading.context";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import ImageIcon from "@mui/icons-material/Image";
import VideoLibraryIcon from "@mui/icons-material/VideoLibrary";
import StorageIcon from "@mui/icons-material/Storage";
import FileUploadComponent from "./components/fileUpload.component";
import FileGalleryComponent from "./components/fileGallery.component";
import FileViewerComponent from "./components/fileViewer.component";
import ConfirmDeleteComponent from "./components/confirmDelete.component";
import ManageAssetsService from "../../services/manageAssets/manageAssets.service";
import BusinessService from "../../services/business/business.service";
import { FileUtils } from "../../utils/fileUtils";

// CSS Import
import "./manageAssets.screen.style.css";

interface IAsset {
  id: number;
  business_id: number;
  user_id: number;
  file_name: string;
  original_file_name: string;
  file_type: "image" | "video";
  file_size: number;
  s3_key: string;
  s3_url: string;
  mime_type: string;
  upload_date: string;
  status: string;
  uploaded_by_name?: string;
  thumbnail_s3_url?: string;
}

interface IStorageInfo {
  totalSizeMB: string;
  maxSizeMB: number;
  usagePercentage: string;
}

interface IPagination {
  totalRecords: number;
  pageCount: number;
  currentPage: number;
  recordsPerPage: number;
}

const ManageAssets: FunctionComponent<PageProps> = ({ title }) => {
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const { setLoading } = useContext(LoadingContext);
  const { setToastConfig } = useContext(ToastContext);
  const dispatch = useDispatch();

  // Initialize services
  const manageAssetsService = new ManageAssetsService(dispatch);
  const businessService = new BusinessService(dispatch);

  // State management
  const [assets, setAssets] = useState<IAsset[]>([]);
  const [businesses, setBusinesses] = useState<any[]>([]);
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(
    null
  );
  const [storageInfo, setStorageInfo] = useState<IStorageInfo | null>(null);
  const [pagination, setPagination] = useState<IPagination>({
    totalRecords: 0,
    pageCount: 0,
    currentPage: 1,
    recordsPerPage: 12,
  });
  const [uploading, setUploading] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<IAsset | null>(null);
  const [viewerOpen, setViewerOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [assetToDelete, setAssetToDelete] = useState<IAsset | null>(null);

  // Load businesses on component mount
  useEffect(() => {
    loadBusinesses();
  }, []);

  // Load assets when business is selected
  useEffect(() => {
    if (selectedBusinessId) {
      loadAssets(1);
    }
  }, [selectedBusinessId]);

  const loadBusinesses = async () => {
    try {
      setLoading(true);
      const response = await businessService.getBusiness(userInfo.id);
      if (response.list && response.list.length > 0) {
        setBusinesses(response.list);
        // Auto-select first business if only one exists
        if (response.list.length === 1) {
          setSelectedBusinessId(response.list[0].id);
        }
      }
    } catch (error: any) {
      setToastConfig(ToastSeverity.Error, "Failed to load businesses", true);
    } finally {
      setLoading(false);
    }
  };

  const loadAssets = async (page: number = 1) => {
    if (!selectedBusinessId) return;

    try {
      setLoading(true);
      const response = await manageAssetsService.getAssets(
        selectedBusinessId,
        page,
        pagination.recordsPerPage
      );

      if (response.success) {
        setAssets(response.data || []);
        setStorageInfo(response.storageInfo);
        setPagination({
          ...response.pagination,
          currentPage: page,
        });
      } else {
        setAssets([]);
        setStorageInfo(null);
      }
    } catch (error: any) {
      setToastConfig(ToastSeverity.Error, "Failed to load assets", true);
      setAssets([]);
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (files: FileList) => {
    if (!selectedBusinessId) {
      setToastConfig(
        ToastSeverity.Warning,
        "Please select a business first",
        true
      );
      return;
    }

    try {
      setUploading(true);
      const response = await manageAssetsService.uploadAssets(
        selectedBusinessId,
        files
      );

      if (response.success) {
        setToastConfig(
          ToastSeverity.Success,
          `${response.uploadedAssets?.length || 0} files uploaded successfully`,
          true
        );

        if (response.errors && response.errors.length > 0) {
          response.errors.forEach((error: any) => {
            setToastConfig(
              ToastSeverity.Warning,
              `${error.fileName}: ${error.error}`,
              true
            );
          });
        }

        // Reload assets
        loadAssets(pagination.currentPage);
      } else {
        setToastConfig(
          ToastSeverity.Error,
          response.message || "Upload failed",
          true
        );
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.response?.data?.message || "Upload failed",
        true
      );
    } finally {
      setUploading(false);
    }
  };

  const handleDeleteAsset = async (asset: IAsset) => {
    setAssetToDelete(asset);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!assetToDelete) return;

    try {
      setLoading(true);
      const response = await manageAssetsService.deleteAsset(assetToDelete.id);

      if (response.success) {
        setToastConfig(
          ToastSeverity.Success,
          "Asset deleted successfully",
          true
        );
        loadAssets(pagination.currentPage);
      } else {
        setToastConfig(
          ToastSeverity.Error,
          response.message || "Delete failed",
          true
        );
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.response?.data?.message || "Delete failed",
        true
      );
    } finally {
      setLoading(false);
      setDeleteDialogOpen(false);
      setAssetToDelete(null);
    }
  };

  const handleViewAsset = (asset: IAsset) => {
    setSelectedAsset(asset);
    setViewerOpen(true);
  };

  const handlePageChange = (
    event: React.ChangeEvent<unknown>,
    page: number
  ) => {
    loadAssets(page);
  };

  const getStorageUsageColor = (
    percentage: number
  ): "success" | "warning" | "error" => {
    if (percentage < 70) return "success";
    if (percentage < 90) return "warning";
    return "error";
  };

  return (
    <div>
      <Box>
        <LeftMenuComponent>
          <Box>
            <Box sx={{ marginBottom: "20px" }}>
              <h3 className="pageTitle">Manage Assets</h3>
              <Typography variant="subtitle2" className="subtitle2">
                Upload, view, and manage your business assets (images and
                videos)
              </Typography>
            </Box>

            {/* Business Selection */}
            {businesses.length > 1 && (
              <Card sx={{ marginBottom: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Select Business
                  </Typography>
                  <Grid container spacing={2}>
                    {businesses.map((business) => (
                      <Grid item key={business.id}>
                        <Button
                          variant={
                            selectedBusinessId === business.id
                              ? "contained"
                              : "outlined"
                          }
                          onClick={() => setSelectedBusinessId(business.id)}
                        >
                          {business.businessName}
                        </Button>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>
            )}

            {selectedBusinessId && (
              <>
                {/* Storage Info */}
                {storageInfo && (
                  <Card sx={{ marginBottom: 3 }}>
                    <CardContent>
                      <Box
                        display="flex"
                        alignItems="center"
                        gap={2}
                        marginBottom={2}
                      >
                        <StorageIcon color="primary" />
                        <Typography variant="h6">Storage Usage</Typography>
                      </Box>
                      <Box marginBottom={2}>
                        <LinearProgress
                          variant="determinate"
                          value={Math.min(
                            parseFloat(storageInfo.usagePercentage),
                            100
                          )}
                          color={getStorageUsageColor(
                            parseFloat(storageInfo.usagePercentage)
                          )}
                          sx={{ height: 10, borderRadius: 5 }}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {storageInfo.totalSizeMB} MB used of{" "}
                        {storageInfo.maxSizeMB} MB (
                        {storageInfo.usagePercentage}%)
                      </Typography>
                    </CardContent>
                  </Card>
                )}

                {/* File Upload */}
                <FileUploadComponent
                  onFileUpload={handleFileUpload}
                  uploading={uploading}
                  maxSizeMB={storageInfo?.maxSizeMB || 1024}
                  currentUsageMB={parseFloat(storageInfo?.totalSizeMB || "0")}
                />

                {/* Assets Gallery */}
                <FileGalleryComponent
                  assets={assets}
                  onViewAsset={handleViewAsset}
                  onDeleteAsset={handleDeleteAsset}
                  formatFileSize={FileUtils.formatFileSize}
                />

                {/* Pagination */}
                {pagination.pageCount > 1 && (
                  <Box display="flex" justifyContent="center" marginTop={3}>
                    <Pagination
                      count={pagination.pageCount}
                      page={pagination.currentPage}
                      onChange={handlePageChange}
                      color="primary"
                      size="large"
                    />
                  </Box>
                )}
              </>
            )}

            {!selectedBusinessId && businesses.length === 0 && (
              <Alert severity="info">
                No businesses found. Please add a business first.
              </Alert>
            )}

            {/* File Viewer Dialog */}
            <FileViewerComponent
              asset={selectedAsset}
              open={viewerOpen}
              onClose={() => {
                setViewerOpen(false);
                setSelectedAsset(null);
              }}
            />

            {/* Delete Confirmation Dialog */}
            <ConfirmDeleteComponent
              open={deleteDialogOpen}
              title="Delete Asset"
              message={`Are you sure you want to delete "${assetToDelete?.original_file_name}"? This action cannot be undone.`}
              onConfirm={confirmDelete}
              onCancel={() => {
                setDeleteDialogOpen(false);
                setAssetToDelete(null);
              }}
            />
          </Box>
        </LeftMenuComponent>
      </Box>
    </div>
  );
};

export default ManageAssets;
