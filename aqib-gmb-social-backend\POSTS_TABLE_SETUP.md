# GMB Posts Table Setup Guide

This guide provides step-by-step instructions for setting up the `gmb_posts` table for bulk post management functionality.

## Prerequisites

- Node.js installed
- Access to the AWS RDS MySQL database
- `.env.development` file configured with database credentials

## Database Configuration

The application reads database settings from `.env.development`:

```env
APP_DB_HOST=aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com
APP_DB_USER=admin
APP_DB_PASSWORD=lAverOpERiaN
APP_DB_NAME=gmb
APP_PORT=3000
```

## Setup Options

### Option 1: Quick Setup (Recommended) ⚡

The fastest and most reliable way to set up the posts table:

```bash
cd aqib-gmb-social-backend
npm run quick:posts-setup
```

**What it does:**
- ✅ Creates table with individual error handling
- ✅ Adds indexes separately with error handling
- ✅ Handles existing tables gracefully
- ✅ Verifies setup completion

### Option 2: Full Setup 🛡️

Uses the complete SQL file with all features:

```bash
npm run setup:posts-table
```

**What it does:**
- ✅ Uses `create_posts_table.sql`
- ✅ Creates table with all indexes and comments
- ✅ Comprehensive verification
- ✅ Detailed table structure display

## Step-by-Step Setup

### Step 1: Test Database Connection

First, verify that you can connect to the database:

```bash
cd aqib-gmb-social-backend
npm run test:db
```

**Expected Output:**
```
🔗 Testing database connection...
📍 Host: aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com
📍 Database: gmb
📍 User: admin
✅ Database connection established successfully!
🧪 Testing basic query...
✅ Basic query successful: { test: 1 }
🔍 Checking database version...
✅ MySQL Version: 8.0.35
```

### Step 2: Setup Posts Table

Choose one of the setup options:

#### Quick Setup (Recommended)
```bash
npm run quick:posts-setup
```

#### Full Setup
```bash
npm run setup:posts-table
```

### Step 3: Verify Setup

Test the API endpoints:

```bash
npm run test:posts-api
```

### Step 4: Start Application

```bash
npm start
```

## Table Structure

The `gmb_posts` table includes the following fields:

### Core Fields
- `id` - Primary key auto-increment ID
- `user_id` - Reference to user who created the post
- `business_id` - Reference to business the post belongs to
- `location_id` - Google My Business location ID
- `account_id` - Google My Business account ID

### Post Management Fields
- `google_post_name` - Google post name/ID for direct editing
- `bulk_post_id` - UUID for grouping bulk posts together
- `is_bulk_post` - Flag indicating if this is part of a bulk post

### Content Fields
- `post_content` - Original post content sent to Google API (JSON)
- `post_response` - Response received from Google API (JSON)
- `summary` - Post summary/content text
- `topic_type` - Post topic type (STANDARD, EVENT, OFFER)
- `language_code` - Post language code
- `state` - Post state (PROCESSING, LIVE, etc.)
- `search_url` - Google search URL for the post

### Timestamp Fields
- `created_at` - Record creation timestamp
- `updated_at` - Record last update timestamp

## Indexes

The table includes optimized indexes for:

### Single Column Indexes
- `idx_user_id` - User-based queries
- `idx_business_id` - Business-based queries
- `idx_location_id` - Location-based queries
- `idx_bulk_post_id` - Bulk post queries
- `idx_google_post_name` - Direct post lookup
- `idx_is_bulk_post` - Bulk post filtering
- `idx_created_at` - Time-based queries

### Composite Indexes
- `idx_user_business` - User + Business queries
- `idx_bulk_posts` - Bulk post ID + flag queries
- `idx_location_posts` - Location + time queries

## API Endpoints

The following API endpoints are available for bulk post management:

### Check Bulk Post Status
```
GET /post/check-bulk-status/:googlePostName
```
Returns whether a post is part of a bulk post group.

### Get Bulk Post Details
```
GET /post/bulk-post-details/:bulkPostId
```
Returns all posts in a bulk post group.

### Get Post by Name
```
GET /post/post-by-name/:googlePostName
```
Returns specific post details by Google post name.

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check `.env.development` file
   - Verify database credentials
   - Ensure database server is accessible

2. **Table Already Exists**
   - Use quick setup for graceful handling
   - Or drop table first: `DROP TABLE gmb_posts;`

3. **Permission Errors**
   - Ensure database user has CREATE and INDEX privileges
   - Check database connection limits

### Manual Verification

Connect to database and verify table creation:

```sql
-- Check if table exists
SHOW TABLES LIKE 'gmb_posts';

-- Check table structure
DESCRIBE gmb_posts;

-- Check indexes
SHOW INDEX FROM gmb_posts;

-- Check row count
SELECT COUNT(*) FROM gmb_posts;
```

## Available Scripts

```bash
# Database connection and setup
npm run test:db              # Test database connection
npm run quick:posts-setup    # Quick setup (recommended)
npm run setup:posts-table    # Full setup with SQL file
npm run test:posts-api       # Test API endpoints

# Development
npm start                    # Start backend server
npm run devStart            # Start with nodemon
```

## Success Indicators

✅ **Setup Successful When:**
- Table created without errors
- All indexes created successfully
- API endpoints respond correctly
- No connection errors
- Frontend can create posts

## Next Steps

After successful setup:

1. **Start Backend Server**: `npm start`
2. **Test Post Creation**: Use frontend to create posts
3. **Verify Bulk Posts**: Create posts for multiple locations
4. **Test Edit Functionality**: Implement edit screen with bulk detection
5. **Monitor Performance**: Check query performance with indexes
