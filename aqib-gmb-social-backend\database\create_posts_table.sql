-- =====================================================
-- GMB Posts Table Creation Script
-- =====================================================
-- This script creates the gmb_posts table for storing
-- Google My Business posts with bulk post support
-- =====================================================

-- Create the gmb_posts table
CREATE TABLE IF NOT EXISTS gmb_posts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  business_id INT NOT NULL,
  location_id VARCHAR(255) NOT NULL,
  account_id VARCHAR(255) NOT NULL,
  google_post_name VARCHAR(500) NOT NULL,
  bulk_post_id VARCHAR(36) NULL,
  is_bulk_post BOOLEAN DEFAULT FALSE,
  post_content JSON NOT NULL,
  post_response JSON NOT NULL,
  summary TEXT,
  topic_type VARCHAR(50),
  language_code VARCHAR(10),
  state VARCHAR(50),
  search_url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Indexes for optimal performance
  INDEX idx_user_id (user_id),
  INDEX idx_business_id (business_id),
  INDEX idx_location_id (location_id),
  INDEX idx_bulk_post_id (bulk_post_id),
  INDEX idx_google_post_name (google_post_name),
  INDEX idx_is_bulk_post (is_bulk_post),
  INDEX idx_created_at (created_at),
  
  -- Composite indexes for common queries
  INDEX idx_user_business (user_id, business_id),
  INDEX idx_bulk_posts (bulk_post_id, is_bulk_post),
  INDEX idx_location_posts (location_id, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add comments to the table and columns
ALTER TABLE gmb_posts COMMENT = 'Stores Google My Business posts with bulk post support';

-- Column comments for documentation
ALTER TABLE gmb_posts 
  MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Primary key auto-increment ID',
  MODIFY COLUMN user_id INT NOT NULL COMMENT 'Reference to user who created the post',
  MODIFY COLUMN business_id INT NOT NULL COMMENT 'Reference to business the post belongs to',
  MODIFY COLUMN location_id VARCHAR(255) NOT NULL COMMENT 'Google My Business location ID',
  MODIFY COLUMN account_id VARCHAR(255) NOT NULL COMMENT 'Google My Business account ID',
  MODIFY COLUMN google_post_name VARCHAR(500) NOT NULL COMMENT 'Google post name/ID for direct editing',
  MODIFY COLUMN bulk_post_id VARCHAR(36) NULL COMMENT 'UUID for grouping bulk posts together',
  MODIFY COLUMN is_bulk_post BOOLEAN DEFAULT FALSE COMMENT 'Flag indicating if this is part of a bulk post',
  MODIFY COLUMN post_content JSON NOT NULL COMMENT 'Original post content sent to Google API',
  MODIFY COLUMN post_response JSON NOT NULL COMMENT 'Response received from Google API',
  MODIFY COLUMN summary TEXT COMMENT 'Post summary/content text',
  MODIFY COLUMN topic_type VARCHAR(50) COMMENT 'Post topic type (STANDARD, EVENT, OFFER)',
  MODIFY COLUMN language_code VARCHAR(10) COMMENT 'Post language code',
  MODIFY COLUMN state VARCHAR(50) COMMENT 'Post state (PROCESSING, LIVE, etc.)',
  MODIFY COLUMN search_url TEXT COMMENT 'Google search URL for the post',
  MODIFY COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation timestamp',
  MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record last update timestamp';
