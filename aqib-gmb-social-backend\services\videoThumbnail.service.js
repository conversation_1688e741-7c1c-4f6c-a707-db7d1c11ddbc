const ffmpeg = require("fluent-ffmpeg");
const ffmpegStatic = require("ffmpeg-static");
const sharp = require("sharp");
const logger = require("../utils/logger");
const s3Service = require("./s3.service");
const { v4: uuidv4 } = require("uuid");
const os = require("os");
const path = require("path");
const fs = require("fs");

// Set ffmpeg path
ffmpeg.setFfmpegPath(ffmpegStatic);

// Get temp directory based on OS
const getTempDir = () => {
  return os.tmpdir();
};

/**
 * Generate thumbnail from video buffer
 * @param {Buffer} videoBuffer - Video file buffer
 * @param {string} businessName - Business name for S3 key
 * @param {number} businessId - Business ID
 * @param {string} originalFileName - Original video file name
 * @returns {Promise<Object>} Thumbnail generation result
 */
const generateVideoThumbnail = async (
  videoBuffer,
  businessName,
  businessId,
  originalFileName
) => {
  try {
    logger.info("Starting video thumbnail generation", {
      businessId,
      originalFileName,
      videoSize: videoBuffer.length,
    });

    // Create a temporary file path for the video
    const tempDir = getTempDir();
    const tempVideoPath = path.join(tempDir, `video_${uuidv4()}.mp4`);
    const tempThumbnailPath = path.join(tempDir, `thumbnail_${uuidv4()}.jpg`);

    // Write video buffer to temporary file
    fs.writeFileSync(tempVideoPath, videoBuffer);

    // Generate thumbnail using ffmpeg
    await new Promise((resolve, reject) => {
      ffmpeg(tempVideoPath)
        .screenshots({
          timestamps: ["00:00:01"], // Take screenshot at 1 second
          filename: path.basename(tempThumbnailPath),
          folder: path.dirname(tempThumbnailPath),
          size: "320x240", // Standard thumbnail size
        })
        .on("end", () => {
          logger.info("Thumbnail generated successfully");
          resolve();
        })
        .on("error", (err) => {
          logger.error("Error generating thumbnail with ffmpeg", {
            error: err.message,
            stack: err.stack,
          });
          reject(err);
        });
    });

    // Read the generated thumbnail
    let thumbnailBuffer = fs.readFileSync(tempThumbnailPath);

    // Optimize thumbnail with sharp
    thumbnailBuffer = await sharp(thumbnailBuffer)
      .resize(320, 240, {
        fit: "cover",
        position: "center",
      })
      .jpeg({
        quality: 80,
        progressive: true,
      })
      .toBuffer();

    // Generate S3 key for thumbnail
    const thumbnailFileName = `${originalFileName.split(".")[0]}_thumbnail.jpg`;
    const thumbnailS3Key = s3Service.generateS3Key(
      businessName,
      businessId,
      thumbnailFileName
    );

    // Upload thumbnail to S3
    const uploadResult = await s3Service.uploadFileToS3(
      thumbnailBuffer,
      thumbnailS3Key,
      "image/jpeg"
    );

    // Clean up temporary files
    try {
      fs.unlinkSync(tempVideoPath);
      fs.unlinkSync(tempThumbnailPath);
    } catch (cleanupError) {
      logger.warn("Error cleaning up temporary files", {
        error: cleanupError.message,
      });
    }

    if (uploadResult.success) {
      logger.info("Video thumbnail uploaded successfully", {
        thumbnailS3Key,
        thumbnailUrl: uploadResult.data.s3Url,
      });

      return {
        success: true,
        thumbnail: {
          s3Key: thumbnailS3Key,
          s3Url: uploadResult.data.s3Url,
          fileName: thumbnailFileName,
          size: thumbnailBuffer.length,
        },
      };
    } else {
      throw new Error(`Failed to upload thumbnail: ${uploadResult.error}`);
    }
  } catch (error) {
    logger.error("Error generating video thumbnail", {
      error: error.message,
      stack: error.stack,
      businessId,
      originalFileName,
    });

    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Generate thumbnail for image (resize and optimize)
 * @param {Buffer} imageBuffer - Image file buffer
 * @param {string} businessName - Business name for S3 key
 * @param {number} businessId - Business ID
 * @param {string} originalFileName - Original image file name
 * @param {string} mimeType - Image MIME type
 * @returns {Promise<Object>} Thumbnail generation result
 */
const generateImageThumbnail = async (
  imageBuffer,
  businessName,
  businessId,
  originalFileName,
  mimeType
) => {
  try {
    logger.info("Starting image thumbnail generation", {
      businessId,
      originalFileName,
      imageSize: imageBuffer.length,
    });

    // Generate thumbnail using sharp
    const thumbnailBuffer = await sharp(imageBuffer)
      .resize(320, 240, {
        fit: "cover",
        position: "center",
      })
      .jpeg({
        quality: 80,
        progressive: true,
      })
      .toBuffer();

    // Generate S3 key for thumbnail
    const thumbnailFileName = `${originalFileName.split(".")[0]}_thumbnail.jpg`;
    const thumbnailS3Key = s3Service.generateS3Key(
      businessName,
      businessId,
      thumbnailFileName
    );

    // Upload thumbnail to S3
    const uploadResult = await s3Service.uploadFileToS3(
      thumbnailBuffer,
      thumbnailS3Key,
      "image/jpeg"
    );

    if (uploadResult.success) {
      logger.info("Image thumbnail uploaded successfully", {
        thumbnailS3Key,
        thumbnailUrl: uploadResult.data.s3Url,
      });

      return {
        success: true,
        thumbnail: {
          s3Key: thumbnailS3Key,
          s3Url: uploadResult.data.s3Url,
          fileName: thumbnailFileName,
          size: thumbnailBuffer.length,
        },
      };
    } else {
      throw new Error(`Failed to upload thumbnail: ${uploadResult.error}`);
    }
  } catch (error) {
    logger.error("Error generating image thumbnail", {
      error: error.message,
      stack: error.stack,
      businessId,
      originalFileName,
    });

    return {
      success: false,
      error: error.message,
    };
  }
};

module.exports = {
  generateVideoThumbnail,
  generateImageThumbnail,
};
