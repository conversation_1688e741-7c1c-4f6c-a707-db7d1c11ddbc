{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\screens\\\\reviewManagement\\\\reviewSettings\\\\components\\\\autoReplySettings.component.test.tsx\";\nimport React from \"react\";\nimport { Box, Typography } from \"@mui/material\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AutoReplySettingsComponent = ({\n  businessId,\n  settings,\n  onSettingsUpdate\n}) => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      children: \"Auto-Reply Settings\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      children: [\"Component is loading... Business ID: \", businessId]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_c = AutoReplySettingsComponent;\nexport default AutoReplySettingsComponent;\nvar _c;\n$RefreshReg$(_c, \"AutoReplySettingsComponent\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "jsxDEV", "_jsxDEV", "AutoReplySettingsComponent", "businessId", "settings", "onSettingsUpdate", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/screens/reviewManagement/reviewSettings/components/autoReplySettings.component.test.tsx"], "sourcesContent": ["import React from \"react\";\nimport { Box, Typography } from \"@mui/material\";\n\ninterface IAutoReplySettingsProps {\n  businessId: number;\n  settings: any;\n  onSettingsUpdate: () => void;\n}\n\nconst AutoReplySettingsComponent: React.FunctionComponent<\n  IAutoReplySettingsProps\n> = ({ businessId, settings, onSettingsUpdate }) => {\n  return (\n    <Box>\n      <Typography variant=\"h6\">Auto-Reply Settings</Typography>\n      <Typography variant=\"body2\">\n        Component is loading... Business ID: {businessId}\n      </Typography>\n    </Box>\n  );\n};\n\nexport default AutoReplySettingsComponent;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQhD,MAAMC,0BAEL,GAAGA,CAAC;EAAEC,UAAU;EAAEC,QAAQ;EAAEC;AAAiB,CAAC,KAAK;EAClD,oBACEJ,OAAA,CAACH,GAAG;IAAAQ,QAAA,gBACFL,OAAA,CAACF,UAAU;MAACQ,OAAO,EAAC,IAAI;MAAAD,QAAA,EAAC;IAAmB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACzDV,OAAA,CAACF,UAAU;MAACQ,OAAO,EAAC,OAAO;MAAAD,QAAA,GAAC,uCACW,EAACH,UAAU;IAAA;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACC,EAAA,GAXIV,0BAEL;AAWD,eAAeA,0BAA0B;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}