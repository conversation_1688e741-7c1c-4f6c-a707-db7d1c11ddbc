import { Component, Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import {
  CREATE_POST,
  DELETE_POST,
  SAVE_SCHEDULED,
  RETRIEVE_POSTS,
  UPLOAD_IMAGE_FILES,
  UPLOAD_IMAGES_S3,
  CHECK_BULK_POST_STATUS,
  GET_BULK_POST_DETAILS,
  GET_POST_BY_NAME,
} from "../../constants/endPoints.constant";
import { Action } from "redux";
import { IGoogleCreatePost } from "../../interfaces/request/IGoogleCreatePost";
import { ISelectionLocationWithPost } from "../../screens/createSocialPost/components/submitPost.component";
import { TOPIC_TYPES } from "../../constants/application.constant";

class PostsService {
  _httpHelperService;
  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  retrievePosts = async (
    userId: number,
    businessGroupId: number,
    businessId: number,
    locationId: number
  ) => {
    return await this._httpHelperService.get(
      `${RETRIEVE_POSTS}/${userId}?businessGroupId=${businessGroupId}&businessId=${businessId}&locationId=${locationId}`
    );
  };

  uploadImagesToServer = async (formData: FormData) => {
    return await this._httpHelperService.postFormData(
      `${UPLOAD_IMAGE_FILES}`,
      formData
    );
  };

  uploadImagesToS3 = async (userId: number, files: File[]) => {
    const formData = new FormData();
    for (let i = 0; i < files.length; i++) {
      formData.append("files", files[i]);
    }
    return await this._httpHelperService.postFormData(
      `${UPLOAD_IMAGES_S3}/${userId}`,
      formData
    );
  };

  createPost = async (
    userId: number,
    createPostData: ISelectionLocationWithPost,
    isBulkPost: boolean = false,
    bulkPostId?: string
  ) => {
    if (createPostData.createGooglePost.topicType === TOPIC_TYPES.Event) {
      delete createPostData.createGooglePost.offer;
    }

    // Add bulk post information to the request
    const requestData = {
      ...createPostData,
      isBulkPost,
      bulkPostId,
    };

    console.log(JSON.stringify(createPostData.createGooglePost));
    return await this._httpHelperService.post(
      `${CREATE_POST}/${userId}`,
      requestData
    );
  };

  deletePost = async (userId: number, route: string) => {
    return await this._httpHelperService.delete(
      `${DELETE_POST}/${userId}?id=${route}`
    );
  };

  checkBulkPostStatus = async (googlePostName: string) => {
    return await this._httpHelperService.get(
      `${CHECK_BULK_POST_STATUS}/${encodeURIComponent(googlePostName)}`
    );
  };

  getBulkPostDetails = async (bulkPostId: string) => {
    return await this._httpHelperService.get(
      `${GET_BULK_POST_DETAILS}/${bulkPostId}`
    );
  };

  getPostByName = async (googlePostName: string) => {
    return await this._httpHelperService.get(
      `${GET_POST_BY_NAME}/${encodeURIComponent(googlePostName)}`
    );
  };
}

export default PostsService;
