import { Component, Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import {
  CREATE_POST,
  DELETE_POST,
  SAVE_SCHEDULED,
  RETRIEVE_POSTS,
  UPLOAD_IMAGE_FILES,
} from "../../constants/endPoints.constant";
import { Action } from "redux";
import { IGoogleCreatePost } from "../../interfaces/request/IGoogleCreatePost";
import { ISelectionLocationWithPost } from "../../screens/createSocialPost/components/submitPost.component";
import { TOPIC_TYPES } from "../../constants/application.constant";

class PostsService {
  _httpHelperService;
  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  retrievePosts = async (
    userId: number,
    businessGroupId: number,
    businessId: number,
    locationId: number
  ) => {
    return await this._httpHelperService.get(
      `${RETRIEVE_POSTS}/${userId}?businessGroupId=${businessGroupId}&businessId=${businessId}&locationId=${locationId}`
    );
  };

  uploadImagesToServer = async (formData: FormData) => {
    return await this._httpHelperService.postFormData(
      `${UPLOAD_IMAGE_FILES}`,
      formData
    );
  };

  createPost = async (
    userId: number,
    createPostData: ISelectionLocationWithPost
  ) => {
    if (createPostData.createGooglePost.topicType === TOPIC_TYPES.Event) {
      delete createPostData.createGooglePost.offer;
    }

    console.log(JSON.stringify(createPostData.createGooglePost));
    return await this._httpHelperService.post(
      `${CREATE_POST}/${userId}`,
      createPostData
    );
  };

  deletePost = async (userId: number, route: string) => {
    return await this._httpHelperService.delete(
      `${DELETE_POST}/${userId}?id=${route}`
    );
  };
}

export default PostsService;
