const pool = require("../config/db");
const RoleType = require("../constants/dbConstants");

module.exports = class Location {
  constructor(id, userId, gmbLocationId, statusId) {
    this.id = id;
    this.userId = userId;
    (this.gmbLocationId = gmbLocationId), (this.statusId = statusId);
  }
  static async Insert(userLocation) {
    try {
      const results = await pool.query(
        "INSERT INTO users_gmb_locations(id,userId,gmbLocationId,statusId) VALUES (?,?,?,?)",
        [
          userLocation.id,
          userLocation.userId.userLocation.gmbLocationId,
          userLocation.statusId,
        ]
      );
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async fetchAll(userId) {
    try {
      const userData = await pool.query("SELECT * FROM users WHERE id = ?", [
        userId,
      ]);
      if (userData[0].roleId === RoleType.Admin) {
        const results = await pool.query("SELECT * FROM gmb_locations");
        return results;
      } else if (userData[0].roleId === RoleType.Manager) {
        const results = await pool.query(
          "SELECT DISTINCT gl.* FROM gmb_locations gl JOIN users_gmb_locations ul ON ul.gmbLocationId = gl.gmbLocationId JOIN gmb_accounts a ON gl.gmbAccountId = a.accountId JOIN gmb_businesses_master b ON a.businessId = b.id JOIN user_business ub ON  ub.businessId = b.id WHERE ub.userId = ? and ub.isGoogleSyncComplete = 1",
          [userId]
        );
        return results;
      } else {
        const results = await pool.query(
          "SELECT gl.* FROM gmb_locations gl JOIN users_gmb_locations ul ON gl.gmbLocationId = ul.gmbLocationId WHERE ul.userId = ?",
          [userId]
        );
        return results;
      }
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async fetchAllPaginated(
    userId,
    pageNo,
    offset,
    businessId,
    businessGroupId,
    search
  ) {
    var results = [];
    var totalRecords = 0;
    try {
      const userData = await pool.query("SELECT * FROM users WHERE id = ?", [
        userId,
      ]);

      if (userData[0].roleId === RoleType.Admin) {
        const rows = await pool.query(
          "SELECT COUNT(1) as rowsCount FROM gmb_locations gl INNER JOIN gmb_accounts ga ON ga.accountId =  gl.gmbAccountId WHERE (? = '' OR gl.gmbLocationName like CONCAT('%', ?, '%')) AND (? = '0' OR ga.businessId = ?) AND (? = '0' OR ga.id = ?);",
          [
            search,
            search,
            businessId,
            businessId,
            businessGroupId,
            businessGroupId,
          ]
        );
        totalRecords = rows[0].rowsCount;
        if (totalRecords > 0) {
          results = await pool.query(
            "SELECT * FROM gmb_locations gl INNER JOIN gmb_accounts ga ON ga.accountId =  gl.gmbAccountId WHERE (? = '' OR gl.gmbLocationName like CONCAT('%', ?, '%')) AND (? = '0' OR ga.businessId = ?) AND (? = '0' OR ga.id = ?) order by gmbLocationName LIMIT ? , ?;",
            [
              search,
              search,
              businessId,
              businessId,
              businessGroupId,
              businessGroupId,
              (+pageNo - 1) * +offset,
              +offset,
            ]
          );
        }
      } else if (userData[0].roleId === RoleType.Manager) {
        const rows = await pool.query(
          "SELECT COUNT(1) as rowsCount FROM gmb_locations gl JOIN gmb_accounts a ON gl.gmbAccountId = a.accountId JOIN gmb_businesses_master b ON a.businessId = b.id JOIN user_business ub ON  ub.businessId = b.id WHERE ub.isGoogleSyncComplete = 1 AND (? = '' OR gl.gmbLocationName like CONCAT('%', ?, '%')) AND (? = '0' OR a.businessId = ?) AND (? = '0' OR a.id = ?) AND ub.userId = ?;",
          [
            search,
            search,
            businessId,
            businessId,
            businessGroupId,
            businessGroupId,
            userId,
          ]
        );
        totalRecords = rows[0].rowsCount;
        if (totalRecords > 0) {
          results = await pool.query(
            "SELECT DISTINCT a.businessId, gl.* FROM gmb_locations gl JOIN gmb_accounts a ON gl.gmbAccountId = a.accountId JOIN gmb_businesses_master b ON a.businessId = b.id JOIN user_business ub ON  ub.businessId = b.id WHERE ub.isGoogleSyncComplete = 1 AND (? = '' OR gl.gmbLocationName like CONCAT('%', ?, '%')) AND (? = '0' OR a.businessId = ?) AND (? = '0' OR a.id = ?) AND ub.userId = ?  order by gl.gmbLocationName LIMIT ? , ?;",
            [
              search,
              search,
              businessId,
              businessId,
              businessGroupId,
              businessGroupId,
              userId,
              (+pageNo - 1) * +offset,
              +offset,
            ]
          );
        }
      } else {
        const rows = await pool.query(
          "SELECT COUNT(1) as rowsCount FROM gmb_locations gl JOIN users_gmb_locations ul ON gl.gmbLocationId = ul.gmbLocationId WHERE (? = '' OR gl.gmbLocationName like CONCAT('%', ?, '%')) AND  ul.userId = ?;",
          [search, search, userId]
        );
        totalRecords = rows[0].rowsCount;

        if (totalRecords > 0) {
          results = await pool.query(
            "SELECT a.businessId, gl.* FROM gmb_locations gl JOIN users_gmb_locations ul ON gl.gmbLocationId = ul.gmbLocationId JOIN gmb_accounts a ON gl.gmbAccountId = a.accountId WHERE (? = '' OR gl.gmbLocationName like CONCAT('%', ?, '%')) AND  ul.userId = ? order by gl.gmbLocationName LIMIT ? , ?;",
            [search, search, userId, (+pageNo - 1) * +offset, +offset]
          );
        }
      }

      return {
        pagination: {
          totalRecords: totalRecords,
          pageCount: Math.ceil(totalRecords / offset),
        },
        results: results,
      };
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async postLocations(locations) {
    try {
      const results = [];

      for (const location of locations) {
        // Insert or update location based on composite unique constraint (gmbAccountId, gmbLocationId)
        const result = await pool.query(
          `INSERT INTO gmb_locations(
            gmbAccountId, gmbLocationId, gmbLocationName, locality, postalCode,
            businessStream, statusId, websiteUri, description, placeId,
            mapsUri, newReviewUri, latitude, longitude, regularHours,
            primaryPhone, reviewsStatusId, createdBy, updatedBy, createdAt
          ) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?, CURRENT_TIMESTAMP)
          ON DUPLICATE KEY UPDATE
            gmbLocationName = VALUES(gmbLocationName),
            locality = VALUES(locality),
            postalCode = VALUES(postalCode),
            businessStream = VALUES(businessStream),
            statusId = VALUES(statusId),
            websiteUri = VALUES(websiteUri),
            description = VALUES(description),
            placeId = VALUES(placeId),
            mapsUri = VALUES(mapsUri),
            newReviewUri = VALUES(newReviewUri),
            latitude = VALUES(latitude),
            longitude = VALUES(longitude),
            regularHours = VALUES(regularHours),
            primaryPhone = VALUES(primaryPhone),
            reviewsStatusId = VALUES(reviewsStatusId),
            updatedBy = VALUES(updatedBy),
            updatedAt = CURRENT_TIMESTAMP`,
          [
            location.accountId,
            location.locationId,
            location.title,
            location.locality,
            location.postalCode,
            location.businessStream,
            location.statusId,
            location.websiteUri,
            location.description,
            location.placeId,
            location.mapsUri,
            location.newReviewUri,
            location.latitude,
            location.longitude,
            location.regularHours,
            location.primaryPhone,
            location.reviewsStatusId,
            location.createdBy,
            location.updatedBy,
          ]
        );

        results.push({
          accountId: location.accountId,
          locationId: location.locationId,
          action: result.affectedRows === 1 ? "inserted" : "updated",
          insertId: result.insertId || null,
        });
      }

      return {
        success: true,
        processedCount: locations.length,
        results: results,
      };
    } catch (error) {
      console.error("Error inserting/updating locations: ", error);
      throw error;
    }
  }

  static async getAccountsCount(userId) {
    try {
      const result = await pool.query(
        "Select COUNT(1) AS rowCount FROM gmb_oauth_tokens WHERE userId = ?",
        [userId]
      );
      return result[0];
    } catch (error) {
      console.error("Error inserting locations: ", error);
      return error;
    }
  }

  static async getAllOAuthTokens(userId) {
    try {
      const userData = await pool.query(
        "SELECT * FROM gmb_oauth_tokens WHERE userId = ?",
        [userId]
      );

      return userData;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async getTokensToRetrievePostsOnLocation(
    userId,
    businessGroupId,
    businessId,
    locationId = 0
  ) {
    try {
      const userDataValue = await pool.query(
        "SELECT * FROM users WHERE id = ?",
        [userId]
      );
      var userDataArray = null;

      var query = `SELECT gat.gmbAccountId, gl.gmbLocationId, gat.accessToken, gat.refreshToken, gat.userId  FROM gmb_oauth_tokens gat
  INNER JOIN gmb_locations gl ON gl.gmbAccountId = gat.gmbAccountId
  INNER JOIN gmb_accounts ga ON ga.accountId = gat.gmbAccountId
  WHERE gat.statusId = 1 ${
    userDataValue[0].roleId === RoleType.User ? "" : " AND gat.userId = ? "
  } AND ga.Id = ? AND ga.businessId = ? AND (? = 0 OR gl.id = ?)`;

      if (userDataValue[0].roleId === RoleType.User) {
        userDataArray = await pool.query(query, [
          businessGroupId,
          businessId,
          locationId,
          locationId,
        ]);
      } else {
        userDataArray = await pool.query(query, [
          userId,
          businessGroupId,
          businessId,
          locationId,
          locationId,
        ]);
      }

      return userDataArray;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async getTokensToRetrievePosts(userId, accountId = "0") {
    try {
      const userData = await pool.query(
        `SELECT gat.gmbAccountId, gl.gmbLocationId, gat.accessToken, gat.refreshToken, gat.userId  FROM gmb_oauth_tokens gat
  INNER JOIN gmb_locations gl ON gl.gmbAccountId = gat.gmbAccountId
  WHERE gat.statusId = 1 AND gat.userId = ? AND (? = '0' OR gat.gmbAccountId = ?) LIMIT 10`,
        [userId, accountId, accountId]
      );

      return userData;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async InsertOrUpdateLocation(location_data) {
    try {
      const results = await pool.query(
        "INSERT INTO users_gmb_locations(userId,gmbLocationId,statusId, createdAt) VALUES (?,?,?, CURRENT_TIMESTAMP) ON DUPLICATE KEY UPDATE updatedAt = CURRENT_TIMESTAMP",
        [
          location_data.userId,
          location_data.gmbLocationId,
          location_data.statusId,
        ]
      );
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }
};
