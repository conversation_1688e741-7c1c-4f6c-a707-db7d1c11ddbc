# Review Settings Setup Guide

This guide provides step-by-step instructions for setting up the Review Settings feature with the AWS RDS MySQL database.

## Prerequisites

- Node.js installed
- Access to the AWS RDS MySQL database
- `.env.development` file configured with database credentials

## Database Configuration

The application reads database settings from `.env.development`:

```env
APP_DB_HOST=aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com
APP_DB_USER=admin
APP_DB_PASSWORD=lAverOpERiaN
APP_DB_NAME=gmb
APP_PORT=3000
```

## Setup Steps

### Step 1: Test Database Connection

First, verify that you can connect to the database:

```bash
cd aqib-gmb-social-backend
npm run test:db
```

**Expected Output:**
```
🔗 Testing database connection...
📍 Host: aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com
📍 Database: gmb
📍 User: admin
✅ Database connection established successfully!
🧪 Testing basic query...
✅ Basic query successful: { test: 1 }
🔍 Checking database version...
✅ MySQL Version: 8.0.35
```

### Step 2: Setup Review Settings Tables

Choose one of the following options:

#### Option A: Quick Setup (Recommended)

This creates tables without foreign key constraints if dependency tables are missing:

```bash
npm run setup:review-settings
```

#### Option B: Full Migration

This requires all dependency tables (`users`, `gmb_businesses_master`) to exist:

```bash
npm run migrate:review-settings
```

**Expected Output:**
```
🚀 Starting Review Settings migration...
✅ Database connection established successfully!
📄 SQL file loaded: /path/to/create_review_settings_tables.sql
📝 Found 8 SQL statements to execute

⚡ Executing statement 1/8...
✅ Statement 1 executed successfully
   📊 Table 'reply_templates' created/verified

⚡ Executing statement 2/8...
✅ Statement 2 executed successfully
   📊 Table 'business_reply_templates' created/verified

⚡ Executing statement 3/8...
✅ Statement 3 executed successfully
   📊 Table 'auto_reply_settings' created/verified

🎉 Review Settings migration completed successfully!

🔍 Verifying migration...
✅ Table 'reply_templates' exists
   📋 Columns: 9 fields
   📊 Records: 5 rows
✅ Table 'business_reply_templates' exists
   📋 Columns: 5 fields
   📊 Records: 0 rows
✅ Table 'auto_reply_settings' exists
   📋 Columns: 10 fields
   📊 Records: 0 rows

📝 Template Summary:
   📊 Total templates: 5
   ⭐ Rating range: 1 - 5 stars

✅ Migration verification completed!
```

### Step 3: Start the Backend Server

```bash
npm start
```

Or for development:

```bash
npm run devStart
```

### Step 4: Test the API Endpoints

```bash
npm run test:review-settings-api
```

**Expected Output:**
```
🚀 Starting Review Settings API Tests...
📍 Base URL: http://localhost:3000/api/review-settings

🧪 Testing Welcome Endpoint...
✅ Welcome endpoint working: Review Settings API is working!

🧪 Testing Create Template...
✅ Template created successfully: { id: 6 }

🧪 Testing Get Templates...
✅ Templates retrieved successfully: 6 templates found

🧪 Testing Update Template...
✅ Template updated successfully

🧪 Testing Auto-Reply Settings...
✅ Auto-reply settings retrieved: No settings found
✅ Auto-reply settings updated successfully

🧪 Testing Delete Template...
✅ Template deleted successfully

🎉 All tests completed!
```

### Step 5: Start the Frontend

```bash
cd aqib-gmb-social-frontend
npm start
```

### Step 6: Access the Feature

1. Open your browser and navigate to the frontend application
2. Go to **Review Management** → **Review Settings**
3. Select a business from the dropdown
4. Create templates and configure auto-reply settings

## Troubleshooting

### Database Connection Issues

**Error: `ECONNREFUSED`**
- Check if your IP is whitelisted in AWS RDS security groups
- Verify the database host and port are correct

**Error: `Access denied`**
- Verify username and password in `.env.development`
- Check if the user has proper permissions

**Error: `Unknown database`**
- Verify the database name exists
- Check if you have access to the specified database

### Table Creation Issues

**Error: `Table doesn't exist`**
- Run the dependency check: `npm run test:db`
- Use the quick setup option: `npm run setup:review-settings`

**Error: `Foreign key constraint fails`**
- Ensure `users` and `gmb_businesses_master` tables exist
- Use the setup script which handles missing dependencies

### API Testing Issues

**Error: `ECONNREFUSED` during API tests**
- Ensure the backend server is running: `npm start`
- Check if the port matches the configuration

**Error: `404 Not Found`**
- Verify the routes are properly registered
- Check if the review settings routes are included in `routes.js`

## Available NPM Scripts

```bash
# Database operations
npm run test:db                    # Test database connection
npm run setup:review-settings      # Quick setup (recommended)
npm run migrate:review-settings    # Full migration

# API testing
npm run test:review-settings-api   # Test all API endpoints

# Development
npm start                          # Start with ntl menu
npm run devStart                   # Start with nodemon
```

## Database Schema

### Tables Created

1. **reply_templates**
   - Stores reply templates for different star ratings
   - Includes template name, content, and default flag
   - Indexed for performance

2. **business_reply_templates**
   - Maps templates to specific businesses
   - Allows business-specific template activation
   - Prevents duplicate mappings

3. **auto_reply_settings**
   - Stores auto-reply configuration per business
   - Includes enabled star ratings, delays, and business hours
   - Uses JSON for flexible star rating storage

### Default Data

The setup creates 5 default templates (1-5 stars) with professional responses suitable for different review ratings.

## Next Steps

After successful setup:

1. **Create Custom Templates**: Add business-specific reply templates
2. **Configure Auto-Reply**: Set up automatic responses for different star ratings
3. **Test the Feature**: Create test reviews and verify auto-reply functionality
4. **Monitor Performance**: Check database performance and optimize if needed

## Support

If you encounter issues:

1. Check the console output for detailed error messages
2. Verify all prerequisites are met
3. Run the database connection test first
4. Use the troubleshooting section above
5. Check the main README for additional information
