const fs = require("fs");
const path = require("path");
const mysql = require("mysql2/promise");
require("dotenv").config({ path: ".env.development" });

/**
 * Setup script for Manage Assets functionality
 * This script will:
 * 1. Add max_upload_size_mb column to user_business table
 * 2. Create business_assets table
 * 3. Update existing records with default values
 */

async function setupManageAssets() {
  let connection;

  try {
    console.log("🚀 Starting Manage Assets setup...");

    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.APP_DB_HOST,
      user: process.env.APP_DB_USER,
      password: process.env.APP_DB_PASSWORD,
      database: process.env.APP_DB_NAME,
      multipleStatements: true,
    });

    console.log("✅ Database connection established");

    // Read the migration SQL file
    const migrationPath = path.join(
      __dirname,
      "../database/migrations/add_manage_assets_tables.sql"
    );
    const migrationSQL = fs.readFileSync(migrationPath, "utf8");

    console.log("📄 Migration SQL loaded");

    // Execute the migration statements separately
    console.log("🔄 Executing migration...");

    // Split SQL statements and execute them one by one
    const statements = migrationSQL
      .split(";")
      .map((stmt) => stmt.trim())
      .filter((stmt) => stmt.length > 0 && !stmt.startsWith("--"));

    for (const statement of statements) {
      if (statement.trim()) {
        console.log(`Executing: ${statement.substring(0, 50)}...`);
        await connection.execute(statement);
      }
    }

    console.log("✅ Migration executed successfully");

    // Verify the changes
    console.log("🔍 Verifying changes...");

    // Check if max_upload_size_mb column exists
    const [columns] = await connection.execute(
      `
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'user_business' AND COLUMN_NAME = 'max_upload_size_mb'
    `,
      [process.env.APP_DB_NAME]
    );

    if (columns.length > 0) {
      console.log("✅ max_upload_size_mb column added to user_business table");
    } else {
      console.log("❌ Failed to add max_upload_size_mb column");

      // Let's check what columns exist in user_business
      const [userBusinessColumns] = await connection.execute(
        `
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'user_business'
      `,
        [process.env.APP_DB_NAME]
      );

      console.log(
        "📋 user_business columns:",
        userBusinessColumns.map((c) => c.COLUMN_NAME).join(", ")
      );
    }

    // Check if business_assets table exists
    const [tables] = await connection.execute(
      `
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'business_assets'
    `,
      [process.env.APP_DB_NAME]
    );

    if (tables.length > 0) {
      console.log("✅ business_assets table created successfully");
    } else {
      console.log("❌ Failed to create business_assets table");
    }

    // Check existing user_business records only if column exists
    if (columns.length > 0) {
      const [userBusinessRecords] = await connection.execute(`
        SELECT COUNT(*) as count,
               COUNT(CASE WHEN max_upload_size_mb IS NOT NULL THEN 1 END) as with_max_size
        FROM user_business
      `);

      const totalRecords = userBusinessRecords[0].count;
      const recordsWithMaxSize = userBusinessRecords[0].with_max_size;

      console.log(
        `📊 User Business Records: ${totalRecords} total, ${recordsWithMaxSize} with max_upload_size_mb set`
      );
    }

    console.log("🎉 Manage Assets setup completed successfully!");
    console.log("");
    console.log("📋 Summary:");
    console.log(
      "- Added max_upload_size_mb column to user_business table (default: 1024 MB)"
    );
    console.log("- Created business_assets table for tracking uploaded files");
    console.log(
      "- Updated existing user_business records with default max size"
    );
    console.log("");
    console.log("🔧 Next steps:");
    console.log(
      "1. Install AWS SDK dependencies: npm install aws-sdk multer-s3"
    );
    console.log("2. Configure AWS S3 bucket permissions");
    console.log("3. Test the API endpoints");
    console.log("4. Implement frontend components");
  } catch (error) {
    console.error("❌ Error during setup:", error.message);
    console.error("Stack trace:", error.stack);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log("🔌 Database connection closed");
    }
  }
}

// Run the setup
if (require.main === module) {
  setupManageAssets();
}

module.exports = { setupManageAssets };
