const fs = require("fs");
const path = require("path");
const mysql = require("mysql2/promise");
require("dotenv").config({ path: ".env.development" });

/**
 * Setup script for Manage Assets functionality
 * This script will:
 * 1. Add max_upload_size_mb column to user_business table
 * 2. Create business_assets table
 * 3. Update existing records with default values
 */

async function setupManageAssets() {
  let connection;

  try {
    console.log("🚀 Starting Manage Assets setup...");

    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.APP_DB_HOST,
      user: process.env.APP_DB_USER,
      password: process.env.APP_DB_PASSWORD,
      database: process.env.APP_DB_NAME,
      multipleStatements: true,
    });

    console.log("✅ Database connection established");

    // Read the migration SQL file
    const migrationPath = path.join(
      __dirname,
      "../database/migrations/add_manage_assets_tables.sql"
    );
    const migrationSQL = fs.readFileSync(migrationPath, "utf8");

    console.log("📄 Migration SQL loaded");

    // Execute the migration statements separately
    console.log("🔄 Executing migration...");

    // Define the statements manually to ensure they execute correctly
    const statements = [
      `ALTER TABLE user_business
       ADD COLUMN max_upload_size_mb INT DEFAULT 1024 COMMENT 'Maximum upload size in MB for business assets (default 1GB)'`,

      `CREATE TABLE IF NOT EXISTS business_assets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        business_id INT NOT NULL,
        user_id INT NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        original_file_name VARCHAR(255) NOT NULL,
        file_type VARCHAR(100) NOT NULL,
        file_size BIGINT NOT NULL COMMENT 'File size in bytes',
        s3_key VARCHAR(500) NOT NULL COMMENT 'S3 object key/path',
        s3_url VARCHAR(1000) NOT NULL COMMENT 'S3 public URL',
        mime_type VARCHAR(100) NOT NULL,
        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'deleted') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (business_id) REFERENCES gmb_businesses_master(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

        INDEX idx_business_assets_business_id (business_id),
        INDEX idx_business_assets_user_id (user_id),
        INDEX idx_business_assets_status (status),
        INDEX idx_business_assets_upload_date (upload_date)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

      `UPDATE user_business
       SET max_upload_size_mb = 1024
       WHERE max_upload_size_mb IS NULL`,
    ];

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        try {
          console.log(
            `📝 Executing statement ${i + 1}/${statements.length}...`
          );
          await connection.execute(statement);
          console.log(`✅ Statement ${i + 1} executed successfully`);
        } catch (error) {
          console.log(`❌ Error in statement ${i + 1}:`, error.message);
          // Continue with other statements even if one fails
        }
      }
    }

    console.log("✅ Migration executed successfully");

    // Verify the changes
    console.log("🔍 Verifying changes...");

    // Check if max_upload_size_mb column exists
    const [columns] = await connection.execute(
      `
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'user_business' AND COLUMN_NAME = 'max_upload_size_mb'
    `,
      [process.env.APP_DB_NAME]
    );

    if (columns.length > 0) {
      console.log("✅ max_upload_size_mb column added to user_business table");
    } else {
      console.log("❌ Failed to add max_upload_size_mb column");

      // Let's check what columns exist in user_business
      const [userBusinessColumns] = await connection.execute(
        `
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'user_business'
      `,
        [process.env.APP_DB_NAME]
      );

      console.log(
        "📋 user_business columns:",
        userBusinessColumns.map((c) => c.COLUMN_NAME).join(", ")
      );
    }

    // Check if business_assets table exists
    const [tables] = await connection.execute(
      `
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'business_assets'
    `,
      [process.env.APP_DB_NAME]
    );

    if (tables.length > 0) {
      console.log("✅ business_assets table created successfully");
    } else {
      console.log("❌ Failed to create business_assets table");
    }

    // Check existing user_business records only if column exists
    if (columns.length > 0) {
      const [userBusinessRecords] = await connection.execute(`
        SELECT COUNT(*) as count,
               COUNT(CASE WHEN max_upload_size_mb IS NOT NULL THEN 1 END) as with_max_size
        FROM user_business
      `);

      const totalRecords = userBusinessRecords[0].count;
      const recordsWithMaxSize = userBusinessRecords[0].with_max_size;

      console.log(
        `📊 User Business Records: ${totalRecords} total, ${recordsWithMaxSize} with max_upload_size_mb set`
      );
    }

    console.log("🎉 Manage Assets setup completed successfully!");
    console.log("");
    console.log("📋 Summary:");
    console.log(
      "- Added max_upload_size_mb column to user_business table (default: 1024 MB)"
    );
    console.log("- Created business_assets table for tracking uploaded files");
    console.log(
      "- Updated existing user_business records with default max size"
    );
    console.log("");
    console.log("🔧 Next steps:");
    console.log(
      "1. Install AWS SDK dependencies: npm install aws-sdk multer-s3"
    );
    console.log("2. Configure AWS S3 bucket permissions");
    console.log("3. Test the API endpoints");
    console.log("4. Implement frontend components");
  } catch (error) {
    console.error("❌ Error during setup:", error.message);
    console.error("Stack trace:", error.stack);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log("🔌 Database connection closed");
    }
  }
}

// Run the setup
if (require.main === module) {
  setupManageAssets();
}

module.exports = { setupManageAssets };
