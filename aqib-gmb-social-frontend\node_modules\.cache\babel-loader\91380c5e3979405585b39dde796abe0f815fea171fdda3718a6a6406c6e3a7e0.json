{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\createPost\\\\createPostTemplates\\\\cards\\\\testimonialCard4\\\\testimonialCard4.component.tsx\";\nimport React from \"react\";\nimport { Box, Typography, Container, CssBaseline } from \"@mui/material\";\nimport UserAvatar from \"../../../../userAvatar/userAvatar.component\";\nimport RatingsStar from \"../../../../ratingsStar/ratingsStar.component\";\n\n//Css Import\nimport \"../testimonialCard4/testimonialCard4.component.style.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestimonialCard4 = props => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    ref: props.divRef,\n    sx: {\n      backgroundImage: `url(${require(\"../../../../../assets/feedbackBackgrouns/5.png\")})`,\n      backgroundColor: props.templateConfig.backgroundColor,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\",\n      backgroundRepeat: \"no-repeat\",\n      backgroundBlendMode: \"overlay\",\n      // Try other blend modes like \"multiply\" if desired\n      color: \"#fff\",\n      textAlign: \"center\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      height: \"100%\",\n      width: \"100%\",\n      border: 1,\n      borderColor: \"black\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          className: \"testimonialInnerFour\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: [props.templateConfig.showAvatar && /*#__PURE__*/_jsxDEV(UserAvatar, {\n              profileImage: props.templateConfig.reviewerImage,\n              fullname: props.templateConfig.reviewerName,\n              style: {\n                width: 60,\n                height: 60,\n                margin: \"0 auto 10px\",\n                background: \"linear-gradient(45deg, #42A5F5, #64B5F6)\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                className: \"testimonialTitle\",\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                children: props.templateConfig.reviewerName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            mb: 2,\n            sx: {\n              color: `${props.templateConfig.fontColor}`\n            },\n            children: props.templateConfig.comment\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: props.templateConfig.showRating && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                mb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(RatingsStar, {\n                starRating: props.templateConfig.starRating\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_c = TestimonialCard4;\nexport default TestimonialCard4;\nvar _c;\n$RefreshReg$(_c, \"TestimonialCard4\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Container", "CssBaseline", "UserAvatar", "RatingsStar", "jsxDEV", "_jsxDEV", "TestimonialCard4", "props", "ref", "divRef", "sx", "backgroundImage", "require", "backgroundColor", "templateConfig", "backgroundSize", "backgroundPosition", "backgroundRepeat", "backgroundBlendMode", "color", "textAlign", "display", "flexDirection", "justifyContent", "alignItems", "height", "width", "border", "borderColor", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "showAvatar", "profileImage", "reviewerImage", "fullname", "reviewerName", "style", "margin", "background", "variant", "fontWeight", "mb", "fontColor", "comment", "showRating", "starRating", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/createPost/createPostTemplates/cards/testimonialCard4/testimonialCard4.component.tsx"], "sourcesContent": ["import React from \"react\";\nimport {\n  Box,\n  Typography,\n  Avatar,\n  Rating,\n  Container,\n  CssBaseline,\n} from \"@mui/material\";\nimport { IPostTemplateConfig } from \"../../../../../types/IPostTemplateConfig\";\nimport { ref } from \"yup\";\nimport StarIcon from \"@mui/icons-material/Star\";\nimport UserAvatar from \"../../../../userAvatar/userAvatar.component\";\nimport RatingsStar from \"../../../../ratingsStar/ratingsStar.component\";\n\n//Css Import\nimport \"../testimonialCard4/testimonialCard4.component.style.css\";\n\nconst TestimonialCard4 = (props: {\n  templateConfig: IPostTemplateConfig;\n  divRef: any;\n}) => {\n  return (\n    <Box\n      ref={props.divRef}\n      sx={{\n        backgroundImage: `url(${require(\"../../../../../assets/feedbackBackgrouns/5.png\")})`,\n        backgroundColor: props.templateConfig.backgroundColor,\n        backgroundSize: \"cover\",\n        backgroundPosition: \"center\",\n        backgroundRepeat: \"no-repeat\",\n        backgroundBlendMode: \"overlay\", // Try other blend modes like \"multiply\" if desired\n        color: \"#fff\",\n        textAlign: \"center\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        height: \"100%\",\n        width: \"100%\",\n        border: 1,\n        borderColor: \"black\",\n      }}\n    >\n      <Container>\n        <CssBaseline />\n        <Box>\n          {/* Main Content */}\n          <Box className=\"testimonialInnerFour\">\n            {/* Avatar and Name */}\n            <Box>\n              {props.templateConfig.showAvatar && (\n                <UserAvatar\n                  profileImage={props.templateConfig.reviewerImage}\n                  fullname={props.templateConfig.reviewerName}\n                  style={{\n                    width: 60,\n                    height: 60,\n                    margin: \"0 auto 10px\",\n                    background: \"linear-gradient(45deg, #42A5F5, #64B5F6)\",\n                  }}\n                />\n              )}\n\n              <Box>\n                <Typography\n                  className=\"testimonialTitle\"\n                  variant=\"h6\"\n                  fontWeight=\"bold\"\n                >\n                  {props.templateConfig.reviewerName}\n                </Typography>\n              </Box>\n            </Box>\n\n            {/* Review Text */}\n            <Typography\n              variant=\"body2\"\n              color=\"text.secondary\"\n              mb={2}\n              sx={{\n                color: `${props.templateConfig.fontColor}`,\n              }}\n            >\n              {props.templateConfig.comment}\n            </Typography>\n            {/* Rating */}\n            <Box>\n              {props.templateConfig.showRating && (\n                <Box\n                  sx={{\n                    display: \"flex\",\n                    justifyContent: \"center\",\n                    mb: 2,\n                  }}\n                >\n                  <RatingsStar starRating={props.templateConfig.starRating} />\n                </Box>\n              )}\n            </Box>\n          </Box>\n        </Box>\n      </Container>\n    </Box>\n  );\n};\n\nexport default TestimonialCard4;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EAGVC,SAAS,EACTC,WAAW,QACN,eAAe;AAItB,OAAOC,UAAU,MAAM,6CAA6C;AACpE,OAAOC,WAAW,MAAM,+CAA+C;;AAEvE;AACA,OAAO,0DAA0D;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,gBAAgB,GAAIC,KAGzB,IAAK;EACJ,oBACEF,OAAA,CAACP,GAAG;IACFU,GAAG,EAAED,KAAK,CAACE,MAAO;IAClBC,EAAE,EAAE;MACFC,eAAe,EAAE,OAAOC,OAAO,CAAC,gDAAgD,CAAC,GAAG;MACpFC,eAAe,EAAEN,KAAK,CAACO,cAAc,CAACD,eAAe;MACrDE,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE,WAAW;MAC7BC,mBAAmB,EAAE,SAAS;MAAE;MAChCC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,QAAQ;MACnBC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,CAAC;MACTC,WAAW,EAAE;IACf,CAAE;IAAAC,QAAA,eAEFxB,OAAA,CAACL,SAAS;MAAA6B,QAAA,gBACRxB,OAAA,CAACJ,WAAW;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACf5B,OAAA,CAACP,GAAG;QAAA+B,QAAA,eAEFxB,OAAA,CAACP,GAAG;UAACoC,SAAS,EAAC,sBAAsB;UAAAL,QAAA,gBAEnCxB,OAAA,CAACP,GAAG;YAAA+B,QAAA,GACDtB,KAAK,CAACO,cAAc,CAACqB,UAAU,iBAC9B9B,OAAA,CAACH,UAAU;cACTkC,YAAY,EAAE7B,KAAK,CAACO,cAAc,CAACuB,aAAc;cACjDC,QAAQ,EAAE/B,KAAK,CAACO,cAAc,CAACyB,YAAa;cAC5CC,KAAK,EAAE;gBACLd,KAAK,EAAE,EAAE;gBACTD,MAAM,EAAE,EAAE;gBACVgB,MAAM,EAAE,aAAa;gBACrBC,UAAU,EAAE;cACd;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACF,eAED5B,OAAA,CAACP,GAAG;cAAA+B,QAAA,eACFxB,OAAA,CAACN,UAAU;gBACTmC,SAAS,EAAC,kBAAkB;gBAC5BS,OAAO,EAAC,IAAI;gBACZC,UAAU,EAAC,MAAM;gBAAAf,QAAA,EAEhBtB,KAAK,CAACO,cAAc,CAACyB;cAAY;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5B,OAAA,CAACN,UAAU;YACT4C,OAAO,EAAC,OAAO;YACfxB,KAAK,EAAC,gBAAgB;YACtB0B,EAAE,EAAE,CAAE;YACNnC,EAAE,EAAE;cACFS,KAAK,EAAE,GAAGZ,KAAK,CAACO,cAAc,CAACgC,SAAS;YAC1C,CAAE;YAAAjB,QAAA,EAEDtB,KAAK,CAACO,cAAc,CAACiC;UAAO;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAEb5B,OAAA,CAACP,GAAG;YAAA+B,QAAA,EACDtB,KAAK,CAACO,cAAc,CAACkC,UAAU,iBAC9B3C,OAAA,CAACP,GAAG;cACFY,EAAE,EAAE;gBACFW,OAAO,EAAE,MAAM;gBACfE,cAAc,EAAE,QAAQ;gBACxBsB,EAAE,EAAE;cACN,CAAE;cAAAhB,QAAA,eAEFxB,OAAA,CAACF,WAAW;gBAAC8C,UAAU,EAAE1C,KAAK,CAACO,cAAc,CAACmC;cAAW;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACiB,EAAA,GAvFI5C,gBAAgB;AAyFtB,eAAeA,gBAAgB;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}