import React from "react";
import {
  Box,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Grid,
  IconButton,
  Chip,
  Tooltip,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import ImageIcon from "@mui/icons-material/Image";
import VideoLibraryIcon from "@mui/icons-material/VideoLibrary";
import { FileUtils } from "../../utils/fileUtils";

interface IAsset {
  id: number;
  business_id: number;
  user_id: number;
  file_name: string;
  original_file_name: string;
  file_type: "image" | "video";
  file_size: number;
  s3_key: string;
  s3_url: string;
  mime_type: string;
  upload_date: string;
  status: string;
  uploaded_by_name?: string;
}

interface FileGalleryComponentProps {
  assets: IAsset[];
  onViewAsset: (asset: IAsset) => void;
  onDeleteAsset: (asset: IAsset) => void;
  formatFileSize: (bytes: number) => string;
}

const FileGalleryComponent: React.FC<FileGalleryComponentProps> = ({
  assets,
  onViewAsset,
  onDeleteAsset,
  formatFileSize,
}) => {
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const renderAssetMedia = (asset: IAsset) => {
    if (FileUtils.isImage(asset.mime_type)) {
      return (
        <CardMedia
          component="img"
          height="140"
          image={asset.s3_url}
          alt={asset.original_file_name}
          sx={{
            objectFit: "cover",
            cursor: "pointer",
          }}
          onClick={() => onViewAsset(asset)}
        />
      );
    } else if (FileUtils.isVideo(asset.mime_type)) {
      return (
        <Box
          sx={{
            height: 140,
            backgroundColor: "#f5f5f5",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            cursor: "pointer",
            position: "relative",
            "&:hover": {
              backgroundColor: "#e0e0e0",
            },
          }}
          onClick={() => onViewAsset(asset)}
        >
          <VideoLibraryIcon sx={{ fontSize: 48, color: "#666" }} />
          <Box
            sx={{
              position: "absolute",
              bottom: 8,
              right: 8,
              backgroundColor: "rgba(0, 0, 0, 0.7)",
              color: "white",
              padding: "2px 6px",
              borderRadius: 1,
              fontSize: "0.75rem",
            }}
          >
            VIDEO
          </Box>
        </Box>
      );
    }

    return (
      <Box
        sx={{
          height: 140,
          backgroundColor: "#f5f5f5",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: "pointer",
        }}
        onClick={() => onViewAsset(asset)}
      >
        <ImageIcon sx={{ fontSize: 48, color: "#ccc" }} />
      </Box>
    );
  };

  if (assets.length === 0) {
    return (
      <Card>
        <CardContent>
          <Box
            sx={{
              textAlign: "center",
              padding: 4,
              color: "#666",
            }}
          >
            <ImageIcon sx={{ fontSize: 64, color: "#ccc", marginBottom: 2 }} />
            <Typography variant="h6" gutterBottom>
              No assets found
            </Typography>
            <Typography variant="body2">
              Upload some images or videos to get started
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Assets Gallery ({assets.length} items)
        </Typography>

        <Grid container spacing={2}>
          {assets.map((asset) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={asset.id}>
              <Card
                sx={{
                  position: "relative",
                  height: 240,
                  cursor: "pointer",
                  transition: "transform 0.2s ease, box-shadow 0.2s ease",
                  "&:hover": {
                    transform: "translateY(-4px)",
                    boxShadow: "0 8px 16px rgba(0, 0, 0, 0.15)",
                    "& .asset-actions": {
                      opacity: 1,
                    },
                  },
                }}
              >
                {renderAssetMedia(asset)}

                <CardContent
                  sx={{ padding: "8px 12px !important", height: 100 }}
                >
                  <Tooltip title={asset.original_file_name}>
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: 500,
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        marginBottom: 1,
                      }}
                    >
                      {asset.original_file_name}
                    </Typography>
                  </Tooltip>

                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      marginBottom: 1,
                    }}
                  >
                    <Typography variant="caption" color="text.secondary">
                      {formatFileSize(asset.file_size)}
                    </Typography>

                    <Chip
                      label={asset.file_type.toUpperCase()}
                      size="small"
                      color={
                        asset.file_type === "image" ? "primary" : "secondary"
                      }
                      sx={{ fontSize: "0.625rem", height: 20 }}
                    />
                  </Box>

                  <Typography
                    variant="caption"
                    color="text.secondary"
                    display="block"
                  >
                    {formatDate(asset.upload_date)}
                  </Typography>

                  {asset.uploaded_by_name && (
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      display="block"
                    >
                      by {asset.uploaded_by_name}
                    </Typography>
                  )}
                </CardContent>

                {/* Action buttons */}
                <Box
                  className="asset-actions"
                  sx={{
                    position: "absolute",
                    top: 8,
                    right: 8,
                    display: "flex",
                    gap: 0.5,
                    opacity: 0,
                    transition: "opacity 0.2s ease",
                  }}
                >
                  <Tooltip title="View">
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        onViewAsset(asset);
                      }}
                      sx={{
                        backgroundColor: "rgba(255, 255, 255, 0.9)",
                        color: "#333",
                        "&:hover": {
                          backgroundColor: "rgba(255, 255, 255, 1)",
                        },
                      }}
                    >
                      <VisibilityIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>

                  <Tooltip title="Delete">
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteAsset(asset);
                      }}
                      sx={{
                        backgroundColor: "rgba(255, 255, 255, 0.9)",
                        color: "#d32f2f",
                        "&:hover": {
                          backgroundColor: "rgba(211, 47, 47, 0.1)",
                        },
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Card>
            </Grid>
          ))}
        </Grid>
      </CardContent>
    </Card>
  );
};

export default FileGalleryComponent;
