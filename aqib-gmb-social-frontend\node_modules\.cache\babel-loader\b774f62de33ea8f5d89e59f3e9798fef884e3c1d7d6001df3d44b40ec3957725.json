{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\serviceItemsDisplay\\\\serviceItemsDisplay.component.tsx\";\nimport React from \"react\";\nimport { Card, CardContent, Typography, Accordion, AccordionSummary, AccordionDetails, Chip } from \"@mui/material\";\nimport ExpandMoreIcon from \"@mui/icons-material/ExpandMore\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ServiceItemsDisplay = props => {\n  const formatServiceType = id => {\n    const raw = id.split(\":\")[1] || \"\";\n    return raw && raw.split(\"_\").map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(\" \");\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    variant: \"outlined\",\n    sx: {\n      p: 2\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Service Items\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          flexWrap: \"wrap\",\n          gap: \"8px\"\n        },\n        children: props.serviceItems && props.serviceItems.map((item, idx) => {\n          let formatted = \"\";\n          if (item.structuredServiceItem) {\n            formatted = formatServiceType(item.structuredServiceItem.serviceTypeId);\n            return /*#__PURE__*/_jsxDEV(Chip, {\n              label: formatted,\n              variant: \"outlined\"\n            }, idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 24\n            }, this);\n          }\n          if (item.freeFormServiceItem) {\n            formatted = item.freeFormServiceItem.label.displayName;\n            return /*#__PURE__*/_jsxDEV(Accordion, {\n              expanded: true,\n              className: \"commonBorderCard\",\n              children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n                expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 51\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  children: formatted\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    flexWrap: \"wrap\",\n                    gap: \"8px\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    children: item.freeFormServiceItem.label.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 61,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 54,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 19\n            }, this);\n          }\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_c = ServiceItemsDisplay;\nexport default ServiceItemsDisplay;\nvar _c;\n$RefreshReg$(_c, \"ServiceItemsDisplay\");", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Accordion", "AccordionSummary", "AccordionDetails", "Chip", "ExpandMoreIcon", "jsxDEV", "_jsxDEV", "ServiceItemsDisplay", "props", "formatServiceType", "id", "raw", "split", "map", "word", "char<PERSON>t", "toUpperCase", "slice", "join", "variant", "sx", "p", "children", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "flexWrap", "gap", "serviceItems", "item", "idx", "formatted", "structuredServiceItem", "serviceTypeId", "label", "freeFormServiceItem", "displayName", "expanded", "className", "expandIcon", "description", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/serviceItemsDisplay/serviceItemsDisplay.component.tsx"], "sourcesContent": ["import React from \"react\";\nimport {\n  <PERSON>,\n  CardContent,\n  Typography,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  List,\n  ListItem,\n  ListItemText,\n  Divider,\n  Chip,\n} from \"@mui/material\";\nimport ExpandMoreIcon from \"@mui/icons-material/ExpandMore\";\n\nconst ServiceItemsDisplay = (props: { serviceItems: any }) => {\n  const formatServiceType = (id: string) => {\n    const raw = id.split(\":\")[1] || \"\";\n    return (\n      raw &&\n      raw\n        .split(\"_\")\n        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))\n        .join(\" \")\n    );\n  };\n\n  return (\n    <Card variant=\"outlined\" sx={{ p: 2 }}>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom>\n          Service Items\n        </Typography>\n        <div style={{ display: \"flex\", flexWrap: \"wrap\", gap: \"8px\" }}>\n          {props.serviceItems &&\n            props.serviceItems.map((item: any, idx: number) => {\n              let formatted = \"\";\n              if (item.structuredServiceItem) {\n                formatted = formatServiceType(\n                  item.structuredServiceItem.serviceTypeId\n                );\n                return <Chip key={idx} label={formatted} variant=\"outlined\" />;\n              }\n\n              if (item.freeFormServiceItem) {\n                formatted = item.freeFormServiceItem.label.displayName;\n                return (\n                  <Accordion expanded className=\"commonBorderCard\">\n                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n                      <Typography>{formatted}</Typography>\n                    </AccordionSummary>\n                    <AccordionDetails>\n                      <div\n                        style={{\n                          display: \"flex\",\n                          flexWrap: \"wrap\",\n                          gap: \"8px\",\n                        }}\n                      >\n                        <Typography>\n                          {item.freeFormServiceItem.label.description}\n                        </Typography>\n                      </div>\n                    </AccordionDetails>\n                  </Accordion>\n                );\n              }\n            })}\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default ServiceItemsDisplay;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAKhBC,IAAI,QACC,eAAe;AACtB,OAAOC,cAAc,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,mBAAmB,GAAIC,KAA4B,IAAK;EAC5D,MAAMC,iBAAiB,GAAIC,EAAU,IAAK;IACxC,MAAMC,GAAG,GAAGD,EAAE,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;IAClC,OACED,GAAG,IACHA,GAAG,CACAC,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,CAC3DC,IAAI,CAAC,GAAG,CAAC;EAEhB,CAAC;EAED,oBACEZ,OAAA,CAACT,IAAI;IAACsB,OAAO,EAAC,UAAU;IAACC,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,eACpChB,OAAA,CAACR,WAAW;MAAAwB,QAAA,gBACVhB,OAAA,CAACP,UAAU;QAACoB,OAAO,EAAC,IAAI;QAACI,YAAY;QAAAD,QAAA,EAAC;MAEtC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbrB,OAAA;QAAKsB,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,QAAQ,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAM,CAAE;QAAAT,QAAA,EAC3Dd,KAAK,CAACwB,YAAY,IACjBxB,KAAK,CAACwB,YAAY,CAACnB,GAAG,CAAC,CAACoB,IAAS,EAAEC,GAAW,KAAK;UACjD,IAAIC,SAAS,GAAG,EAAE;UAClB,IAAIF,IAAI,CAACG,qBAAqB,EAAE;YAC9BD,SAAS,GAAG1B,iBAAiB,CAC3BwB,IAAI,CAACG,qBAAqB,CAACC,aAC7B,CAAC;YACD,oBAAO/B,OAAA,CAACH,IAAI;cAAWmC,KAAK,EAAEH,SAAU;cAAChB,OAAO,EAAC;YAAU,GAAzCe,GAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAwC,CAAC;UAChE;UAEA,IAAIM,IAAI,CAACM,mBAAmB,EAAE;YAC5BJ,SAAS,GAAGF,IAAI,CAACM,mBAAmB,CAACD,KAAK,CAACE,WAAW;YACtD,oBACElC,OAAA,CAACN,SAAS;cAACyC,QAAQ;cAACC,SAAS,EAAC,kBAAkB;cAAApB,QAAA,gBAC9ChB,OAAA,CAACL,gBAAgB;gBAAC0C,UAAU,eAAErC,OAAA,CAACF,cAAc;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAL,QAAA,eAC/ChB,OAAA,CAACP,UAAU;kBAAAuB,QAAA,EAAEa;gBAAS;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACnBrB,OAAA,CAACJ,gBAAgB;gBAAAoB,QAAA,eACfhB,OAAA;kBACEsB,KAAK,EAAE;oBACLC,OAAO,EAAE,MAAM;oBACfC,QAAQ,EAAE,MAAM;oBAChBC,GAAG,EAAE;kBACP,CAAE;kBAAAT,QAAA,eAEFhB,OAAA,CAACP,UAAU;oBAAAuB,QAAA,EACRW,IAAI,CAACM,mBAAmB,CAACD,KAAK,CAACM;kBAAW;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAEhB;QACF,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAACkB,EAAA,GAzDItC,mBAAmB;AA2DzB,eAAeA,mBAAmB;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}