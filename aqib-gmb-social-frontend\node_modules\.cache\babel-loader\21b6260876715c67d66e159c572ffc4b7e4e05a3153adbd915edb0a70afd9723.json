{"ast": null, "code": "export let RoleType = /*#__PURE__*/function (RoleType) {\n  RoleType[RoleType[\"Admin\"] = 1] = \"Admin\";\n  RoleType[RoleType[\"Manager\"] = 2] = \"Manager\";\n  RoleType[RoleType[\"User\"] = 3] = \"User\";\n  return RoleType;\n}({});\nexport const DEFAULT_PAGINATION = {\n  pageNo: 1,\n  offset: 10\n};\nexport const STARRATINGMAP = {\n  ONE: 1,\n  TWO: 2,\n  THREE: 3,\n  FOUR: 4,\n  FIVE: 5\n};", "map": {"version": 3, "names": ["RoleType", "DEFAULT_PAGINATION", "pageNo", "offset", "STARRATINGMAP", "ONE", "TWO", "THREE", "FOUR", "FIVE"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/constants/dbConstant.constant.tsx"], "sourcesContent": ["import { IPaginationModel } from \"../interfaces/IPaginationModel\";\n\nexport enum RoleType {\n  Admin = 1,\n  Manager = 2,\n  User = 3,\n}\n\nexport const DEFAULT_PAGINATION: IPaginationModel = {\n  pageNo: 1,\n  offset: 10,\n};\n\nexport const STARRATINGMAP = {\n  ONE: 1,\n  TWO: 2,\n  THREE: 3,\n  FOUR: 4,\n  FIVE: 5,\n};\n"], "mappings": "AAEA,WAAYA,QAAQ,0BAARA,QAAQ;EAARA,QAAQ,CAARA,QAAQ;EAARA,QAAQ,CAARA,QAAQ;EAARA,QAAQ,CAARA,QAAQ;EAAA,OAARA,QAAQ;AAAA;AAMpB,OAAO,MAAMC,kBAAoC,GAAG;EAClDC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE;AACV,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG;EAC3BC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,CAAC;EACRC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE;AACR,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}