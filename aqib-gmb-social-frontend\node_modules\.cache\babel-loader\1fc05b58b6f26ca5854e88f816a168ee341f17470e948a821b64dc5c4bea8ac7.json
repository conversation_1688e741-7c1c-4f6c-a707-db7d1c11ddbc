{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\screens\\\\createSocialPost\\\\components\\\\submitPost.component.tsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useEffect, useState } from \"react\";\nimport { Button, MenuItem, FormControl, InputLabel, OutlinedInput, Chip, Accordion, AccordionSummary, AccordionDetails, Typography, Checkbox, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, FormControlLabel, Box } from \"@mui/material\";\nimport Select from \"@mui/material/Select\";\nimport BusinessService from \"../../../services/business/business.service\";\nimport { LoadingContext } from \"../../../context/loading.context\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Formik } from \"formik\";\nimport * as yup from \"yup\";\nimport ExpandMoreIcon from \"@mui/icons-material/ExpandMore\";\nimport { FormHelperText } from \"@mui/material\";\nimport { TOPIC_TYPES } from \"../../../constants/application.constant\";\nimport Stack from \"@mui/material/Stack\";\n\n//Css Import\n// import \"../createSocialPost/components/submitPost.component.style.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SubmitPost = props => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    userInfo\n  } = useSelector(state => state.authReducer);\n  const {\n    isShow,\n    closeModal,\n    createPostModel,\n    savePosts\n  } = props;\n  const [open, setOpen] = useState(isShow);\n  const [selectedValue, setSelectedValue] = useState(\"\"); // Single select\n  const [selectedOptions, setSelectedOptions] = useState([]); // Multi-select\n  const _businessService = new BusinessService(dispatch);\n  const handleOpen = () => setOpen(true);\n  const handleClose = () => setOpen(false);\n  const [businessGroups, setBusinessGroups] = useState([]);\n  const [locations, setLocations] = useState([]);\n  const [businessList, setBusinessList] = useState([]);\n  const {\n    setLoading\n  } = useContext(LoadingContext);\n  const handleDropdownChange = event => {\n    setSelectedValue(event.target.value);\n  };\n  const [selectedLocationData, setSelectedLocationData] = useState([]);\n  const handleMultiSelectChange = event => {\n    setSelectedOptions(event.target.value);\n  };\n  const [selectAllLocations, setSelectAllLocations] = useState(false);\n  const [expandedAccordions, setExpandedAccordions] = useState([]);\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const handleNext = () => {\n    if (createPostModel) {\n      setCurrentIndex(prevIndex => (prevIndex + 1) % (createPostModel === null || createPostModel === void 0 ? void 0 : createPostModel.googleRequest.media.length));\n    }\n  };\n  const handlePrev = () => {\n    if (createPostModel) {\n      setCurrentIndex(prevIndex => (prevIndex - 1 + (createPostModel === null || createPostModel === void 0 ? void 0 : createPostModel.googleRequest.media.length)) % (createPostModel === null || createPostModel === void 0 ? void 0 : createPostModel.googleRequest.media.length));\n    }\n  };\n  const DEBUG_MODE = !(process.env.NODE_ENV === \"development\");\n  const SubmitPostSchema = yup.object().shape({\n    businessId: yup.number().typeError(\"Business ID must be a number\") // Ensure it's a number\n    .moreThan(0, \"Business ID is required\").required(\"Business ID is required\"),\n    // Ensure > 0.required(\"Business ID is required\"),\n    accountId: yup.array().of(yup.string().required()).min(1, \"At least one account ID is required\").required(),\n    locationId: yup.array().of(yup.object().shape({})) // Any object type allowed\n    .min(1, \"At least one location is required\").required(\"Location array is required\")\n  });\n  const INITIAL_VALUES = {\n    businessId: 0,\n    accountId: [],\n    locationId: []\n  };\n  const [initialValues, setInitialValues] = useState(INITIAL_VALUES);\n  const getBusiness = async () => {\n    try {\n      setLoading(true);\n      let businessListResp = await _businessService.getBusiness(userInfo.id);\n      if (businessListResp.list.length > 0) {\n        setBusinessList(businessListResp.list);\n      }\n    } catch (error) {}\n    setLoading(false);\n  };\n  const getBusinessGroups = async () => {\n    try {\n      setLoading(true);\n      let businessGroups = await _businessService.getBusinessGroups(userInfo.id);\n      if (businessGroups.data.length > 0) {\n        setBusinessGroups(businessGroups.data);\n      }\n    } catch (error) {}\n    setLoading(false);\n  };\n  const getLocationsList = async () => {\n    try {\n      setLoading(true);\n      let locationsList = await _businessService.getLocations(userInfo.id);\n      if (locationsList.list.length > 0) {\n        setLocations(locationsList.list);\n      }\n    } catch (error) {}\n    setLoading(false);\n  };\n  useEffect(() => {\n    getBusiness();\n    getLocationsList();\n  }, []);\n\n  // Effect to update selectAllLocations state when initialValues changes\n  useEffect(() => {\n    if (initialValues && initialValues.locationId && initialValues.accountId) {\n      const filteredLocations = locations.filter(x => initialValues.accountId.includes(x.gmbAccountId));\n      if (filteredLocations.length > 0 && initialValues.locationId.length === filteredLocations.length) {\n        setSelectAllLocations(true);\n\n        // Open all location accordions when all are selected\n        const locationIds = filteredLocations.map(loc => loc.gmbLocationId);\n        setExpandedAccordions(locationIds);\n      } else {\n        setSelectAllLocations(false);\n      }\n    }\n  }, [initialValues.locationId, initialValues.accountId, locations]);\n  const ITEM_HEIGHT = 48;\n  const ITEM_PADDING_TOP = 8;\n  const MenuProps = {\n    PaperProps: {\n      style: {\n        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,\n        width: 250\n      }\n    }\n  };\n  const _handleCreatePosts = async (values, formikHelpers) => {\n    if (!DEBUG_MODE) {\n      const isValid = await SubmitPostSchema.isValid(values);\n      console.log(props);\n      if (isValid) {\n        savePosts && savePosts(selectedLocationData);\n      }\n    }\n  };\n  const handleValidation = async values => {\n    try {\n      // Validate form using Yup schema\n      await SubmitPostSchema.validate(values, {\n        abortEarly: false\n      });\n      console.log(\"Form Submitted Successfully ✅\", values);\n    } catch (validationError) {\n      // Convert Yup validation error into Formik-compatible errors\n      const formattedErrors = validationError.inner.reduce((acc, err) => {\n        acc[err.path] = err.message;\n        return acc;\n      }, {});\n      return formattedErrors;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Formik, {\n    enableReinitialize: true,\n    initialValues: {\n      ...initialValues\n    },\n    validationSchema: SubmitPostSchema,\n    onSubmit: (values, formikHelpers) => {\n      _handleCreatePosts(values, formikHelpers);\n    },\n    validate: handleValidation,\n    children: ({\n      values,\n      errors,\n      touched,\n      handleChange,\n      handleBlur,\n      handleSubmit,\n      setFieldValue,\n      handleReset,\n      isSubmitting,\n      isValid,\n      setSubmitting\n      /* and other goodies */\n    }) => /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      onReset: handleReset,\n      className: \"commonModal\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: \"height100\",\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          id: \"modal-modal-title\",\n          variant: \"h6\",\n          component: \"h2\",\n          className: \"modal-modal-title\",\n          children: \"Select Locations to Create Post\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          id: \"modal-modal-description\",\n          className: \"modal-modal-description\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"commonInput qwe\",\n              children: [\" \", /*#__PURE__*/_jsxDEV(FormControl, {\n                variant: \"outlined\",\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"outlined-country-dropdown-label\",\n                  children: \"Business\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  fullWidth: true,\n                  id: \"businessId\",\n                  label: \"Business\",\n                  value: values.businessId.toString(),\n                  onChange: evt => {\n                    getBusinessGroups();\n                    setFieldValue(\"accountId\", []);\n                    setFieldValue(\"locationId\", []);\n                    setTimeout(() => {\n                      setFieldValue(\"businessId\", +evt.target.value);\n                    }, 1000);\n                  },\n                  sx: {\n                    backgroundColor: \"var(--whiteColor)\",\n                    borderRadius: \"5px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: 0,\n                    children: \"Select\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 23\n                  }, this), businessList && businessList.map(business => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: business.id.toString(),\n                    children: business.businessName\n                  }, business.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 21\n                }, this), errors.businessId && touched.businessId && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  className: \"errorMessage\",\n                  children: errors.businessId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"commonInput\",\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                variant: \"outlined\",\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"demo-multiple-name-label\",\n                  children: \"Groups\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  id: \"accountId\",\n                  multiple: true,\n                  value: values.accountId,\n                  onChange: event => {\n                    const {\n                      target: {\n                        value\n                      }\n                    } = event;\n                    setFieldValue(\"accountId\", value);\n                  },\n                  input: /*#__PURE__*/_jsxDEV(OutlinedInput, {\n                    label: \"Business\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 30\n                  }, this),\n                  MenuProps: MenuProps,\n                  sx: {\n                    backgroundColor: \"var(--whiteColor)\",\n                    borderRadius: \"5px\"\n                  },\n                  children: businessGroups.filter(x => x.businessId === values.businessId).map(businessGroup => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: businessGroup.accountId,\n                    children: businessGroup.accountName\n                  }, businessGroup.accountId, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: \"flex\"\n                  },\n                  children: values.accountId.map(value => /*#__PURE__*/_jsxDEV(Chip, {\n                    label: businessGroups.filter(x => x.accountId === value)[0].accountName || \"\",\n                    clickable: true,\n                    style: {\n                      margin: 2,\n                      backgroundColor: \"#FFF\"\n                    },\n                    onDelete: e => {\n                      setFieldValue(\"accountId\", values.accountId.filter(x => x != value));\n                      setFieldValue(\"locationId\", values.locationId.filter(x => x.gmbAccountId != value));\n                    },\n                    onClick: () => console.log(\"clicked chip\"),\n                    variant: \"outlined\",\n                    color: \"primary\"\n                  }, value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 21\n                }, this), errors.accountId && touched.accountId && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  className: \"errorMessage\",\n                  children: errors.accountId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 23\n                }, this), errors && errors.locationId && Object.keys(errors).length > 0 && Object.keys(errors).length == 1 && Object.entries(errors).map(([field, errorMsg]) => field === \"locationId\" && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  className: \"errorMessage\",\n                  children: `${errorMsg}`\n                }, field, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 29\n                }, this)), locations.filter(x => values.accountId.includes(x.gmbAccountId)).length > 0 && /*#__PURE__*/_jsxDEV(Accordion, {\n                  expanded: false,\n                  sx: {\n                    \"&.MuiAccordion-root\": {\n                      \"&:before\": {\n                        display: \"none\"\n                      }\n                    },\n                    \"& .MuiAccordionSummary-root\": {\n                      minHeight: \"48px\",\n                      \"&.Mui-expanded\": {\n                        minHeight: \"48px\"\n                      }\n                    }\n                  },\n                  children: /*#__PURE__*/_jsxDEV(AccordionSummary, {\n                    expandIcon: null,\n                    onClick: e => {\n                      // Prevent accordion from expanding/collapsing\n                      e.stopPropagation();\n                    },\n                    children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                      control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                        checked: selectAllLocations || values.locationId.length > 0 && values.locationId.length === locations.filter(x => values.accountId.includes(x.gmbAccountId)).length,\n                        onChange: (event, checked) => {\n                          event.stopPropagation(); // Prevent accordion from toggling when checkbox is clicked\n                          setSelectAllLocations(checked);\n\n                          // Get all location IDs\n                          const allLocations = locations.filter(x => values.accountId.includes(x.gmbAccountId));\n                          if (checked) {\n                            // Select all locations\n                            setFieldValue(\"locationId\", allLocations);\n\n                            // Open all location accordions\n                            const locationIds = allLocations.map(loc => loc.gmbLocationId);\n                            setExpandedAccordions([...locationIds]);\n\n                            // Add all locations to selectedLocationData\n                            if (createPostModel) {\n                              const newSelectedLocationData = [];\n                              allLocations.forEach(location => {\n                                let postData = {\n                                  ...createPostModel.googleRequest\n                                };\n                                if (postData && postData.topicType === TOPIC_TYPES.Event) {\n                                  postData.summary = postData.summary.split(\"{{Address}}\").join(location.gmbLocationName);\n                                  postData.summary = postData.summary.split(\"{{Area}}\").join(location.locality);\n                                  postData.summary = postData.summary.split(\"{{Pincode}}\").join(location.postalCode);\n                                }\n                                const accountInfo = businessGroups.filter(x => x.accountId === location.gmbAccountId)[0];\n                                const business = businessList.filter(x => x.id === accountInfo.businessId)[0];\n                                newSelectedLocationData.push({\n                                  locationInfo: {\n                                    businessId: business.id,\n                                    businessName: business.businessName,\n                                    accountId: accountInfo.id,\n                                    accountName: accountInfo.accountName,\n                                    locationId: location.id,\n                                    locationName: location.gmbLocationName\n                                  },\n                                  createGooglePost: postData,\n                                  scheduleLater: null\n                                });\n                              });\n                              setSelectedLocationData(newSelectedLocationData);\n                            }\n                          } else {\n                            // Unselect all locations\n                            setFieldValue(\"locationId\", []);\n                            setSelectedLocationData([]);\n\n                            // Close all accordions\n                            setExpandedAccordions([]);\n                          }\n                        },\n                        onClick: e => e.stopPropagation() // Prevent accordion from toggling when checkbox is clicked\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 489,\n                        columnNumber: 31\n                      }, this),\n                      label: \"Select All Locations\",\n                      sx: {\n                        flexGrow: 1\n                      },\n                      onClick: e => e.stopPropagation() // Prevent accordion from toggling when label is clicked\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 23\n                }, this), locations.filter(x => values.accountId.includes(x.gmbAccountId)).map((location, index) => /*#__PURE__*/_jsxDEV(Accordion, {\n                  expanded: expandedAccordions.includes(location.gmbLocationId),\n                  children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n                    expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 41\n                    }, this),\n                    onClick: () => {\n                      // Toggle the expansion state of this accordion\n                      if (expandedAccordions.includes(location.gmbLocationId)) {\n                        setExpandedAccordions(expandedAccordions.filter(id => id !== location.gmbLocationId));\n                      } else {\n                        setExpandedAccordions([...expandedAccordions, location.gmbLocationId]);\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                      control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                        checked: values.locationId.filter(x => x.gmbLocationId === location.gmbLocationId).length > 0,\n                        onChange: (event, checked) => {\n                          event.stopPropagation(); // Prevent accordion from toggling when checkbox is clicked\n                          if (checked) {\n                            // Add this location to the selected locations\n                            const newLocationIds = [...values.locationId, location];\n                            setFieldValue(\"locationId\", newLocationIds);\n\n                            // Open this accordion when selected\n                            if (!expandedAccordions.includes(location.gmbLocationId)) {\n                              setExpandedAccordions([...expandedAccordions, location.gmbLocationId]);\n                            }\n\n                            // Check if all locations are now selected\n                            const filteredLocations = locations.filter(x => values.accountId.includes(x.gmbAccountId));\n                            if (newLocationIds.length === filteredLocations.length) {\n                              setSelectAllLocations(true);\n                            }\n                            if (createPostModel) {\n                              let postData = {\n                                ...createPostModel.googleRequest\n                              };\n                              if (postData && postData.topicType === TOPIC_TYPES.Event) {\n                                postData.summary = postData.summary.split(\"{{Address}}\").join(location.gmbLocationName);\n                                postData.summary = postData.summary.split(\"{{Area}}\").join(location.locality);\n                                postData.summary = postData.summary.split(\"{{Pincode}}\").join(location.postalCode);\n                              }\n                              const accountInfo = businessGroups.filter(x => x.accountId === location.gmbAccountId)[0];\n                              const business = businessList.filter(x => x.id === accountInfo.businessId)[0];\n                              const selectedLocationsDataInfo = {\n                                locationInfo: {\n                                  businessId: business.id,\n                                  businessName: business.businessName,\n                                  accountId: accountInfo.id,\n                                  accountName: accountInfo.accountName,\n                                  locationId: location.id,\n                                  locationName: location.gmbLocationName\n                                },\n                                createGooglePost: postData,\n                                scheduleLater: null\n                              };\n                              setSelectedLocationData([...selectedLocationData, selectedLocationsDataInfo]);\n                            }\n                          } else {\n                            // Remove this location from the selected locations\n                            const newLocationIds = values.locationId.filter(x => x.gmbLocationId != location.gmbLocationId);\n                            setFieldValue(\"locationId\", newLocationIds);\n\n                            // Close this accordion when unselected\n                            setExpandedAccordions(expandedAccordions.filter(id => id !== location.gmbLocationId));\n\n                            // If any location is unselected, uncheck the \"Select All\" checkbox\n                            setSelectAllLocations(false);\n\n                            // Also remove from selectedLocationData\n                            setSelectedLocationData(selectedLocationData.filter(item => item.locationInfo.locationId !== location.id));\n                          }\n\n                          // Prevent the click from propagating to the accordion\n                          event.stopPropagation();\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 634,\n                        columnNumber: 33\n                      }, this),\n                      label: location.gmbLocationName,\n                      sx: {\n                        flexGrow: 1\n                      },\n                      onClick: e => e.stopPropagation() // Prevent accordion from toggling when label is clicked\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 632,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n                    children: /*#__PURE__*/_jsxDEV(TableContainer, {\n                      component: Paper,\n                      children: /*#__PURE__*/_jsxDEV(Table, {\n                        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                          children: /*#__PURE__*/_jsxDEV(TableRow, {\n                            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                              children: /*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Locality\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 787,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 786,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                              children: /*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Zip Code\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 790,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 789,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                              children: /*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Address\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 793,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 792,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                              children: /*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Primary Phone\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 796,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 795,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 785,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 784,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                          children: /*#__PURE__*/_jsxDEV(TableRow, {\n                            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                              children: location.locality\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 802,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                              children: location.postalCode\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 803,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                              children: location.gmbLocationName\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 804,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                              children: location.primaryPhone\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 807,\n                              columnNumber: 37\n                            }, this)]\n                          }, index, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 801,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 800,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 783,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 782,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 27\n                  }, this)]\n                }, location.gmbLocationId, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"\",\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            className: \"commonFooter\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              className: \"secondaryOutlineBtn\",\n              onClick: closeModal,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 961,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              variant: \"contained\",\n              className: \"primaryFillBtn\",\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 960,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 959,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 290,\n    columnNumber: 5\n  }, this);\n};\n_s(SubmitPost, \"jOwb4NV1xM2ndZ/qnK0/l+8hMpM=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = SubmitPost;\nexport default SubmitPost;\nvar _c;\n$RefreshReg$(_c, \"SubmitPost\");", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useState", "<PERSON><PERSON>", "MenuItem", "FormControl", "InputLabel", "OutlinedInput", "Chip", "Accordion", "AccordionSummary", "AccordionDetails", "Typography", "Checkbox", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "FormControlLabel", "Box", "Select", "BusinessService", "LoadingContext", "useDispatch", "useSelector", "<PERSON><PERSON>", "yup", "ExpandMoreIcon", "FormHelperText", "TOPIC_TYPES", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "SubmitPost", "props", "_s", "dispatch", "userInfo", "state", "authReducer", "isShow", "closeModal", "createPostModel", "savePosts", "open", "<PERSON><PERSON><PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedValue", "selectedOptions", "setSelectedOptions", "_businessService", "handleOpen", "handleClose", "businessGroups", "setBusinessGroups", "locations", "setLocations", "businessList", "setBusinessList", "setLoading", "handleDropdownChange", "event", "target", "value", "selectedLocationData", "setSelectedLocationData", "handleMultiSelectChange", "selectAllLocations", "setSelectAllLocations", "expandedAccordions", "setExpandedAccordions", "currentIndex", "setCurrentIndex", "handleNext", "prevIndex", "googleRequest", "media", "length", "handlePrev", "DEBUG_MODE", "process", "env", "NODE_ENV", "SubmitPostSchema", "object", "shape", "businessId", "number", "typeError", "moreThan", "required", "accountId", "array", "of", "string", "min", "locationId", "INITIAL_VALUES", "initialValues", "setInitialValues", "getBusiness", "businessListResp", "id", "list", "error", "getBusinessGroups", "data", "getLocationsList", "locationsList", "getLocations", "filteredLocations", "filter", "x", "includes", "gmbAccountId", "locationIds", "map", "loc", "gmbLocationId", "ITEM_HEIGHT", "ITEM_PADDING_TOP", "MenuProps", "PaperProps", "style", "maxHeight", "width", "_handleCreatePosts", "values", "formikHelpers", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "handleValidation", "validate", "abort<PERSON><PERSON><PERSON>", "validationError", "formattedErrors", "inner", "reduce", "acc", "err", "path", "message", "enableReinitialize", "validationSchema", "onSubmit", "children", "errors", "touched", "handleChange", "handleBlur", "handleSubmit", "setFieldValue", "handleReset", "isSubmitting", "setSubmitting", "onReset", "className", "variant", "component", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "label", "toString", "onChange", "evt", "setTimeout", "sx", "backgroundColor", "borderRadius", "business", "businessName", "multiple", "input", "businessGroup", "accountName", "display", "clickable", "margin", "onDelete", "e", "onClick", "color", "Object", "keys", "entries", "field", "errorMsg", "expanded", "minHeight", "expandIcon", "stopPropagation", "control", "checked", "allLocations", "newSelectedLocationData", "for<PERSON>ach", "location", "postData", "topicType", "Event", "summary", "split", "join", "gmbLocationName", "locality", "postalCode", "accountInfo", "push", "locationInfo", "locationName", "createGooglePost", "scheduleLater", "flexGrow", "index", "newLocationIds", "selectedLocationsDataInfo", "item", "primaryPhone", "direction", "type", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/screens/createSocialPost/components/submitPost.component.tsx"], "sourcesContent": ["import React, { useContext, useEffect, useState } from \"react\";\r\nimport {\r\n  Button,\r\n  Modal,\r\n  MenuItem,\r\n  FormControl,\r\n  InputLabel,\r\n  ListItemText,\r\n  OutlinedInput,\r\n  Chip,\r\n  Accordion,\r\n  AccordionSummary,\r\n  AccordionDetails,\r\n  Typography,\r\n  Checkbox,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  Paper,\r\n  FormControlLabel,\r\n  Box,\r\n  Dialog,\r\n  AppBar,\r\n  IconButton,\r\n  Toolbar,\r\n} from \"@mui/material\";\r\nimport Select, { SelectChangeEvent } from \"@mui/material/Select\";\r\nimport {\r\n  IBusinessGroup,\r\n  IBusinessGroupsResponseModel,\r\n} from \"../../../interfaces/response/IBusinessGroupsResponseModel\";\r\nimport BusinessService from \"../../../services/business/business.service\";\r\nimport {\r\n  ILocation,\r\n  ILocationsListResponseModel,\r\n} from \"../../../interfaces/response/ILocationsListResponseModel\";\r\nimport { LoadingContext } from \"../../../context/loading.context\";\r\nimport {\r\n  IBusiness,\r\n  IBusinessListResponseModel,\r\n} from \"../../../interfaces/response/IBusinessListResponseModel\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { Formik } from \"formik\";\r\nimport * as yup from \"yup\";\r\nimport { RoleType } from \"../../../constants/dbConstant.constant\";\r\nimport ExpandMoreIcon from \"@mui/icons-material/ExpandMore\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { IGoogleCreatePost } from \"../../../interfaces/request/IGoogleCreatePost\";\r\nimport Card from \"@mui/material/Card\";\r\nimport { CardContent } from \"@mui/material\";\r\nimport { CardMedia, Grid } from \"@mui/material\";\r\nimport { ArrowBackIos, ArrowForwardIos } from \"@mui/icons-material\";\r\nimport LinearProgressWithLabel from \"../../../components/LinearProgressWithLabel/LinearProgressWithLabel.component\";\r\nimport { FormHelperText, InputAdornment } from \"@mui/material\";\r\nimport { TOPIC_TYPES } from \"../../../constants/application.constant\";\r\nimport { ISchedulePostRequestModel } from \"../../../interfaces/request/ISchedulePostRequestModel\";\r\nimport Stack from \"@mui/material/Stack\";\r\n\r\n//Css Import\r\n// import \"../createSocialPost/components/submitPost.component.style.css\";\r\n\r\ntype CreatePostData = {\r\n  googleRequest: IGoogleCreatePost;\r\n  schedule?: any;\r\n  images: any;\r\n};\r\n\r\nexport interface ICreatePostLocation {\r\n  businessId: number;\r\n  accountId: string[];\r\n  locationId: ILocation[];\r\n}\r\n\r\nexport interface IModalWithSelect {\r\n  isShow: boolean;\r\n  closeModal?: () => void | undefined;\r\n  createPostModel?: CreatePostData;\r\n  savePosts?: (\r\n    createGooglePostList: ISelectionLocationWithPost[]\r\n  ) => Promise<void> | undefined;\r\n}\r\n\r\nexport interface ISelectedLocations {\r\n  businessId: number;\r\n  businessName: string;\r\n  accountId: number;\r\n  accountName: string;\r\n  locationId: number;\r\n  locationName: string;\r\n  status?: boolean;\r\n  viewUrl?: string;\r\n}\r\n\r\nexport interface ISelectionLocationWithPost {\r\n  locationInfo: ISelectedLocations;\r\n  createGooglePost: IGoogleCreatePost;\r\n  scheduleLater: ISchedulePostRequestModel | null;\r\n}\r\n\r\nconst SubmitPost = (props: IModalWithSelect) => {\r\n  const dispatch = useDispatch();\r\n  const { userInfo } = useSelector((state: any) => state.authReducer);\r\n  const { isShow, closeModal, createPostModel, savePosts } = props;\r\n  const [open, setOpen] = useState(isShow);\r\n  const [selectedValue, setSelectedValue] = useState(\"\"); // Single select\r\n  const [selectedOptions, setSelectedOptions] = useState([]); // Multi-select\r\n  const _businessService = new BusinessService(dispatch);\r\n  const handleOpen = () => setOpen(true);\r\n  const handleClose = () => setOpen(false);\r\n  const [businessGroups, setBusinessGroups] = useState<IBusinessGroup[]>([]);\r\n  const [locations, setLocations] = useState<ILocation[]>([]);\r\n  const [businessList, setBusinessList] = useState<IBusiness[]>([]);\r\n  const { setLoading } = useContext(LoadingContext);\r\n  const handleDropdownChange = (event: any) => {\r\n    setSelectedValue(event.target.value);\r\n  };\r\n\r\n  const [selectedLocationData, setSelectedLocationData] = useState<\r\n    ISelectionLocationWithPost[]\r\n  >([]);\r\n\r\n  const handleMultiSelectChange = (event: any) => {\r\n    setSelectedOptions(event.target.value);\r\n  };\r\n\r\n  const [selectAllLocations, setSelectAllLocations] = useState(false);\r\n  const [expandedAccordions, setExpandedAccordions] = useState<string[]>([]);\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n\r\n  const handleNext = () => {\r\n    if (createPostModel) {\r\n      setCurrentIndex(\r\n        (prevIndex) =>\r\n          (prevIndex + 1) % createPostModel?.googleRequest.media.length\r\n      );\r\n    }\r\n  };\r\n\r\n  const handlePrev = () => {\r\n    if (createPostModel) {\r\n      setCurrentIndex(\r\n        (prevIndex) =>\r\n          (prevIndex - 1 + createPostModel?.googleRequest.media.length) %\r\n          createPostModel?.googleRequest.media.length\r\n      );\r\n    }\r\n  };\r\n\r\n  const DEBUG_MODE = !(process.env.NODE_ENV === \"development\");\r\n\r\n  const SubmitPostSchema = yup.object().shape({\r\n    businessId: yup\r\n      .number()\r\n      .typeError(\"Business ID must be a number\") // Ensure it's a number\r\n      .moreThan(0, \"Business ID is required\")\r\n      .required(\"Business ID is required\"), // Ensure > 0.required(\"Business ID is required\"),\r\n    accountId: yup\r\n      .array()\r\n      .of(yup.string().required())\r\n      .min(1, \"At least one account ID is required\")\r\n      .required(),\r\n    locationId: yup\r\n      .array()\r\n      .of(yup.object().shape({})) // Any object type allowed\r\n      .min(1, \"At least one location is required\")\r\n      .required(\"Location array is required\"),\r\n  });\r\n\r\n  const INITIAL_VALUES = {\r\n    businessId: 0,\r\n    accountId: [],\r\n    locationId: [],\r\n  };\r\n  const [initialValues, setInitialValues] =\r\n    useState<ICreatePostLocation>(INITIAL_VALUES);\r\n\r\n  const getBusiness = async () => {\r\n    try {\r\n      setLoading(true);\r\n      let businessListResp: IBusinessListResponseModel =\r\n        await _businessService.getBusiness(userInfo.id);\r\n      if (businessListResp.list.length > 0) {\r\n        setBusinessList(businessListResp.list);\r\n      }\r\n    } catch (error) {}\r\n\r\n    setLoading(false);\r\n  };\r\n\r\n  const getBusinessGroups = async () => {\r\n    try {\r\n      setLoading(true);\r\n      let businessGroups: IBusinessGroupsResponseModel =\r\n        await _businessService.getBusinessGroups(userInfo.id);\r\n      if (businessGroups.data.length > 0) {\r\n        setBusinessGroups(businessGroups.data);\r\n      }\r\n    } catch (error) {}\r\n\r\n    setLoading(false);\r\n  };\r\n\r\n  const getLocationsList = async () => {\r\n    try {\r\n      setLoading(true);\r\n      let locationsList: ILocationsListResponseModel =\r\n        await _businessService.getLocations(userInfo.id);\r\n      if (locationsList.list.length > 0) {\r\n        setLocations(locationsList.list);\r\n      }\r\n    } catch (error) {}\r\n\r\n    setLoading(false);\r\n  };\r\n\r\n  useEffect(() => {\r\n    getBusiness();\r\n    getLocationsList();\r\n  }, []);\r\n\r\n  // Effect to update selectAllLocations state when initialValues changes\r\n  useEffect(() => {\r\n    if (initialValues && initialValues.locationId && initialValues.accountId) {\r\n      const filteredLocations = locations.filter((x) =>\r\n        initialValues.accountId.includes(x.gmbAccountId)\r\n      );\r\n\r\n      if (\r\n        filteredLocations.length > 0 &&\r\n        initialValues.locationId.length === filteredLocations.length\r\n      ) {\r\n        setSelectAllLocations(true);\r\n\r\n        // Open all location accordions when all are selected\r\n        const locationIds = filteredLocations.map((loc) => loc.gmbLocationId);\r\n        setExpandedAccordions(locationIds);\r\n      } else {\r\n        setSelectAllLocations(false);\r\n      }\r\n    }\r\n  }, [initialValues.locationId, initialValues.accountId, locations]);\r\n\r\n  const ITEM_HEIGHT = 48;\r\n  const ITEM_PADDING_TOP = 8;\r\n  const MenuProps = {\r\n    PaperProps: {\r\n      style: {\r\n        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,\r\n        width: 250,\r\n      },\r\n    },\r\n  };\r\n\r\n  const _handleCreatePosts = async (\r\n    values: ICreatePostLocation,\r\n    formikHelpers: any\r\n  ) => {\r\n    if (!DEBUG_MODE) {\r\n      const isValid = await SubmitPostSchema.isValid(values);\r\n      console.log(props);\r\n      if (isValid) {\r\n        savePosts && savePosts(selectedLocationData);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleValidation = async (values: ICreatePostLocation) => {\r\n    try {\r\n      // Validate form using Yup schema\r\n      await SubmitPostSchema.validate(values, { abortEarly: false });\r\n      console.log(\"Form Submitted Successfully ✅\", values);\r\n    } catch (validationError: any) {\r\n      // Convert Yup validation error into Formik-compatible errors\r\n      const formattedErrors = validationError.inner.reduce(\r\n        (acc: any, err: any) => {\r\n          acc[err.path] = err.message;\r\n          return acc;\r\n        },\r\n        {}\r\n      );\r\n\r\n      return formattedErrors;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Formik\r\n      enableReinitialize\r\n      initialValues={{ ...initialValues }}\r\n      validationSchema={SubmitPostSchema}\r\n      onSubmit={(values, formikHelpers) => {\r\n        _handleCreatePosts(values, formikHelpers);\r\n      }}\r\n      validate={handleValidation}\r\n    >\r\n      {({\r\n        values,\r\n        errors,\r\n        touched,\r\n        handleChange,\r\n        handleBlur,\r\n        handleSubmit,\r\n        setFieldValue,\r\n        handleReset,\r\n        isSubmitting,\r\n        isValid,\r\n        setSubmitting,\r\n        /* and other goodies */\r\n      }) => (\r\n        <form\r\n          onSubmit={handleSubmit}\r\n          onReset={handleReset}\r\n          className=\"commonModal\"\r\n        >\r\n          <Box className=\"height100\">\r\n            <Typography\r\n              id=\"modal-modal-title\"\r\n              variant=\"h6\"\r\n              component=\"h2\"\r\n              className=\"modal-modal-title\"\r\n            >\r\n              Select Locations to Create Post\r\n            </Typography>\r\n\r\n            <Box\r\n              id=\"modal-modal-description\"\r\n              className=\"modal-modal-description\"\r\n            >\r\n              <Box>\r\n                <Box className=\"commonInput qwe\">\r\n                  {\" \"}\r\n                  <FormControl variant=\"outlined\" fullWidth>\r\n                    <InputLabel id=\"outlined-country-dropdown-label\">\r\n                      Business\r\n                    </InputLabel>\r\n\r\n                    <Select\r\n                      fullWidth\r\n                      id=\"businessId\"\r\n                      label=\"Business\"\r\n                      value={values.businessId.toString()}\r\n                      onChange={(evt: SelectChangeEvent) => {\r\n                        getBusinessGroups();\r\n                        setFieldValue(\"accountId\", []);\r\n                        setFieldValue(\"locationId\", []);\r\n                        setTimeout(() => {\r\n                          setFieldValue(\"businessId\", +evt.target.value);\r\n                        }, 1000);\r\n                      }}\r\n                      sx={{\r\n                        backgroundColor: \"var(--whiteColor)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    >\r\n                      <MenuItem value={0}>Select</MenuItem>\r\n                      {businessList &&\r\n                        businessList.map((business: IBusiness) => (\r\n                          <MenuItem\r\n                            key={business.id}\r\n                            value={business.id.toString()}\r\n                          >\r\n                            {business.businessName}\r\n                          </MenuItem>\r\n                        ))}\r\n                    </Select>\r\n                    {errors.businessId && touched.businessId && (\r\n                      <FormHelperText className=\"errorMessage\">\r\n                        {errors.businessId}\r\n                      </FormHelperText>\r\n                    )}\r\n                  </FormControl>\r\n                </Box>\r\n                <Box className=\"commonInput\">\r\n                  <FormControl variant=\"outlined\" fullWidth>\r\n                    <InputLabel id=\"demo-multiple-name-label\">\r\n                      Groups\r\n                    </InputLabel>\r\n                    <Select\r\n                      id=\"accountId\"\r\n                      multiple={true}\r\n                      value={values.accountId}\r\n                      onChange={(event: SelectChangeEvent<string[]>) => {\r\n                        const {\r\n                          target: { value },\r\n                        } = event;\r\n                        setFieldValue(\"accountId\", value);\r\n                      }}\r\n                      input={<OutlinedInput label=\"Business\" />}\r\n                      MenuProps={MenuProps}\r\n                      sx={{\r\n                        backgroundColor: \"var(--whiteColor)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    >\r\n                      {businessGroups\r\n                        .filter((x) => x.businessId === values.businessId)\r\n                        .map((businessGroup: IBusinessGroup) => (\r\n                          <MenuItem\r\n                            key={businessGroup.accountId}\r\n                            value={businessGroup.accountId}\r\n                          >\r\n                            {businessGroup.accountName}\r\n                          </MenuItem>\r\n                        ))}\r\n                    </Select>\r\n                    <Box sx={{ display: \"flex\" }}>\r\n                      {values.accountId.map((value: string) => (\r\n                        <Chip\r\n                          key={value}\r\n                          label={\r\n                            businessGroups.filter(\r\n                              (x) => x.accountId === value\r\n                            )[0].accountName || \"\"\r\n                          }\r\n                          clickable\r\n                          style={{\r\n                            margin: 2,\r\n                            backgroundColor: \"#FFF\",\r\n                          }}\r\n                          onDelete={(e) => {\r\n                            setFieldValue(\r\n                              \"accountId\",\r\n                              values.accountId.filter((x) => x != value)\r\n                            );\r\n\r\n                            setFieldValue(\r\n                              \"locationId\",\r\n                              values.locationId.filter(\r\n                                (x: ILocation) => x.gmbAccountId != value\r\n                              )\r\n                            );\r\n                          }}\r\n                          onClick={() => console.log(\"clicked chip\")}\r\n                          variant=\"outlined\"\r\n                          color=\"primary\"\r\n                        />\r\n                      ))}\r\n                    </Box>\r\n                    {errors.accountId && touched.accountId && (\r\n                      <FormHelperText className=\"errorMessage\">\r\n                        {errors.accountId}\r\n                      </FormHelperText>\r\n                    )}\r\n                    {errors &&\r\n                      errors.locationId &&\r\n                      Object.keys(errors).length > 0 &&\r\n                      Object.keys(errors).length == 1 &&\r\n                      Object.entries(errors).map(\r\n                        ([field, errorMsg]) =>\r\n                          field === \"locationId\" && (\r\n                            <FormHelperText\r\n                              className=\"errorMessage\"\r\n                              key={field}\r\n                            >{`${errorMsg}`}</FormHelperText>\r\n                          )\r\n                      )}\r\n                    {/* Select All Accordion */}\r\n                    {locations.filter((x) =>\r\n                      values.accountId.includes(x.gmbAccountId)\r\n                    ).length > 0 && (\r\n                      <Accordion\r\n                        expanded={false}\r\n                        sx={{\r\n                          \"&.MuiAccordion-root\": {\r\n                            \"&:before\": {\r\n                              display: \"none\",\r\n                            },\r\n                          },\r\n                          \"& .MuiAccordionSummary-root\": {\r\n                            minHeight: \"48px\",\r\n                            \"&.Mui-expanded\": {\r\n                              minHeight: \"48px\",\r\n                            },\r\n                          },\r\n                        }}\r\n                      >\r\n                        <AccordionSummary\r\n                          expandIcon={null}\r\n                          onClick={(e) => {\r\n                            // Prevent accordion from expanding/collapsing\r\n                            e.stopPropagation();\r\n                          }}\r\n                        >\r\n                          <FormControlLabel\r\n                            control={\r\n                              <Checkbox\r\n                                checked={\r\n                                  selectAllLocations ||\r\n                                  (values.locationId.length > 0 &&\r\n                                    values.locationId.length ===\r\n                                      locations.filter((x) =>\r\n                                        values.accountId.includes(\r\n                                          x.gmbAccountId\r\n                                        )\r\n                                      ).length)\r\n                                }\r\n                                onChange={(\r\n                                  event: React.ChangeEvent<HTMLInputElement>,\r\n                                  checked: boolean\r\n                                ) => {\r\n                                  event.stopPropagation(); // Prevent accordion from toggling when checkbox is clicked\r\n                                  setSelectAllLocations(checked);\r\n\r\n                                  // Get all location IDs\r\n                                  const allLocations = locations.filter((x) =>\r\n                                    values.accountId.includes(x.gmbAccountId)\r\n                                  );\r\n\r\n                                  if (checked) {\r\n                                    // Select all locations\r\n                                    setFieldValue(\"locationId\", allLocations);\r\n\r\n                                    // Open all location accordions\r\n                                    const locationIds = allLocations.map(\r\n                                      (loc) => loc.gmbLocationId\r\n                                    );\r\n                                    setExpandedAccordions([...locationIds]);\r\n\r\n                                    // Add all locations to selectedLocationData\r\n                                    if (createPostModel) {\r\n                                      const newSelectedLocationData: ISelectionLocationWithPost[] =\r\n                                        [];\r\n\r\n                                      allLocations.forEach((location) => {\r\n                                        let postData = {\r\n                                          ...createPostModel.googleRequest,\r\n                                        };\r\n\r\n                                        if (\r\n                                          postData &&\r\n                                          postData.topicType ===\r\n                                            TOPIC_TYPES.Event\r\n                                        ) {\r\n                                          postData.summary = postData.summary\r\n                                            .split(\"{{Address}}\")\r\n                                            .join(location.gmbLocationName);\r\n                                          postData.summary = postData.summary\r\n                                            .split(\"{{Area}}\")\r\n                                            .join(location.locality);\r\n                                          postData.summary = postData.summary\r\n                                            .split(\"{{Pincode}}\")\r\n                                            .join(location.postalCode);\r\n                                        }\r\n\r\n                                        const accountInfo =\r\n                                          businessGroups.filter(\r\n                                            (x: IBusinessGroup) =>\r\n                                              x.accountId ===\r\n                                              location.gmbAccountId\r\n                                          )[0];\r\n\r\n                                        const business = businessList.filter(\r\n                                          (x: IBusiness) =>\r\n                                            x.id === accountInfo.businessId\r\n                                        )[0];\r\n\r\n                                        newSelectedLocationData.push({\r\n                                          locationInfo: {\r\n                                            businessId: business.id,\r\n                                            businessName: business.businessName,\r\n                                            accountId: accountInfo.id,\r\n                                            accountName:\r\n                                              accountInfo.accountName,\r\n                                            locationId: location.id,\r\n                                            locationName:\r\n                                              location.gmbLocationName,\r\n                                          },\r\n                                          createGooglePost: postData,\r\n                                          scheduleLater: null,\r\n                                        });\r\n                                      });\r\n\r\n                                      setSelectedLocationData(\r\n                                        newSelectedLocationData\r\n                                      );\r\n                                    }\r\n                                  } else {\r\n                                    // Unselect all locations\r\n                                    setFieldValue(\"locationId\", []);\r\n                                    setSelectedLocationData([]);\r\n\r\n                                    // Close all accordions\r\n                                    setExpandedAccordions([]);\r\n                                  }\r\n                                }}\r\n                                onClick={(e) => e.stopPropagation()} // Prevent accordion from toggling when checkbox is clicked\r\n                              />\r\n                            }\r\n                            label=\"Select All Locations\"\r\n                            sx={{ flexGrow: 1 }}\r\n                            onClick={(e) => e.stopPropagation()} // Prevent accordion from toggling when label is clicked\r\n                          />\r\n                        </AccordionSummary>\r\n                      </Accordion>\r\n                    )}\r\n\r\n                    {/* Individual Location Accordions */}\r\n                    {locations\r\n                      .filter((x) => values.accountId.includes(x.gmbAccountId))\r\n                      .map((location: ILocation, index: number) => (\r\n                        <Accordion\r\n                          key={location.gmbLocationId}\r\n                          expanded={expandedAccordions.includes(\r\n                            location.gmbLocationId\r\n                          )}\r\n                        >\r\n                          <AccordionSummary\r\n                            expandIcon={<ExpandMoreIcon />}\r\n                            onClick={() => {\r\n                              // Toggle the expansion state of this accordion\r\n                              if (\r\n                                expandedAccordions.includes(\r\n                                  location.gmbLocationId\r\n                                )\r\n                              ) {\r\n                                setExpandedAccordions(\r\n                                  expandedAccordions.filter(\r\n                                    (id) => id !== location.gmbLocationId\r\n                                  )\r\n                                );\r\n                              } else {\r\n                                setExpandedAccordions([\r\n                                  ...expandedAccordions,\r\n                                  location.gmbLocationId,\r\n                                ]);\r\n                              }\r\n                            }}\r\n                          >\r\n                            <FormControlLabel\r\n                              control={\r\n                                <Checkbox\r\n                                  checked={\r\n                                    values.locationId.filter(\r\n                                      (x: ILocation) =>\r\n                                        x.gmbLocationId ===\r\n                                        location.gmbLocationId\r\n                                    ).length > 0\r\n                                  }\r\n                                  onChange={(\r\n                                    event: React.ChangeEvent<HTMLInputElement>,\r\n                                    checked: boolean\r\n                                  ) => {\r\n                                    event.stopPropagation(); // Prevent accordion from toggling when checkbox is clicked\r\n                                    if (checked) {\r\n                                      // Add this location to the selected locations\r\n                                      const newLocationIds = [\r\n                                        ...values.locationId,\r\n                                        location,\r\n                                      ];\r\n                                      setFieldValue(\r\n                                        \"locationId\",\r\n                                        newLocationIds\r\n                                      );\r\n\r\n                                      // Open this accordion when selected\r\n                                      if (\r\n                                        !expandedAccordions.includes(\r\n                                          location.gmbLocationId\r\n                                        )\r\n                                      ) {\r\n                                        setExpandedAccordions([\r\n                                          ...expandedAccordions,\r\n                                          location.gmbLocationId,\r\n                                        ]);\r\n                                      }\r\n\r\n                                      // Check if all locations are now selected\r\n                                      const filteredLocations =\r\n                                        locations.filter((x) =>\r\n                                          values.accountId.includes(\r\n                                            x.gmbAccountId\r\n                                          )\r\n                                        );\r\n                                      if (\r\n                                        newLocationIds.length ===\r\n                                        filteredLocations.length\r\n                                      ) {\r\n                                        setSelectAllLocations(true);\r\n                                      }\r\n\r\n                                      if (createPostModel) {\r\n                                        let postData = {\r\n                                          ...createPostModel.googleRequest,\r\n                                        };\r\n\r\n                                        if (\r\n                                          postData &&\r\n                                          postData.topicType ===\r\n                                            TOPIC_TYPES.Event\r\n                                        ) {\r\n                                          postData.summary = postData.summary\r\n                                            .split(\"{{Address}}\")\r\n                                            .join(location.gmbLocationName);\r\n                                          postData.summary = postData.summary\r\n                                            .split(\"{{Area}}\")\r\n                                            .join(location.locality);\r\n                                          postData.summary = postData.summary\r\n                                            .split(\"{{Pincode}}\")\r\n                                            .join(location.postalCode);\r\n                                        }\r\n\r\n                                        const accountInfo =\r\n                                          businessGroups.filter(\r\n                                            (x: IBusinessGroup) =>\r\n                                              x.accountId ===\r\n                                              location.gmbAccountId\r\n                                          )[0];\r\n                                        const business = businessList.filter(\r\n                                          (x: IBusiness) =>\r\n                                            x.id === accountInfo.businessId\r\n                                        )[0];\r\n\r\n                                        const selectedLocationsDataInfo: ISelectionLocationWithPost =\r\n                                          {\r\n                                            locationInfo: {\r\n                                              businessId: business.id,\r\n                                              businessName:\r\n                                                business.businessName,\r\n                                              accountId: accountInfo.id,\r\n                                              accountName:\r\n                                                accountInfo.accountName,\r\n                                              locationId: location.id,\r\n                                              locationName:\r\n                                                location.gmbLocationName,\r\n                                            },\r\n                                            createGooglePost: postData,\r\n                                            scheduleLater: null,\r\n                                          };\r\n\r\n                                        setSelectedLocationData([\r\n                                          ...selectedLocationData,\r\n                                          selectedLocationsDataInfo,\r\n                                        ]);\r\n                                      }\r\n                                    } else {\r\n                                      // Remove this location from the selected locations\r\n                                      const newLocationIds =\r\n                                        values.locationId.filter(\r\n                                          (x: ILocation) =>\r\n                                            x.gmbLocationId !=\r\n                                            location.gmbLocationId\r\n                                        );\r\n                                      setFieldValue(\r\n                                        \"locationId\",\r\n                                        newLocationIds\r\n                                      );\r\n\r\n                                      // Close this accordion when unselected\r\n                                      setExpandedAccordions(\r\n                                        expandedAccordions.filter(\r\n                                          (id) => id !== location.gmbLocationId\r\n                                        )\r\n                                      );\r\n\r\n                                      // If any location is unselected, uncheck the \"Select All\" checkbox\r\n                                      setSelectAllLocations(false);\r\n\r\n                                      // Also remove from selectedLocationData\r\n                                      setSelectedLocationData(\r\n                                        selectedLocationData.filter(\r\n                                          (item) =>\r\n                                            item.locationInfo.locationId !==\r\n                                            location.id\r\n                                        )\r\n                                      );\r\n                                    }\r\n\r\n                                    // Prevent the click from propagating to the accordion\r\n                                    event.stopPropagation();\r\n                                  }}\r\n                                />\r\n                              }\r\n                              label={location.gmbLocationName}\r\n                              sx={{ flexGrow: 1 }}\r\n                              onClick={(e) => e.stopPropagation()} // Prevent accordion from toggling when label is clicked\r\n                            />\r\n                          </AccordionSummary>\r\n                          <AccordionDetails>\r\n                            <TableContainer component={Paper}>\r\n                              <Table>\r\n                                <TableHead>\r\n                                  <TableRow>\r\n                                    <TableCell>\r\n                                      <b>Locality</b>\r\n                                    </TableCell>\r\n                                    <TableCell>\r\n                                      <b>Zip Code</b>\r\n                                    </TableCell>\r\n                                    <TableCell>\r\n                                      <b>Address</b>\r\n                                    </TableCell>\r\n                                    <TableCell>\r\n                                      <b>Primary Phone</b>\r\n                                    </TableCell>\r\n                                  </TableRow>\r\n                                </TableHead>\r\n                                <TableBody>\r\n                                  <TableRow key={index}>\r\n                                    <TableCell>{location.locality}</TableCell>\r\n                                    <TableCell>{location.postalCode}</TableCell>\r\n                                    <TableCell>\r\n                                      {location.gmbLocationName}\r\n                                    </TableCell>\r\n                                    <TableCell>\r\n                                      {location.primaryPhone}\r\n                                    </TableCell>\r\n                                  </TableRow>\r\n                                </TableBody>\r\n                              </Table>\r\n                            </TableContainer>\r\n                          </AccordionDetails>\r\n                        </Accordion>\r\n                      ))}\r\n                    {/* <Grid xs={6} className=\"height100\">\r\n                  <Box className=\"lceRight\">\r\n                    <Grid container spacing={2}>\r\n                      {selectedLocationData &&\r\n                        selectedLocationData.map(\r\n                          (\r\n                            createPostModele: ISelectionLocationWithPost,\r\n                            index: number\r\n                          ) => {\r\n                            return (\r\n                              <Grid xs={12} md={6}>\r\n                                <Card\r\n                                  sx={{\r\n                                    boxShadow: 3,\r\n                                    minHeight: 450,\r\n                                    overflow: \"hidden\",\r\n                                    borderRadius: 2,\r\n                                    margin: 1,\r\n                                  }}\r\n                                >\r\n                                  {createPostModele && (\r\n                                    <CardMedia\r\n                                      component=\"div\"\r\n                                      sx={{\r\n                                        display: \"flex\",\r\n                                        justifyContent: \"center\",\r\n                                        alignItems: \"center\",\r\n                                        position: \"relative\",\r\n                                      }}\r\n                                    >\r\n                                      <img\r\n                                        src={URL.createObjectURL(\r\n                                          createPostModele.createGooglePost\r\n                                            .media[\r\n                                            currentIndex\r\n                                          ] as unknown as MediaSource\r\n                                        )}\r\n                                        alt={`Image ${currentIndex + 1}`}\r\n                                        style={{\r\n                                          width: \"100%\",\r\n                                          height: \"300px\",\r\n                                          objectFit: \"fill\",\r\n                                          borderRadius: \"8px\",\r\n                                          transition:\r\n                                            \"opacity 0.5s ease-in-out\",\r\n                                        }}\r\n                                        referrerPolicy=\"no-referrer\"\r\n                                      />\r\n\r\n                                      {createPostModele.createGooglePost.media\r\n                                        .length > 1 &&\r\n                                        currentIndex > 0 && (\r\n                                          <IconButton\r\n                                            onClick={handlePrev}\r\n                                            sx={{\r\n                                              position: \"absolute\",\r\n                                              left: 10,\r\n                                              backgroundColor:\r\n                                                \"rgba(0,0,0,0.5)\",\r\n                                              color: \"white\",\r\n                                              \"&:hover\": {\r\n                                                backgroundColor:\r\n                                                  \"rgba(0,0,0,0.7)\",\r\n                                              },\r\n                                            }}\r\n                                          >\r\n                                            <ArrowBackIos />\r\n                                          </IconButton>\r\n                                        )}\r\n\r\n                                      {createPostModele.createGooglePost.media\r\n                                        .length > 1 &&\r\n                                        currentIndex <\r\n                                          createPostModele.createGooglePost\r\n                                            .media.length && (\r\n                                          <IconButton\r\n                                            onClick={handleNext}\r\n                                            sx={{\r\n                                              position: \"absolute\",\r\n                                              right: 10,\r\n                                              backgroundColor:\r\n                                                \"rgba(0,0,0,0.5)\",\r\n                                              color: \"white\",\r\n                                              \"&:hover\": {\r\n                                                backgroundColor:\r\n                                                  \"rgba(0,0,0,0.7)\",\r\n                                              },\r\n                                            }}\r\n                                          >\r\n                                            <ArrowForwardIos />\r\n                                          </IconButton>\r\n                                        )}\r\n                                    </CardMedia>\r\n                                  )}\r\n\r\n                                  <CardContent>\r\n                                    {createPostModele.createGooglePost\r\n                                      .event && (\r\n                                      <Typography\r\n                                        variant=\"subtitle1\"\r\n                                        fontWeight=\"bold\"\r\n                                      >\r\n                                        {\r\n                                          createPostModele.createGooglePost\r\n                                            .event.title\r\n                                        }\r\n                                      </Typography>\r\n                                    )}\r\n\r\n                                    {createPostModele && (\r\n                                      <Typography\r\n                                        variant=\"body2\"\r\n                                        color=\"textSecondary\"\r\n                                      >\r\n                                        {\r\n                                          createPostModele.createGooglePost\r\n                                            .summary\r\n                                        }\r\n                                      </Typography>\r\n                                    )}\r\n\r\n                                    <Typography\r\n                                      variant=\"caption\"\r\n                                      display=\"block\"\r\n                                      sx={{ mt: 1 }}\r\n                                    >\r\n                                      Available On: 1 Location\r\n                                    </Typography>\r\n                                  </CardContent>\r\n                                </Card>\r\n                              </Grid>\r\n                            );\r\n                          }\r\n                        )}\r\n                    </Grid>\r\n                  </Box>\r\n                </Grid> */}\r\n                  </FormControl>\r\n                </Box>\r\n              </Box>\r\n            </Box>\r\n\r\n            <Box className=\"\">\r\n              <Stack direction=\"row\" className=\"commonFooter\">\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  className=\"secondaryOutlineBtn\"\r\n                  onClick={closeModal}\r\n                >\r\n                  Cancel\r\n                </Button>\r\n                <Button\r\n                  type=\"submit\"\r\n                  variant=\"contained\"\r\n                  className=\"primaryFillBtn\"\r\n                >\r\n                  Save\r\n                </Button>\r\n              </Stack>\r\n            </Box>\r\n          </Box>\r\n        </form>\r\n      )}\r\n    </Formik>\r\n  );\r\n};\r\n\r\nexport default SubmitPost;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC9D,SACEC,MAAM,EAENC,QAAQ,EACRC,WAAW,EACXC,UAAU,EAEVC,aAAa,EACbC,IAAI,EACJC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,GAAG,QAKE,eAAe;AACtB,OAAOC,MAAM,MAA6B,sBAAsB;AAKhE,OAAOC,eAAe,MAAM,6CAA6C;AAKzE,SAASC,cAAc,QAAQ,kCAAkC;AAKjE,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,QAAQ;AAC/B,OAAO,KAAKC,GAAG,MAAM,KAAK;AAE1B,OAAOC,cAAc,MAAM,gCAAgC;AAQ3D,SAASC,cAAc,QAAwB,eAAe;AAC9D,SAASC,WAAW,QAAQ,yCAAyC;AAErE,OAAOC,KAAK,MAAM,qBAAqB;;AAEvC;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAwCA,MAAMC,UAAU,GAAIC,KAAuB,IAAK;EAAAC,EAAA;EAC9C,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc;EAAS,CAAC,GAAGb,WAAW,CAAEc,KAAU,IAAKA,KAAK,CAACC,WAAW,CAAC;EACnE,MAAM;IAAEC,MAAM;IAAEC,UAAU;IAAEC,eAAe;IAAEC;EAAU,CAAC,GAAGT,KAAK;EAChE,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAG9C,QAAQ,CAACyC,MAAM,CAAC;EACxC,MAAM,CAACM,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACiD,eAAe,EAAEC,kBAAkB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5D,MAAMmD,gBAAgB,GAAG,IAAI7B,eAAe,CAACe,QAAQ,CAAC;EACtD,MAAMe,UAAU,GAAGA,CAAA,KAAMN,OAAO,CAAC,IAAI,CAAC;EACtC,MAAMO,WAAW,GAAGA,CAAA,KAAMP,OAAO,CAAC,KAAK,CAAC;EACxC,MAAM,CAACQ,cAAc,EAAEC,iBAAiB,CAAC,GAAGvD,QAAQ,CAAmB,EAAE,CAAC;EAC1E,MAAM,CAACwD,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAc,EAAE,CAAC;EAC3D,MAAM,CAAC0D,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAc,EAAE,CAAC;EACjE,MAAM;IAAE4D;EAAW,CAAC,GAAG9D,UAAU,CAACyB,cAAc,CAAC;EACjD,MAAMsC,oBAAoB,GAAIC,KAAU,IAAK;IAC3Cd,gBAAgB,CAACc,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACtC,CAAC;EAED,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlE,QAAQ,CAE9D,EAAE,CAAC;EAEL,MAAMmE,uBAAuB,GAAIL,KAAU,IAAK;IAC9CZ,kBAAkB,CAACY,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACxC,CAAC;EAED,MAAM,CAACI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACsE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvE,QAAQ,CAAW,EAAE,CAAC;EAC1E,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;EAEnD,MAAM0E,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI/B,eAAe,EAAE;MACnB8B,eAAe,CACZE,SAAS,IACR,CAACA,SAAS,GAAG,CAAC,KAAIhC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiC,aAAa,CAACC,KAAK,CAACC,MAAM,CACjE,CAAC;IACH;EACF,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIpC,eAAe,EAAE;MACnB8B,eAAe,CACZE,SAAS,IACR,CAACA,SAAS,GAAG,CAAC,IAAGhC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiC,aAAa,CAACC,KAAK,CAACC,MAAM,MAC5DnC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiC,aAAa,CAACC,KAAK,CAACC,MAAM,CAC/C,CAAC;IACH;EACF,CAAC;EAED,MAAME,UAAU,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,CAAC;EAE5D,MAAMC,gBAAgB,GAAGzD,GAAG,CAAC0D,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IAC1CC,UAAU,EAAE5D,GAAG,CACZ6D,MAAM,CAAC,CAAC,CACRC,SAAS,CAAC,8BAA8B,CAAC,CAAC;IAAA,CAC1CC,QAAQ,CAAC,CAAC,EAAE,yBAAyB,CAAC,CACtCC,QAAQ,CAAC,yBAAyB,CAAC;IAAE;IACxCC,SAAS,EAAEjE,GAAG,CACXkE,KAAK,CAAC,CAAC,CACPC,EAAE,CAACnE,GAAG,CAACoE,MAAM,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,CAAC,CAC3BK,GAAG,CAAC,CAAC,EAAE,qCAAqC,CAAC,CAC7CL,QAAQ,CAAC,CAAC;IACbM,UAAU,EAAEtE,GAAG,CACZkE,KAAK,CAAC,CAAC,CACPC,EAAE,CAACnE,GAAG,CAAC0D,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAAA,CAC3BU,GAAG,CAAC,CAAC,EAAE,mCAAmC,CAAC,CAC3CL,QAAQ,CAAC,4BAA4B;EAC1C,CAAC,CAAC;EAEF,MAAMO,cAAc,GAAG;IACrBX,UAAU,EAAE,CAAC;IACbK,SAAS,EAAE,EAAE;IACbK,UAAU,EAAE;EACd,CAAC;EACD,MAAM,CAACE,aAAa,EAAEC,gBAAgB,CAAC,GACrCpG,QAAQ,CAAsBkG,cAAc,CAAC;EAE/C,MAAMG,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFzC,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI0C,gBAA4C,GAC9C,MAAMnD,gBAAgB,CAACkD,WAAW,CAAC/D,QAAQ,CAACiE,EAAE,CAAC;MACjD,IAAID,gBAAgB,CAACE,IAAI,CAAC1B,MAAM,GAAG,CAAC,EAAE;QACpCnB,eAAe,CAAC2C,gBAAgB,CAACE,IAAI,CAAC;MACxC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE,CAAC;IAEjB7C,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAM8C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF9C,UAAU,CAAC,IAAI,CAAC;MAChB,IAAIN,cAA4C,GAC9C,MAAMH,gBAAgB,CAACuD,iBAAiB,CAACpE,QAAQ,CAACiE,EAAE,CAAC;MACvD,IAAIjD,cAAc,CAACqD,IAAI,CAAC7B,MAAM,GAAG,CAAC,EAAE;QAClCvB,iBAAiB,CAACD,cAAc,CAACqD,IAAI,CAAC;MACxC;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE,CAAC;IAEjB7C,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMgD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFhD,UAAU,CAAC,IAAI,CAAC;MAChB,IAAIiD,aAA0C,GAC5C,MAAM1D,gBAAgB,CAAC2D,YAAY,CAACxE,QAAQ,CAACiE,EAAE,CAAC;MAClD,IAAIM,aAAa,CAACL,IAAI,CAAC1B,MAAM,GAAG,CAAC,EAAE;QACjCrB,YAAY,CAACoD,aAAa,CAACL,IAAI,CAAC;MAClC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE,CAAC;IAEjB7C,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED7D,SAAS,CAAC,MAAM;IACdsG,WAAW,CAAC,CAAC;IACbO,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7G,SAAS,CAAC,MAAM;IACd,IAAIoG,aAAa,IAAIA,aAAa,CAACF,UAAU,IAAIE,aAAa,CAACP,SAAS,EAAE;MACxE,MAAMmB,iBAAiB,GAAGvD,SAAS,CAACwD,MAAM,CAAEC,CAAC,IAC3Cd,aAAa,CAACP,SAAS,CAACsB,QAAQ,CAACD,CAAC,CAACE,YAAY,CACjD,CAAC;MAED,IACEJ,iBAAiB,CAACjC,MAAM,GAAG,CAAC,IAC5BqB,aAAa,CAACF,UAAU,CAACnB,MAAM,KAAKiC,iBAAiB,CAACjC,MAAM,EAC5D;QACAT,qBAAqB,CAAC,IAAI,CAAC;;QAE3B;QACA,MAAM+C,WAAW,GAAGL,iBAAiB,CAACM,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACC,aAAa,CAAC;QACrEhD,qBAAqB,CAAC6C,WAAW,CAAC;MACpC,CAAC,MAAM;QACL/C,qBAAqB,CAAC,KAAK,CAAC;MAC9B;IACF;EACF,CAAC,EAAE,CAAC8B,aAAa,CAACF,UAAU,EAAEE,aAAa,CAACP,SAAS,EAAEpC,SAAS,CAAC,CAAC;EAElE,MAAMgE,WAAW,GAAG,EAAE;EACtB,MAAMC,gBAAgB,GAAG,CAAC;EAC1B,MAAMC,SAAS,GAAG;IAChBC,UAAU,EAAE;MACVC,KAAK,EAAE;QACLC,SAAS,EAAEL,WAAW,GAAG,GAAG,GAAGC,gBAAgB;QAC/CK,KAAK,EAAE;MACT;IACF;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAG,MAAAA,CACzBC,MAA2B,EAC3BC,aAAkB,KACf;IACH,IAAI,CAACjD,UAAU,EAAE;MACf,MAAMkD,OAAO,GAAG,MAAM9C,gBAAgB,CAAC8C,OAAO,CAACF,MAAM,CAAC;MACtDG,OAAO,CAACC,GAAG,CAACjG,KAAK,CAAC;MAClB,IAAI+F,OAAO,EAAE;QACXtF,SAAS,IAAIA,SAAS,CAACqB,oBAAoB,CAAC;MAC9C;IACF;EACF,CAAC;EAED,MAAMoE,gBAAgB,GAAG,MAAOL,MAA2B,IAAK;IAC9D,IAAI;MACF;MACA,MAAM5C,gBAAgB,CAACkD,QAAQ,CAACN,MAAM,EAAE;QAAEO,UAAU,EAAE;MAAM,CAAC,CAAC;MAC9DJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEJ,MAAM,CAAC;IACtD,CAAC,CAAC,OAAOQ,eAAoB,EAAE;MAC7B;MACA,MAAMC,eAAe,GAAGD,eAAe,CAACE,KAAK,CAACC,MAAM,CAClD,CAACC,GAAQ,EAAEC,GAAQ,KAAK;QACtBD,GAAG,CAACC,GAAG,CAACC,IAAI,CAAC,GAAGD,GAAG,CAACE,OAAO;QAC3B,OAAOH,GAAG;MACZ,CAAC,EACD,CAAC,CACH,CAAC;MAED,OAAOH,eAAe;IACxB;EACF,CAAC;EAED,oBACExG,OAAA,CAACP,MAAM;IACLsH,kBAAkB;IAClB7C,aAAa,EAAE;MAAE,GAAGA;IAAc,CAAE;IACpC8C,gBAAgB,EAAE7D,gBAAiB;IACnC8D,QAAQ,EAAEA,CAAClB,MAAM,EAAEC,aAAa,KAAK;MACnCF,kBAAkB,CAACC,MAAM,EAAEC,aAAa,CAAC;IAC3C,CAAE;IACFK,QAAQ,EAAED,gBAAiB;IAAAc,QAAA,EAE1BA,CAAC;MACAnB,MAAM;MACNoB,MAAM;MACNC,OAAO;MACPC,YAAY;MACZC,UAAU;MACVC,YAAY;MACZC,aAAa;MACbC,WAAW;MACXC,YAAY;MACZzB,OAAO;MACP0B;MACA;IACF,CAAC,kBACC3H,OAAA;MACEiH,QAAQ,EAAEM,YAAa;MACvBK,OAAO,EAAEH,WAAY;MACrBI,SAAS,EAAC,aAAa;MAAAX,QAAA,eAEvBlH,OAAA,CAACb,GAAG;QAAC0I,SAAS,EAAC,WAAW;QAAAX,QAAA,gBACxBlH,OAAA,CAACvB,UAAU;UACT6F,EAAE,EAAC,mBAAmB;UACtBwD,OAAO,EAAC,IAAI;UACZC,SAAS,EAAC,IAAI;UACdF,SAAS,EAAC,mBAAmB;UAAAX,QAAA,EAC9B;QAED;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbnI,OAAA,CAACb,GAAG;UACFmF,EAAE,EAAC,yBAAyB;UAC5BuD,SAAS,EAAC,yBAAyB;UAAAX,QAAA,eAEnClH,OAAA,CAACb,GAAG;YAAA+H,QAAA,gBACFlH,OAAA,CAACb,GAAG;cAAC0I,SAAS,EAAC,iBAAiB;cAAAX,QAAA,GAC7B,GAAG,eACJlH,OAAA,CAAC9B,WAAW;gBAAC4J,OAAO,EAAC,UAAU;gBAACM,SAAS;gBAAAlB,QAAA,gBACvClH,OAAA,CAAC7B,UAAU;kBAACmG,EAAE,EAAC,iCAAiC;kBAAA4C,QAAA,EAAC;gBAEjD;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAEbnI,OAAA,CAACZ,MAAM;kBACLgJ,SAAS;kBACT9D,EAAE,EAAC,YAAY;kBACf+D,KAAK,EAAC,UAAU;kBAChBtG,KAAK,EAAEgE,MAAM,CAACzC,UAAU,CAACgF,QAAQ,CAAC,CAAE;kBACpCC,QAAQ,EAAGC,GAAsB,IAAK;oBACpC/D,iBAAiB,CAAC,CAAC;oBACnB+C,aAAa,CAAC,WAAW,EAAE,EAAE,CAAC;oBAC9BA,aAAa,CAAC,YAAY,EAAE,EAAE,CAAC;oBAC/BiB,UAAU,CAAC,MAAM;sBACfjB,aAAa,CAAC,YAAY,EAAE,CAACgB,GAAG,CAAC1G,MAAM,CAACC,KAAK,CAAC;oBAChD,CAAC,EAAE,IAAI,CAAC;kBACV,CAAE;kBACF2G,EAAE,EAAE;oBACFC,eAAe,EAAE,mBAAmB;oBACpCC,YAAY,EAAE;kBAChB,CAAE;kBAAA1B,QAAA,gBAEFlH,OAAA,CAAC/B,QAAQ;oBAAC8D,KAAK,EAAE,CAAE;oBAAAmF,QAAA,EAAC;kBAAM;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,EACpC1G,YAAY,IACXA,YAAY,CAAC2D,GAAG,CAAEyD,QAAmB,iBACnC7I,OAAA,CAAC/B,QAAQ;oBAEP8D,KAAK,EAAE8G,QAAQ,CAACvE,EAAE,CAACgE,QAAQ,CAAC,CAAE;oBAAApB,QAAA,EAE7B2B,QAAQ,CAACC;kBAAY,GAHjBD,QAAQ,CAACvE,EAAE;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIR,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EACRhB,MAAM,CAAC7D,UAAU,IAAI8D,OAAO,CAAC9D,UAAU,iBACtCtD,OAAA,CAACJ,cAAc;kBAACiI,SAAS,EAAC,cAAc;kBAAAX,QAAA,EACrCC,MAAM,CAAC7D;gBAAU;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACjB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACNnI,OAAA,CAACb,GAAG;cAAC0I,SAAS,EAAC,aAAa;cAAAX,QAAA,eAC1BlH,OAAA,CAAC9B,WAAW;gBAAC4J,OAAO,EAAC,UAAU;gBAACM,SAAS;gBAAAlB,QAAA,gBACvClH,OAAA,CAAC7B,UAAU;kBAACmG,EAAE,EAAC,0BAA0B;kBAAA4C,QAAA,EAAC;gBAE1C;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbnI,OAAA,CAACZ,MAAM;kBACLkF,EAAE,EAAC,WAAW;kBACdyE,QAAQ,EAAE,IAAK;kBACfhH,KAAK,EAAEgE,MAAM,CAACpC,SAAU;kBACxB4E,QAAQ,EAAG1G,KAAkC,IAAK;oBAChD,MAAM;sBACJC,MAAM,EAAE;wBAAEC;sBAAM;oBAClB,CAAC,GAAGF,KAAK;oBACT2F,aAAa,CAAC,WAAW,EAAEzF,KAAK,CAAC;kBACnC,CAAE;kBACFiH,KAAK,eAAEhJ,OAAA,CAAC5B,aAAa;oBAACiK,KAAK,EAAC;kBAAU;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC1C1C,SAAS,EAAEA,SAAU;kBACrBiD,EAAE,EAAE;oBACFC,eAAe,EAAE,mBAAmB;oBACpCC,YAAY,EAAE;kBAChB,CAAE;kBAAA1B,QAAA,EAED7F,cAAc,CACZ0D,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAC1B,UAAU,KAAKyC,MAAM,CAACzC,UAAU,CAAC,CACjD8B,GAAG,CAAE6D,aAA6B,iBACjCjJ,OAAA,CAAC/B,QAAQ;oBAEP8D,KAAK,EAAEkH,aAAa,CAACtF,SAAU;oBAAAuD,QAAA,EAE9B+B,aAAa,CAACC;kBAAW,GAHrBD,aAAa,CAACtF,SAAS;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIpB,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACTnI,OAAA,CAACb,GAAG;kBAACuJ,EAAE,EAAE;oBAAES,OAAO,EAAE;kBAAO,CAAE;kBAAAjC,QAAA,EAC1BnB,MAAM,CAACpC,SAAS,CAACyB,GAAG,CAAErD,KAAa,iBAClC/B,OAAA,CAAC3B,IAAI;oBAEHgK,KAAK,EACHhH,cAAc,CAAC0D,MAAM,CAClBC,CAAC,IAAKA,CAAC,CAACrB,SAAS,KAAK5B,KACzB,CAAC,CAAC,CAAC,CAAC,CAACmH,WAAW,IAAI,EACrB;oBACDE,SAAS;oBACTzD,KAAK,EAAE;sBACL0D,MAAM,EAAE,CAAC;sBACTV,eAAe,EAAE;oBACnB,CAAE;oBACFW,QAAQ,EAAGC,CAAC,IAAK;sBACf/B,aAAa,CACX,WAAW,EACXzB,MAAM,CAACpC,SAAS,CAACoB,MAAM,CAAEC,CAAC,IAAKA,CAAC,IAAIjD,KAAK,CAC3C,CAAC;sBAEDyF,aAAa,CACX,YAAY,EACZzB,MAAM,CAAC/B,UAAU,CAACe,MAAM,CACrBC,CAAY,IAAKA,CAAC,CAACE,YAAY,IAAInD,KACtC,CACF,CAAC;oBACH,CAAE;oBACFyH,OAAO,EAAEA,CAAA,KAAMtD,OAAO,CAACC,GAAG,CAAC,cAAc,CAAE;oBAC3C2B,OAAO,EAAC,UAAU;oBAClB2B,KAAK,EAAC;kBAAS,GA1BV1H,KAAK;oBAAAiG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA2BX,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EACLhB,MAAM,CAACxD,SAAS,IAAIyD,OAAO,CAACzD,SAAS,iBACpC3D,OAAA,CAACJ,cAAc;kBAACiI,SAAS,EAAC,cAAc;kBAAAX,QAAA,EACrCC,MAAM,CAACxD;gBAAS;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACjB,EACAhB,MAAM,IACLA,MAAM,CAACnD,UAAU,IACjB0F,MAAM,CAACC,IAAI,CAACxC,MAAM,CAAC,CAACtE,MAAM,GAAG,CAAC,IAC9B6G,MAAM,CAACC,IAAI,CAACxC,MAAM,CAAC,CAACtE,MAAM,IAAI,CAAC,IAC/B6G,MAAM,CAACE,OAAO,CAACzC,MAAM,CAAC,CAAC/B,GAAG,CACxB,CAAC,CAACyE,KAAK,EAAEC,QAAQ,CAAC,KAChBD,KAAK,KAAK,YAAY,iBACpB7J,OAAA,CAACJ,cAAc;kBACbiI,SAAS,EAAC,cAAc;kBAAAX,QAAA,EAExB,GAAG4C,QAAQ;gBAAE,GADRD,KAAK;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACoB,CAEtC,CAAC,EAEF5G,SAAS,CAACwD,MAAM,CAAEC,CAAC,IAClBe,MAAM,CAACpC,SAAS,CAACsB,QAAQ,CAACD,CAAC,CAACE,YAAY,CAC1C,CAAC,CAACrC,MAAM,GAAG,CAAC,iBACV7C,OAAA,CAAC1B,SAAS;kBACRyL,QAAQ,EAAE,KAAM;kBAChBrB,EAAE,EAAE;oBACF,qBAAqB,EAAE;sBACrB,UAAU,EAAE;wBACVS,OAAO,EAAE;sBACX;oBACF,CAAC;oBACD,6BAA6B,EAAE;sBAC7Ba,SAAS,EAAE,MAAM;sBACjB,gBAAgB,EAAE;wBAChBA,SAAS,EAAE;sBACb;oBACF;kBACF,CAAE;kBAAA9C,QAAA,eAEFlH,OAAA,CAACzB,gBAAgB;oBACf0L,UAAU,EAAE,IAAK;oBACjBT,OAAO,EAAGD,CAAC,IAAK;sBACd;sBACAA,CAAC,CAACW,eAAe,CAAC,CAAC;oBACrB,CAAE;oBAAAhD,QAAA,eAEFlH,OAAA,CAACd,gBAAgB;sBACfiL,OAAO,eACLnK,OAAA,CAACtB,QAAQ;wBACP0L,OAAO,EACLjI,kBAAkB,IACjB4D,MAAM,CAAC/B,UAAU,CAACnB,MAAM,GAAG,CAAC,IAC3BkD,MAAM,CAAC/B,UAAU,CAACnB,MAAM,KACtBtB,SAAS,CAACwD,MAAM,CAAEC,CAAC,IACjBe,MAAM,CAACpC,SAAS,CAACsB,QAAQ,CACvBD,CAAC,CAACE,YACJ,CACF,CAAC,CAACrC,MACP;wBACD0F,QAAQ,EAAEA,CACR1G,KAA0C,EAC1CuI,OAAgB,KACb;0BACHvI,KAAK,CAACqI,eAAe,CAAC,CAAC,CAAC,CAAC;0BACzB9H,qBAAqB,CAACgI,OAAO,CAAC;;0BAE9B;0BACA,MAAMC,YAAY,GAAG9I,SAAS,CAACwD,MAAM,CAAEC,CAAC,IACtCe,MAAM,CAACpC,SAAS,CAACsB,QAAQ,CAACD,CAAC,CAACE,YAAY,CAC1C,CAAC;0BAED,IAAIkF,OAAO,EAAE;4BACX;4BACA5C,aAAa,CAAC,YAAY,EAAE6C,YAAY,CAAC;;4BAEzC;4BACA,MAAMlF,WAAW,GAAGkF,YAAY,CAACjF,GAAG,CACjCC,GAAG,IAAKA,GAAG,CAACC,aACf,CAAC;4BACDhD,qBAAqB,CAAC,CAAC,GAAG6C,WAAW,CAAC,CAAC;;4BAEvC;4BACA,IAAIzE,eAAe,EAAE;8BACnB,MAAM4J,uBAAqD,GACzD,EAAE;8BAEJD,YAAY,CAACE,OAAO,CAAEC,QAAQ,IAAK;gCACjC,IAAIC,QAAQ,GAAG;kCACb,GAAG/J,eAAe,CAACiC;gCACrB,CAAC;gCAED,IACE8H,QAAQ,IACRA,QAAQ,CAACC,SAAS,KAChB7K,WAAW,CAAC8K,KAAK,EACnB;kCACAF,QAAQ,CAACG,OAAO,GAAGH,QAAQ,CAACG,OAAO,CAChCC,KAAK,CAAC,aAAa,CAAC,CACpBC,IAAI,CAACN,QAAQ,CAACO,eAAe,CAAC;kCACjCN,QAAQ,CAACG,OAAO,GAAGH,QAAQ,CAACG,OAAO,CAChCC,KAAK,CAAC,UAAU,CAAC,CACjBC,IAAI,CAACN,QAAQ,CAACQ,QAAQ,CAAC;kCAC1BP,QAAQ,CAACG,OAAO,GAAGH,QAAQ,CAACG,OAAO,CAChCC,KAAK,CAAC,aAAa,CAAC,CACpBC,IAAI,CAACN,QAAQ,CAACS,UAAU,CAAC;gCAC9B;gCAEA,MAAMC,WAAW,GACf7J,cAAc,CAAC0D,MAAM,CAClBC,CAAiB,IAChBA,CAAC,CAACrB,SAAS,KACX6G,QAAQ,CAACtF,YACb,CAAC,CAAC,CAAC,CAAC;gCAEN,MAAM2D,QAAQ,GAAGpH,YAAY,CAACsD,MAAM,CACjCC,CAAY,IACXA,CAAC,CAACV,EAAE,KAAK4G,WAAW,CAAC5H,UACzB,CAAC,CAAC,CAAC,CAAC;gCAEJgH,uBAAuB,CAACa,IAAI,CAAC;kCAC3BC,YAAY,EAAE;oCACZ9H,UAAU,EAAEuF,QAAQ,CAACvE,EAAE;oCACvBwE,YAAY,EAAED,QAAQ,CAACC,YAAY;oCACnCnF,SAAS,EAAEuH,WAAW,CAAC5G,EAAE;oCACzB4E,WAAW,EACTgC,WAAW,CAAChC,WAAW;oCACzBlF,UAAU,EAAEwG,QAAQ,CAAClG,EAAE;oCACvB+G,YAAY,EACVb,QAAQ,CAACO;kCACb,CAAC;kCACDO,gBAAgB,EAAEb,QAAQ;kCAC1Bc,aAAa,EAAE;gCACjB,CAAC,CAAC;8BACJ,CAAC,CAAC;8BAEFtJ,uBAAuB,CACrBqI,uBACF,CAAC;4BACH;0BACF,CAAC,MAAM;4BACL;4BACA9C,aAAa,CAAC,YAAY,EAAE,EAAE,CAAC;4BAC/BvF,uBAAuB,CAAC,EAAE,CAAC;;4BAE3B;4BACAK,qBAAqB,CAAC,EAAE,CAAC;0BAC3B;wBACF,CAAE;wBACFkH,OAAO,EAAGD,CAAC,IAAKA,CAAC,CAACW,eAAe,CAAC,CAAE,CAAC;sBAAA;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CACF;sBACDE,KAAK,EAAC,sBAAsB;sBAC5BK,EAAE,EAAE;wBAAE8C,QAAQ,EAAE;sBAAE,CAAE;sBACpBhC,OAAO,EAAGD,CAAC,IAAKA,CAAC,CAACW,eAAe,CAAC,CAAE,CAAC;oBAAA;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACc;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CACZ,EAGA5G,SAAS,CACPwD,MAAM,CAAEC,CAAC,IAAKe,MAAM,CAACpC,SAAS,CAACsB,QAAQ,CAACD,CAAC,CAACE,YAAY,CAAC,CAAC,CACxDE,GAAG,CAAC,CAACoF,QAAmB,EAAEiB,KAAa,kBACtCzL,OAAA,CAAC1B,SAAS;kBAERyL,QAAQ,EAAE1H,kBAAkB,CAAC4C,QAAQ,CACnCuF,QAAQ,CAAClF,aACX,CAAE;kBAAA4B,QAAA,gBAEFlH,OAAA,CAACzB,gBAAgB;oBACf0L,UAAU,eAAEjK,OAAA,CAACL,cAAc;sBAAAqI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC/BqB,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACA,IACEnH,kBAAkB,CAAC4C,QAAQ,CACzBuF,QAAQ,CAAClF,aACX,CAAC,EACD;wBACAhD,qBAAqB,CACnBD,kBAAkB,CAAC0C,MAAM,CACtBT,EAAE,IAAKA,EAAE,KAAKkG,QAAQ,CAAClF,aAC1B,CACF,CAAC;sBACH,CAAC,MAAM;wBACLhD,qBAAqB,CAAC,CACpB,GAAGD,kBAAkB,EACrBmI,QAAQ,CAAClF,aAAa,CACvB,CAAC;sBACJ;oBACF,CAAE;oBAAA4B,QAAA,eAEFlH,OAAA,CAACd,gBAAgB;sBACfiL,OAAO,eACLnK,OAAA,CAACtB,QAAQ;wBACP0L,OAAO,EACLrE,MAAM,CAAC/B,UAAU,CAACe,MAAM,CACrBC,CAAY,IACXA,CAAC,CAACM,aAAa,KACfkF,QAAQ,CAAClF,aACb,CAAC,CAACzC,MAAM,GAAG,CACZ;wBACD0F,QAAQ,EAAEA,CACR1G,KAA0C,EAC1CuI,OAAgB,KACb;0BACHvI,KAAK,CAACqI,eAAe,CAAC,CAAC,CAAC,CAAC;0BACzB,IAAIE,OAAO,EAAE;4BACX;4BACA,MAAMsB,cAAc,GAAG,CACrB,GAAG3F,MAAM,CAAC/B,UAAU,EACpBwG,QAAQ,CACT;4BACDhD,aAAa,CACX,YAAY,EACZkE,cACF,CAAC;;4BAED;4BACA,IACE,CAACrJ,kBAAkB,CAAC4C,QAAQ,CAC1BuF,QAAQ,CAAClF,aACX,CAAC,EACD;8BACAhD,qBAAqB,CAAC,CACpB,GAAGD,kBAAkB,EACrBmI,QAAQ,CAAClF,aAAa,CACvB,CAAC;4BACJ;;4BAEA;4BACA,MAAMR,iBAAiB,GACrBvD,SAAS,CAACwD,MAAM,CAAEC,CAAC,IACjBe,MAAM,CAACpC,SAAS,CAACsB,QAAQ,CACvBD,CAAC,CAACE,YACJ,CACF,CAAC;4BACH,IACEwG,cAAc,CAAC7I,MAAM,KACrBiC,iBAAiB,CAACjC,MAAM,EACxB;8BACAT,qBAAqB,CAAC,IAAI,CAAC;4BAC7B;4BAEA,IAAI1B,eAAe,EAAE;8BACnB,IAAI+J,QAAQ,GAAG;gCACb,GAAG/J,eAAe,CAACiC;8BACrB,CAAC;8BAED,IACE8H,QAAQ,IACRA,QAAQ,CAACC,SAAS,KAChB7K,WAAW,CAAC8K,KAAK,EACnB;gCACAF,QAAQ,CAACG,OAAO,GAAGH,QAAQ,CAACG,OAAO,CAChCC,KAAK,CAAC,aAAa,CAAC,CACpBC,IAAI,CAACN,QAAQ,CAACO,eAAe,CAAC;gCACjCN,QAAQ,CAACG,OAAO,GAAGH,QAAQ,CAACG,OAAO,CAChCC,KAAK,CAAC,UAAU,CAAC,CACjBC,IAAI,CAACN,QAAQ,CAACQ,QAAQ,CAAC;gCAC1BP,QAAQ,CAACG,OAAO,GAAGH,QAAQ,CAACG,OAAO,CAChCC,KAAK,CAAC,aAAa,CAAC,CACpBC,IAAI,CAACN,QAAQ,CAACS,UAAU,CAAC;8BAC9B;8BAEA,MAAMC,WAAW,GACf7J,cAAc,CAAC0D,MAAM,CAClBC,CAAiB,IAChBA,CAAC,CAACrB,SAAS,KACX6G,QAAQ,CAACtF,YACb,CAAC,CAAC,CAAC,CAAC;8BACN,MAAM2D,QAAQ,GAAGpH,YAAY,CAACsD,MAAM,CACjCC,CAAY,IACXA,CAAC,CAACV,EAAE,KAAK4G,WAAW,CAAC5H,UACzB,CAAC,CAAC,CAAC,CAAC;8BAEJ,MAAMqI,yBAAqD,GACzD;gCACEP,YAAY,EAAE;kCACZ9H,UAAU,EAAEuF,QAAQ,CAACvE,EAAE;kCACvBwE,YAAY,EACVD,QAAQ,CAACC,YAAY;kCACvBnF,SAAS,EAAEuH,WAAW,CAAC5G,EAAE;kCACzB4E,WAAW,EACTgC,WAAW,CAAChC,WAAW;kCACzBlF,UAAU,EAAEwG,QAAQ,CAAClG,EAAE;kCACvB+G,YAAY,EACVb,QAAQ,CAACO;gCACb,CAAC;gCACDO,gBAAgB,EAAEb,QAAQ;gCAC1Bc,aAAa,EAAE;8BACjB,CAAC;8BAEHtJ,uBAAuB,CAAC,CACtB,GAAGD,oBAAoB,EACvB2J,yBAAyB,CAC1B,CAAC;4BACJ;0BACF,CAAC,MAAM;4BACL;4BACA,MAAMD,cAAc,GAClB3F,MAAM,CAAC/B,UAAU,CAACe,MAAM,CACrBC,CAAY,IACXA,CAAC,CAACM,aAAa,IACfkF,QAAQ,CAAClF,aACb,CAAC;4BACHkC,aAAa,CACX,YAAY,EACZkE,cACF,CAAC;;4BAED;4BACApJ,qBAAqB,CACnBD,kBAAkB,CAAC0C,MAAM,CACtBT,EAAE,IAAKA,EAAE,KAAKkG,QAAQ,CAAClF,aAC1B,CACF,CAAC;;4BAED;4BACAlD,qBAAqB,CAAC,KAAK,CAAC;;4BAE5B;4BACAH,uBAAuB,CACrBD,oBAAoB,CAAC+C,MAAM,CACxB6G,IAAI,IACHA,IAAI,CAACR,YAAY,CAACpH,UAAU,KAC5BwG,QAAQ,CAAClG,EACb,CACF,CAAC;0BACH;;0BAEA;0BACAzC,KAAK,CAACqI,eAAe,CAAC,CAAC;wBACzB;sBAAE;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CACF;sBACDE,KAAK,EAAEmC,QAAQ,CAACO,eAAgB;sBAChCrC,EAAE,EAAE;wBAAE8C,QAAQ,EAAE;sBAAE,CAAE;sBACpBhC,OAAO,EAAGD,CAAC,IAAKA,CAAC,CAACW,eAAe,CAAC,CAAE,CAAC;oBAAA;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACc,CAAC,eACnBnI,OAAA,CAACxB,gBAAgB;oBAAA0I,QAAA,eACflH,OAAA,CAAClB,cAAc;sBAACiJ,SAAS,EAAE9I,KAAM;sBAAAiI,QAAA,eAC/BlH,OAAA,CAACrB,KAAK;wBAAAuI,QAAA,gBACJlH,OAAA,CAACjB,SAAS;0BAAAmI,QAAA,eACRlH,OAAA,CAAChB,QAAQ;4BAAAkI,QAAA,gBACPlH,OAAA,CAACnB,SAAS;8BAAAqI,QAAA,eACRlH,OAAA;gCAAAkH,QAAA,EAAG;8BAAQ;gCAAAc,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN,CAAC,eACZnI,OAAA,CAACnB,SAAS;8BAAAqI,QAAA,eACRlH,OAAA;gCAAAkH,QAAA,EAAG;8BAAQ;gCAAAc,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN,CAAC,eACZnI,OAAA,CAACnB,SAAS;8BAAAqI,QAAA,eACRlH,OAAA;gCAAAkH,QAAA,EAAG;8BAAO;gCAAAc,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,eACZnI,OAAA,CAACnB,SAAS;8BAAAqI,QAAA,eACRlH,OAAA;gCAAAkH,QAAA,EAAG;8BAAa;gCAAAc,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACX,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACZnI,OAAA,CAACpB,SAAS;0BAAAsI,QAAA,eACRlH,OAAA,CAAChB,QAAQ;4BAAAkI,QAAA,gBACPlH,OAAA,CAACnB,SAAS;8BAAAqI,QAAA,EAAEsD,QAAQ,CAACQ;4BAAQ;8BAAAhD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAY,CAAC,eAC1CnI,OAAA,CAACnB,SAAS;8BAAAqI,QAAA,EAAEsD,QAAQ,CAACS;4BAAU;8BAAAjD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAY,CAAC,eAC5CnI,OAAA,CAACnB,SAAS;8BAAAqI,QAAA,EACPsD,QAAQ,CAACO;4BAAe;8BAAA/C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChB,CAAC,eACZnI,OAAA,CAACnB,SAAS;8BAAAqI,QAAA,EACPsD,QAAQ,CAACqB;4BAAY;8BAAA7D,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACb,CAAC;0BAAA,GARCsD,KAAK;4BAAAzD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OASV;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA,GAjNdqC,QAAQ,CAAClF,aAAa;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkNlB,CACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0IO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnI,OAAA,CAACb,GAAG;UAAC0I,SAAS,EAAC,EAAE;UAAAX,QAAA,eACflH,OAAA,CAACF,KAAK;YAACgM,SAAS,EAAC,KAAK;YAACjE,SAAS,EAAC,cAAc;YAAAX,QAAA,gBAC7ClH,OAAA,CAAChC,MAAM;cACL8J,OAAO,EAAC,UAAU;cAClBD,SAAS,EAAC,qBAAqB;cAC/B2B,OAAO,EAAE/I,UAAW;cAAAyG,QAAA,EACrB;YAED;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnI,OAAA,CAAChC,MAAM;cACL+N,IAAI,EAAC,QAAQ;cACbjE,OAAO,EAAC,WAAW;cACnBD,SAAS,EAAC,gBAAgB;cAAAX,QAAA,EAC3B;YAED;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EACP;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAAChI,EAAA,CA/2BIF,UAAU;EAAA,QACGV,WAAW,EACPC,WAAW;AAAA;AAAAwM,EAAA,GAF5B/L,UAAU;AAi3BhB,eAAeA,UAAU;AAAC,IAAA+L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}