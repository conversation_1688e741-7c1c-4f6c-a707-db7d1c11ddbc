const axios = require('axios');
const qs = require('qs');

module.exports = {
    refreshToken: async (requestObj) => {
        try {
            const response = await axios.post('https://oauth2.googleapis.com/token', qs.stringify(requestObj), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });
            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: error
            };
        }
    }
}