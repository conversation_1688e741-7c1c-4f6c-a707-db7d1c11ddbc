{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\header\\\\header.component.tsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport Box from \"@mui/material/Box\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { logOut } from \"../../actions/auth.actions\";\nimport UserAvatar from \"../userAvatar/userAvatar.component\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction HeaderComponent() {\n  _s();\n  const {\n    userInfo\n  } = useSelector(state => state.authReducer);\n  const dispatch = useDispatch();\n  const logoutUser = () => dispatch(logOut());\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const openSubMenu = Boolean(anchorEl);\n  const handleClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"display-contents\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      className: \"commonTopBarR\",\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        className: \"profileName\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          className: \"\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Hello\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), \",\\xA0\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"userName\",\n            children: userInfo && userInfo.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: userInfo && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              paddingLeft: \"8px\"\n            },\n            children: /*#__PURE__*/_jsxDEV(UserAvatar, {\n              fullname: userInfo.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this)\n        }, void 0, false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n}\n_s(HeaderComponent, \"mx4RBiQogdzvFFt+X063z22bIFU=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = HeaderComponent;\nexport default HeaderComponent;\nvar _c;\n$RefreshReg$(_c, \"HeaderComponent\");", "map": {"version": 3, "names": ["React", "Box", "useDispatch", "useSelector", "logOut", "UserAvatar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HeaderComponent", "_s", "userInfo", "state", "authReducer", "dispatch", "logoutUser", "anchorEl", "setAnchorEl", "useState", "openSubMenu", "Boolean", "handleClick", "event", "currentTarget", "handleClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "sx", "paddingLeft", "fullname", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/header/header.component.tsx"], "sourcesContent": ["import React from \"react\";\nimport Box from \"@mui/material/Box\";\nimport IconButton from \"@mui/material/IconButton\";\nimport SettingsOutlinedIcon from \"@mui/icons-material/SettingsOutlined\";\nimport Button from \"@mui/material/Button\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport Menu from \"@mui/material/Menu\";\nimport MenuItem from \"@mui/material/MenuItem\";\nimport { logOut } from \"../../actions/auth.actions\";\nimport UserAvatar from \"../userAvatar/userAvatar.component\";\nimport LogoutIcon from \"@mui/icons-material/Logout\";\n\nfunction HeaderComponent() {\n  const { userInfo } = useSelector((state: any) => state.authReducer);\n  const dispatch = useDispatch();\n  const logoutUser = () => dispatch<any>(logOut());\n  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);\n  const openSubMenu = Boolean(anchorEl);\n  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n\n  return (\n    <Box className=\"display-contents\">\n      <Box className=\"commonTopBarR\">\n        {/* <Box className=\"notification\">\n          <span className=\"notificationIcon\">\n            <IconButton aria-label=\"settings\" className=\"iconBtn\">\n              <SettingsOutlinedIcon />\n            </IconButton>\n          </span>\n          <span className=\"notificationValue\">19</span>\n        </Box> */}\n        {/* <Box className=\"notification\">\n          <span className=\"notificationIcon\">\n            <IconButton\n              aria-label=\"settings\"\n              className=\"iconBtn\"\n              onClick={() => logoutUser()}\n            >\n              <LogoutIcon />\n            </IconButton>\n          </span>\n        </Box> */}\n        <Box className=\"profileName\">\n          <Box className=\"\">\n            <span>Hello</span>,&nbsp;\n            <span className=\"userName\">{userInfo && userInfo.name}</span>\n          </Box>\n        </Box>\n        <Box>\n          {userInfo && (\n            <>\n              {/* <Button\n                id=\"basic-button\"\n                aria-controls={openSubMenu ? \"basic-menu\" : undefined}\n                aria-haspopup=\"true\"\n                aria-expanded={openSubMenu ? \"true\" : undefined}\n                onClick={handleClick}\n              > */}\n              <Box sx={{ paddingLeft: \"8px\" }}>\n                <UserAvatar fullname={userInfo.name} />\n              </Box>\n\n              {/* <img\n              alt=\"MyLocoBiz - Profile Photo\"\n              className=\"width100 profilePic\"\n              src={require(\"../../assets/common/profile.png\")}\n            /> */}\n              {/* </Button> */}\n              {/* <Menu\n                id=\"basic-menu\"\n                anchorEl={anchorEl}\n                open={openSubMenu}\n                onClose={handleClose}\n                MenuListProps={{\n                  \"aria-labelledby\": \"basic-button\",\n                }}\n              > */}\n              {/* <MenuItem onClick={handleClose}>Profile</MenuItem>\n                <MenuItem onClick={handleClose}>My account</MenuItem> */}\n              {/* <MenuItem onClick={() => logoutUser()}>Logout</MenuItem>\n              </Menu> */}\n            </>\n          )}\n        </Box>\n      </Box>\n    </Box>\n  );\n}\n\nexport default HeaderComponent;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,GAAG,MAAM,mBAAmB;AAInC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAGtD,SAASC,MAAM,QAAQ,4BAA4B;AACnD,OAAOC,UAAU,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG5D,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAS,CAAC,GAAGT,WAAW,CAAEU,KAAU,IAAKA,KAAK,CAACC,WAAW,CAAC;EACnE,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,UAAU,GAAGA,CAAA,KAAMD,QAAQ,CAAMX,MAAM,CAAC,CAAC,CAAC;EAChD,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,KAAK,CAACmB,QAAQ,CAAqB,IAAI,CAAC;EACxE,MAAMC,WAAW,GAAGC,OAAO,CAACJ,QAAQ,CAAC;EACrC,MAAMK,WAAW,GAAIC,KAA0C,IAAK;IAClEL,WAAW,CAACK,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EACD,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBP,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,oBACEX,OAAA,CAACN,GAAG;IAACyB,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAC/BpB,OAAA,CAACN,GAAG;MAACyB,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAoB5BpB,OAAA,CAACN,GAAG;QAACyB,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BpB,OAAA,CAACN,GAAG;UAACyB,SAAS,EAAC,EAAE;UAAAC,QAAA,gBACfpB,OAAA;YAAAoB,QAAA,EAAM;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,SAClB,eAAAxB,OAAA;YAAMmB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEf,QAAQ,IAAIA,QAAQ,CAACoB;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNxB,OAAA,CAACN,GAAG;QAAA0B,QAAA,EACDf,QAAQ,iBACPL,OAAA,CAAAE,SAAA;UAAAkB,QAAA,eAQEpB,OAAA,CAACN,GAAG;YAACgC,EAAE,EAAE;cAAEC,WAAW,EAAE;YAAM,CAAE;YAAAP,QAAA,eAC9BpB,OAAA,CAACF,UAAU;cAAC8B,QAAQ,EAAEvB,QAAQ,CAACoB;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC,gBAqBN;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACpB,EAAA,CAhFQD,eAAe;EAAA,QACDP,WAAW,EACfD,WAAW;AAAA;AAAAkC,EAAA,GAFrB1B,eAAe;AAkFxB,eAAeA,eAAe;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}