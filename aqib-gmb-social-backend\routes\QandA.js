const express = require("express");
const router = express.Router();
const validateSchema = require("../middleware/validateRequest");
const { isAuthenticated } = require("../middleware/isAuthenticated");
const { gmbTokenMapping } = require("../middleware/gmbTokenMapping");

const {
  welcome,
  refreshQandA,
  QandAList,
  replyToQuestions,
} = require("../controllers/QandA.controllers");

router.get("/", welcome);
router.get("/QandA-list", isAuthenticated, gmbTokenMapping, refreshQandA);
router.get("/QandA-list/:userId", QandAList);
router.post("/reply", isAuthenticated, gmbTokenMapping, replyToQuestions);

module.exports = router;
