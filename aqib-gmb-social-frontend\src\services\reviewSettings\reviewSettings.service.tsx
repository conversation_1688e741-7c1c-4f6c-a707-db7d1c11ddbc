import { Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import { Action } from "redux";
import {
  REVIEW_SETTINGS_TEMPLATES,
  REVIEW_SETTINGS_TEMPLATE_BY_ID,
  REVIEW_SETTINGS_MAP_TEMPLATE,
  REVIEW_SETTINGS_AUTO_REPLY,
  REVIEW_SETTINGS_AUTO_REPLY_TEMPLATE,
} from "../../constants/endPoints.constant";

// Interfaces
export interface IReplyTemplate {
  id?: number;
  created_by: number;
  star_rating: number;
  template_name: string;
  template_content: string;
  is_default: boolean;
  business_id?: number;
  business_template_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface ICreateReplyTemplateRequest {
  starRating: number;
  templateName: string;
  templateContent: string;
  isDefault: boolean;
  businessId?: number;
}

export interface IUpdateReplyTemplateRequest {
  starRating: number;
  templateName: string;
  templateContent: string;
  isDefault: boolean;
}

export interface IAutoReplySettings {
  id?: number;
  business_id: number;
  is_enabled: boolean;
  enabled_star_ratings: number[];
  delay_minutes: number;
  only_business_hours: boolean;
  business_hours_start: string;
  business_hours_end: string;
  created_at?: string;
  updated_at?: string;
}

export interface IMapTemplateToBusinessesRequest {
  businessIds: number[];
}

export interface IReplyTemplatesResponse {
  message: string;
  data: IReplyTemplate[];
}

export interface IAutoReplySettingsResponse {
  message: string;
  data: IAutoReplySettings | null;
}

export interface IApiResponse {
  message: string;
  data?: any;
}

class ReviewSettingsService {
  _httpHelperService;

  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  // Get all reply templates for a user
  getReplyTemplates = async (
    userId: number,
    businessId?: number
  ): Promise<IReplyTemplatesResponse> => {
    const url = businessId
      ? `${REVIEW_SETTINGS_TEMPLATES(userId)}?businessId=${businessId}`
      : REVIEW_SETTINGS_TEMPLATES(userId);
    return await this._httpHelperService.get(url);
  };

  // Create a new reply template
  createReplyTemplate = async (
    userId: number,
    templateData: ICreateReplyTemplateRequest
  ): Promise<IApiResponse> => {
    return await this._httpHelperService.post(
      REVIEW_SETTINGS_TEMPLATES(userId),
      templateData
    );
  };

  // Update a reply template
  updateReplyTemplate = async (
    userId: number,
    templateId: number,
    templateData: IUpdateReplyTemplateRequest
  ): Promise<IApiResponse> => {
    return await this._httpHelperService.put(
      REVIEW_SETTINGS_TEMPLATE_BY_ID(userId, templateId),
      templateData
    );
  };

  // Delete a reply template
  deleteReplyTemplate = async (
    userId: number,
    templateId: number
  ): Promise<IApiResponse> => {
    return await this._httpHelperService.delete(
      REVIEW_SETTINGS_TEMPLATE_BY_ID(userId, templateId)
    );
  };

  // Map template to businesses
  mapTemplateToBusinesses = async (
    userId: number,
    templateId: number,
    businessIds: number[]
  ): Promise<IApiResponse> => {
    return await this._httpHelperService.post(
      REVIEW_SETTINGS_MAP_TEMPLATE(userId, templateId),
      { businessIds }
    );
  };

  // Get auto-reply settings for a business
  getAutoReplySettings = async (
    businessId: number
  ): Promise<IAutoReplySettingsResponse> => {
    return await this._httpHelperService.get(
      REVIEW_SETTINGS_AUTO_REPLY(businessId)
    );
  };

  // Update auto-reply settings
  updateAutoReplySettings = async (
    businessId: number,
    settings: Omit<
      IAutoReplySettings,
      "id" | "business_id" | "created_at" | "updated_at"
    >
  ): Promise<IApiResponse> => {
    return await this._httpHelperService.put(
      REVIEW_SETTINGS_AUTO_REPLY(businessId),
      settings
    );
  };

  // Get template for auto-reply (used by auto-reply system)
  getTemplateForAutoReply = async (
    businessId: number,
    starRating: number
  ): Promise<IApiResponse> => {
    return await this._httpHelperService.get(
      REVIEW_SETTINGS_AUTO_REPLY_TEMPLATE(businessId, starRating)
    );
  };
}

export default ReviewSettingsService;
