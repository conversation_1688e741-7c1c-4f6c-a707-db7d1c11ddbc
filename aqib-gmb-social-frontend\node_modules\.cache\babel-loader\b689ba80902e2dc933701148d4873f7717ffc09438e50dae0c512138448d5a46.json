{"ast": null, "code": "import HttpHelperService from \"../httpHelper.service\";\n// API endpoints\nconst REVIEW_SETTINGS_BASE = \"/review-settings\";\nconst TEMPLATES_ENDPOINT = userId => `${REVIEW_SETTINGS_BASE}/templates/${userId}`;\n_c = TEMPLATES_ENDPOINT;\nconst TEMPLATE_BY_ID_ENDPOINT = (userId, templateId) => `${REVIEW_SETTINGS_BASE}/templates/${userId}/${templateId}`;\n_c2 = TEMPLATE_BY_ID_ENDPOINT;\nconst MAP_TEMPLATE_ENDPOINT = (userId, templateId) => `${REVIEW_SETTINGS_BASE}/templates/${userId}/${templateId}/map-businesses`;\n_c3 = MAP_TEMPLATE_ENDPOINT;\nconst AUTO_REPLY_ENDPOINT = businessId => `${REVIEW_SETTINGS_BASE}/auto-reply/${businessId}`;\n\n// Interfaces\n_c4 = AUTO_REPLY_ENDPOINT;\nclass ReviewSettingsService {\n  constructor(dispatch) {\n    this._httpHelperService = void 0;\n    // Get all reply templates for a user\n    this.getReplyTemplates = async (userId, businessId) => {\n      const params = businessId ? {\n        businessId\n      } : {};\n      return await this._httpHelperService.get(TEMPLATES_ENDPOINT(userId), {}, params);\n    };\n    // Create a new reply template\n    this.createReplyTemplate = async (userId, templateData) => {\n      return await this._httpHelperService.post(TEMPLATES_ENDPOINT(userId), templateData);\n    };\n    // Update a reply template\n    this.updateReplyTemplate = async (userId, templateId, templateData) => {\n      return await this._httpHelperService.put(TEMPLATE_BY_ID_ENDPOINT(userId, templateId), templateData);\n    };\n    // Delete a reply template\n    this.deleteReplyTemplate = async (userId, templateId) => {\n      return await this._httpHelperService.delete(TEMPLATE_BY_ID_ENDPOINT(userId, templateId));\n    };\n    // Map template to businesses\n    this.mapTemplateToBusinesses = async (userId, templateId, businessIds) => {\n      return await this._httpHelperService.post(MAP_TEMPLATE_ENDPOINT(userId, templateId), {\n        businessIds\n      });\n    };\n    // Get auto-reply settings for a business\n    this.getAutoReplySettings = async businessId => {\n      return await this._httpHelperService.get(AUTO_REPLY_ENDPOINT(businessId));\n    };\n    // Update auto-reply settings\n    this.updateAutoReplySettings = async (businessId, settings) => {\n      return await this._httpHelperService.put(AUTO_REPLY_ENDPOINT(businessId), settings);\n    };\n    // Get template for auto-reply (used by auto-reply system)\n    this.getTemplateForAutoReply = async (businessId, starRating) => {\n      return await this._httpHelperService.get(`${REVIEW_SETTINGS_BASE}/auto-reply-template/${businessId}/${starRating}`);\n    };\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n}\nexport default ReviewSettingsService;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"TEMPLATES_ENDPOINT\");\n$RefreshReg$(_c2, \"TEMPLATE_BY_ID_ENDPOINT\");\n$RefreshReg$(_c3, \"MAP_TEMPLATE_ENDPOINT\");\n$RefreshReg$(_c4, \"AUTO_REPLY_ENDPOINT\");", "map": {"version": 3, "names": ["HttpHelperService", "REVIEW_SETTINGS_BASE", "TEMPLATES_ENDPOINT", "userId", "_c", "TEMPLATE_BY_ID_ENDPOINT", "templateId", "_c2", "MAP_TEMPLATE_ENDPOINT", "_c3", "AUTO_REPLY_ENDPOINT", "businessId", "_c4", "ReviewSettingsService", "constructor", "dispatch", "_httpHelperService", "getReplyTemplates", "params", "get", "createReplyTemplate", "templateData", "post", "updateReplyTemplate", "put", "deleteReplyTemplate", "delete", "mapTemplateToBusinesses", "businessIds", "getAutoReplySettings", "updateAutoReplySettings", "settings", "getTemplateForAutoReply", "starRating", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/reviewSettings/reviewSettings.service.tsx"], "sourcesContent": ["import { Dispatch } from \"react\";\nimport HttpHelperService from \"../httpHelper.service\";\nimport { Action } from \"redux\";\n\n// API endpoints\nconst REVIEW_SETTINGS_BASE = \"/review-settings\";\nconst TEMPLATES_ENDPOINT = (userId: number) =>\n  `${REVIEW_SETTINGS_BASE}/templates/${userId}`;\nconst TEMPLATE_BY_ID_ENDPOINT = (userId: number, templateId: number) =>\n  `${REVIEW_SETTINGS_BASE}/templates/${userId}/${templateId}`;\nconst MAP_TEMPLATE_ENDPOINT = (userId: number, templateId: number) =>\n  `${REVIEW_SETTINGS_BASE}/templates/${userId}/${templateId}/map-businesses`;\nconst AUTO_REPLY_ENDPOINT = (businessId: number) =>\n  `${REVIEW_SETTINGS_BASE}/auto-reply/${businessId}`;\n\n// Interfaces\nexport interface IReplyTemplate {\n  id?: number;\n  created_by: number;\n  star_rating: number;\n  template_name: string;\n  template_content: string;\n  is_default: boolean;\n  business_id?: number;\n  business_template_active?: boolean;\n  created_at?: string;\n  updated_at?: string;\n}\n\nexport interface ICreateReplyTemplateRequest {\n  starRating: number;\n  templateName: string;\n  templateContent: string;\n  isDefault: boolean;\n  businessId?: number;\n}\n\nexport interface IUpdateReplyTemplateRequest {\n  starRating: number;\n  templateName: string;\n  templateContent: string;\n  isDefault: boolean;\n}\n\nexport interface IAutoReplySettings {\n  id?: number;\n  business_id: number;\n  is_enabled: boolean;\n  enabled_star_ratings: number[];\n  delay_minutes: number;\n  only_business_hours: boolean;\n  business_hours_start: string;\n  business_hours_end: string;\n  created_at?: string;\n  updated_at?: string;\n}\n\nexport interface IMapTemplateToBusinessesRequest {\n  businessIds: number[];\n}\n\nexport interface IReplyTemplatesResponse {\n  message: string;\n  data: IReplyTemplate[];\n}\n\nexport interface IAutoReplySettingsResponse {\n  message: string;\n  data: IAutoReplySettings | null;\n}\n\nexport interface IApiResponse {\n  message: string;\n  data?: any;\n}\n\nclass ReviewSettingsService {\n  _httpHelperService;\n\n  constructor(dispatch: Dispatch<Action>) {\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  // Get all reply templates for a user\n  getReplyTemplates = async (\n    userId: number,\n    businessId?: number\n  ): Promise<IReplyTemplatesResponse> => {\n    const params = businessId ? { businessId } : {};\n    return await this._httpHelperService.get(\n      TEMPLATES_ENDPOINT(userId),\n      {},\n      params\n    );\n  };\n\n  // Create a new reply template\n  createReplyTemplate = async (\n    userId: number,\n    templateData: ICreateReplyTemplateRequest\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.post(\n      TEMPLATES_ENDPOINT(userId),\n      templateData\n    );\n  };\n\n  // Update a reply template\n  updateReplyTemplate = async (\n    userId: number,\n    templateId: number,\n    templateData: IUpdateReplyTemplateRequest\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.put(\n      TEMPLATE_BY_ID_ENDPOINT(userId, templateId),\n      templateData\n    );\n  };\n\n  // Delete a reply template\n  deleteReplyTemplate = async (\n    userId: number,\n    templateId: number\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.delete(\n      TEMPLATE_BY_ID_ENDPOINT(userId, templateId)\n    );\n  };\n\n  // Map template to businesses\n  mapTemplateToBusinesses = async (\n    userId: number,\n    templateId: number,\n    businessIds: number[]\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.post(\n      MAP_TEMPLATE_ENDPOINT(userId, templateId),\n      { businessIds }\n    );\n  };\n\n  // Get auto-reply settings for a business\n  getAutoReplySettings = async (\n    businessId: number\n  ): Promise<IAutoReplySettingsResponse> => {\n    return await this._httpHelperService.get(AUTO_REPLY_ENDPOINT(businessId));\n  };\n\n  // Update auto-reply settings\n  updateAutoReplySettings = async (\n    businessId: number,\n    settings: Omit<\n      IAutoReplySettings,\n      \"id\" | \"business_id\" | \"created_at\" | \"updated_at\"\n    >\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.put(\n      AUTO_REPLY_ENDPOINT(businessId),\n      settings\n    );\n  };\n\n  // Get template for auto-reply (used by auto-reply system)\n  getTemplateForAutoReply = async (\n    businessId: number,\n    starRating: number\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.get(\n      `${REVIEW_SETTINGS_BASE}/auto-reply-template/${businessId}/${starRating}`\n    );\n  };\n}\n\nexport default ReviewSettingsService;\n"], "mappings": "AACA,OAAOA,iBAAiB,MAAM,uBAAuB;AAGrD;AACA,MAAMC,oBAAoB,GAAG,kBAAkB;AAC/C,MAAMC,kBAAkB,GAAIC,MAAc,IACxC,GAAGF,oBAAoB,cAAcE,MAAM,EAAE;AAACC,EAAA,GAD1CF,kBAAkB;AAExB,MAAMG,uBAAuB,GAAGA,CAACF,MAAc,EAAEG,UAAkB,KACjE,GAAGL,oBAAoB,cAAcE,MAAM,IAAIG,UAAU,EAAE;AAACC,GAAA,GADxDF,uBAAuB;AAE7B,MAAMG,qBAAqB,GAAGA,CAACL,MAAc,EAAEG,UAAkB,KAC/D,GAAGL,oBAAoB,cAAcE,MAAM,IAAIG,UAAU,iBAAiB;AAACG,GAAA,GADvED,qBAAqB;AAE3B,MAAME,mBAAmB,GAAIC,UAAkB,IAC7C,GAAGV,oBAAoB,eAAeU,UAAU,EAAE;;AAEpD;AAAAC,GAAA,GAHMF,mBAAmB;AAgEzB,MAAMG,qBAAqB,CAAC;EAG1BC,WAAWA,CAACC,QAA0B,EAAE;IAAA,KAFxCC,kBAAkB;IAMlB;IAAA,KACAC,iBAAiB,GAAG,OAClBd,MAAc,EACdQ,UAAmB,KACkB;MACrC,MAAMO,MAAM,GAAGP,UAAU,GAAG;QAAEA;MAAW,CAAC,GAAG,CAAC,CAAC;MAC/C,OAAO,MAAM,IAAI,CAACK,kBAAkB,CAACG,GAAG,CACtCjB,kBAAkB,CAACC,MAAM,CAAC,EAC1B,CAAC,CAAC,EACFe,MACF,CAAC;IACH,CAAC;IAED;IAAA,KACAE,mBAAmB,GAAG,OACpBjB,MAAc,EACdkB,YAAyC,KACf;MAC1B,OAAO,MAAM,IAAI,CAACL,kBAAkB,CAACM,IAAI,CACvCpB,kBAAkB,CAACC,MAAM,CAAC,EAC1BkB,YACF,CAAC;IACH,CAAC;IAED;IAAA,KACAE,mBAAmB,GAAG,OACpBpB,MAAc,EACdG,UAAkB,EAClBe,YAAyC,KACf;MAC1B,OAAO,MAAM,IAAI,CAACL,kBAAkB,CAACQ,GAAG,CACtCnB,uBAAuB,CAACF,MAAM,EAAEG,UAAU,CAAC,EAC3Ce,YACF,CAAC;IACH,CAAC;IAED;IAAA,KACAI,mBAAmB,GAAG,OACpBtB,MAAc,EACdG,UAAkB,KACQ;MAC1B,OAAO,MAAM,IAAI,CAACU,kBAAkB,CAACU,MAAM,CACzCrB,uBAAuB,CAACF,MAAM,EAAEG,UAAU,CAC5C,CAAC;IACH,CAAC;IAED;IAAA,KACAqB,uBAAuB,GAAG,OACxBxB,MAAc,EACdG,UAAkB,EAClBsB,WAAqB,KACK;MAC1B,OAAO,MAAM,IAAI,CAACZ,kBAAkB,CAACM,IAAI,CACvCd,qBAAqB,CAACL,MAAM,EAAEG,UAAU,CAAC,EACzC;QAAEsB;MAAY,CAChB,CAAC;IACH,CAAC;IAED;IAAA,KACAC,oBAAoB,GAAG,MACrBlB,UAAkB,IACsB;MACxC,OAAO,MAAM,IAAI,CAACK,kBAAkB,CAACG,GAAG,CAACT,mBAAmB,CAACC,UAAU,CAAC,CAAC;IAC3E,CAAC;IAED;IAAA,KACAmB,uBAAuB,GAAG,OACxBnB,UAAkB,EAClBoB,QAGC,KACyB;MAC1B,OAAO,MAAM,IAAI,CAACf,kBAAkB,CAACQ,GAAG,CACtCd,mBAAmB,CAACC,UAAU,CAAC,EAC/BoB,QACF,CAAC;IACH,CAAC;IAED;IAAA,KACAC,uBAAuB,GAAG,OACxBrB,UAAkB,EAClBsB,UAAkB,KACQ;MAC1B,OAAO,MAAM,IAAI,CAACjB,kBAAkB,CAACG,GAAG,CACtC,GAAGlB,oBAAoB,wBAAwBU,UAAU,IAAIsB,UAAU,EACzE,CAAC;IACH,CAAC;IA1FC,IAAI,CAACjB,kBAAkB,GAAG,IAAIhB,iBAAiB,CAACe,QAAQ,CAAC;EAC3D;AA0FF;AAEA,eAAeF,qBAAqB;AAAC,IAAAT,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA;AAAAsB,YAAA,CAAA9B,EAAA;AAAA8B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAAtB,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}