const base_url = ``;
const GMB_ENDPOINTS = {
  get_accounts: `${base_url}https://mybusinessaccountmanagement.googleapis.com/v1/accounts`,
  get_locations: (accountId, refreshToken) =>
    `${base_url}https://mybusinessbusinessinformation.googleapis.com/v1/accounts/${accountId}/locations?readMask=name,languageCode,storeCode,title,phoneNumbers,categories,storefrontAddress,websiteUri,regularHours,specialHours,serviceArea,labels,adWordsLocationExtensions,latlng,openInfo,metadata,profile,relationshipData,moreHours,serviceItems${
      refreshToken ? "&pageToken=" + encodeURIComponent(refreshToken) : ""
    }`,
  get_location_summary: (locationId) =>
    `${base_url}https://mybusinessbusinessinformation.googleapis.com/v1/locations/${locationId}?read_mask=name,languageCode,storeCode,title,phoneNumbers,categories,storefrontAddress,websiteUri,regularHours,specialHours,serviceArea,labels,adWordsLocationExtensions,latlng,openInfo,metadata,profile,relationshipData,moreHours,serviceItems`,
  get_reviews: (accountId, locationId) =>
    `${base_url}https://mybusiness.googleapis.com/v4/accounts/${accountId}/locations/${locationId}/reviews`,
  get_QandA: (locationId) =>
    `${base_url} https://mybusinessqanda.googleapis.com/v1/locations/${locationId}/questions`,
  get_locationMetrics: (locationId, startDateRange, endDateRange) =>
    `${base_url} https://businessprofileperformance.googleapis.com/v1/locations/${locationId}:fetchMultiDailyMetricsTimeSeries?dailyMetrics=WEBSITE_CLICKS&dailyMetrics=CALL_CLICKS&dailyMetrics=BUSINESS_DIRECTION_REQUESTS&dailyMetrics=BUSINESS_IMPRESSIONS_DESKTOP_MAPS&dailyMetrics=BUSINESS_IMPRESSIONS_DESKTOP_SEARCH&dailyMetrics=BUSINESS_IMPRESSIONS_MOBILE_MAPS&dailyMetrics=BUSINESS_IMPRESSIONS_MOBILE_SEARCH&dailyMetrics=BUSINESS_CONVERSATIONS&dailyRange.start_date.year=${startDateRange.year}&dailyRange.start_date.month=${startDateRange.month}&dailyRange.start_date.day=${startDateRange.day}&dailyRange.end_date.year=${endDateRange.year}&dailyRange.end_date.month=${endDateRange.month}&dailyRange.end_date.day=${endDateRange.day}`,
  get_searchkeywords: (locationId, startDateRange, endDateRange) =>
    `${base_url} https://businessprofileperformance.googleapis.com/v1/locations/${locationId}/searchkeywords/impressions/monthly?monthlyRange.start_month.year=${startDateRange.year}&monthlyRange.start_month.month=${startDateRange.month}&monthlyRange.end_month.year=${endDateRange.year}&monthlyRange.end_month.month=${endDateRange.month}`,
  put_reviewReply: (accountId, locationId, reviewId) =>
    `${base_url} https://mybusiness.googleapis.com/v4/accounts/${accountId}/locations/${locationId}/reviews/${reviewId}/reply`,
  upsert_QandAReply: (locationId, questionId) =>
    `${base_url} https://mybusinessqanda.googleapis.com/v1/locations/${locationId}/questions/${questionId}/answers:upsert`,
  start_upload: (accountId, locationId) =>
    `${base_url} https://mybusiness.googleapis.com/v4/accounts/${accountId}/locations/${locationId}/media:startUpload`,
  upload_media: (resourceName) =>
    `${base_url} https://mybusiness.googleapis.com/upload/v1/media/${resourceName}?upload_type=media`,
  retrieve_posts: (accountId, locationId) =>
    `https://mybusiness.googleapis.com/v4/accounts/${accountId}/locations/${locationId}/localPosts`,
  upload_server_media: (accountId, locationId) =>
    `https://mybusiness.googleapis.com/v4/accounts/${accountId}/locations/${locationId}/media`,
  deletePosts: (route) => `https://mybusiness.googleapis.com/v4/${route}`,
};

module.exports = GMB_ENDPOINTS;
