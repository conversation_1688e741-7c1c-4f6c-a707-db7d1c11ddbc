{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\screens\\\\posts\\\\updatesSection\\\\updatesSection.screen.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React from \"react\";\nimport { Card, CardContent, Typography, Button, Box, Grid, CardMedia } from \"@mui/material\";\nimport { useNavigate } from \"react-router-dom\";\nimport { TOPIC_TYPES } from \"../../../constants/application.constant\";\nimport { useSelector } from \"react-redux\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UpdateCard = props => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    title,\n    description,\n    buttonText,\n    buttonColor,\n    bgColor,\n    imagePath,\n    navigationType,\n    isDisabled\n  } = props;\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      backgroundColor: bgColor,\n      borderRadius: 2,\n      boxShadow: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n      component: \"img\",\n      height: \"250\",\n      image: imagePath,\n      alt: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        fontWeight: \"bold\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          marginBottom: 2\n        },\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"updatesShapeBtn\",\n        variant: \"contained\",\n        fullWidth: true,\n        color: buttonColor === \"primary\" ? \"primary\" : buttonColor === \"success\" ? \"success\" : \"secondary\",\n        onClick: () => navigate(`/post-management/create-social-post`),\n        disabled: !isDisabled,\n        children: buttonText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_s(UpdateCard, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = UpdateCard;\nconst UpdatesSection = () => {\n  _s2();\n  const {\n    userInfo,\n    rbAccess\n  } = useSelector(state => state.authReducer);\n  const updatesData = [{\n    id: 1,\n    title: \"Post Updates\",\n    description: \"Update your customers with the latest information about your Brand.\",\n    buttonText: \"Create New Post\",\n    buttonColor: \"secondary\",\n    bgColor: \"#E8F0FE\",\n    imagePath: require(\"../../../assets/common/create-new-post.jpg\"),\n    navigationType: TOPIC_TYPES.Event,\n    isDisabled: Boolean(rbAccess && rbAccess.PostsCreate)\n  }, {\n    id: 2,\n    title: \"Offers & Deals Updates\",\n    description: \"Add Offers & Deals to generate more customer interest & impressions\",\n    buttonText: \"Create New Offer\",\n    buttonColor: \"success\",\n    bgColor: \"#E0F7FA\",\n    imagePath: require(\"../../../assets/common/offers-deals.jpg\"),\n    navigationType: TOPIC_TYPES.Offer,\n    isDisabled: Boolean(rbAccess && rbAccess.PostsCreate)\n  }, {\n    id: 3,\n    title: \"Event Updates\",\n    description: \"Tell customers about upcoming Events & Occasions at your Location\",\n    buttonText: \"Update Event Updates\",\n    buttonColor: \"primary\",\n    bgColor: \"#FCE4EC\",\n    imagePath: require(\"../../../assets/common/event-updates.jpg\"),\n    navigationType: TOPIC_TYPES.Event,\n    isDisabled: Boolean(rbAccess && rbAccess.PostsCreate)\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      flexGrow: 1,\n      padding: 3\n    },\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: updatesData.map(update => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        sx: {\n          padding: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(UpdateCard, {\n          ...update\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 13\n        }, this)\n      }, update.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_s2(UpdatesSection, \"IbaeYxr8wmhLFk9x9Bb5NOgYOII=\", false, function () {\n  return [useSelector];\n});\n_c2 = UpdatesSection;\nexport default UpdatesSection;\nvar _c, _c2;\n$RefreshReg$(_c, \"UpdateCard\");\n$RefreshReg$(_c2, \"UpdatesSection\");", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Box", "Grid", "CardMedia", "useNavigate", "TOPIC_TYPES", "useSelector", "jsxDEV", "_jsxDEV", "UpdateCard", "props", "_s", "navigate", "title", "description", "buttonText", "buttonColor", "bgColor", "imagePath", "navigationType", "isDisabled", "sx", "backgroundColor", "borderRadius", "boxShadow", "children", "component", "height", "image", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "marginBottom", "className", "fullWidth", "color", "onClick", "disabled", "_c", "UpdatesSection", "_s2", "userInfo", "rbAccess", "state", "authReducer", "updatesData", "id", "require", "Event", "Boolean", "PostsCreate", "Offer", "flexGrow", "padding", "container", "spacing", "map", "update", "item", "xs", "sm", "md", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/screens/posts/updatesSection/updatesSection.screen.tsx"], "sourcesContent": ["import React from \"react\";\nimport {\n  Card,\n  CardContent,\n  Typography,\n  Button,\n  Box,\n  Grid,\n  CardMedia,\n} from \"@mui/material\";\nimport { useNavigate } from \"react-router-dom\";\nimport { TOPIC_TYPES } from \"../../../constants/application.constant\";\nimport { useSelector } from \"react-redux\";\n\nconst UpdateCard = (props: {\n  title: string;\n  description: string;\n  buttonText: string;\n  buttonColor: string;\n  bgColor: string;\n  imagePath: string;\n  navigationType: string;\n  isDisabled: boolean;\n}) => {\n  const navigate = useNavigate();\n  const {\n    title,\n    description,\n    buttonText,\n    buttonColor,\n    bgColor,\n    imagePath,\n    navigationType,\n    isDisabled,\n  } = props;\n\n  return (\n    <Card sx={{ backgroundColor: bgColor, borderRadius: 2, boxShadow: 3 }}>\n      <CardMedia component=\"img\" height=\"250\" image={imagePath} alt={title} />\n      <CardContent>\n        <Typography variant=\"h6\" fontWeight=\"bold\">\n          {title}\n        </Typography>\n        <Typography variant=\"body2\" sx={{ marginBottom: 2 }}>\n          {description}\n        </Typography>\n        <Button\n          className=\"updatesShapeBtn\"\n          variant=\"contained\"\n          fullWidth\n          color={\n            buttonColor === \"primary\"\n              ? \"primary\"\n              : buttonColor === \"success\"\n              ? \"success\"\n              : \"secondary\"\n          }\n          onClick={() => navigate(`/post-management/create-social-post`)}\n          disabled={!isDisabled}\n        >\n          {buttonText}\n        </Button>\n      </CardContent>\n    </Card>\n  );\n};\n\nconst UpdatesSection = () => {\n  const { userInfo, rbAccess } = useSelector((state: any) => state.authReducer);\n\n  const updatesData = [\n    {\n      id: 1,\n      title: \"Post Updates\",\n      description:\n        \"Update your customers with the latest information about your Brand.\",\n      buttonText: \"Create New Post\",\n      buttonColor: \"secondary\",\n      bgColor: \"#E8F0FE\",\n      imagePath: require(\"../../../assets/common/create-new-post.jpg\"),\n      navigationType: TOPIC_TYPES.Event,\n      isDisabled: Boolean(rbAccess && rbAccess.PostsCreate),\n    },\n    {\n      id: 2,\n      title: \"Offers & Deals Updates\",\n      description:\n        \"Add Offers & Deals to generate more customer interest & impressions\",\n      buttonText: \"Create New Offer\",\n      buttonColor: \"success\",\n      bgColor: \"#E0F7FA\",\n      imagePath: require(\"../../../assets/common/offers-deals.jpg\"),\n      navigationType: TOPIC_TYPES.Offer,\n      isDisabled: Boolean(rbAccess && rbAccess.PostsCreate),\n    },\n    {\n      id: 3,\n      title: \"Event Updates\",\n      description:\n        \"Tell customers about upcoming Events & Occasions at your Location\",\n      buttonText: \"Update Event Updates\",\n      buttonColor: \"primary\",\n      bgColor: \"#FCE4EC\",\n      imagePath: require(\"../../../assets/common/event-updates.jpg\"),\n      navigationType: TOPIC_TYPES.Event,\n      isDisabled: Boolean(rbAccess && rbAccess.PostsCreate),\n    },\n  ];\n\n  return (\n    <Box sx={{ flexGrow: 1, padding: 3 }}>\n      <Grid container spacing={2}>\n        {updatesData.map((update) => (\n          <Grid item xs={12} sm={6} md={4} key={update.id} sx={{ padding: 2 }}>\n            <UpdateCard {...update} />\n          </Grid>\n        ))}\n      </Grid>\n    </Box>\n  );\n};\n\nexport default UpdatesSection;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,SAAS,QACJ,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,yCAAyC;AACrE,SAASC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,UAAU,GAAIC,KASnB,IAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IACJS,KAAK;IACLC,WAAW;IACXC,UAAU;IACVC,WAAW;IACXC,OAAO;IACPC,SAAS;IACTC,cAAc;IACdC;EACF,CAAC,GAAGV,KAAK;EAET,oBACEF,OAAA,CAACX,IAAI;IAACwB,EAAE,EAAE;MAAEC,eAAe,EAAEL,OAAO;MAAEM,YAAY,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACpEjB,OAAA,CAACL,SAAS;MAACuB,SAAS,EAAC,KAAK;MAACC,MAAM,EAAC,KAAK;MAACC,KAAK,EAAEV,SAAU;MAACW,GAAG,EAAEhB;IAAM;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxEzB,OAAA,CAACV,WAAW;MAAA2B,QAAA,gBACVjB,OAAA,CAACT,UAAU;QAACmC,OAAO,EAAC,IAAI;QAACC,UAAU,EAAC,MAAM;QAAAV,QAAA,EACvCZ;MAAK;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACbzB,OAAA,CAACT,UAAU;QAACmC,OAAO,EAAC,OAAO;QAACb,EAAE,EAAE;UAAEe,YAAY,EAAE;QAAE,CAAE;QAAAX,QAAA,EACjDX;MAAW;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACbzB,OAAA,CAACR,MAAM;QACLqC,SAAS,EAAC,iBAAiB;QAC3BH,OAAO,EAAC,WAAW;QACnBI,SAAS;QACTC,KAAK,EACHvB,WAAW,KAAK,SAAS,GACrB,SAAS,GACTA,WAAW,KAAK,SAAS,GACzB,SAAS,GACT,WACL;QACDwB,OAAO,EAAEA,CAAA,KAAM5B,QAAQ,CAAC,qCAAqC,CAAE;QAC/D6B,QAAQ,EAAE,CAACrB,UAAW;QAAAK,QAAA,EAErBV;MAAU;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAACtB,EAAA,CAnDIF,UAAU;EAAA,QAUGL,WAAW;AAAA;AAAAsC,EAAA,GAVxBjC,UAAU;AAqDhB,MAAMkC,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAM;IAAEC,QAAQ;IAAEC;EAAS,CAAC,GAAGxC,WAAW,CAAEyC,KAAU,IAAKA,KAAK,CAACC,WAAW,CAAC;EAE7E,MAAMC,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,CAAC;IACLrC,KAAK,EAAE,cAAc;IACrBC,WAAW,EACT,qEAAqE;IACvEC,UAAU,EAAE,iBAAiB;IAC7BC,WAAW,EAAE,WAAW;IACxBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAEiC,OAAO,CAAC,4CAA4C,CAAC;IAChEhC,cAAc,EAAEd,WAAW,CAAC+C,KAAK;IACjChC,UAAU,EAAEiC,OAAO,CAACP,QAAQ,IAAIA,QAAQ,CAACQ,WAAW;EACtD,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLrC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EACT,qEAAqE;IACvEC,UAAU,EAAE,kBAAkB;IAC9BC,WAAW,EAAE,SAAS;IACtBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAEiC,OAAO,CAAC,yCAAyC,CAAC;IAC7DhC,cAAc,EAAEd,WAAW,CAACkD,KAAK;IACjCnC,UAAU,EAAEiC,OAAO,CAACP,QAAQ,IAAIA,QAAQ,CAACQ,WAAW;EACtD,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLrC,KAAK,EAAE,eAAe;IACtBC,WAAW,EACT,mEAAmE;IACrEC,UAAU,EAAE,sBAAsB;IAClCC,WAAW,EAAE,SAAS;IACtBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAEiC,OAAO,CAAC,0CAA0C,CAAC;IAC9DhC,cAAc,EAAEd,WAAW,CAAC+C,KAAK;IACjChC,UAAU,EAAEiC,OAAO,CAACP,QAAQ,IAAIA,QAAQ,CAACQ,WAAW;EACtD,CAAC,CACF;EAED,oBACE9C,OAAA,CAACP,GAAG;IAACoB,EAAE,EAAE;MAAEmC,QAAQ,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAE,CAAE;IAAAhC,QAAA,eACnCjB,OAAA,CAACN,IAAI;MAACwD,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAlC,QAAA,EACxBwB,WAAW,CAACW,GAAG,CAAEC,MAAM,iBACtBrD,OAAA,CAACN,IAAI;QAAC4D,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAiB5C,EAAE,EAAE;UAAEoC,OAAO,EAAE;QAAE,CAAE;QAAAhC,QAAA,eAClEjB,OAAA,CAACC,UAAU;UAAA,GAAKoD;QAAM;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC,GADU4B,MAAM,CAACX,EAAE;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEzC,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACW,GAAA,CArDID,cAAc;EAAA,QACarC,WAAW;AAAA;AAAA4D,GAAA,GADtCvB,cAAc;AAuDpB,eAAeA,cAAc;AAAC,IAAAD,EAAA,EAAAwB,GAAA;AAAAC,YAAA,CAAAzB,EAAA;AAAAyB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}