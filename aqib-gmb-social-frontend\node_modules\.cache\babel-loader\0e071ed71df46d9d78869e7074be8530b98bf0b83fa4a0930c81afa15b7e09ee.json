{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\charts\\\\registeredEmployees.charts.tsx\";\nimport React from \"react\";\nimport { Bar } from \"react-chartjs-2\";\nimport { Chart, registerables } from \"chart.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChart.register(...registerables);\nexport const RegisteredEmployeesChart = props => {\n  const MONTHS = [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\n\n  //   const months = (config: any) => {\n  //     var cfg = config || {};\n  //     var count = cfg.count || 12;\n  //     var section = cfg.section;\n  //     var values = [];\n  //     var i, value;\n\n  //     for (i = 0; i < count; ++i) {\n  //       value = MONTHS[Math.ceil(i) % 12];\n  //       values.push(value.substring(0, section));\n  //     }\n\n  //     return values;\n  //   }\n\n  //   const labels = months({count: 7});\n\n  const data = {\n    labels: [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\"],\n    datasets: [{\n      label: \"Users\",\n      data: [65, 59, 80, 81, 56, 55, 40],\n      backgroundColor: [\"rgba(255, 99, 132, 0.2)\", \"rgba(255, 159, 64, 0.2)\", \"rgba(255, 205, 86, 0.2)\", \"rgba(75, 192, 192, 0.2)\", \"rgba(54, 162, 235, 0.2)\", \"rgba(153, 102, 255, 0.2)\", \"rgba(201, 203, 207, 0.2)\"],\n      borderColor: [\"rgb(255, 99, 132)\", \"rgb(255, 159, 64)\", \"rgb(255, 205, 86)\", \"rgb(75, 192, 192)\", \"rgb(54, 162, 235)\", \"rgb(153, 102, 255)\", \"rgb(201, 203, 207)\"],\n      borderWidth: 1\n    }]\n  };\n  const options = {\n    maintainAspectRatio: false,\n    // Allows custom height and width\n    responsive: true\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chart-container\",\n    children: /*#__PURE__*/_jsxDEV(Bar, {\n      height: 400,\n      data: data,\n      options: options\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n};\n_c = RegisteredEmployeesChart;\nvar _c;\n$RefreshReg$(_c, \"RegisteredEmployeesChart\");", "map": {"version": 3, "names": ["React", "Bar", "Chart", "registerables", "jsxDEV", "_jsxDEV", "register", "RegisteredEmployeesChart", "props", "MONTHS", "data", "labels", "datasets", "label", "backgroundColor", "borderColor", "borderWidth", "options", "maintainAspectRatio", "responsive", "className", "children", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/charts/registeredEmployees.charts.tsx"], "sourcesContent": ["import React from \"react\";\nimport { Bar } from \"react-chartjs-2\";\nimport { Chart, registerables } from \"chart.js\";\nimport { Card, CardContent } from \"@mui/material\";\nChart.register(...registerables);\n\nexport const RegisteredEmployeesChart = (props: { chartData: any }) => {\n  const MONTHS = [\n    \"January\",\n    \"February\",\n    \"March\",\n    \"April\",\n    \"May\",\n    \"June\",\n    \"July\",\n    \"August\",\n    \"September\",\n    \"October\",\n    \"November\",\n    \"December\",\n  ];\n\n  //   const months = (config: any) => {\n  //     var cfg = config || {};\n  //     var count = cfg.count || 12;\n  //     var section = cfg.section;\n  //     var values = [];\n  //     var i, value;\n\n  //     for (i = 0; i < count; ++i) {\n  //       value = MONTHS[Math.ceil(i) % 12];\n  //       values.push(value.substring(0, section));\n  //     }\n\n  //     return values;\n  //   }\n\n  //   const labels = months({count: 7});\n\n  const data = {\n    labels: [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\"],\n    datasets: [\n      {\n        label: \"Users\",\n        data: [65, 59, 80, 81, 56, 55, 40],\n        backgroundColor: [\n          \"rgba(255, 99, 132, 0.2)\",\n          \"rgba(255, 159, 64, 0.2)\",\n          \"rgba(255, 205, 86, 0.2)\",\n          \"rgba(75, 192, 192, 0.2)\",\n          \"rgba(54, 162, 235, 0.2)\",\n          \"rgba(153, 102, 255, 0.2)\",\n          \"rgba(201, 203, 207, 0.2)\",\n        ],\n        borderColor: [\n          \"rgb(255, 99, 132)\",\n          \"rgb(255, 159, 64)\",\n          \"rgb(255, 205, 86)\",\n          \"rgb(75, 192, 192)\",\n          \"rgb(54, 162, 235)\",\n          \"rgb(153, 102, 255)\",\n          \"rgb(201, 203, 207)\",\n        ],\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const options = {\n    maintainAspectRatio: false, // Allows custom height and width\n    responsive: true,\n  };\n\n  return (\n    <div className=\"chart-container\">\n      <Bar height={400} data={data} options={options} />\n    </div>\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,QAAQ,iBAAiB;AACrC,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhDH,KAAK,CAACI,QAAQ,CAAC,GAAGH,aAAa,CAAC;AAEhC,OAAO,MAAMI,wBAAwB,GAAIC,KAAyB,IAAK;EACrE,MAAMC,MAAM,GAAG,CACb,SAAS,EACT,UAAU,EACV,OAAO,EACP,OAAO,EACP,KAAK,EACL,MAAM,EACN,MAAM,EACN,QAAQ,EACR,WAAW,EACX,SAAS,EACT,UAAU,EACV,UAAU,CACX;;EAED;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;;EAEA,MAAMC,IAAI,GAAG;IACXC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;IACxEC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,OAAO;MACdH,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAClCI,eAAe,EAAE,CACf,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,0BAA0B,EAC1B,0BAA0B,CAC3B;MACDC,WAAW,EAAE,CACX,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,oBAAoB,EACpB,oBAAoB,CACrB;MACDC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,MAAMC,OAAO,GAAG;IACdC,mBAAmB,EAAE,KAAK;IAAE;IAC5BC,UAAU,EAAE;EACd,CAAC;EAED,oBACEd,OAAA;IAAKe,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BhB,OAAA,CAACJ,GAAG;MAACqB,MAAM,EAAE,GAAI;MAACZ,IAAI,EAAEA,IAAK;MAACO,OAAO,EAAEA;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/C,CAAC;AAEV,CAAC;AAACC,EAAA,GAxEWpB,wBAAwB;AAAA,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}