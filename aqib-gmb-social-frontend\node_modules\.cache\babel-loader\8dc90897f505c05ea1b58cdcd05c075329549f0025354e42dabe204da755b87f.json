{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\userAvatarWIthName\\\\userAvatarWIthName.component.tsx\";\nimport Stack from \"@mui/material/Stack\";\nimport UserAvatar from \"../userAvatar/userAvatar.component\";\nimport Box from \"@mui/material/Box\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserAvatarWithName = props => {\n  return /*#__PURE__*/_jsxDEV(Stack, {\n    direction: \"row\",\n    spacing: 2,\n    alignItems: \"center\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(UserAvatar, {\n        fullname: props.fullname\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(\"h4\", {\n        children: props.fullname\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = UserAvatarWithName;\nexport default UserAvatarWithName;\nvar _c;\n$RefreshReg$(_c, \"UserAvatarWithName\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "UserAvatar", "Box", "jsxDEV", "_jsxDEV", "UserAvatarWithName", "props", "direction", "spacing", "alignItems", "children", "fullname", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/userAvatarWIthName/userAvatarWIthName.component.tsx"], "sourcesContent": ["import Stack from \"@mui/material/Stack\";\nimport UserAvatar from \"../userAvatar/userAvatar.component\";\nimport Box from \"@mui/material/Box\";\n\nconst UserAvatarWithName = (props: { fullname: string }) => {\n  return (\n    <Stack direction=\"row\" spacing={2} alignItems=\"center\">\n      <Box>\n        <UserAvatar fullname={props.fullname} />\n      </Box>\n      <Box>\n        <h4>{props.fullname}</h4>\n      </Box>\n    </Stack>\n  );\n};\n\nexport default UserAvatarWithName;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,qBAAqB;AACvC,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,GAAG,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,kBAAkB,GAAIC,KAA2B,IAAK;EAC1D,oBACEF,OAAA,CAACJ,KAAK;IAACO,SAAS,EAAC,KAAK;IAACC,OAAO,EAAE,CAAE;IAACC,UAAU,EAAC,QAAQ;IAAAC,QAAA,gBACpDN,OAAA,CAACF,GAAG;MAAAQ,QAAA,eACFN,OAAA,CAACH,UAAU;QAACU,QAAQ,EAAEL,KAAK,CAACK;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eACNX,OAAA,CAACF,GAAG;MAAAQ,QAAA,eACFN,OAAA;QAAAM,QAAA,EAAKJ,KAAK,CAACK;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACC,EAAA,GAXIX,kBAAkB;AAaxB,eAAeA,kBAAkB;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}