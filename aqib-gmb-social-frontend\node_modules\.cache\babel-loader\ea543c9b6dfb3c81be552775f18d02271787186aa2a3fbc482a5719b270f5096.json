{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\screens\\\\dashboardV2\\\\searchBreakdown.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Box, Typography, List, ListItem, Button, Dialog, DialogTitle, DialogContent, CircularProgress, Grid, Chip } from \"@mui/material\";\nimport LocationMetricsService from \"../../services/locationMetrics/locationMetrics.service\";\nimport { useDispatch } from \"react-redux\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport IconButton from \"@mui/material/IconButton\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SearchBreakdown = ({\n  accountId,\n  locationId,\n  from,\n  to\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const [open, setOpen] = useState(false);\n  const [data, setData] = useState([]);\n  const [nextPageToken, setNextPageToken] = useState(null);\n  const modalRef = useRef(null);\n  const isFetchingRef = useRef(false);\n  const _locationMetricsService = new LocationMetricsService(dispatch);\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    if (accountId && locationId && from && to) {\n      fetchSearchKeywords();\n    }\n  }, [accountId, locationId, from, to]);\n  const fetchSearchKeywords = async () => {\n    try {\n      setLoading(true);\n      const requestObj = {\n        startDate: from,\n        endDate: to\n      };\n      const getMetricsRequestHeader = {\n        \"x-gmb-account-id\": accountId,\n        \"x-gmb-location-id\": locationId\n      };\n      let response = await _locationMetricsService.getSearchkeywords(requestObj, getMetricsRequestHeader);\n      console.log(\"Search keywords:\", response.data);\n      setData(response.data.searchKeywordsCounts);\n      setNextPageToken(response.data.nextPageToken || null);\n    } catch (error) {\n      console.error(\"Failed to fetch analytics data\", error);\n      setData([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchMoreData = async pageToken => {\n    const requestObj = {\n      startDate: from,\n      endDate: to,\n      pageToken: pageToken\n    };\n    const getMetricsRequestHeader = {\n      \"x-gmb-account-id\": accountId,\n      \"x-gmb-location-id\": locationId\n    };\n    let response = await _locationMetricsService.getSearchkeywords(requestObj, getMetricsRequestHeader);\n    const result = response.data;\n    return result;\n  };\n  useEffect(() => {\n    const handleScroll = async () => {\n      if (modalRef.current && modalRef.current.scrollTop + modalRef.current.clientHeight >= modalRef.current.scrollHeight - 100 && nextPageToken && !isFetchingRef.current) {\n        isFetchingRef.current = true;\n        const response = await fetchMoreData(nextPageToken);\n        setData(prev => [...prev, ...response.searchKeywordsCounts]);\n        setNextPageToken(response.nextPageToken || null);\n        isFetchingRef.current = false;\n      }\n    };\n    let refCurrent = null;\n    setTimeout(() => {\n      refCurrent = modalRef.current;\n      if (refCurrent) {\n        refCurrent.addEventListener(\"scroll\", handleScroll);\n      }\n    }, 1000);\n    return () => {\n      if (refCurrent) {\n        refCurrent.removeEventListener(\"scroll\", handleScroll);\n      }\n    };\n  }, [nextPageToken, loading, open]);\n  const handleOpen = () => {\n    setOpen(true);\n    setTimeout(() => {\n      var _modalRef$current;\n      (_modalRef$current = modalRef.current) === null || _modalRef$current === void 0 ? void 0 : _modalRef$current.focus();\n    }, 1000);\n  };\n  const handleClose = () => setOpen(false);\n  const getCountValue = item => item.insightsValue.value || item.insightsValue.threshold;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: \"100%\",\n      height: \"100%\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      className: \"\",\n      sx: {\n        p: 2,\n        backgroundColor: \"#fff\",\n        color: \"#000\",\n        borderRadius: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        fontWeight: \"bold\",\n        gutterBottom: true,\n        children: data && data.reduce((sum, item) => sum + parseInt(getCountValue(item) || \"0\"), 0)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: \"Searches showed your Business Profile in the search results\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: data && data.slice(0, 18).map((item, index) => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 6,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: \"100%\",\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [index + 1, \". \", item.searchKeyword]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: getCountValue(item),\n              color: \"primary\",\n              variant: \"outlined\",\n              sx: {\n                ml: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 17\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        style: {\n          textTransform: \"capitalize\"\n        },\n        variant: \"outlined\",\n        onClick: handleOpen,\n        sx: {\n          mt: 2\n        },\n        children: \"See more\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: open,\n        onClose: handleClose,\n        fullWidth: true,\n        maxWidth: \"sm\",\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          sx: {\n            m: 0,\n            p: 2,\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"All Search Terms\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            sx: {\n              color: theme => theme.palette.grey[500]\n            },\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          dividers: true,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            ref: modalRef,\n            tabIndex: 0 // This helps move focus here\n            ,\n            sx: {\n              maxHeight: 400,\n              overflowY: \"auto\",\n              outline: \"none\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(List, {\n              children: data && data.map((item, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n                divider: true,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: \"100%\",\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    children: [index + 1, \". \", item.searchKeyword]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: getCountValue(item),\n                    color: \"primary\",\n                    variant: \"outlined\",\n                    sx: {\n                      ml: 2\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 23\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), loading && /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24,\n              sx: {\n                display: \"block\",\n                mx: \"auto\",\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n};\n_s(SearchBreakdown, \"hO/2BGNgvaQAV0uLGpDy3UBLr50=\", false, function () {\n  return [useDispatch];\n});\n_c = SearchBreakdown;\nexport default SearchBreakdown;\nvar _c;\n$RefreshReg$(_c, \"SearchBreakdown\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "Typography", "List", "ListItem", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CircularProgress", "Grid", "Chip", "LocationMetricsService", "useDispatch", "CloseIcon", "IconButton", "jsxDEV", "_jsxDEV", "SearchBreakdown", "accountId", "locationId", "from", "to", "_s", "dispatch", "open", "<PERSON><PERSON><PERSON>", "data", "setData", "nextPageToken", "setNextPageToken", "modalRef", "isFetchingRef", "_locationMetricsService", "loading", "setLoading", "fetchSearchKeywords", "requestObj", "startDate", "endDate", "getMetricsRequestHeader", "response", "getSearchkeywords", "console", "log", "searchKeywordsCounts", "error", "fetchMoreData", "pageToken", "result", "handleScroll", "current", "scrollTop", "clientHeight", "scrollHeight", "prev", "refCurrent", "setTimeout", "addEventListener", "removeEventListener", "handleOpen", "_modalRef$current", "focus", "handleClose", "getCountValue", "item", "insightsValue", "value", "threshold", "style", "width", "height", "children", "className", "sx", "p", "backgroundColor", "color", "borderRadius", "variant", "fontWeight", "gutterBottom", "reduce", "sum", "parseInt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "slice", "map", "index", "xs", "sm", "md", "lg", "display", "justifyContent", "alignItems", "searchKeyword", "label", "ml", "textTransform", "onClick", "mt", "onClose", "fullWidth", "max<PERSON><PERSON><PERSON>", "m", "theme", "palette", "grey", "dividers", "ref", "tabIndex", "maxHeight", "overflowY", "outline", "divider", "size", "mx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/screens/dashboardV2/searchBreakdown.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef, useContext } from \"react\";\nimport {\n  Box,\n  Typography,\n  List,\n  ListItem,\n  ListItemText,\n  Button,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  CircularProgress,\n  Grid,\n  Chip,\n} from \"@mui/material\";\nimport { ILocationMetricsRequestModel } from \"../../interfaces/request/ILocationMetricsRequestModel\";\nimport LocationMetricsService from \"../../services/locationMetrics/locationMetrics.service\";\nimport { useDispatch } from \"react-redux\";\nimport { LoadingContext } from \"../../context/loading.context\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport IconButton from \"@mui/material/IconButton\";\n\ninterface InsightsValue {\n  value?: string;\n  threshold?: string;\n}\n\ninterface SearchKeyword {\n  searchKeyword: string;\n  insightsValue: InsightsValue;\n}\n\ninterface Props {\n  accountId: any;\n  locationId: any;\n  from: any;\n  to: any;\n}\n\nconst SearchBreakdown: React.FC<Props> = ({\n  accountId,\n  locationId,\n  from,\n  to,\n}) => {\n  const dispatch = useDispatch();\n  const [open, setOpen] = useState(false);\n  const [data, setData] = useState<SearchKeyword[]>([]);\n  const [nextPageToken, setNextPageToken] = useState<string | null>(null);\n  const modalRef = useRef<HTMLDivElement | null>(null);\n  const isFetchingRef = useRef(false);\n  const _locationMetricsService = new LocationMetricsService(dispatch);\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    if (accountId && locationId && from && to) {\n      fetchSearchKeywords();\n    }\n  }, [accountId, locationId, from, to]);\n\n  const fetchSearchKeywords = async () => {\n    try {\n      setLoading(true);\n      const requestObj: ILocationMetricsRequestModel = {\n        startDate: from,\n        endDate: to,\n      };\n      const getMetricsRequestHeader = {\n        \"x-gmb-account-id\": accountId,\n        \"x-gmb-location-id\": locationId,\n      };\n      let response: any = await _locationMetricsService.getSearchkeywords(\n        requestObj,\n        getMetricsRequestHeader\n      );\n\n      console.log(\"Search keywords:\", response.data);\n\n      setData(response.data.searchKeywordsCounts);\n      setNextPageToken(response.data.nextPageToken || null);\n    } catch (error) {\n      console.error(\"Failed to fetch analytics data\", error);\n      setData([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchMoreData = async (pageToken: string) => {\n    const requestObj: ILocationMetricsRequestModel = {\n      startDate: from,\n      endDate: to,\n      pageToken: pageToken,\n    };\n    const getMetricsRequestHeader = {\n      \"x-gmb-account-id\": accountId,\n      \"x-gmb-location-id\": locationId,\n    };\n    let response: any = await _locationMetricsService.getSearchkeywords(\n      requestObj,\n      getMetricsRequestHeader\n    );\n\n    const result = response.data;\n    return result;\n  };\n\n  useEffect(() => {\n    const handleScroll = async () => {\n      if (\n        modalRef.current &&\n        modalRef.current.scrollTop + modalRef.current.clientHeight >=\n          modalRef.current.scrollHeight - 100 &&\n        nextPageToken &&\n        !isFetchingRef.current\n      ) {\n        isFetchingRef.current = true;\n        const response = await fetchMoreData(nextPageToken);\n        setData((prev) => [...prev, ...response.searchKeywordsCounts]);\n        setNextPageToken(response.nextPageToken || null);\n        isFetchingRef.current = false;\n      }\n    };\n\n    let refCurrent: HTMLDivElement | null = null;\n    setTimeout(() => {\n      refCurrent = modalRef.current;\n      if (refCurrent) {\n        refCurrent.addEventListener(\"scroll\", handleScroll);\n      }\n    }, 1000);\n\n    return () => {\n      if (refCurrent) {\n        refCurrent.removeEventListener(\"scroll\", handleScroll);\n      }\n    };\n  }, [nextPageToken, loading, open]);\n\n  const handleOpen = () => {\n    setOpen(true);\n    setTimeout(() => {\n      modalRef.current?.focus();\n    }, 1000);\n  };\n  const handleClose = () => setOpen(false);\n\n  const getCountValue = (item: SearchKeyword) =>\n    item.insightsValue.value || item.insightsValue.threshold;\n\n  return (\n    <div style={{ width: \"100%\", height: \"100%\" }}>\n      <Box className=\"\"\n        sx={{ p: 2, backgroundColor: \"#fff\", color: \"#000\", borderRadius: 2 }}\n      >\n        <Typography variant=\"h4\" fontWeight=\"bold\" gutterBottom>\n          {data &&\n            data.reduce(\n              (sum, item) => sum + parseInt(getCountValue(item) || \"0\"),\n              0\n            )}\n        </Typography>\n        <Typography variant=\"subtitle1\" gutterBottom>\n          Searches showed your Business Profile in the search results\n        </Typography>\n        <Grid container spacing={2}>\n          {data &&\n            data.slice(0, 18).map((item, index) => (\n              <Grid item xs={12} sm={6} md={6} lg={6} key={index}>\n                <Box\n                  sx={{\n                    width: \"100%\",\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                  }}\n                >\n                  <Typography variant=\"body1\">\n                    {index + 1}. {item.searchKeyword}\n                  </Typography>\n                  <Chip\n                    label={getCountValue(item)}\n                    color=\"primary\"\n                    variant=\"outlined\"\n                    sx={{ ml: 2 }}\n                  />\n                </Box>\n              </Grid>\n            ))}\n        </Grid>\n        <Button\n          style={{ textTransform: \"capitalize\" }}\n          variant=\"outlined\"\n          onClick={handleOpen}\n          sx={{ mt: 2 }}\n        >\n          See more\n        </Button>\n\n        <Dialog open={open} onClose={handleClose} fullWidth maxWidth=\"sm\">\n          <DialogTitle\n            sx={{\n              m: 0,\n              p: 2,\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n            }}\n          >\n            <Typography variant=\"h6\">All Search Terms</Typography>\n            <IconButton\n              aria-label=\"close\"\n              onClick={handleClose}\n              sx={{\n                color: (theme) => theme.palette.grey[500],\n              }}\n            >\n              <CloseIcon />\n            </IconButton>\n          </DialogTitle>\n          <DialogContent dividers>\n            <Box\n              ref={modalRef}\n              tabIndex={0} // This helps move focus here\n              sx={{ maxHeight: 400, overflowY: \"auto\", outline: \"none\" }}\n            >\n              <List>\n                {data &&\n                  data.map((item, index) => (\n                    <ListItem key={index} divider>\n                      <Box\n                        sx={{\n                          width: \"100%\",\n                          display: \"flex\",\n                          justifyContent: \"space-between\",\n                          alignItems: \"center\",\n                        }}\n                      >\n                        <Typography variant=\"body1\">\n                          {index + 1}. {item.searchKeyword}\n                        </Typography>\n                        <Chip\n                          label={getCountValue(item)}\n                          color=\"primary\"\n                          variant=\"outlined\"\n                          sx={{ ml: 2 }}\n                        />\n                      </Box>\n                    </ListItem>\n                  ))}\n              </List>\n              {loading && (\n                <CircularProgress\n                  size={24}\n                  sx={{ display: \"block\", mx: \"auto\", mt: 2 }}\n                />\n              )}\n            </Box>\n          </DialogContent>\n        </Dialog>\n      </Box>\n    </div>\n  );\n};\n\nexport default SearchBreakdown;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAoB,OAAO;AACtE,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,QAAQ,EAERC,MAAM,EACNC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,gBAAgB,EAChBC,IAAI,EACJC,IAAI,QACC,eAAe;AAEtB,OAAOC,sBAAsB,MAAM,wDAAwD;AAC3F,SAASC,WAAW,QAAQ,aAAa;AAEzC,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,UAAU,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAmBlD,MAAMC,eAAgC,GAAGA,CAAC;EACxCC,SAAS;EACTC,UAAU;EACVC,IAAI;EACJC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,IAAI,EAAEC,OAAO,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAAC4B,IAAI,EAAEC,OAAO,CAAC,GAAG7B,QAAQ,CAAkB,EAAE,CAAC;EACrD,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAMgC,QAAQ,GAAG/B,MAAM,CAAwB,IAAI,CAAC;EACpD,MAAMgC,aAAa,GAAGhC,MAAM,CAAC,KAAK,CAAC;EACnC,MAAMiC,uBAAuB,GAAG,IAAIrB,sBAAsB,CAACY,QAAQ,CAAC;EACpE,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7CD,SAAS,CAAC,MAAM;IACd,IAAIqB,SAAS,IAAIC,UAAU,IAAIC,IAAI,IAAIC,EAAE,EAAE;MACzCc,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACjB,SAAS,EAAEC,UAAU,EAAEC,IAAI,EAAEC,EAAE,CAAC,CAAC;EAErC,MAAMc,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAME,UAAwC,GAAG;QAC/CC,SAAS,EAAEjB,IAAI;QACfkB,OAAO,EAAEjB;MACX,CAAC;MACD,MAAMkB,uBAAuB,GAAG;QAC9B,kBAAkB,EAAErB,SAAS;QAC7B,mBAAmB,EAAEC;MACvB,CAAC;MACD,IAAIqB,QAAa,GAAG,MAAMR,uBAAuB,CAACS,iBAAiB,CACjEL,UAAU,EACVG,uBACF,CAAC;MAEDG,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEH,QAAQ,CAACd,IAAI,CAAC;MAE9CC,OAAO,CAACa,QAAQ,CAACd,IAAI,CAACkB,oBAAoB,CAAC;MAC3Cf,gBAAgB,CAACW,QAAQ,CAACd,IAAI,CAACE,aAAa,IAAI,IAAI,CAAC;IACvD,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDlB,OAAO,CAAC,EAAE,CAAC;IACb,CAAC,SAAS;MACRO,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMY,aAAa,GAAG,MAAOC,SAAiB,IAAK;IACjD,MAAMX,UAAwC,GAAG;MAC/CC,SAAS,EAAEjB,IAAI;MACfkB,OAAO,EAAEjB,EAAE;MACX0B,SAAS,EAAEA;IACb,CAAC;IACD,MAAMR,uBAAuB,GAAG;MAC9B,kBAAkB,EAAErB,SAAS;MAC7B,mBAAmB,EAAEC;IACvB,CAAC;IACD,IAAIqB,QAAa,GAAG,MAAMR,uBAAuB,CAACS,iBAAiB,CACjEL,UAAU,EACVG,uBACF,CAAC;IAED,MAAMS,MAAM,GAAGR,QAAQ,CAACd,IAAI;IAC5B,OAAOsB,MAAM;EACf,CAAC;EAEDnD,SAAS,CAAC,MAAM;IACd,MAAMoD,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IACEnB,QAAQ,CAACoB,OAAO,IAChBpB,QAAQ,CAACoB,OAAO,CAACC,SAAS,GAAGrB,QAAQ,CAACoB,OAAO,CAACE,YAAY,IACxDtB,QAAQ,CAACoB,OAAO,CAACG,YAAY,GAAG,GAAG,IACrCzB,aAAa,IACb,CAACG,aAAa,CAACmB,OAAO,EACtB;QACAnB,aAAa,CAACmB,OAAO,GAAG,IAAI;QAC5B,MAAMV,QAAQ,GAAG,MAAMM,aAAa,CAAClB,aAAa,CAAC;QACnDD,OAAO,CAAE2B,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE,GAAGd,QAAQ,CAACI,oBAAoB,CAAC,CAAC;QAC9Df,gBAAgB,CAACW,QAAQ,CAACZ,aAAa,IAAI,IAAI,CAAC;QAChDG,aAAa,CAACmB,OAAO,GAAG,KAAK;MAC/B;IACF,CAAC;IAED,IAAIK,UAAiC,GAAG,IAAI;IAC5CC,UAAU,CAAC,MAAM;MACfD,UAAU,GAAGzB,QAAQ,CAACoB,OAAO;MAC7B,IAAIK,UAAU,EAAE;QACdA,UAAU,CAACE,gBAAgB,CAAC,QAAQ,EAAER,YAAY,CAAC;MACrD;IACF,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAM;MACX,IAAIM,UAAU,EAAE;QACdA,UAAU,CAACG,mBAAmB,CAAC,QAAQ,EAAET,YAAY,CAAC;MACxD;IACF,CAAC;EACH,CAAC,EAAE,CAACrB,aAAa,EAAEK,OAAO,EAAET,IAAI,CAAC,CAAC;EAElC,MAAMmC,UAAU,GAAGA,CAAA,KAAM;IACvBlC,OAAO,CAAC,IAAI,CAAC;IACb+B,UAAU,CAAC,MAAM;MAAA,IAAAI,iBAAA;MACf,CAAAA,iBAAA,GAAA9B,QAAQ,CAACoB,OAAO,cAAAU,iBAAA,uBAAhBA,iBAAA,CAAkBC,KAAK,CAAC,CAAC;IAC3B,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD,MAAMC,WAAW,GAAGA,CAAA,KAAMrC,OAAO,CAAC,KAAK,CAAC;EAExC,MAAMsC,aAAa,GAAIC,IAAmB,IACxCA,IAAI,CAACC,aAAa,CAACC,KAAK,IAAIF,IAAI,CAACC,aAAa,CAACE,SAAS;EAE1D,oBACEnD,OAAA;IAAKoD,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,eAC5CvD,OAAA,CAAChB,GAAG;MAACwE,SAAS,EAAC,EAAE;MACfC,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,eAAe,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,YAAY,EAAE;MAAE,CAAE;MAAAN,QAAA,gBAEtEvD,OAAA,CAACf,UAAU;QAAC6E,OAAO,EAAC,IAAI;QAACC,UAAU,EAAC,MAAM;QAACC,YAAY;QAAAT,QAAA,EACpD7C,IAAI,IACHA,IAAI,CAACuD,MAAM,CACT,CAACC,GAAG,EAAElB,IAAI,KAAKkB,GAAG,GAAGC,QAAQ,CAACpB,aAAa,CAACC,IAAI,CAAC,IAAI,GAAG,CAAC,EACzD,CACF;MAAC;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eACbvE,OAAA,CAACf,UAAU;QAAC6E,OAAO,EAAC,WAAW;QAACE,YAAY;QAAAT,QAAA,EAAC;MAE7C;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvE,OAAA,CAACP,IAAI;QAAC+E,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAlB,QAAA,EACxB7C,IAAI,IACHA,IAAI,CAACgE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,GAAG,CAAC,CAAC3B,IAAI,EAAE4B,KAAK,kBAChC5E,OAAA,CAACP,IAAI;UAACuD,IAAI;UAAC6B,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,eACrCvD,OAAA,CAAChB,GAAG;YACFyE,EAAE,EAAE;cACFJ,KAAK,EAAE,MAAM;cACb4B,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,eAAe;cAC/BC,UAAU,EAAE;YACd,CAAE;YAAA5B,QAAA,gBAEFvD,OAAA,CAACf,UAAU;cAAC6E,OAAO,EAAC,OAAO;cAAAP,QAAA,GACxBqB,KAAK,GAAG,CAAC,EAAC,IAAE,EAAC5B,IAAI,CAACoC,aAAa;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACbvE,OAAA,CAACN,IAAI;cACH2F,KAAK,EAAEtC,aAAa,CAACC,IAAI,CAAE;cAC3BY,KAAK,EAAC,SAAS;cACfE,OAAO,EAAC,UAAU;cAClBL,EAAE,EAAE;gBAAE6B,EAAE,EAAE;cAAE;YAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC,GAlBqCK,KAAK;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmB5C,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACPvE,OAAA,CAACZ,MAAM;QACLgE,KAAK,EAAE;UAAEmC,aAAa,EAAE;QAAa,CAAE;QACvCzB,OAAO,EAAC,UAAU;QAClB0B,OAAO,EAAE7C,UAAW;QACpBc,EAAE,EAAE;UAAEgC,EAAE,EAAE;QAAE,CAAE;QAAAlC,QAAA,EACf;MAED;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETvE,OAAA,CAACX,MAAM;QAACmB,IAAI,EAAEA,IAAK;QAACkF,OAAO,EAAE5C,WAAY;QAAC6C,SAAS;QAACC,QAAQ,EAAC,IAAI;QAAArC,QAAA,gBAC/DvD,OAAA,CAACV,WAAW;UACVmE,EAAE,EAAE;YACFoC,CAAC,EAAE,CAAC;YACJnC,CAAC,EAAE,CAAC;YACJuB,OAAO,EAAE,MAAM;YACfC,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE;UACd,CAAE;UAAA5B,QAAA,gBAEFvD,OAAA,CAACf,UAAU;YAAC6E,OAAO,EAAC,IAAI;YAAAP,QAAA,EAAC;UAAgB;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACtDvE,OAAA,CAACF,UAAU;YACT,cAAW,OAAO;YAClB0F,OAAO,EAAE1C,WAAY;YACrBW,EAAE,EAAE;cACFG,KAAK,EAAGkC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,CAAC,GAAG;YAC1C,CAAE;YAAAzC,QAAA,eAEFvD,OAAA,CAACH,SAAS;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACdvE,OAAA,CAACT,aAAa;UAAC0G,QAAQ;UAAA1C,QAAA,eACrBvD,OAAA,CAAChB,GAAG;YACFkH,GAAG,EAAEpF,QAAS;YACdqF,QAAQ,EAAE,CAAE,CAAC;YAAA;YACb1C,EAAE,EAAE;cAAE2C,SAAS,EAAE,GAAG;cAAEC,SAAS,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAO,CAAE;YAAA/C,QAAA,gBAE3DvD,OAAA,CAACd,IAAI;cAAAqE,QAAA,EACF7C,IAAI,IACHA,IAAI,CAACiE,GAAG,CAAC,CAAC3B,IAAI,EAAE4B,KAAK,kBACnB5E,OAAA,CAACb,QAAQ;gBAAaoH,OAAO;gBAAAhD,QAAA,eAC3BvD,OAAA,CAAChB,GAAG;kBACFyE,EAAE,EAAE;oBACFJ,KAAK,EAAE,MAAM;oBACb4B,OAAO,EAAE,MAAM;oBACfC,cAAc,EAAE,eAAe;oBAC/BC,UAAU,EAAE;kBACd,CAAE;kBAAA5B,QAAA,gBAEFvD,OAAA,CAACf,UAAU;oBAAC6E,OAAO,EAAC,OAAO;oBAAAP,QAAA,GACxBqB,KAAK,GAAG,CAAC,EAAC,IAAE,EAAC5B,IAAI,CAACoC,aAAa;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC,eACbvE,OAAA,CAACN,IAAI;oBACH2F,KAAK,EAAEtC,aAAa,CAACC,IAAI,CAAE;oBAC3BY,KAAK,EAAC,SAAS;oBACfE,OAAO,EAAC,UAAU;oBAClBL,EAAE,EAAE;sBAAE6B,EAAE,EAAE;oBAAE;kBAAE;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC,GAlBOK,KAAK;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBV,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,EACNtD,OAAO,iBACNjB,OAAA,CAACR,gBAAgB;cACfgH,IAAI,EAAE,EAAG;cACT/C,EAAE,EAAE;gBAAEwB,OAAO,EAAE,OAAO;gBAAEwB,EAAE,EAAE,MAAM;gBAAEhB,EAAE,EAAE;cAAE;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjE,EAAA,CA/NIL,eAAgC;EAAA,QAMnBL,WAAW;AAAA;AAAA8G,EAAA,GANxBzG,eAAgC;AAiOtC,eAAeA,eAAe;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}