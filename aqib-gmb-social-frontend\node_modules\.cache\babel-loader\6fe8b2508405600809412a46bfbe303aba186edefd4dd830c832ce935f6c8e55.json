{"ast": null, "code": "import HttpHelperService from \"../httpHelper.service\";\nimport { LIST_OF_ROLE, UPDATE_ROLE } from \"../../constants/endPoints.constant\";\nclass RolesService {\n  constructor(dispatch) {\n    this._httpHelperService = void 0;\n    this.roleList = async userId => {\n      return await this._httpHelperService.get(`${LIST_OF_ROLE}/${userId}`);\n    };\n    this.updateRole = async (roleId, request) => {\n      return await this._httpHelperService.put(`${UPDATE_ROLE}/${roleId}`, request);\n    };\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n}\nexport default RolesService;", "map": {"version": 3, "names": ["HttpHelperService", "LIST_OF_ROLE", "UPDATE_ROLE", "RolesService", "constructor", "dispatch", "_httpHelperService", "roleList", "userId", "get", "updateRole", "roleId", "request", "put"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/roles/roles.service.tsx"], "sourcesContent": ["import { Component, Dispatch } from \"react\";\nimport HttpHelperService from \"../httpHelper.service\";\nimport {\n  LIST_OF_LOCATIONS,\n  LIST_OF_ROLE,\n  UPDATE_ROLE,\n} from \"../../constants/endPoints.constant\";\nimport { Action } from \"redux\";\n\nclass RolesService {\n  _httpHelperService;\n  constructor(dispatch: Dispatch<Action>) {\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  roleList = async (userId: number) => {\n    return await this._httpHelperService.get(`${LIST_OF_ROLE}/${userId}`);\n  };\n\n  updateRole = async (roleId: number | undefined, request: any) => {\n    return await this._httpHelperService.put(\n      `${UPDATE_ROLE}/${roleId}`,\n      request\n    );\n  };\n}\n\nexport default RolesService;\n"], "mappings": "AACA,OAAOA,iBAAiB,MAAM,uBAAuB;AACrD,SAEEC,YAAY,EACZC,WAAW,QACN,oCAAoC;AAG3C,MAAMC,YAAY,CAAC;EAEjBC,WAAWA,CAACC,QAA0B,EAAE;IAAA,KADxCC,kBAAkB;IAAA,KAKlBC,QAAQ,GAAG,MAAOC,MAAc,IAAK;MACnC,OAAO,MAAM,IAAI,CAACF,kBAAkB,CAACG,GAAG,CAAC,GAAGR,YAAY,IAAIO,MAAM,EAAE,CAAC;IACvE,CAAC;IAAA,KAEDE,UAAU,GAAG,OAAOC,MAA0B,EAAEC,OAAY,KAAK;MAC/D,OAAO,MAAM,IAAI,CAACN,kBAAkB,CAACO,GAAG,CACtC,GAAGX,WAAW,IAAIS,MAAM,EAAE,EAC1BC,OACF,CAAC;IACH,CAAC;IAZC,IAAI,CAACN,kBAAkB,GAAG,IAAIN,iBAAiB,CAACK,QAAQ,CAAC;EAC3D;AAYF;AAEA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}