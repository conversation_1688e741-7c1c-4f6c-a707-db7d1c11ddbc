const { OAuth2Client } = require("google-auth-library");
const logger = require("../utils/logger");
const { google } = require("googleapis");
const keys = require("../config/OAuthKey.json");
const gmbToken = require("../models/gmb.models");
const cryptoService = require("../services/crypto.service");
const gmbService = require("../services/gmb.service");
const GMB_ACTIONS = require("../constants/gmb-actions");
// const { getLocations } = require("./locations.controller");

const welcome = async (req, res) => {
  try {
    logger.logControllerAction("gmb", "welcome", req.requestId);

    const response = {
      message: `GMB Home Page`,
    };

    logger.info("GMB welcome endpoint accessed", {
      requestId: req.requestId,
    });

    res.send(response);
  } catch (error) {
    logger.error("Error in GMB welcome", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res
      .status(500)
      .json({ message: "Internal server error", error: error.message });
  }
};

const authenticate = async (req, res) => {
  try {
    const { businessEmail, businessId } = req.body;

    logger.logControllerAction("gmb", "authenticate", req.requestId, {
      businessEmail: businessEmail,
      businessId: businessId,
    });

    logger.info("GMB authentication started", {
      requestId: req.requestId,
      businessEmail: businessEmail,
      businessId: businessId,
    });

    const oAuth2Client = new OAuth2Client(
      keys.web.client_id,
      keys.web.client_secret,
      keys.web.redirect_uris[0]
    );

    const encText = await cryptoService.encrypt(
      `${businessEmail}||${businessId}`,
      "authentication"
    );

    const authorizeUrl = oAuth2Client.generateAuthUrl({
      access_type: "offline",
      prompt: "consent",
      scope: [
        "https://www.googleapis.com/auth/userinfo.profile",
        "https://www.googleapis.com/auth/plus.business.manage",
        "https://www.googleapis.com/auth/business.manage",
        "https://www.googleapis.com/auth/userinfo.email",
      ],
      state: encText,
    });

    logger.info("GMB authentication URL generated successfully", {
      requestId: req.requestId,
      businessEmail: businessEmail,
      hasAuthorizeUrl: !!authorizeUrl,
    });

    res.json({ message: "api is working", authorizeUrl });
    return authorizeUrl;
  } catch (error) {
    logger.error("Error in GMB authenticate", {
      requestId: req.requestId,
      businessEmail: req.body?.businessEmail,
      businessId: req.body?.businessId,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      message: "Authentication failed",
      error: error.message,
    });
  }
};

const gmbLocations = async (req, res) => {
  try {
    const { accessToken, account_id } = req.body;

    logger.logControllerAction("gmb", "gmbLocations", req.requestId, {
      account_id: account_id,
      hasAccessToken: !!accessToken,
    });

    logger.info("Fetching GMB locations", {
      requestId: req.requestId,
      account_id: account_id,
    });

    // Get locations data
    const locations_result = await gmbService.reqGMBApi({
      req: accessToken,
      action: GMB_ACTIONS.get_locations,
      reqBodyData: account_id,
    });

    logger.info("GMB locations fetched successfully", {
      requestId: req.requestId,
      account_id: account_id,
      locationCount: locations_result?.data?.locations?.length || 0,
    });

    return res.json({ message: "locations", locations: locations_result });
  } catch (error) {
    logger.error("Error fetching GMB locations", {
      requestId: req.requestId,
      account_id: req.body?.account_id,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      message: "Failed to fetch locations",
      error: error.message,
    });
  }
};

const gmbAccounts = async (req, requestedBusiness, tokens) => {
  try {
    // Get accounts data
    const accounts_result = await gmbService.reqGMBApi({
      req,
      action: GMB_ACTIONS.get_accounts,
      reqBodyData: null,
    });
    const rawData = await accounts_result.data;
    const accounts_data = [];
    for (let i = 0; i < rawData.accounts.length; i++) {
      const account_data = {
        account_id: rawData.accounts[i].name.split("/")[1],
        account_Name: rawData.accounts[i].accountName,
        account_Type: rawData.accounts[i].type,
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        userId: req.body.userId,
        businessId: requestedBusiness,
      };
      accounts_data.push(account_data);
    }
    await gmbToken.resetAccountStatus(requestedBusiness);
    await gmbToken.updateGoogleSyncComplete(requestedBusiness, req.body.userId);
    await gmbToken.resetOAuth(req.body.userId);
    if (accounts_data.length > 0) {
      for (const account of accounts_data) {
        await gmbToken.InsertOAuth(account);
        await gmbToken.InsertAccount(account);
      }
    }

    return { status: 200, data: true };
  } catch (error) {
    console.log(error);
    return { status: 409, data: false };
  }
};

const gmbCallback = async (req, res) => {
  try {
    logger.logControllerAction("gmb", "gmbCallback", req.requestId, {
      hasCode: !!req.body.code,
      hasState: !!req.body.state,
      url: req.url,
    });

    logger.info("GMB callback processing started", {
      requestId: req.requestId,
      url: req.url,
      hasCode: !!req.body.code,
    });

    const oAuth2Client = new OAuth2Client(
      keys.web.client_id,
      keys.web.client_secret,
      keys.web.redirect_uris[0]
    );

    if (req.url.indexOf("/callback") > -1) {
      logger.info("Processing GMB OAuth callback", {
        requestId: req.requestId,
      });

      const r = await oAuth2Client.getToken(req.body.code);
      oAuth2Client.setCredentials({
        access_token: r.tokens.access_token,
        refresh_token: r.tokens.refresh_token,
      });

      req["user"] = {};
      req["user"]["gmbToken"] = r.tokens.access_token;
      req["user"]["refreshToken"] = r.tokens.refresh_token;

      const peopleApi = google.people({ version: "v1", auth: oAuth2Client });
      const { data } = await peopleApi.people.get({
        resourceName: "people/me",
        personFields: "emailAddresses",
      });

      const email =
        data.emailAddresses && data.emailAddresses.length > 0
          ? data.emailAddresses[0].value
          : null;

      const decryptText = await cryptoService.decrypt(
        req.body.state,
        "authentication"
      );

      const requestedByEmail = decryptText.split("||")[0];
      const requestedBusiness = decryptText.split("||")[1];

      logger.info("GMB OAuth email verification", {
        requestId: req.requestId,
        requestedByEmail: requestedByEmail,
        authenticatedEmail: email,
        businessId: requestedBusiness,
      });

      if (
        requestedByEmail.trim().toLowerCase() !== email.trim().toLowerCase()
      ) {
        logger.warn("GMB OAuth email mismatch", {
          requestId: req.requestId,
          requestedByEmail: requestedByEmail,
          authenticatedEmail: email,
        });

        return res.status(400).json({ message: "Invalid request" });
      }

      // Fetch accounts
      logger.info("Fetching GMB accounts", {
        requestId: req.requestId,
        businessId: requestedBusiness,
        email: email,
      });

      const acounts_result = await gmbAccounts(
        req,
        requestedBusiness,
        r.tokens
      );

      logger.info("GMB callback completed successfully", {
        requestId: req.requestId,
        businessId: requestedBusiness,
        accountsStatus: acounts_result.status,
        accountsData: acounts_result.data,
      });

      res
        .status(acounts_result.status)
        .json({ message: "data", data: acounts_result.data });
    } else {
      logger.warn("Invalid GMB callback URL", {
        requestId: req.requestId,
        url: req.url,
      });

      res.status(400).send({
        message: "Authenticate Fn",
        data: req.body,
      });
    }
  } catch (error) {
    logger.error("Error in GMB callback", {
      requestId: req.requestId,
      url: req.url,
      hasCode: !!req.body?.code,
      hasState: !!req.body?.state,
      error: error.message,
      stack: error.stack,
    });

    res.status(400).json({
      message: "Failed Authentication",
      file: "gmb.controller",
      error: error.message,
    });
  }
};

module.exports = {
  welcome,
  authenticate,
  gmbCallback,
  gmbLocations,
};
