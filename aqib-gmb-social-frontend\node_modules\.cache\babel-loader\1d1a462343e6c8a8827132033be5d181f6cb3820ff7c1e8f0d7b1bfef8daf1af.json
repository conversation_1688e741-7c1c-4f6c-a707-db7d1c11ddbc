{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\colorPalette\\\\colorPalette.component.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography, Button } from \"@mui/material\";\nimport Grid2 from \"@mui/material/Grid2\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nvar deepEqual = require(\"deep-equal\");\nconst solidColors = [\"#B2C8C0\", \"#004E92\", \"#607D8B\", \"#1E88E5\", \"#F44336\", \"#E91E63\", \"#00BCD4\", \"#FB8C00\", \"#673AB7\", \"#37474F\", \"#00ACC1\", \"#FF5252\", \"#5D4037\", \"#FFD740\", \"#8E24AA\", \"#D50000\", \"#F3F781\", \"#009688\", \"#8E24AA\", \"#43A047\", \"#E1BEE7\", \"#F3E5F5\", \"#B2EBF2\"];\nconst gradientColors = [\"linear-gradient(to right, #12C2E9, #C471ED)\",\n// Aurora purple\n\"linear-gradient(to right, #F7971E, #FFD200)\",\n// Golden sunshine\n\"linear-gradient(to right, #8E2DE2, #4A00E0)\",\n// Rich purple depth\n\"linear-gradient(to right, #FFE000, #799F0C)\",\n// Lime splash\n\"linear-gradient(to right, #1FA2FF, #12D8FA)\",\n// Fresh blue blend\n\"linear-gradient(to right, #FF5F6D, #FFC371)\",\n// Tropical sunrise\n\"linear-gradient(to right, #8e2de2, #4a00e0)\", \"linear-gradient(to right, #ff512f, #dd2476)\", \"linear-gradient(to right, #00c9ff, #92fe9d)\", \"linear-gradient(to right, #f12711, #f5af19)\", \"linear-gradient(to right, #396afc, #2948ff)\", \"linear-gradient(to right, #00b09b, #96c93d)\", \"linear-gradient(to right, #7f00ff, #e100ff)\", \"linear-gradient(to right, #4e54c8, #8f94fb)\", \"linear-gradient(to right, #ff9a9e, #fad0c4)\", \"linear-gradient(to right, #d4fc79, #96e6a1)\", \"linear-gradient(to right, #e8cbc0, #636fa4)\", \"linear-gradient(to right, #00c6ff, #0072ff)\", \"linear-gradient(to right, #11998e, #38ef7d)\", \"linear-gradient(to right, #f7971e, #ffd200)\", \"linear-gradient(to right, #DA4453, #89216B)\",\n// Velvet red\n\"linear-gradient(to right, #6EE7B7, #3B82F6)\",\n// Aqua calm\n\"linear-gradient(to right, #FF4E50, #F9D423)\",\n// Vibrant sunrise\n\"linear-gradient(to right, #C33764, #1D2671)\" // Deep raspberry\n];\nconst gradientColorsDiagonal = [\"linear-gradient(45deg, #FF5733, #FFC300, #DAF7A6)\" /* Vibrant Sunset */, \"linear-gradient(45deg, #6A11CB, #2575FC, #A7F3D0)\" /* Purple-Blue-Green */, \"linear-gradient(45deg, #FF6A88, #FFC3A0, #FFF6A3)\" /* Soft Pink Glow */, \"linear-gradient(45deg, #4CA1AF, #C4E0E5, #F8F9FA)\" /* Aqua Mist */, \"linear-gradient(45deg, #FBD3E9, #BB377D, #9D50BB)\" /* Rosy Purple */, \"linear-gradient(45deg, #FF9A8B, #FF6A88, #FFEBB7)\" /* Peachy Gradient */, \"linear-gradient(45deg, #667EEA, #764BA2, #D4FC79)\" /* Cool Indigo */, \"linear-gradient(45deg, #36D1DC, #5B86E5, #92FE9D)\" /* Ocean Blues */, \"linear-gradient(45deg, #FF512F, #DD2476, #FCEE21)\" /* Fiery Coral */, \"linear-gradient(45deg, #FF9966, #FF5E62, #FDEB71)\" /* Vibrant Coral */, \"linear-gradient(45deg, #00C9FF, #92FE9D, #FAFFD1)\" /* Fresh Aqua */, \"linear-gradient(45deg, #FC466B, #3F5EFB, #FFC371)\" /* Electric Sunset */, \"linear-gradient(45deg, #12C2E9, #C471ED, #F64F59)\" /* Aurora Blend */, \"linear-gradient(45deg, #F7971E, #FFD200, #C3FFD8)\" /* Golden Sunshine */, \"linear-gradient(45deg, #8E2DE2, #4A00E0, #F8FFAE)\" /* Vibrant Violet */, \"linear-gradient(45deg, #FFE000, #799F0C, #56AB2F)\" /* Lime Freshness */, \"linear-gradient(45deg, #1FA2FF, #12D8FA, #A1FFCE)\" /* Sky Blue Blend */, \"linear-gradient(45deg, #FF5F6D, #FFC371, #FFF3B0)\" /* Tropical Sunrise */, \"linear-gradient(45deg, #DA4453, #89216B, #F9D423)\" /* Velvet Red */, \"linear-gradient(45deg, #6EE7B7, #3B82F6, #E0E7FF)\" /* Aqua Calmness */, \"linear-gradient(45deg, #FF4E50, #F9D423, #FFC3A0)\" /* Vibrant Sunrise */, \"linear-gradient(45deg, #C33764, #1D2671, #6DD5FA)\" /* Deep Raspberry */, \"linear-gradient(45deg, #FAD961, #F76B1C, #F8F9F9)\" /* Orange Blaze */, \"linear-gradient(45deg, #FC5C7D, #6A82FB, #FFE29F)\" /* Passion Blend */, \"linear-gradient(45deg, #ED4264, #FFEDBC, #FAF4D3)\" /* Cherry Burst */, \"linear-gradient(45deg, #0093E9, #80D0C7, #D9FAE3)\" /* Ocean Breeze */, \"linear-gradient(45deg, #FBAB7E, #F7CE68, #FCEBEB)\" /* Peach Gold */, \"linear-gradient(45deg, #2193B0, #6DD5ED, #D4FC79)\" /* Calm Ocean */, \"linear-gradient(45deg, #A770EF, #FDB99B, #FFF6E9)\" /* Dreamy Sunset */, \"linear-gradient(45deg, #74EBD5, #9FACE6, #EBF8FF)\" /* Soft Teal */];\nconst randomGradients = [\"linear-gradient(45deg, #9AE63D, #6A6E93, #AA65FD, #74192C)\", \"linear-gradient(45deg, #6B4BDB, #023CD8, #A125C4, #7F421B)\", \"linear-gradient(45deg, #4BDADF, #9D6E11, #E4BA64, #7A4105)\", \"linear-gradient(45deg, #FB2238, #47ED31, #520FCA, #4532CA)\", \"linear-gradient(45deg, #5AD402, #689A0F, #ACF41B, #4BDC56)\", \"linear-gradient(45deg, #F0F7EA, #F416DE, #D4100E, #F36D1C)\", \"linear-gradient(45deg, #E77EC9, #C8B55C, #FE469F, #96CE0D)\", \"linear-gradient(45deg, #8E25E7, #A108A2, #299124, #5DBA2F)\", \"linear-gradient(45deg, #10ADF3, #20F47E, #37ECF7, #EC695C)\", \"linear-gradient(45deg, #FA88FE, #DC828F, #BDEE81, #80C704)\", \"linear-gradient(45deg, #215D18, #CB144D, #5B08F2, #539EED)\", \"linear-gradient(45deg, #4DB4B6, #978214, #299088, #3EFC63)\", \"linear-gradient(45deg, #103E1F, #6AB5C4, #627CFE, #CCCF8A)\", \"linear-gradient(45deg, #9541AA, #A14897, #898A5A, #81DE74)\", \"linear-gradient(45deg, #1FE656, #2707C3, #099F57, #293074)\", \"linear-gradient(45deg, #57D2DA, #701F8D, #0D2B9F, #B91084)\", \"linear-gradient(45deg, #44905D, #0FEDF7, #CB9EB4, #1B1ED8)\", \"linear-gradient(45deg, #4EB34B, #FC1850, #404C83, #8A9D0E)\", \"linear-gradient(45deg, #35E9D6, #9D1D56, #8E278B, #42882E)\", \"linear-gradient(45deg, #CAA79E, #670175, #84F58A, #A300D6)\", \"linear-gradient(45deg, #52ADC6, #5A9377, #73EFD4, #6B0085)\", \"linear-gradient(45deg, #9F86FC, #8C79B7, #2E3D4C, #417AE9)\", \"linear-gradient(45deg, #DE2238, #651B0D, #EB4602, #66C77E)\", \"linear-gradient(45deg, #89250C, #BFC917, #938956, #DC99E9)\", \"linear-gradient(45deg, #E918F2, #5C9468, #D3B745, #C8AD9C)\"];\nconst ColorPalette = props => {\n  _s();\n  const [postTemplateConfig, setPostTemplateConfig] = useState(props.templateConfig);\n  useEffect(() => {\n    if (!deepEqual(postTemplateConfig, props.templateConfig)) {\n      props.callBack(postTemplateConfig);\n    }\n  }, [postTemplateConfig]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      padding: 2,\n      border: \"1px solid #ccc\",\n      borderRadius: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Edit Background\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"subtitle1\",\n      gutterBottom: true,\n      children: \"Solid Color\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid2, {\n      container: true,\n      spacing: 2,\n      children: solidColors.map((color, index) => /*#__PURE__*/_jsxDEV(Grid2, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          sx: {\n            width: \"40px\",\n            height: \"40px\",\n            backgroundColor: color,\n            borderRadius: \"4px\",\n            cursor: \"pointer\"\n          },\n          onClick: () => setPostTemplateConfig({\n            ...props.templateConfig,\n            backgroundColor: color\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"subtitle1\",\n      gutterBottom: true,\n      sx: {\n        marginTop: 3\n      },\n      children: \"Gradient Color\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid2, {\n      container: true,\n      spacing: 2,\n      children: gradientColors.map((gradient, index) => /*#__PURE__*/_jsxDEV(Grid2, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          sx: {\n            width: \"40px\",\n            height: \"40px\",\n            background: gradient,\n            borderRadius: \"4px\",\n            cursor: \"pointer\"\n          },\n          onClick: () => setPostTemplateConfig({\n            ...props.templateConfig,\n            backgroundColor: gradient\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"subtitle1\",\n      gutterBottom: true,\n      sx: {\n        marginTop: 3\n      },\n      children: \"Diagonal Tri Color\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid2, {\n      container: true,\n      spacing: 2,\n      children: gradientColorsDiagonal.map((gradient, index) => /*#__PURE__*/_jsxDEV(Grid2, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          sx: {\n            width: \"40px\",\n            height: \"40px\",\n            background: gradient,\n            borderRadius: \"4px\",\n            cursor: \"pointer\"\n          },\n          onClick: () => setPostTemplateConfig({\n            ...props.templateConfig,\n            backgroundColor: gradient\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"subtitle1\",\n      gutterBottom: true,\n      sx: {\n        marginTop: 3\n      },\n      children: \"Random Gradients\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid2, {\n      container: true,\n      spacing: 2,\n      children: randomGradients.map((gradient, index) => /*#__PURE__*/_jsxDEV(Grid2, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          sx: {\n            width: \"40px\",\n            height: \"40px\",\n            background: gradient,\n            borderRadius: \"4px\",\n            cursor: \"pointer\"\n          },\n          onClick: () => setPostTemplateConfig({\n            ...props.templateConfig,\n            backgroundColor: gradient\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 5\n  }, this);\n};\n_s(ColorPalette, \"YFygPVAiBkLjowYq/dBcw93Ov7w=\");\n_c = ColorPalette;\nexport default ColorPalette;\nvar _c;\n$RefreshReg$(_c, \"ColorPalette\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "<PERSON><PERSON>", "Grid2", "jsxDEV", "_jsxDEV", "deepEqual", "require", "solidColors", "gradientColors", "gradientColorsDiagonal", "randomGradients", "ColorPalette", "props", "_s", "postTemplateConfig", "setPostTemplateConfig", "templateConfig", "callBack", "sx", "padding", "border", "borderRadius", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "map", "color", "index", "width", "height", "backgroundColor", "cursor", "onClick", "marginTop", "gradient", "background", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/colorPalette/colorPalette.component.tsx"], "sourcesContent": ["import Avatar from \"@mui/material/Avatar\";\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography, Grid, Button } from \"@mui/material\";\nimport Grid2 from \"@mui/material/Grid2\";\nimport { IPostTemplateConfig } from \"../../types/IPostTemplateConfig\";\n\nvar deepEqual = require(\"deep-equal\");\n\nconst solidColors = [\n  \"#B2C8C0\",\n  \"#004E92\",\n  \"#607D8B\",\n  \"#1E88E5\",\n  \"#F44336\",\n  \"#E91E63\",\n  \"#00BCD4\",\n  \"#FB8C00\",\n  \"#673AB7\",\n  \"#37474F\",\n  \"#00ACC1\",\n  \"#FF5252\",\n  \"#5D4037\",\n  \"#FFD740\",\n  \"#8E24AA\",\n  \"#D50000\",\n  \"#F3F781\",\n  \"#009688\",\n  \"#8E24AA\",\n  \"#43A047\",\n  \"#E1BEE7\",\n  \"#F3E5F5\",\n  \"#B2EBF2\",\n];\n\nconst gradientColors = [\n  \"linear-gradient(to right, #12C2E9, #C471ED)\", // Aurora purple\n  \"linear-gradient(to right, #F7971E, #FFD200)\", // Golden sunshine\n  \"linear-gradient(to right, #8E2DE2, #4A00E0)\", // Rich purple depth\n  \"linear-gradient(to right, #FFE000, #799F0C)\", // Lime splash\n  \"linear-gradient(to right, #1FA2FF, #12D8FA)\", // Fresh blue blend\n  \"linear-gradient(to right, #FF5F6D, #FFC371)\", // Tropical sunrise\n  \"linear-gradient(to right, #8e2de2, #4a00e0)\",\n  \"linear-gradient(to right, #ff512f, #dd2476)\",\n  \"linear-gradient(to right, #00c9ff, #92fe9d)\",\n  \"linear-gradient(to right, #f12711, #f5af19)\",\n  \"linear-gradient(to right, #396afc, #2948ff)\",\n  \"linear-gradient(to right, #00b09b, #96c93d)\",\n  \"linear-gradient(to right, #7f00ff, #e100ff)\",\n  \"linear-gradient(to right, #4e54c8, #8f94fb)\",\n  \"linear-gradient(to right, #ff9a9e, #fad0c4)\",\n  \"linear-gradient(to right, #d4fc79, #96e6a1)\",\n  \"linear-gradient(to right, #e8cbc0, #636fa4)\",\n  \"linear-gradient(to right, #00c6ff, #0072ff)\",\n  \"linear-gradient(to right, #11998e, #38ef7d)\",\n  \"linear-gradient(to right, #f7971e, #ffd200)\",\n  \"linear-gradient(to right, #DA4453, #89216B)\", // Velvet red\n  \"linear-gradient(to right, #6EE7B7, #3B82F6)\", // Aqua calm\n  \"linear-gradient(to right, #FF4E50, #F9D423)\", // Vibrant sunrise\n  \"linear-gradient(to right, #C33764, #1D2671)\", // Deep raspberry\n];\n\nconst gradientColorsDiagonal = [\n  \"linear-gradient(45deg, #FF5733, #FFC300, #DAF7A6)\" /* Vibrant Sunset */,\n  \"linear-gradient(45deg, #6A11CB, #2575FC, #A7F3D0)\" /* Purple-Blue-Green */,\n  \"linear-gradient(45deg, #FF6A88, #FFC3A0, #FFF6A3)\" /* Soft Pink Glow */,\n  \"linear-gradient(45deg, #4CA1AF, #C4E0E5, #F8F9FA)\" /* Aqua Mist */,\n  \"linear-gradient(45deg, #FBD3E9, #BB377D, #9D50BB)\" /* Rosy Purple */,\n  \"linear-gradient(45deg, #FF9A8B, #FF6A88, #FFEBB7)\" /* Peachy Gradient */,\n  \"linear-gradient(45deg, #667EEA, #764BA2, #D4FC79)\" /* Cool Indigo */,\n  \"linear-gradient(45deg, #36D1DC, #5B86E5, #92FE9D)\" /* Ocean Blues */,\n  \"linear-gradient(45deg, #FF512F, #DD2476, #FCEE21)\" /* Fiery Coral */,\n  \"linear-gradient(45deg, #FF9966, #FF5E62, #FDEB71)\" /* Vibrant Coral */,\n  \"linear-gradient(45deg, #00C9FF, #92FE9D, #FAFFD1)\" /* Fresh Aqua */,\n  \"linear-gradient(45deg, #FC466B, #3F5EFB, #FFC371)\" /* Electric Sunset */,\n  \"linear-gradient(45deg, #12C2E9, #C471ED, #F64F59)\" /* Aurora Blend */,\n  \"linear-gradient(45deg, #F7971E, #FFD200, #C3FFD8)\" /* Golden Sunshine */,\n  \"linear-gradient(45deg, #8E2DE2, #4A00E0, #F8FFAE)\" /* Vibrant Violet */,\n  \"linear-gradient(45deg, #FFE000, #799F0C, #56AB2F)\" /* Lime Freshness */,\n  \"linear-gradient(45deg, #1FA2FF, #12D8FA, #A1FFCE)\" /* Sky Blue Blend */,\n  \"linear-gradient(45deg, #FF5F6D, #FFC371, #FFF3B0)\" /* Tropical Sunrise */,\n  \"linear-gradient(45deg, #DA4453, #89216B, #F9D423)\" /* Velvet Red */,\n  \"linear-gradient(45deg, #6EE7B7, #3B82F6, #E0E7FF)\" /* Aqua Calmness */,\n  \"linear-gradient(45deg, #FF4E50, #F9D423, #FFC3A0)\" /* Vibrant Sunrise */,\n  \"linear-gradient(45deg, #C33764, #1D2671, #6DD5FA)\" /* Deep Raspberry */,\n  \"linear-gradient(45deg, #FAD961, #F76B1C, #F8F9F9)\" /* Orange Blaze */,\n  \"linear-gradient(45deg, #FC5C7D, #6A82FB, #FFE29F)\" /* Passion Blend */,\n  \"linear-gradient(45deg, #ED4264, #FFEDBC, #FAF4D3)\" /* Cherry Burst */,\n  \"linear-gradient(45deg, #0093E9, #80D0C7, #D9FAE3)\" /* Ocean Breeze */,\n  \"linear-gradient(45deg, #FBAB7E, #F7CE68, #FCEBEB)\" /* Peach Gold */,\n  \"linear-gradient(45deg, #2193B0, #6DD5ED, #D4FC79)\" /* Calm Ocean */,\n  \"linear-gradient(45deg, #A770EF, #FDB99B, #FFF6E9)\" /* Dreamy Sunset */,\n  \"linear-gradient(45deg, #74EBD5, #9FACE6, #EBF8FF)\" /* Soft Teal */,\n];\n\nconst randomGradients = [\n  \"linear-gradient(45deg, #9AE63D, #6A6E93, #AA65FD, #74192C)\",\n  \"linear-gradient(45deg, #6B4BDB, #023CD8, #A125C4, #7F421B)\",\n  \"linear-gradient(45deg, #4BDADF, #9D6E11, #E4BA64, #7A4105)\",\n  \"linear-gradient(45deg, #FB2238, #47ED31, #520FCA, #4532CA)\",\n  \"linear-gradient(45deg, #5AD402, #689A0F, #ACF41B, #4BDC56)\",\n  \"linear-gradient(45deg, #F0F7EA, #F416DE, #D4100E, #F36D1C)\",\n  \"linear-gradient(45deg, #E77EC9, #C8B55C, #FE469F, #96CE0D)\",\n  \"linear-gradient(45deg, #8E25E7, #A108A2, #299124, #5DBA2F)\",\n  \"linear-gradient(45deg, #10ADF3, #20F47E, #37ECF7, #EC695C)\",\n  \"linear-gradient(45deg, #FA88FE, #DC828F, #BDEE81, #80C704)\",\n  \"linear-gradient(45deg, #215D18, #CB144D, #5B08F2, #539EED)\",\n  \"linear-gradient(45deg, #4DB4B6, #978214, #299088, #3EFC63)\",\n  \"linear-gradient(45deg, #103E1F, #6AB5C4, #627CFE, #CCCF8A)\",\n  \"linear-gradient(45deg, #9541AA, #A14897, #898A5A, #81DE74)\",\n  \"linear-gradient(45deg, #1FE656, #2707C3, #099F57, #293074)\",\n  \"linear-gradient(45deg, #57D2DA, #701F8D, #0D2B9F, #B91084)\",\n  \"linear-gradient(45deg, #44905D, #0FEDF7, #CB9EB4, #1B1ED8)\",\n  \"linear-gradient(45deg, #4EB34B, #FC1850, #404C83, #8A9D0E)\",\n  \"linear-gradient(45deg, #35E9D6, #9D1D56, #8E278B, #42882E)\",\n  \"linear-gradient(45deg, #CAA79E, #670175, #84F58A, #A300D6)\",\n  \"linear-gradient(45deg, #52ADC6, #5A9377, #73EFD4, #6B0085)\",\n  \"linear-gradient(45deg, #9F86FC, #8C79B7, #2E3D4C, #417AE9)\",\n  \"linear-gradient(45deg, #DE2238, #651B0D, #EB4602, #66C77E)\",\n  \"linear-gradient(45deg, #89250C, #BFC917, #938956, #DC99E9)\",\n  \"linear-gradient(45deg, #E918F2, #5C9468, #D3B745, #C8AD9C)\",\n];\n\nconst ColorPalette = (props: {\n  templateConfig: IPostTemplateConfig;\n  callBack: (\n    postTemplateConfig: IPostTemplateConfig\n  ) => undefined | void | null;\n}) => {\n  const [postTemplateConfig, setPostTemplateConfig] =\n    useState<IPostTemplateConfig>(props.templateConfig);\n\n  useEffect(() => {\n    if (!deepEqual(postTemplateConfig, props.templateConfig)) {\n      props.callBack(postTemplateConfig);\n    }\n  }, [postTemplateConfig]);\n\n  return (\n    <Box sx={{ padding: 2, border: \"1px solid #ccc\", borderRadius: 2 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        Edit Background\n      </Typography>\n\n      {/* Solid Colors Section */}\n      <Typography variant=\"subtitle1\" gutterBottom>\n        Solid Color\n      </Typography>\n      <Grid2 container spacing={2}>\n        {solidColors.map((color, index) => (\n          <Grid2 key={index}>\n            <Button\n              sx={{\n                width: \"40px\",\n                height: \"40px\",\n                backgroundColor: color,\n                borderRadius: \"4px\",\n                cursor: \"pointer\",\n              }}\n              onClick={() =>\n                setPostTemplateConfig({\n                  ...props.templateConfig,\n                  backgroundColor: color,\n                })\n              }\n            />\n          </Grid2>\n        ))}\n      </Grid2>\n\n      {/* Gradient Colors Section */}\n      <Typography variant=\"subtitle1\" gutterBottom sx={{ marginTop: 3 }}>\n        Gradient Color\n      </Typography>\n      <Grid2 container spacing={2}>\n        {gradientColors.map((gradient, index) => (\n          <Grid2 key={index}>\n            <Button\n              sx={{\n                width: \"40px\",\n                height: \"40px\",\n                background: gradient,\n                borderRadius: \"4px\",\n                cursor: \"pointer\",\n              }}\n              onClick={() =>\n                setPostTemplateConfig({\n                  ...props.templateConfig,\n                  backgroundColor: gradient,\n                })\n              }\n            />\n          </Grid2>\n        ))}\n      </Grid2>\n\n      <Typography variant=\"subtitle1\" gutterBottom sx={{ marginTop: 3 }}>\n        Diagonal Tri Color\n      </Typography>\n      <Grid2 container spacing={2}>\n        {gradientColorsDiagonal.map((gradient, index) => (\n          <Grid2 key={index}>\n            <Button\n              sx={{\n                width: \"40px\",\n                height: \"40px\",\n                background: gradient,\n                borderRadius: \"4px\",\n                cursor: \"pointer\",\n              }}\n              onClick={() =>\n                setPostTemplateConfig({\n                  ...props.templateConfig,\n                  backgroundColor: gradient,\n                })\n              }\n            />\n          </Grid2>\n        ))}\n      </Grid2>\n\n      <Typography variant=\"subtitle1\" gutterBottom sx={{ marginTop: 3 }}>\n        Random Gradients\n      </Typography>\n      <Grid2 container spacing={2}>\n        {randomGradients.map((gradient, index) => (\n          <Grid2 key={index}>\n            <Button\n              sx={{\n                width: \"40px\",\n                height: \"40px\",\n                background: gradient,\n                borderRadius: \"4px\",\n                cursor: \"pointer\",\n              }}\n              onClick={() =>\n                setPostTemplateConfig({\n                  ...props.templateConfig,\n                  backgroundColor: gradient,\n                })\n              }\n            />\n          </Grid2>\n        ))}\n      </Grid2>\n    </Box>\n  );\n};\n\nexport default ColorPalette;\n"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAQC,MAAM,QAAQ,eAAe;AAC7D,OAAOC,KAAK,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGxC,IAAIC,SAAS,GAAGC,OAAO,CAAC,YAAY,CAAC;AAErC,MAAMC,WAAW,GAAG,CAClB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;AAED,MAAMC,cAAc,GAAG,CACrB,6CAA6C;AAAE;AAC/C,6CAA6C;AAAE;AAC/C,6CAA6C;AAAE;AAC/C,6CAA6C;AAAE;AAC/C,6CAA6C;AAAE;AAC/C,6CAA6C;AAAE;AAC/C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C,EAC7C,6CAA6C;AAAE;AAC/C,6CAA6C;AAAE;AAC/C,6CAA6C;AAAE;AAC/C,6CAA6C,CAAE;AAAA,CAChD;AAED,MAAMC,sBAAsB,GAAG,CAC7B,mDAAmD,CAAC,sBACpD,mDAAmD,CAAC,yBACpD,mDAAmD,CAAC,sBACpD,mDAAmD,CAAC,iBACpD,mDAAmD,CAAC,mBACpD,mDAAmD,CAAC,uBACpD,mDAAmD,CAAC,mBACpD,mDAAmD,CAAC,mBACpD,mDAAmD,CAAC,mBACpD,mDAAmD,CAAC,qBACpD,mDAAmD,CAAC,kBACpD,mDAAmD,CAAC,uBACpD,mDAAmD,CAAC,oBACpD,mDAAmD,CAAC,uBACpD,mDAAmD,CAAC,sBACpD,mDAAmD,CAAC,sBACpD,mDAAmD,CAAC,sBACpD,mDAAmD,CAAC,wBACpD,mDAAmD,CAAC,kBACpD,mDAAmD,CAAC,qBACpD,mDAAmD,CAAC,uBACpD,mDAAmD,CAAC,sBACpD,mDAAmD,CAAC,oBACpD,mDAAmD,CAAC,qBACpD,mDAAmD,CAAC,oBACpD,mDAAmD,CAAC,oBACpD,mDAAmD,CAAC,kBACpD,mDAAmD,CAAC,kBACpD,mDAAmD,CAAC,qBACpD,mDAAmD,CAAC,gBACrD;AAED,MAAMC,eAAe,GAAG,CACtB,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,CAC7D;AAED,MAAMC,YAAY,GAAIC,KAKrB,IAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAC/CjB,QAAQ,CAAsBc,KAAK,CAACI,cAAc,CAAC;EAErDnB,SAAS,CAAC,MAAM;IACd,IAAI,CAACQ,SAAS,CAACS,kBAAkB,EAAEF,KAAK,CAACI,cAAc,CAAC,EAAE;MACxDJ,KAAK,CAACK,QAAQ,CAACH,kBAAkB,CAAC;IACpC;EACF,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EAExB,oBACEV,OAAA,CAACL,GAAG;IAACmB,EAAE,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,MAAM,EAAE,gBAAgB;MAAEC,YAAY,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACjElB,OAAA,CAACJ,UAAU;MAACuB,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbxB,OAAA,CAACJ,UAAU;MAACuB,OAAO,EAAC,WAAW;MAACC,YAAY;MAAAF,QAAA,EAAC;IAE7C;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbxB,OAAA,CAACF,KAAK;MAAC2B,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAR,QAAA,EACzBf,WAAW,CAACwB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC5B7B,OAAA,CAACF,KAAK;QAAAoB,QAAA,eACJlB,OAAA,CAACH,MAAM;UACLiB,EAAE,EAAE;YACFgB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,eAAe,EAAEJ,KAAK;YACtBX,YAAY,EAAE,KAAK;YACnBgB,MAAM,EAAE;UACV,CAAE;UACFC,OAAO,EAAEA,CAAA,KACPvB,qBAAqB,CAAC;YACpB,GAAGH,KAAK,CAACI,cAAc;YACvBoB,eAAe,EAAEJ;UACnB,CAAC;QACF;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC,GAfQK,KAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBV,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGRxB,OAAA,CAACJ,UAAU;MAACuB,OAAO,EAAC,WAAW;MAACC,YAAY;MAACN,EAAE,EAAE;QAAEqB,SAAS,EAAE;MAAE,CAAE;MAAAjB,QAAA,EAAC;IAEnE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbxB,OAAA,CAACF,KAAK;MAAC2B,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAR,QAAA,EACzBd,cAAc,CAACuB,GAAG,CAAC,CAACS,QAAQ,EAAEP,KAAK,kBAClC7B,OAAA,CAACF,KAAK;QAAAoB,QAAA,eACJlB,OAAA,CAACH,MAAM;UACLiB,EAAE,EAAE;YACFgB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdM,UAAU,EAAED,QAAQ;YACpBnB,YAAY,EAAE,KAAK;YACnBgB,MAAM,EAAE;UACV,CAAE;UACFC,OAAO,EAAEA,CAAA,KACPvB,qBAAqB,CAAC;YACpB,GAAGH,KAAK,CAACI,cAAc;YACvBoB,eAAe,EAAEI;UACnB,CAAC;QACF;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC,GAfQK,KAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBV,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAERxB,OAAA,CAACJ,UAAU;MAACuB,OAAO,EAAC,WAAW;MAACC,YAAY;MAACN,EAAE,EAAE;QAAEqB,SAAS,EAAE;MAAE,CAAE;MAAAjB,QAAA,EAAC;IAEnE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbxB,OAAA,CAACF,KAAK;MAAC2B,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAR,QAAA,EACzBb,sBAAsB,CAACsB,GAAG,CAAC,CAACS,QAAQ,EAAEP,KAAK,kBAC1C7B,OAAA,CAACF,KAAK;QAAAoB,QAAA,eACJlB,OAAA,CAACH,MAAM;UACLiB,EAAE,EAAE;YACFgB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdM,UAAU,EAAED,QAAQ;YACpBnB,YAAY,EAAE,KAAK;YACnBgB,MAAM,EAAE;UACV,CAAE;UACFC,OAAO,EAAEA,CAAA,KACPvB,qBAAqB,CAAC;YACpB,GAAGH,KAAK,CAACI,cAAc;YACvBoB,eAAe,EAAEI;UACnB,CAAC;QACF;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC,GAfQK,KAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBV,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAERxB,OAAA,CAACJ,UAAU;MAACuB,OAAO,EAAC,WAAW;MAACC,YAAY;MAACN,EAAE,EAAE;QAAEqB,SAAS,EAAE;MAAE,CAAE;MAAAjB,QAAA,EAAC;IAEnE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbxB,OAAA,CAACF,KAAK;MAAC2B,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAR,QAAA,EACzBZ,eAAe,CAACqB,GAAG,CAAC,CAACS,QAAQ,EAAEP,KAAK,kBACnC7B,OAAA,CAACF,KAAK;QAAAoB,QAAA,eACJlB,OAAA,CAACH,MAAM;UACLiB,EAAE,EAAE;YACFgB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdM,UAAU,EAAED,QAAQ;YACpBnB,YAAY,EAAE,KAAK;YACnBgB,MAAM,EAAE;UACV,CAAE;UACFC,OAAO,EAAEA,CAAA,KACPvB,qBAAqB,CAAC;YACpB,GAAGH,KAAK,CAACI,cAAc;YACvBoB,eAAe,EAAEI;UACnB,CAAC;QACF;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC,GAfQK,KAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBV,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACf,EAAA,CA5HIF,YAAY;AAAA+B,EAAA,GAAZ/B,YAAY;AA8HlB,eAAeA,YAAY;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}