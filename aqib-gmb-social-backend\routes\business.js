const express = require("express");
const router = express.Router();
const validateSchema = require("../middleware/validateRequest");
const { isAuthenticated } = require("../middleware/isAuthenticated");

const {
  welcome,
  addBusiness,
  businessList,
  deleteBusiness,
  updateBusiness,
  enableDisableBusiness,
  businessListPaginated,
} = require("../controllers/business.controller");

/**
 * @swagger
 * components:
 *   schemas:
 *     Business:
 *       type: object
 *       required:
 *         - userId
 *         - businessName
 *         - businessEmail
 *         - statusId
 *         - createdBy
 *         - updatedBy
 *       properties:
 *         id:
 *           type: integer
 *           description: The auto-generated id of the business
 *         userId:
 *           type: integer
 *           description: User ID associated with the business
 *         businessName:
 *           type: string
 *           description: Name of the business
 *         businessEmail:
 *           type: string
 *           format: email
 *           description: Email address of the business
 *         businessUrl:
 *           type: string
 *           description: URL of the business website
 *         statusId:
 *           type: integer
 *           description: Status of the business
 *         createdBy:
 *           type: integer
 *           description: User ID who created the business
 *         updatedBy:
 *           type: integer
 *           description: User ID who last updated the business
 *         isActive:
 *           type: integer
 *           description: Whether the business is active (1) or not (0)
 */

/**
 * @swagger
 * /v1/business:
 *   get:
 *     summary: Business welcome page
 *     description: Returns a welcome message for the Business API
 *     tags: [Business]
 *     responses:
 *       200:
 *         description: Welcome message
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Business Home Page
 */
router.get("/", welcome);

/**
 * @swagger
 * /v1/business/business-list/{userId}:
 *   get:
 *     summary: Get list of businesses
 *     description: Retrieves a list of all businesses for a specific user ID
 *     tags: [Business]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the user requesting the list
 *     responses:
 *       201:
 *         description: List of businesses
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Business List!
 *                 list:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Business'
 *       404:
 *         description: Businesses not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/business-list/:userId", isAuthenticated, businessList);

/**
 * @swagger
 * /v1/business/business-list-paginated/{userId}:
 *   get:
 *     summary: Get paginated list of businesses
 *     description: Retrieves a paginated list of businesses for a specific user ID
 *     tags: [Business]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the user requesting the list
 *       - in: query
 *         name: pageNo
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       201:
 *         description: Paginated list of businesses
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Business List!
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Business'
 *                 totalRecords:
 *                   type: integer
 *       404:
 *         description: Businesses not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/business-list-paginated/:userId",
  isAuthenticated,
  businessListPaginated
);

/**
 * @swagger
 * /v1/business/add-business:
 *   post:
 *     summary: Add a new business
 *     description: Creates a new business with the provided information
 *     tags: [Business]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - businessName
 *               - businessEmail
 *               - statusId
 *               - createdBy
 *               - updatedBy
 *             properties:
 *               userId:
 *                 type: integer
 *               businessName:
 *                 type: string
 *                 minLength: 3
 *               businessEmail:
 *                 type: string
 *                 format: email
 *               businessUrl:
 *                 type: string
 *               statusId:
 *                 type: integer
 *               createdBy:
 *                 type: integer
 *               updatedBy:
 *                 type: integer
 *     responses:
 *       201:
 *         description: Business created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Business Created!
 *                 response:
 *                   type: object
 *       404:
 *         description: Business not created
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/add-business",
  isAuthenticated,
  validateSchema("business", "addBusiness"),
  addBusiness
);

/**
 * @swagger
 * /v1/business/edit-business/{id}:
 *   put:
 *     summary: Update business information
 *     description: Updates a business's information
 *     tags: [Business]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the business to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: integer
 *               businessName:
 *                 type: string
 *               businessEmail:
 *                 type: string
 *                 format: email
 *               businessUrl:
 *                 type: string
 *               statusId:
 *                 type: integer
 *               createdBy:
 *                 type: integer
 *               updatedBy:
 *                 type: integer
 *     responses:
 *       201:
 *         description: Business updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Business Updated!
 *                 list:
 *                   type: object
 *       404:
 *         description: Update failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put("/edit-business/:id", isAuthenticated, updateBusiness);

/**
 * @swagger
 * /v1/business/delete-business/{id}:
 *   delete:
 *     summary: Delete business
 *     description: Deletes a business
 *     tags: [Business]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the business to delete
 *     responses:
 *       201:
 *         description: Business deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Business Deleted!
 *                 list:
 *                   type: object
 *       404:
 *         description: Deletion failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.delete("/delete-business/:id", isAuthenticated, deleteBusiness);

/**
 * @swagger
 * /v1/business/enable-disable/{id}:
 *   post:
 *     summary: Enable or disable business
 *     description: Enables or disables a business
 *     tags: [Business]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the business to enable/disable
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - isActive
 *             properties:
 *               isActive:
 *                 type: integer
 *                 description: 1 for active, 0 for inactive
 *     responses:
 *       201:
 *         description: Business status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Business Updated!
 *                 list:
 *                   type: object
 *       404:
 *         description: Update failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post("/enable-disable/:id", isAuthenticated, enableDisableBusiness);

module.exports = router;
