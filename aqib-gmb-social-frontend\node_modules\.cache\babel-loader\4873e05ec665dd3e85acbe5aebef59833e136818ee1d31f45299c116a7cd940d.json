{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\ratingsStar\\\\ratingsStar.component.tsx\",\n  _s = $RefreshSig$();\nimport StarRoundedIcon from \"@mui/icons-material/StarRounded\";\nimport { STARRATINGMAP } from \"../../constants/dbConstant.constant\";\nimport Box from \"@mui/material/Box\";\nimport { useEffect, useState } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RatingsStar = props => {\n  _s();\n  const {\n    size\n  } = props;\n  const [iconSize, setIconSize] = useState(30);\n  useEffect(() => {\n    if (size && size > 0) {\n      setIconSize(size);\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: Array.from({\n      length: 5\n    }).map((_, index) => {\n      {\n        return index < STARRATINGMAP[props.starRating] ? /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(StarRoundedIcon, {\n            sx: {\n              fontSize: iconSize,\n              color: \"#FFD700\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(StarRoundedIcon, {\n            sx: {\n              fontSize: iconSize,\n              color: \"#a5a5a5\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 13\n        }, this);\n      }\n    })\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n};\n_s(RatingsStar, \"dF3oW3rgdddd5vRCS8/amQ8jp84=\");\n_c = RatingsStar;\nexport default RatingsStar;\nvar _c;\n$RefreshReg$(_c, \"RatingsStar\");", "map": {"version": 3, "names": ["StarRoundedIcon", "STARRATINGMAP", "Box", "useEffect", "useState", "jsxDEV", "_jsxDEV", "RatingsStar", "props", "_s", "size", "iconSize", "setIconSize", "children", "Array", "from", "length", "map", "_", "index", "starRating", "sx", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/ratingsStar/ratingsStar.component.tsx"], "sourcesContent": ["import Avatar from \"@mui/material/Avatar\";\nimport StarRoundedIcon from \"@mui/icons-material/StarRounded\";\nimport { STARRATINGMAP } from \"../../constants/dbConstant.constant\";\nimport Box from \"@mui/material/Box\";\nimport { useEffect, useState } from \"react\";\n\nconst RatingsStar = (props: { starRating: string; size?: number }) => {\n  const { size } = props;\n  const [iconSize, setIconSize] = useState<number>(30);\n\n  useEffect(() => {\n    if (size && size > 0) {\n      setIconSize(size);\n    }\n  }, []);\n\n  return (\n    <Box>\n      {Array.from({\n        length: 5,\n      }).map((_, index) => {\n        {\n          return index <\n            STARRATINGMAP[props.starRating as keyof typeof STARRATINGMAP] ? (\n            <span key={index}>\n              <StarRoundedIcon\n                sx={{\n                  fontSize: iconSize,\n                  color: \"#FFD700\",\n                }}\n              />\n            </span>\n          ) : (\n            <span key={index}>\n              <StarRoundedIcon sx={{ fontSize: iconSize, color: \"#a5a5a5\" }} />\n            </span>\n          );\n        }\n      })}\n    </Box>\n  );\n};\n\nexport default RatingsStar;\n"], "mappings": ";;AACA,OAAOA,eAAe,MAAM,iCAAiC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,OAAOC,GAAG,MAAM,mBAAmB;AACnC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,WAAW,GAAIC,KAA4C,IAAK;EAAAC,EAAA;EACpE,MAAM;IAAEC;EAAK,CAAC,GAAGF,KAAK;EACtB,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAS,EAAE,CAAC;EAEpDD,SAAS,CAAC,MAAM;IACd,IAAIO,IAAI,IAAIA,IAAI,GAAG,CAAC,EAAE;MACpBE,WAAW,CAACF,IAAI,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEJ,OAAA,CAACJ,GAAG;IAAAW,QAAA,EACDC,KAAK,CAACC,IAAI,CAAC;MACVC,MAAM,EAAE;IACV,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;MACnB;QACE,OAAOA,KAAK,GACVlB,aAAa,CAACO,KAAK,CAACY,UAAU,CAA+B,gBAC7Dd,OAAA;UAAAO,QAAA,eACEP,OAAA,CAACN,eAAe;YACdqB,EAAE,EAAE;cACFC,QAAQ,EAAEX,QAAQ;cAClBY,KAAK,EAAE;YACT;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GANOR,KAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOV,CAAC,gBAEPrB,OAAA;UAAAO,QAAA,eACEP,OAAA,CAACN,eAAe;YAACqB,EAAE,EAAE;cAAEC,QAAQ,EAAEX,QAAQ;cAAEY,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC,GADxDR,KAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACP;MACH;IACF,CAAC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAClB,EAAA,CAnCIF,WAAW;AAAAqB,EAAA,GAAXrB,WAAW;AAqCjB,eAAeA,WAAW;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}