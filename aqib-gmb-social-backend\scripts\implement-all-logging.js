const fs = require('fs');
const path = require('path');

/**
 * Final script to implement logging in all remaining controller methods
 * This script provides specific implementations for each controller
 */

const controllersDir = path.join(__dirname, '../controllers');

/**
 * Add logging to user controller methods
 */
function addUserControllerLogging() {
  const filePath = path.join(controllersDir, 'user.controller.js');
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Add logger import if not present
  if (!content.includes('require("../utils/logger")')) {
    content = content.replace(
      'const User = require("../models/user.models");',
      'const User = require("../models/user.models");\nconst logger = require("../utils/logger");'
    );
  }
  
  // Add basic logging to welcome function
  content = content.replace(
    /const welcome = async \(req, res\) => {\s*res\.send\({[\s\S]*?}\);[\s\S]*?};/,
    `const welcome = async (req, res) => {
  try {
    logger.logControllerAction('user', 'welcome', req.requestId);
    logger.info('User welcome endpoint accessed', { requestId: req.requestId });
    res.send({ message: "User Home Page" });
  } catch (error) {
    logger.error('Error in user welcome', { requestId: req.requestId, error: error.message, stack: error.stack });
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};`
  );
  
  fs.writeFileSync(filePath, content);
  console.log('✓ Added logging to user.controller.js');
}

/**
 * Add logging to posts controller methods
 */
function addPostsControllerLogging() {
  const filePath = path.join(controllersDir, 'posts.controller.js');
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Add logger import if not present
  if (!content.includes('require("../utils/logger")')) {
    const lines = content.split('\n');
    let insertIndex = 0;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('require(') && !lines[i].includes('//')) {
        insertIndex = i + 1;
      }
    }
    lines.splice(insertIndex, 0, 'const logger = require("../utils/logger");');
    content = lines.join('\n');
  }
  
  // Add basic logging to welcome function
  content = content.replace(
    /const welcome = async \(req, res\) => {\s*res\.send\({[\s\S]*?}\);[\s\S]*?};/,
    `const welcome = async (req, res) => {
  try {
    logger.logControllerAction('posts', 'welcome', req.requestId);
    logger.info('Posts welcome endpoint accessed', { requestId: req.requestId });
    res.send({ message: "Posts Home Page" });
  } catch (error) {
    logger.error('Error in posts welcome', { requestId: req.requestId, error: error.message, stack: error.stack });
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};`
  );
  
  fs.writeFileSync(filePath, content);
  console.log('✓ Added logging to posts.controller.js');
}

/**
 * Add logging to reviews controller methods
 */
function addReviewsControllerLogging() {
  const filePath = path.join(controllersDir, 'reviews.controller.js');
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Add logger import if not present
  if (!content.includes('require("../utils/logger")')) {
    const lines = content.split('\n');
    let insertIndex = 0;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('require(') && !lines[i].includes('//')) {
        insertIndex = i + 1;
      }
    }
    lines.splice(insertIndex, 0, 'const logger = require("../utils/logger");');
    content = lines.join('\n');
  }
  
  // Add basic logging to welcome function
  content = content.replace(
    /const welcome = async \(req, res\) => {\s*res\.send\({[\s\S]*?}\);[\s\S]*?};/,
    `const welcome = async (req, res) => {
  try {
    logger.logControllerAction('reviews', 'welcome', req.requestId);
    logger.info('Reviews welcome endpoint accessed', { requestId: req.requestId });
    res.send({ message: "Reviews Home Page" });
  } catch (error) {
    logger.error('Error in reviews welcome', { requestId: req.requestId, error: error.message, stack: error.stack });
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};`
  );
  
  fs.writeFileSync(filePath, content);
  console.log('✓ Added logging to reviews.controller.js');
}

/**
 * Add logging to QandA controller methods
 */
function addQandAControllerLogging() {
  const filePath = path.join(controllersDir, 'QandA.controllers.js');
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Add logger import if not present
  if (!content.includes('require("../utils/logger")')) {
    const lines = content.split('\n');
    let insertIndex = 0;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('require(') && !lines[i].includes('//')) {
        insertIndex = i + 1;
      }
    }
    lines.splice(insertIndex, 0, 'const logger = require("../utils/logger");');
    content = lines.join('\n');
  }
  
  // Add basic logging to welcome function
  content = content.replace(
    /const welcome = async \(req, res\) => {\s*res\.send\({[\s\S]*?}\);[\s\S]*?};/,
    `const welcome = async (req, res) => {
  try {
    logger.logControllerAction('QandA', 'welcome', req.requestId);
    logger.info('QandA welcome endpoint accessed', { requestId: req.requestId });
    res.send({ message: "QandA Home Page" });
  } catch (error) {
    logger.error('Error in QandA welcome', { requestId: req.requestId, error: error.message, stack: error.stack });
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};`
  );
  
  fs.writeFileSync(filePath, content);
  console.log('✓ Added logging to QandA.controllers.js');
}

/**
 * Add logging to remaining controllers
 */
function addRemainingControllerLogging() {
  const controllers = [
    'role.controller.js',
    'accounts.controller.js',
    'locationMetrics.controller.js'
  ];
  
  controllers.forEach(fileName => {
    const filePath = path.join(controllersDir, fileName);
    if (fs.existsSync(filePath)) {
      let content = fs.readFileSync(filePath, 'utf8');
      
      // Add logger import if not present
      if (!content.includes('require("../utils/logger")')) {
        const lines = content.split('\n');
        let insertIndex = 0;
        for (let i = 0; i < lines.length; i++) {
          if (lines[i].includes('require(') && !lines[i].includes('//')) {
            insertIndex = i + 1;
          }
        }
        lines.splice(insertIndex, 0, 'const logger = require("../utils/logger");');
        content = lines.join('\n');
        fs.writeFileSync(filePath, content);
        console.log(`✓ Added logger import to ${fileName}`);
      }
    }
  });
}

/**
 * Main function to implement all logging
 */
function implementAllLogging() {
  console.log('Implementing comprehensive logging in all controllers...\n');
  
  try {
    addUserControllerLogging();
    addPostsControllerLogging();
    addReviewsControllerLogging();
    addQandAControllerLogging();
    addRemainingControllerLogging();
    
    console.log('\n' + '='.repeat(60));
    console.log('ALL CONTROLLER LOGGING IMPLEMENTED!');
    console.log('='.repeat(60));
    console.log('\nCompleted controllers:');
    console.log('✓ auth.controller.js (manually implemented)');
    console.log('✓ business.controller.js (manually implemented)');
    console.log('✓ locations.controller.js (manually implemented)');
    console.log('✓ gmb.controller.js (manually implemented)');
    console.log('✓ user.controller.js (basic logging added)');
    console.log('✓ posts.controller.js (basic logging added)');
    console.log('✓ reviews.controller.js (basic logging added)');
    console.log('✓ QandA.controllers.js (basic logging added)');
    console.log('✓ role.controller.js (logger import added)');
    console.log('✓ accounts.controller.js (logger import added)');
    console.log('✓ locationMetrics.controller.js (logger import added)');
    
    console.log('\nNext steps:');
    console.log('1. Review each controller file');
    console.log('2. Add specific logging to remaining methods using the templates');
    console.log('3. Test the application to ensure all logging works');
    console.log('4. Monitor log files for proper output');
    
  } catch (error) {
    console.error('Error implementing logging:', error.message);
  }
}

// Run the script
if (require.main === module) {
  implementAllLogging();
}

module.exports = {
  implementAllLogging
};
