const logger = require("../utils/logger");
const ManageAssets = require("../models/manageAssets.models");
const s3Service = require("../services/s3.service");
const videoThumbnailService = require("../services/videoThumbnail.service");
const { v4: uuidv4 } = require("uuid");
const path = require("path");
const pool = require("../config/db");

/**
 * Welcome endpoint for manage assets
 */
const welcome = async (req, res) => {
  try {
    logger.logControllerAction("ManageAssets", "welcome", req.requestId);
    logger.info("ManageAssets welcome endpoint accessed", {
      requestId: req.requestId,
    });
    res.send({ message: "Manage Assets API" });
  } catch (error) {
    logger.error("Error in ManageAssets welcome", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res
      .status(500)
      .json({ message: "Internal server error", error: error.message });
  }
};

/**
 * Upload files to S3 and save to database
 */
const uploadAssets = async (req, res) => {
  try {
    logger.logControllerAction("ManageAssets", "uploadAssets", req.requestId);

    const { businessId } = req.params;
    const userId = req.user.userId;
    const files = req.files;

    if (!files || files.length === 0) {
      return res.status(400).json({
        success: false,
        message: "No files provided",
      });
    }

    if (!businessId) {
      return res.status(400).json({
        success: false,
        message: "Business ID is required",
      });
    }

    // Get business info and max upload size
    const maxUploadSizeMB = await ManageAssets.getMaxUploadSize(businessId);
    const maxUploadSizeBytes = maxUploadSizeMB * 1024 * 1024;

    // Get current total size
    const currentTotalSize = await ManageAssets.getTotalSizeByBusinessId(
      businessId
    );

    // Calculate total size of new files
    const newFilesTotalSize = files.reduce(
      (total, file) => total + file.size,
      0
    );

    // Check if upload would exceed limit
    if (currentTotalSize + newFilesTotalSize > maxUploadSizeBytes) {
      const remainingSpace = maxUploadSizeBytes - currentTotalSize;
      return res.status(400).json({
        success: false,
        message: `Upload would exceed storage limit. Available space: ${(
          remainingSpace /
          (1024 * 1024)
        ).toFixed(2)} MB`,
        currentSize: (currentTotalSize / (1024 * 1024)).toFixed(2),
        maxSize: maxUploadSizeMB,
        requestedSize: (newFilesTotalSize / (1024 * 1024)).toFixed(2),
      });
    }

    // Validate file types (images and videos supported by Google Business)
    const allowedMimeTypes = [
      // Images
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
      // Videos
      "video/mp4",
      "video/avi",
      "video/mov",
      "video/wmv",
      "video/flv",
      "video/webm",
    ];

    const uploadedAssets = [];
    const errors = [];

    // Get business name for S3 key generation
    const businessResult = await pool.query(
      "SELECT businessName FROM gmb_businesses_master WHERE id = ?",
      [businessId]
    );

    if (businessResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Business not found",
      });
    }

    const businessName = businessResult[0].businessName;

    for (const file of files) {
      try {
        // Validate file type
        if (!allowedMimeTypes.includes(file.mimetype)) {
          errors.push({
            fileName: file.originalname,
            error: `Unsupported file type: ${file.mimetype}`,
          });
          continue;
        }

        // Generate unique file name
        const fileExtension = path.extname(file.originalname);
        const uniqueFileName = `${uuidv4()}${fileExtension}`;

        // Generate S3 key
        const s3Key = s3Service.generateS3Key(
          businessName,
          businessId,
          uniqueFileName
        );

        // Upload to S3
        const uploadResult = await s3Service.uploadFileToS3(
          file.buffer,
          s3Key,
          file.mimetype
        );

        if (!uploadResult.success) {
          errors.push({
            fileName: file.originalname,
            error: uploadResult.error,
          });
          continue;
        }

        // Determine file type
        const fileType = file.mimetype.startsWith("image/") ? "image" : "video";

        // Generate thumbnail
        let thumbnailResult = null;
        try {
          if (fileType === "video") {
            logger.info("Generating video thumbnail", {
              fileName: file.originalname,
            });
            thumbnailResult =
              await videoThumbnailService.generateVideoThumbnail(
                file.buffer,
                businessName,
                businessId,
                file.originalname
              );
          } else if (fileType === "image") {
            logger.info("Generating image thumbnail", {
              fileName: file.originalname,
            });
            thumbnailResult =
              await videoThumbnailService.generateImageThumbnail(
                file.buffer,
                businessName,
                businessId,
                file.originalname,
                file.mimetype
              );
          }
        } catch (thumbnailError) {
          logger.warn("Failed to generate thumbnail", {
            fileName: file.originalname,
            error: thumbnailError.message,
          });
          // Continue without thumbnail - not a critical failure
        }

        // Save to database
        const assetData = new ManageAssets(
          businessId,
          userId,
          uniqueFileName,
          file.originalname,
          fileType,
          file.size,
          s3Key,
          uploadResult.data.s3Url,
          file.mimetype,
          thumbnailResult && thumbnailResult.success
            ? thumbnailResult.thumbnail
            : null
        );

        const dbResult = await ManageAssets.Insert(assetData);

        if (dbResult.success) {
          uploadedAssets.push({
            id: dbResult.assetId,
            fileName: uniqueFileName,
            originalFileName: file.originalname,
            fileType,
            fileSize: file.size,
            s3Url: uploadResult.data.s3Url,
            mimeType: file.mimetype,
          });
        } else {
          // If DB save fails, clean up S3 file
          await s3Service.deleteFileFromS3(s3Key);
          errors.push({
            fileName: file.originalname,
            error: "Failed to save file record to database",
          });
        }
      } catch (error) {
        logger.error("Error processing file upload", {
          fileName: file.originalname,
          error: error.message,
          stack: error.stack,
        });
        errors.push({
          fileName: file.originalname,
          error: error.message,
        });
      }
    }

    logger.info("Assets upload completed", {
      businessId,
      userId,
      uploadedCount: uploadedAssets.length,
      errorCount: errors.length,
    });

    res.status(200).json({
      success: true,
      message: `${uploadedAssets.length} files uploaded successfully`,
      uploadedAssets,
      errors: errors.length > 0 ? errors : undefined,
    });
  } catch (error) {
    logger.error("Error in ManageAssets uploadAssets", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Get assets for a business with pagination
 */
const getAssets = async (req, res) => {
  try {
    logger.logControllerAction("ManageAssets", "getAssets", req.requestId);

    const { businessId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    if (!businessId) {
      return res.status(400).json({
        success: false,
        message: "Business ID is required",
      });
    }

    const result = await ManageAssets.getAssetsByBusinessId(
      businessId,
      parseInt(page),
      parseInt(limit)
    );

    if (result.success) {
      // Get current total size and max size
      const totalSize = await ManageAssets.getTotalSizeByBusinessId(businessId);
      const maxSize = await ManageAssets.getMaxUploadSize(businessId);

      // Generate signed URLs for assets
      const assetsWithSignedUrls = result.assets.map((asset) => ({
        ...asset,
        s3_url: s3Service.getFileUrl(asset.s3_key, 3600), // 1 hour expiration
        thumbnail_s3_url: asset.thumbnail_s3_key
          ? s3Service.getFileUrl(asset.thumbnail_s3_key, 3600)
          : null, // Generate signed URL for thumbnail if exists
      }));

      res.status(200).json({
        success: true,
        data: assetsWithSignedUrls,
        pagination: result.pagination,
        storageInfo: {
          totalSizeMB: (totalSize / (1024 * 1024)).toFixed(2),
          maxSizeMB: maxSize,
          usagePercentage: (
            (totalSize / (maxSize * 1024 * 1024)) *
            100
          ).toFixed(2),
        },
      });
    } else {
      res.status(404).json({
        success: false,
        message: "No assets found",
      });
    }
  } catch (error) {
    logger.error("Error in ManageAssets getAssets", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Get single asset by ID
 */
const getAssetById = async (req, res) => {
  try {
    logger.logControllerAction("ManageAssets", "getAssetById", req.requestId);

    const { assetId } = req.params;

    if (!assetId) {
      return res.status(400).json({
        success: false,
        message: "Asset ID is required",
      });
    }

    const result = await ManageAssets.getAssetById(assetId);

    if (result.success) {
      // Generate signed URL for the asset
      const assetWithSignedUrl = {
        ...result.asset,
        s3_url: s3Service.getFileUrl(result.asset.s3_key, 3600), // 1 hour expiration
        thumbnail_s3_url: result.asset.thumbnail_s3_key
          ? s3Service.getFileUrl(result.asset.thumbnail_s3_key, 3600)
          : null, // Generate signed URL for thumbnail if exists
      };

      res.status(200).json({
        success: true,
        data: assetWithSignedUrl,
      });
    } else {
      res.status(404).json({
        success: false,
        message: result.message,
      });
    }
  } catch (error) {
    logger.error("Error in ManageAssets getAssetById", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Delete asset
 */
const deleteAsset = async (req, res) => {
  try {
    logger.logControllerAction("ManageAssets", "deleteAsset", req.requestId);

    const { assetId } = req.params;
    const userId = req.user.userId;

    if (!assetId) {
      return res.status(400).json({
        success: false,
        message: "Asset ID is required",
      });
    }

    // Get asset details first
    const assetResult = await ManageAssets.getAssetById(assetId);

    if (!assetResult.success) {
      return res.status(404).json({
        success: false,
        message: "Asset not found",
      });
    }

    const asset = assetResult.asset;

    // Delete from database (soft delete)
    const deleteResult = await ManageAssets.deleteAsset(assetId, userId);

    if (deleteResult.success) {
      // Delete main file from S3
      const s3DeleteResult = await s3Service.deleteFileFromS3(asset.s3_key);

      if (!s3DeleteResult.success) {
        logger.warn(
          "Failed to delete file from S3, but database record was deleted",
          {
            assetId,
            s3Key: asset.s3_key,
            error: s3DeleteResult.error,
          }
        );
      }

      // Delete thumbnail from S3 if it exists
      if (asset.thumbnail_s3_key) {
        const thumbnailDeleteResult = await s3Service.deleteFileFromS3(
          asset.thumbnail_s3_key
        );

        if (!thumbnailDeleteResult.success) {
          logger.warn(
            "Failed to delete thumbnail from S3, but database record was deleted",
            {
              assetId,
              thumbnailS3Key: asset.thumbnail_s3_key,
              error: thumbnailDeleteResult.error,
            }
          );
        }
      }

      res.status(200).json({
        success: true,
        message: "Asset deleted successfully",
      });
    } else {
      res.status(400).json({
        success: false,
        message: deleteResult.message,
      });
    }
  } catch (error) {
    logger.error("Error in ManageAssets deleteAsset", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Update max upload size for a business
 */
const updateMaxUploadSize = async (req, res) => {
  try {
    logger.logControllerAction(
      "ManageAssets",
      "updateMaxUploadSize",
      req.requestId
    );

    const { businessId } = req.params;
    const { maxSizeMB } = req.body;

    if (!businessId || !maxSizeMB) {
      return res.status(400).json({
        success: false,
        message: "Business ID and max size are required",
      });
    }

    if (maxSizeMB < 1 || maxSizeMB > 10240) {
      // 1MB to 10GB limit
      return res.status(400).json({
        success: false,
        message: "Max size must be between 1 MB and 10240 MB (10 GB)",
      });
    }

    const result = await ManageAssets.updateMaxUploadSize(
      businessId,
      maxSizeMB
    );

    if (result.success) {
      res.status(200).json({
        success: true,
        message: result.message,
      });
    } else {
      res.status(404).json({
        success: false,
        message: result.message,
      });
    }
  } catch (error) {
    logger.error("Error in ManageAssets updateMaxUploadSize", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

module.exports = {
  welcome,
  uploadAssets,
  getAssets,
  getAssetById,
  deleteAsset,
  updateMaxUploadSize,
};
