-- Migration: Fix Manage Assets table location
-- Date: 2025-01-02
-- Description: Move max_upload_size_mb column from user_business to gmb_businesses_master table

-- Remove max_upload_size_mb column from user_business table if it exists
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'user_business' 
     AND COLUMN_NAME = 'max_upload_size_mb') > 0,
    'ALTER TABLE user_business DROP COLUMN max_upload_size_mb',
    'SELECT "Column max_upload_size_mb does not exist in user_business" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add max_upload_size_mb column to gmb_businesses_master table if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'gmb_businesses_master' 
     AND COLUMN_NAME = 'max_upload_size_mb') = 0,
    'ALTER TABLE gmb_businesses_master ADD COLUMN max_upload_size_mb INT DEFAULT 1024 COMMENT "Maximum upload size in MB for business assets (default 1GB)"',
    'SELECT "Column max_upload_size_mb already exists in gmb_businesses_master" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing gmb_businesses_master records to have default max_upload_size_mb if NULL
UPDATE gmb_businesses_master 
SET max_upload_size_mb = 1024 
WHERE max_upload_size_mb IS NULL;
