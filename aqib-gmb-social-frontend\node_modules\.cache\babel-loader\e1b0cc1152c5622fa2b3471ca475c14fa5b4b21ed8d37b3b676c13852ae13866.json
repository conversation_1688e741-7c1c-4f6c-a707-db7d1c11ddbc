{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\charts\\\\pie.charts.tsx\";\nimport React from \"react\";\nimport { Chart, registerables } from \"chart.js\";\nimport { Pie } from \"react-chartjs-2\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChart.register(...registerables);\nexport const PieChart = props => {\n  const data = {\n    labels: [\"Scheduled\", \"Posted\", \"Failed\"],\n    datasets: [{\n      label: \"# of Posts\",\n      data: [200, 120, 80],\n      backgroundColor: [\"rgba(255, 206, 86, 0.2)\", \"rgba(75, 192, 192, 0.2)\", \"rgba(255, 99, 132, 0.2)\"],\n      borderColor: [\"rgba(255, 206, 86, 1)\", \"rgba(75, 192, 192, 1)\", \"rgba(255, 99, 132, 1)\"],\n      borderWidth: 1\n    }]\n  };\n  const options = {\n    maintainAspectRatio: false,\n    // Allows custom height and width\n    responsive: true\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chart-container\",\n    children: /*#__PURE__*/_jsxDEV(Pie, {\n      height: 400,\n      data: data,\n      options: options\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_c = PieChart;\nvar _c;\n$RefreshReg$(_c, \"PieChart\");", "map": {"version": 3, "names": ["React", "Chart", "registerables", "Pie", "jsxDEV", "_jsxDEV", "register", "<PERSON><PERSON><PERSON>", "props", "data", "labels", "datasets", "label", "backgroundColor", "borderColor", "borderWidth", "options", "maintainAspectRatio", "responsive", "className", "children", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/charts/pie.charts.tsx"], "sourcesContent": ["import React from \"react\";\nimport { Bar, PolarArea } from \"react-chartjs-2\";\nimport { Chart, registerables } from \"chart.js\";\nimport { Pie } from \"react-chartjs-2\";\nChart.register(...registerables);\n\nexport const PieChart = (props: { chartData: any }) => {\n  const data = {\n    labels: [\"Scheduled\", \"Posted\", \"Failed\"],\n    datasets: [\n      {\n        label: \"# of Posts\",\n        data: [200, 120, 80],\n        backgroundColor: [\n          \"rgba(255, 206, 86, 0.2)\",\n          \"rgba(75, 192, 192, 0.2)\",\n          \"rgba(255, 99, 132, 0.2)\",\n        ],\n        borderColor: [\n          \"rgba(255, 206, 86, 1)\",\n          \"rgba(75, 192, 192, 1)\",\n          \"rgba(255, 99, 132, 1)\",\n        ],\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const options = {\n    maintainAspectRatio: false, // Allows custom height and width\n    responsive: true,\n  };\n\n  return (\n    <div className=\"chart-container\">\n      <Pie height={400} data={data} options={options} />\n    </div>\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAC/C,SAASC,GAAG,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACtCJ,KAAK,CAACK,QAAQ,CAAC,GAAGJ,aAAa,CAAC;AAEhC,OAAO,MAAMK,QAAQ,GAAIC,KAAyB,IAAK;EACrD,MAAMC,IAAI,GAAG;IACXC,MAAM,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACzCC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,YAAY;MACnBH,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;MACpBI,eAAe,EAAE,CACf,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,CAC1B;MACDC,WAAW,EAAE,CACX,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,CACxB;MACDC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,MAAMC,OAAO,GAAG;IACdC,mBAAmB,EAAE,KAAK;IAAE;IAC5BC,UAAU,EAAE;EACd,CAAC;EAED,oBACEb,OAAA;IAAKc,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9Bf,OAAA,CAACF,GAAG;MAACkB,MAAM,EAAE,GAAI;MAACZ,IAAI,EAAEA,IAAK;MAACO,OAAO,EAAEA;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/C,CAAC;AAEV,CAAC;AAACC,EAAA,GAhCWnB,QAAQ;AAAA,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}