{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\screens\\\\qanda\\\\qanda.screen.tsx\",\n  _s2 = $RefreshSig$();\nimport { useContext, useEffect, useState } from \"react\";\n//Widgets\nimport Box from \"@mui/material/Box\";\nimport Button from \"@mui/material/Button\";\nimport Typography from \"@mui/material/Typography\";\nimport Select from \"@mui/material/Select\";\nimport MenuItem from \"@mui/material/MenuItem\";\nimport InputLabel from \"@mui/material/InputLabel\";\nimport FormControl from \"@mui/material/FormControl\";\nimport Grid from \"@mui/material/Grid\";\nimport Card from \"@mui/material/Card\";\nimport { CardContent, Grid2, List, ListItem, FormHelperText } from \"@mui/material\";\nimport LeftMenuComponent from \"../../components/leftMenu/leftMenu.component\";\nimport \"../qanda/qanda.screen.style.css\";\nimport { Formik } from \"formik\";\nimport * as yup from \"yup\";\nimport { DEFAULT_PAGINATION } from \"../../constants/dbConstant.constant\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { LoadingContext } from \"../../context/loading.context\";\nimport BusinessService from \"../../services/business/business.service\";\nimport LocationService from \"../../services/location/location.service\";\nimport ApplicationHelperService from \"../../services/ApplicationHelperService\";\nimport GenericDrawer from \"../../components/genericDrawer/genericDrawer.component\";\nimport QandAService from \"../../services/qanda/qanda.service\";\nimport SendOutlinedIcon from \"@mui/icons-material/SendOutlined\";\nimport ReplyQuestionAnswerComponent from \"../../components/replyQuestionAnswer/replyQuestionAnswer.component\";\nimport { getIn } from \"formik\";\nimport { ToastSeverity } from \"../../constants/toastSeverity.constant\";\nimport { MessageConstants } from \"../../constants/message.constant\";\nimport { ToastContext } from \"../../context/toast.context\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QandA = ({\n  title\n}) => {\n  _s2();\n  var _s = $RefreshSig$();\n  const dispatch = useDispatch();\n  const [openqanda, setOpenqanda] = useState(false);\n  const {\n    userInfo,\n    rbAccess\n  } = useSelector(state => state.authReducer);\n  const _businessService = new BusinessService(dispatch);\n  const _locationService = new LocationService(dispatch);\n  const _qandAService = new QandAService(dispatch);\n  const _applicationHelperService = new ApplicationHelperService({});\n  const {\n    setLoading\n  } = useContext(LoadingContext);\n  const {\n    setToastConfig,\n    setOpen\n  } = useContext(ToastContext);\n  const [businessGroups, setBusinessGroups] = useState([]);\n  const [businessGroupsOnBusiness, setBusinessGroupsOnBusiness] = useState([]);\n  const [qandaList, setQandAList] = useState([]);\n\n  // const [locationListOnBusiness, setLocationListOnBusiness] = useState<\n  //   ILocation[]\n  // >([]);\n  const [paginationModel, setPaginationModel] = useState(DEFAULT_PAGINATION);\n  const [locationList, setLocationList] = useState([]);\n  const [businessList, setBusinessList] = useState([]);\n  const INITIAL_VALUES = {\n    businessId: 0,\n    businessGroupId: \"0\",\n    locationId: \"0\",\n    orderBy: \"CREATETIME ASC\",\n    rating: \"ALL\",\n    tags: \"0\",\n    searchText: \"\"\n  };\n  const [initialValues, setInitialValues] = useState(INITIAL_VALUES);\n  const getBusiness = async () => {\n    try {\n      setLoading(true);\n      let roles = await _businessService.getBusiness(userInfo.id);\n      if (roles.list.length > 0) {\n        setBusinessList(roles.list);\n      }\n    } catch (error) {}\n    setLoading(false);\n  };\n  const getBusinessGroups = async () => {\n    try {\n      setLoading(true);\n      let businessGroups = await _businessService.getBusinessGroups(userInfo.id);\n      if (businessGroups.data.length > 0) {\n        setBusinessGroups(businessGroups.data);\n      }\n    } catch (error) {}\n    setLoading(false);\n  };\n  const LocationSchema = yup.object().shape({\n    businessId: yup.number().typeError(\"Business ID must be a number\").moreThan(0, \"Business must be selected\") // Ensures value is greater than 0\n    .required(\"Business is required\"),\n    businessGroupId: yup.string().nonNullable().required(\"Account is required\").test(\"len\", `Account is required`, val => val != \"0\"),\n    locationId: yup.string().nonNullable().required(\"Location is required\").test(\"len\", `Location is required`, val => val != \"0\")\n  });\n  useEffect(() => {\n    getBusiness();\n    getBusinessGroups();\n    fetchLocationsPaginated();\n  }, []);\n  const fetchLocationsPaginated = async () => {\n    try {\n      setLoading(true);\n      const locationListResponse = await _businessService.getLocations(userInfo.id);\n      console.log(\"Location List: \", locationListResponse.list);\n      setLocationList(locationListResponse.list);\n    } catch (error) {}\n    setLoading(false);\n  };\n  const fetchQuestionsAnswersListPaginated = async locationId => {\n    try {\n      setLoading(true);\n      const reviewsResponse = await _qandAService.getQA(userInfo.id, locationId);\n      console.log(\"Question and Answers Response: \", reviewsResponse);\n      setQandAList(reviewsResponse.list);\n    } catch (error) {}\n    setLoading(false);\n  };\n  const fetchQuestionsAnswers = async values => {\n    try {\n      const isValid = await LocationSchema.isValid(values);\n      if (isValid) {\n        fetchQuestionsAnswersListPaginated(values.locationId);\n      }\n    } catch (error) {}\n  };\n  const syncQandA = async values => {\n    try {\n      setLoading(true);\n      const syncReviewsRequest = {\n        \"x-gmb-account-id\": values.businessGroupId,\n        \"x-gmb-business-id\": values.businessId,\n        \"x-gmb-location-id\": values.locationId\n      };\n      const reviewsResponse = await _qandAService.refreshQA(syncReviewsRequest);\n      if (reviewsResponse.success) {\n        setToastConfig(ToastSeverity.Success, reviewsResponse.message, true);\n        fetchQuestionsAnswers(values);\n      } else {\n        setToastConfig(ToastSeverity.Error, reviewsResponse.message, true);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response$data$, _error$response$data$2;\n      setLoading(false);\n      if (error !== null && error !== void 0 && (_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && (_error$response$data$ = _error$response$data.data) !== null && _error$response$data$ !== void 0 && (_error$response$data$2 = _error$response$data$.error) !== null && _error$response$data$2 !== void 0 && _error$response$data$2.details) {\n        var _error$response2, _error$response2$data, _error$response2$data2, _error$response2$data3;\n        setToastConfig(ToastSeverity.Error, error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : (_error$response2$data2 = _error$response2$data.data) === null || _error$response2$data2 === void 0 ? void 0 : (_error$response2$data3 = _error$response2$data2.error) === null || _error$response2$data3 === void 0 ? void 0 : _error$response2$data3.details[0].reason, true);\n      } else {\n        setToastConfig(ToastSeverity.Error, MessageConstants.ApiErrorStandardMessage, true);\n      }\n    }\n  };\n  const QuestionAnswersComponent = props => {\n    _s();\n    const [openReplyMessage, setOpenReplyMessage] = useState(false);\n    return /*#__PURE__*/_jsxDEV(Grid2, {\n      size: 6,\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        variant: \"outlined\",\n        className: \"commonCard reviewCard height100\",\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          className: \"pad0\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"m-0\",\n              children: props.qandanswer.question\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(List, {\n                className: \"\",\n                children: /*#__PURE__*/_jsxDEV(ListItem, {\n                  className: \"pad0\",\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    className: \"textTruncate\",\n                    children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                      children: \"Answer: \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 23\n                    }, this), props.qandanswer.answer, \".\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), Boolean(rbAccess && rbAccess.QandAReply) && /*#__PURE__*/_jsxDEV(Box, {\n                className: \"qandaReply\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  className: \"tagBtn\",\n                  onClick: () => setOpenReplyMessage(true),\n                  variant: \"contained\",\n                  startIcon: /*#__PURE__*/_jsxDEV(SendOutlinedIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 34\n                  }, this),\n                  children: \"Reply\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(GenericDrawer, {\n        component: /*#__PURE__*/_jsxDEV(ReplyQuestionAnswerComponent, {\n          questionAnswer: props.qandanswer,\n          closeDrawer: () => setOpenReplyMessage(!openReplyMessage)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this),\n        isShow: openReplyMessage,\n        callback: () => setOpenReplyMessage(!openReplyMessage)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)]\n    }, props.index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this);\n  };\n  _s(QuestionAnswersComponent, \"Iws6SOQeVgNiH5n6k4JbyZUKpM4=\");\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(LeftMenuComponent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"bodyPart\",\n            children: [/*#__PURE__*/_jsxDEV(Formik, {\n              enableReinitialize: true,\n              initialValues: {\n                ...initialValues\n              },\n              validationSchema: LocationSchema,\n              onSubmit: (values, {\n                setSubmitting\n              }) => {\n                fetchQuestionsAnswers(values);\n              },\n              children: ({\n                values,\n                errors,\n                touched,\n                handleChange,\n                handleBlur,\n                handleSubmit,\n                setFieldValue,\n                handleReset,\n                isSubmitting,\n                isValid\n                /* and other goodies */\n              }) => /*#__PURE__*/_jsxDEV(\"form\", {\n                onSubmit: handleSubmit,\n                onReset: handleReset,\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  className: \"commonTableHeader\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"commonTitle pageTitle\",\n                    children: \"List of queries for Business\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 23\n                  }, this), Boolean(rbAccess && rbAccess.ReviewsRefresh) && /*#__PURE__*/_jsxDEV(Button, {\n                    onClick: () => syncQandA(values),\n                    className: values.locationId === \"0\" ? \"tableActionBtnDisabled\" : \"tableActionBtn\",\n                    sx: {\n                      minHeight: \"50px\",\n                      // Set the desired height\n                      width: \"100px\"\n                    },\n                    startIcon: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      viewBox: \"0 0 48 48\",\n                      width: \"24px\",\n                      height: \"24px\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        fill: \"#fbc02d\",\n                        d: \"M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12 s5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24s8.955,20,20,20 s20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 320,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        fill: \"#e53935\",\n                        d: \"M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039 l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 324,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        fill: \"#4caf50\",\n                        d: \"M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36 c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 328,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        fill: \"#1565c0\",\n                        d: \"M43.611,20.083L43.595,20L42,20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571 c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 332,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 29\n                    }, this),\n                    disabled: values.locationId === \"0\",\n                    children: \"Sync\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    lg: 3,\n                    children: /*#__PURE__*/_jsxDEV(FormControl, {\n                      variant: \"filled\",\n                      fullWidth: true,\n                      error: Boolean(getIn(errors, \"businessId\") && getIn(touched, \"businessId\")),\n                      children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                        id: \"outlined-country-dropdown-label\",\n                        children: \"Business\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 354,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Select, {\n                        fullWidth: true,\n                        id: \"businessId\",\n                        label: \"Business\",\n                        value: values.businessId.toString(),\n                        onChange: evt => {\n                          setBusinessGroupsOnBusiness(businessGroups.filter(x => x.businessId === +evt.target.value));\n                          setFieldValue(\"businessId\", +evt.target.value);\n                          setFieldValue(\"businessGroupId\", \"0\");\n                          setFieldValue(\"locationId\", \"0\");\n                          setPaginationModel({\n                            ...DEFAULT_PAGINATION\n                          });\n                        },\n                        onBlur: handleBlur,\n                        sx: {\n                          backgroundColor: \"var(--whiteColor)\",\n                          borderRadius: \"5px\"\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                          value: 0,\n                          children: \"Select\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 381,\n                          columnNumber: 29\n                        }, this), businessList && businessList.map(business => /*#__PURE__*/_jsxDEV(MenuItem, {\n                          value: business.id.toString(),\n                          children: business.businessName\n                        }, business.id, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 384,\n                          columnNumber: 33\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 358,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                        children: touched.businessId && errors.businessId ? errors.businessId : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 392,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 346,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    lg: 3,\n                    children: /*#__PURE__*/_jsxDEV(FormControl, {\n                      variant: \"filled\",\n                      fullWidth: true,\n                      error: Boolean(getIn(errors, \"businessGroupId\") && getIn(touched, \"businessGroupId\")),\n                      children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                        id: \"outlined-country-dropdown-label\",\n                        children: \"Group\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 408,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Select, {\n                        fullWidth: true,\n                        id: \"businessGroupId\",\n                        label: \"Group\",\n                        value: values.businessGroupId || \"\" // Ensure default is empty string\n                        ,\n                        onChange: evt => {\n                          const selectedValue = evt.target.value;\n                          setFieldValue(\"businessGroupId\", selectedValue); // Ensure consistent type\n                          setFieldValue(\"locationId\", \"0\");\n                          setPaginationModel({\n                            ...DEFAULT_PAGINATION\n                          });\n                        },\n                        onBlur: handleBlur,\n                        sx: {\n                          backgroundColor: \"var(--whiteColor)\",\n                          borderRadius: \"5px\"\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                          value: \"0\",\n                          children: \"Select\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 429,\n                          columnNumber: 29\n                        }, this), businessGroups.filter(x => x.businessId === values.businessId).map(businessGroup => /*#__PURE__*/_jsxDEV(MenuItem, {\n                          value: businessGroup.accountId,\n                          children: businessGroup.accountName\n                        }, businessGroup.accountId, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 436,\n                          columnNumber: 33\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 412,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                        children: touched.businessGroupId && errors.businessGroupId ? errors.businessGroupId : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 444,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 400,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    lg: 3,\n                    children: /*#__PURE__*/_jsxDEV(FormControl, {\n                      variant: \"filled\",\n                      fullWidth: true,\n                      error: Boolean(getIn(errors, \"locationId\") && getIn(touched, \"locationId\")),\n                      children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                        id: \"outlined-country-dropdown-label\",\n                        children: \"Location\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 460,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Select, {\n                        fullWidth: true,\n                        id: \"locationId\",\n                        label: \"Location\",\n                        value: values.locationId.toString(),\n                        onChange: evt => {\n                          setFieldValue(\"locationId\", evt.target.value);\n                          setPaginationModel({\n                            ...DEFAULT_PAGINATION\n                          });\n                        },\n                        onBlur: handleBlur,\n                        sx: {\n                          backgroundColor: \"var(--whiteColor)\",\n                          borderRadius: \"5px\"\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                          value: \"0\",\n                          children: \"Select\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 479,\n                          columnNumber: 29\n                        }, this), locationList.filter(x => x.gmbAccountId === values.businessGroupId).map(location => /*#__PURE__*/_jsxDEV(MenuItem, {\n                          value: location.gmbLocationId,\n                          children: location.gmbLocationName\n                        }, location.gmbLocationId, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 486,\n                          columnNumber: 33\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 464,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                        children: touched.locationId && errors.locationId ? errors.locationId : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 494,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    lg: 3,\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      className: \"commonShapeBtn\",\n                      type: \"submit\",\n                      variant: \"contained\",\n                      fullWidth: true,\n                      children: \"Apply\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 502,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                children: /*#__PURE__*/_jsxDEV(Grid2, {\n                  container: true,\n                  spacing: 2,\n                  children: qandaList.length > 0 && qandaList.map((question, index) => /*#__PURE__*/_jsxDEV(QuestionAnswersComponent, {\n                    qandanswer: question,\n                    index: index\n                  }, \"q-and-a\" + index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 270,\n    columnNumber: 5\n  }, this);\n};\n_s2(QandA, \"X+DcoQkxm2+otjOn9LJtH0vC1OI=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = QandA;\nexport default QandA;\nvar _c;\n$RefreshReg$(_c, \"QandA\");", "map": {"version": 3, "names": ["useContext", "useEffect", "useState", "Box", "<PERSON><PERSON>", "Typography", "Select", "MenuItem", "InputLabel", "FormControl", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid2", "List", "ListItem", "FormHelperText", "LeftMenuComponent", "<PERSON><PERSON>", "yup", "DEFAULT_PAGINATION", "useDispatch", "useSelector", "LoadingContext", "BusinessService", "LocationService", "ApplicationHelperService", "GenericDrawer", "QandAService", "SendOutlinedIcon", "ReplyQuestionAnswerComponent", "getIn", "ToastSeverity", "MessageConstants", "ToastContext", "jsxDEV", "_jsxDEV", "QandA", "title", "_s2", "_s", "$RefreshSig$", "dispatch", "openqanda", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "userInfo", "rbAccess", "state", "authReducer", "_businessService", "_locationService", "_qandAService", "_applicationHelperService", "setLoading", "setToastConfig", "<PERSON><PERSON><PERSON>", "businessGroups", "setBusinessGroups", "businessGroupsOnBusiness", "setBusinessGroupsOnBusiness", "qandaList", "setQandAList", "paginationModel", "setPaginationModel", "locationList", "setLocationList", "businessList", "setBusinessList", "INITIAL_VALUES", "businessId", "businessGroupId", "locationId", "orderBy", "rating", "tags", "searchText", "initialValues", "setInitialValues", "getBusiness", "roles", "id", "list", "length", "error", "getBusinessGroups", "data", "LocationSchema", "object", "shape", "number", "typeError", "moreThan", "required", "string", "nonNullable", "test", "val", "fetchLocationsPaginated", "locationListResponse", "getLocations", "console", "log", "fetchQuestionsAnswersListPaginated", "reviewsResponse", "getQA", "fetchQuestionsAnswers", "values", "<PERSON><PERSON><PERSON><PERSON>", "syncQandA", "syncReviewsRequest", "refreshQA", "success", "Success", "message", "Error", "_error$response", "_error$response$data", "_error$response$data$", "_error$response$data$2", "response", "details", "_error$response2", "_error$response2$data", "_error$response2$data2", "_error$response2$data3", "reason", "ApiErrorStandardMessage", "QuestionAnswersComponent", "props", "openReplyMessage", "setOpenReplyMessage", "size", "children", "variant", "className", "<PERSON><PERSON><PERSON><PERSON>", "question", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "answer", "Boolean", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onClick", "startIcon", "component", "questionAnswer", "closeDrawer", "isShow", "callback", "index", "enableReinitialize", "validationSchema", "onSubmit", "setSubmitting", "errors", "touched", "handleChange", "handleBlur", "handleSubmit", "setFieldValue", "handleReset", "isSubmitting", "onReset", "ReviewsRefresh", "sx", "minHeight", "width", "xmlns", "viewBox", "height", "fill", "d", "disabled", "container", "spacing", "item", "xs", "md", "lg", "fullWidth", "label", "value", "toString", "onChange", "evt", "filter", "x", "target", "onBlur", "backgroundColor", "borderRadius", "map", "business", "businessName", "selected<PERSON><PERSON><PERSON>", "businessGroup", "accountId", "accountName", "gmbAccountId", "location", "gmbLocationId", "gmbLocationName", "type", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/screens/qanda/qanda.screen.tsx"], "sourcesContent": ["import { FunctionComponent, useContext, useEffect, useState } from \"react\";\nimport PageProps from \"../../models/PageProps.interface\";\n\n//Widgets\nimport Box from \"@mui/material/Box\";\nimport Button from \"@mui/material/Button\";\nimport Typography from \"@mui/material/Typography\";\nimport Select from \"@mui/material/Select\";\nimport MenuItem from \"@mui/material/MenuItem\";\nimport InputLabel from \"@mui/material/InputLabel\";\n\nimport FormControl from \"@mui/material/FormControl\";\nimport Grid from \"@mui/material/Grid\";\nimport Card from \"@mui/material/Card\";\nimport {\n  CardContent,\n  Divider,\n  Drawer,\n  Grid2,\n  List,\n  ListItem,\n  SelectChangeEvent,\n  FormHelperText,\n} from \"@mui/material\";\nimport LeftMenuComponent from \"../../components/leftMenu/leftMenu.component\";\nimport \"../qanda/qanda.screen.style.css\";\nimport { Formik } from \"formik\";\nimport {\n  IBusinessGroup,\n  IBusinessGroupsResponseModel,\n} from \"../../interfaces/response/IBusinessGroupsResponseModel\";\nimport * as yup from \"yup\";\nimport { DEFAULT_PAGINATION } from \"../../constants/dbConstant.constant\";\nimport { IPaginationModel } from \"../../interfaces/IPaginationModel\";\nimport {\n  ILocation,\n  ILocationsListResponseModel,\n} from \"../../interfaces/response/ILocationsListResponseModel\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { LoadingContext } from \"../../context/loading.context\";\nimport BusinessService from \"../../services/business/business.service\";\nimport LocationService from \"../../services/location/location.service\";\nimport {\n  IBusiness,\n  IBusinessListResponseModel,\n} from \"../../interfaces/response/IBusinessListResponseModel\";\nimport { IReviewsListRequestModel } from \"../../interfaces/request/IReviewsListRequestModel\";\nimport ApplicationHelperService from \"../../services/ApplicationHelperService\";\nimport {\n  IQuestionAnswer,\n  IQuestionAnswersResponseModel,\n} from \"../../interfaces/response/IQuestionAnswersResponseModel\";\nimport GenericDrawer from \"../../components/genericDrawer/genericDrawer.component\";\nimport QandAService from \"../../services/qanda/qanda.service\";\nimport SendOutlinedIcon from \"@mui/icons-material/SendOutlined\";\nimport ReplyQuestionAnswerComponent from \"../../components/replyQuestionAnswer/replyQuestionAnswer.component\";\nimport { getIn } from \"formik\";\nimport { ToastSeverity } from \"../../constants/toastSeverity.constant\";\nimport { MessageConstants } from \"../../constants/message.constant\";\nimport { ToastContext } from \"../../context/toast.context\";\n\nconst QandA: FunctionComponent<PageProps> = ({ title }) => {\n  const dispatch = useDispatch();\n  const [openqanda, setOpenqanda] = useState(false);\n  const { userInfo, rbAccess } = useSelector((state: any) => state.authReducer);\n  const _businessService = new BusinessService(dispatch);\n  const _locationService = new LocationService(dispatch);\n  const _qandAService = new QandAService(dispatch);\n  const _applicationHelperService = new ApplicationHelperService({});\n  const { setLoading } = useContext(LoadingContext);\n  const { setToastConfig, setOpen } = useContext(ToastContext);\n  const [businessGroups, setBusinessGroups] = useState<IBusinessGroup[]>([]);\n  const [businessGroupsOnBusiness, setBusinessGroupsOnBusiness] = useState<\n    IBusinessGroup[]\n  >([]);\n  const [qandaList, setQandAList] = useState<IQuestionAnswer[]>([]);\n\n  // const [locationListOnBusiness, setLocationListOnBusiness] = useState<\n  //   ILocation[]\n  // >([]);\n  const [paginationModel, setPaginationModel] =\n    useState<IPaginationModel>(DEFAULT_PAGINATION);\n  const [locationList, setLocationList] = useState<ILocation[]>([]);\n  const [businessList, setBusinessList] = useState<IBusiness[]>([]);\n  const INITIAL_VALUES: IReviewsListRequestModel = {\n    businessId: 0,\n    businessGroupId: \"0\",\n    locationId: \"0\",\n    orderBy: \"CREATETIME ASC\",\n    rating: \"ALL\",\n    tags: \"0\",\n    searchText: \"\",\n  };\n\n  const [initialValues, setInitialValues] =\n    useState<IReviewsListRequestModel>(INITIAL_VALUES);\n\n  const getBusiness = async () => {\n    try {\n      setLoading(true);\n      let roles: IBusinessListResponseModel =\n        await _businessService.getBusiness(userInfo.id);\n      if (roles.list.length > 0) {\n        setBusinessList(roles.list);\n      }\n    } catch (error) {}\n\n    setLoading(false);\n  };\n\n  const getBusinessGroups = async () => {\n    try {\n      setLoading(true);\n      let businessGroups: IBusinessGroupsResponseModel =\n        await _businessService.getBusinessGroups(userInfo.id);\n      if (businessGroups.data.length > 0) {\n        setBusinessGroups(businessGroups.data);\n      }\n    } catch (error) {}\n\n    setLoading(false);\n  };\n\n  const LocationSchema = yup.object().shape({\n    businessId: yup\n      .number()\n      .typeError(\"Business ID must be a number\")\n      .moreThan(0, \"Business must be selected\") // Ensures value is greater than 0\n      .required(\"Business is required\"),\n    businessGroupId: yup\n      .string()\n      .nonNullable()\n      .required(\"Account is required\")\n      .test(\"len\", `Account is required`, (val) => val != \"0\"),\n    locationId: yup\n      .string()\n      .nonNullable()\n      .required(\"Location is required\")\n      .test(\"len\", `Location is required`, (val) => val != \"0\"),\n  });\n\n  useEffect(() => {\n    getBusiness();\n    getBusinessGroups();\n    fetchLocationsPaginated();\n  }, []);\n\n  const fetchLocationsPaginated = async () => {\n    try {\n      setLoading(true);\n      const locationListResponse: ILocationsListResponseModel =\n        await _businessService.getLocations(userInfo.id);\n      console.log(\"Location List: \", locationListResponse.list);\n      setLocationList(locationListResponse.list);\n    } catch (error) {}\n\n    setLoading(false);\n  };\n\n  const fetchQuestionsAnswersListPaginated = async (locationId: string) => {\n    try {\n      setLoading(true);\n      const reviewsResponse: IQuestionAnswersResponseModel =\n        await _qandAService.getQA(userInfo.id, locationId);\n      console.log(\"Question and Answers Response: \", reviewsResponse);\n      setQandAList(reviewsResponse.list);\n    } catch (error) {}\n\n    setLoading(false);\n  };\n\n  const fetchQuestionsAnswers = async (values: IReviewsListRequestModel) => {\n    try {\n      const isValid = await LocationSchema.isValid(values);\n      if (isValid) {\n        fetchQuestionsAnswersListPaginated(values.locationId);\n      }\n    } catch (error: any) {}\n  };\n\n  const syncQandA = async (values: IReviewsListRequestModel) => {\n    try {\n      setLoading(true);\n      const syncReviewsRequest = {\n        \"x-gmb-account-id\": values.businessGroupId,\n        \"x-gmb-business-id\": values.businessId,\n        \"x-gmb-location-id\": values.locationId,\n      };\n\n      const reviewsResponse: IQuestionAnswersResponseModel =\n        await _qandAService.refreshQA(syncReviewsRequest);\n\n      if (reviewsResponse.success) {\n        setToastConfig(ToastSeverity.Success, reviewsResponse.message, true);\n        fetchQuestionsAnswers(values);\n      } else {\n        setToastConfig(ToastSeverity.Error, reviewsResponse.message, true);\n      }\n    } catch (error: any) {\n      setLoading(false);\n      if (error?.response?.data?.data?.error?.details) {\n        setToastConfig(\n          ToastSeverity.Error,\n          error?.response?.data?.data?.error?.details[0].reason,\n          true\n        );\n      } else {\n        setToastConfig(\n          ToastSeverity.Error,\n          MessageConstants.ApiErrorStandardMessage,\n          true\n        );\n      }\n    }\n  };\n\n  const QuestionAnswersComponent = (props: {\n    qandanswer: IQuestionAnswer;\n    index: number;\n  }) => {\n    const [openReplyMessage, setOpenReplyMessage] = useState<boolean>(false);\n\n    return (\n      <Grid2 size={6} key={props.index}>\n        <Card variant=\"outlined\" className=\"commonCard reviewCard height100\">\n          <CardContent className=\"pad0\">\n            <Box>\n              <h3 className=\"m-0\">{props.qandanswer.question}</h3>\n              <Box>\n                <List className=\"\">\n                  <ListItem className=\"pad0\">\n                    <Typography className=\"textTruncate\">\n                      <b>Answer: </b>\n                      {props.qandanswer.answer}.\n                    </Typography>\n                  </ListItem>\n                </List>\n                {Boolean(rbAccess && rbAccess.QandAReply) && (\n                  <Box className=\"qandaReply\">\n                    <Button\n                      className=\"tagBtn\"\n                      onClick={() => setOpenReplyMessage(true)}\n                      variant=\"contained\"\n                      startIcon={<SendOutlinedIcon />}\n                    >\n                      Reply\n                    </Button>\n                  </Box>\n                )}\n              </Box>\n            </Box>\n          </CardContent>\n        </Card>\n\n        <GenericDrawer\n          component={\n            <ReplyQuestionAnswerComponent\n              questionAnswer={props.qandanswer}\n              closeDrawer={() => setOpenReplyMessage(!openReplyMessage)}\n            />\n          }\n          isShow={openReplyMessage}\n          callback={() => setOpenReplyMessage(!openReplyMessage)}\n        />\n      </Grid2>\n    );\n  };\n\n  return (\n    <div>\n      <Box>\n        <Box>\n          <LeftMenuComponent>\n            <Box className=\"bodyPart\">\n              <Formik\n                enableReinitialize\n                initialValues={{ ...initialValues }}\n                validationSchema={LocationSchema}\n                onSubmit={(values, { setSubmitting }) => {\n                  fetchQuestionsAnswers(values);\n                }}\n              >\n                {({\n                  values,\n                  errors,\n                  touched,\n                  handleChange,\n                  handleBlur,\n                  handleSubmit,\n                  setFieldValue,\n                  handleReset,\n                  isSubmitting,\n                  isValid,\n                  /* and other goodies */\n                }) => (\n                  <form onSubmit={handleSubmit} onReset={handleReset}>\n                    <Box className=\"commonTableHeader\">\n                      <h3 className=\"commonTitle pageTitle\">\n                        List of queries for Business\n                      </h3>\n                      {Boolean(rbAccess && rbAccess.ReviewsRefresh) && (\n                        <Button\n                          onClick={() => syncQandA(values)}\n                          className={\n                            values.locationId === \"0\"\n                              ? \"tableActionBtnDisabled\"\n                              : \"tableActionBtn\"\n                          }\n                          sx={{\n                            minHeight: \"50px\", // Set the desired height\n                            width: \"100px\",\n                          }}\n                          startIcon={\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              viewBox=\"0 0 48 48\"\n                              width=\"24px\"\n                              height=\"24px\"\n                            >\n                              <path\n                                fill=\"#fbc02d\"\n                                d=\"M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12 s5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24s8.955,20,20,20 s20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z\"\n                              />\n                              <path\n                                fill=\"#e53935\"\n                                d=\"M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039 l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z\"\n                              />\n                              <path\n                                fill=\"#4caf50\"\n                                d=\"M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36 c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z\"\n                              />\n                              <path\n                                fill=\"#1565c0\"\n                                d=\"M43.611,20.083L43.595,20L42,20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571 c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z\"\n                              />\n                            </svg>\n                          }\n                          disabled={values.locationId === \"0\"}\n                        >\n                          Sync\n                        </Button>\n                      )}\n                    </Box>\n                    <Grid container spacing={2}>\n                      <Grid item xs={12} md={6} lg={3}>\n                        <FormControl\n                          variant=\"filled\"\n                          fullWidth\n                          error={Boolean(\n                            getIn(errors, \"businessId\") &&\n                              getIn(touched, \"businessId\")\n                          )}\n                        >\n                          <InputLabel id=\"outlined-country-dropdown-label\">\n                            Business\n                          </InputLabel>\n\n                          <Select\n                            fullWidth\n                            id=\"businessId\"\n                            label=\"Business\"\n                            value={values.businessId.toString()}\n                            onChange={(evt: SelectChangeEvent) => {\n                              setBusinessGroupsOnBusiness(\n                                businessGroups.filter(\n                                  (x: IBusinessGroup) =>\n                                    x.businessId === +evt.target.value\n                                )\n                              );\n                              setFieldValue(\"businessId\", +evt.target.value);\n                              setFieldValue(\"businessGroupId\", \"0\");\n                              setFieldValue(\"locationId\", \"0\");\n                              setPaginationModel({ ...DEFAULT_PAGINATION });\n                            }}\n                            onBlur={handleBlur}\n                            sx={{\n                              backgroundColor: \"var(--whiteColor)\",\n                              borderRadius: \"5px\",\n                            }}\n                          >\n                            <MenuItem value={0}>Select</MenuItem>\n                            {businessList &&\n                              businessList.map((business: IBusiness) => (\n                                <MenuItem\n                                  key={business.id}\n                                  value={business.id.toString()}\n                                >\n                                  {business.businessName}\n                                </MenuItem>\n                              ))}\n                          </Select>\n                          <FormHelperText>\n                            {touched.businessId && errors.businessId\n                              ? errors.businessId\n                              : \"\"}\n                          </FormHelperText>\n                        </FormControl>\n                      </Grid>\n                      <Grid item xs={12} md={6} lg={3}>\n                        <FormControl\n                          variant=\"filled\"\n                          fullWidth\n                          error={Boolean(\n                            getIn(errors, \"businessGroupId\") &&\n                              getIn(touched, \"businessGroupId\")\n                          )}\n                        >\n                          <InputLabel id=\"outlined-country-dropdown-label\">\n                            Group\n                          </InputLabel>\n\n                          <Select\n                            fullWidth\n                            id=\"businessGroupId\"\n                            label=\"Group\"\n                            value={values.businessGroupId || \"\"} // Ensure default is empty string\n                            onChange={(evt: SelectChangeEvent) => {\n                              const selectedValue = evt.target.value;\n                              setFieldValue(\"businessGroupId\", selectedValue); // Ensure consistent type\n                              setFieldValue(\"locationId\", \"0\");\n                              setPaginationModel({ ...DEFAULT_PAGINATION });\n                            }}\n                            onBlur={handleBlur}\n                            sx={{\n                              backgroundColor: \"var(--whiteColor)\",\n                              borderRadius: \"5px\",\n                            }}\n                          >\n                            <MenuItem value=\"0\">Select</MenuItem>\n                            {businessGroups\n                              .filter(\n                                (x: IBusinessGroup) =>\n                                  x.businessId === values.businessId\n                              )\n                              .map((businessGroup: IBusinessGroup) => (\n                                <MenuItem\n                                  key={businessGroup.accountId}\n                                  value={businessGroup.accountId}\n                                >\n                                  {businessGroup.accountName}\n                                </MenuItem>\n                              ))}\n                          </Select>\n                          <FormHelperText>\n                            {touched.businessGroupId && errors.businessGroupId\n                              ? errors.businessGroupId\n                              : \"\"}\n                          </FormHelperText>\n                        </FormControl>\n                      </Grid>\n                      <Grid item xs={12} md={6} lg={3}>\n                        <FormControl\n                          variant=\"filled\"\n                          fullWidth\n                          error={Boolean(\n                            getIn(errors, \"locationId\") &&\n                              getIn(touched, \"locationId\")\n                          )}\n                        >\n                          <InputLabel id=\"outlined-country-dropdown-label\">\n                            Location\n                          </InputLabel>\n\n                          <Select\n                            fullWidth\n                            id=\"locationId\"\n                            label=\"Location\"\n                            value={values.locationId.toString()}\n                            onChange={(evt: SelectChangeEvent) => {\n                              setFieldValue(\"locationId\", evt.target.value);\n                              setPaginationModel({ ...DEFAULT_PAGINATION });\n                            }}\n                            onBlur={handleBlur}\n                            sx={{\n                              backgroundColor: \"var(--whiteColor)\",\n                              borderRadius: \"5px\",\n                            }}\n                          >\n                            <MenuItem value={\"0\"}>Select</MenuItem>\n                            {locationList\n                              .filter(\n                                (x: ILocation) =>\n                                  x.gmbAccountId === values.businessGroupId\n                              )\n                              .map((location: ILocation) => (\n                                <MenuItem\n                                  key={location.gmbLocationId}\n                                  value={location.gmbLocationId}\n                                >\n                                  {location.gmbLocationName}\n                                </MenuItem>\n                              ))}\n                          </Select>\n                          <FormHelperText>\n                            {touched.locationId && errors.locationId\n                              ? errors.locationId\n                              : \"\"}\n                          </FormHelperText>\n                        </FormControl>\n                      </Grid>\n                      <Grid item xs={12} md={6} lg={3}>\n                        <Button\n                          className=\"commonShapeBtn\"\n                          type=\"submit\"\n                          variant=\"contained\"\n                          fullWidth\n                        >\n                          Apply\n                        </Button>\n                      </Grid>\n                    </Grid>\n                  </form>\n                )}\n              </Formik>\n              <Box>\n                <Box>\n                  {/* <Box className=\"commonTableHeader\">\n                    <h3>Q&A</h3>\n                  </Box> */}\n                  <Grid2 container spacing={2}>\n                    {qandaList.length > 0 &&\n                      qandaList.map(\n                        (question: IQuestionAnswer, index: number) => (\n                          <QuestionAnswersComponent\n                            key={\"q-and-a\" + index}\n                            qandanswer={question}\n                            index={index}\n                          />\n                        )\n                      )}\n                  </Grid2>\n                </Box>\n              </Box>\n            </Box>\n          </LeftMenuComponent>\n        </Box>\n      </Box>\n    </div>\n  );\n};\n\nexport default QandA;\n"], "mappings": ";;AAAA,SAA4BA,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAG1E;AACA,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,UAAU,MAAM,0BAA0B;AAEjD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SACEC,WAAW,EAGXC,KAAK,EACLC,IAAI,EACJC,QAAQ,EAERC,cAAc,QACT,eAAe;AACtB,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAO,iCAAiC;AACxC,SAASC,MAAM,QAAQ,QAAQ;AAK/B,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SAASC,kBAAkB,QAAQ,qCAAqC;AAMxE,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,OAAOC,eAAe,MAAM,0CAA0C;AACtE,OAAOC,eAAe,MAAM,0CAA0C;AAMtE,OAAOC,wBAAwB,MAAM,yCAAyC;AAK9E,OAAOC,aAAa,MAAM,wDAAwD;AAClF,OAAOC,YAAY,MAAM,oCAAoC;AAC7D,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,4BAA4B,MAAM,oEAAoE;AAC7G,SAASC,KAAK,QAAQ,QAAQ;AAC9B,SAASC,aAAa,QAAQ,wCAAwC;AACtE,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,YAAY,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,KAAmC,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,GAAA;EAAA,IAAAC,EAAA,GAAAC,YAAA;EACzD,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAE2C,QAAQ;IAAEC;EAAS,CAAC,GAAGxB,WAAW,CAAEyB,KAAU,IAAKA,KAAK,CAACC,WAAW,CAAC;EAC7E,MAAMC,gBAAgB,GAAG,IAAIzB,eAAe,CAACkB,QAAQ,CAAC;EACtD,MAAMQ,gBAAgB,GAAG,IAAIzB,eAAe,CAACiB,QAAQ,CAAC;EACtD,MAAMS,aAAa,GAAG,IAAIvB,YAAY,CAACc,QAAQ,CAAC;EAChD,MAAMU,yBAAyB,GAAG,IAAI1B,wBAAwB,CAAC,CAAC,CAAC,CAAC;EAClE,MAAM;IAAE2B;EAAW,CAAC,GAAGrD,UAAU,CAACuB,cAAc,CAAC;EACjD,MAAM;IAAE+B,cAAc;IAAEC;EAAQ,CAAC,GAAGvD,UAAU,CAACkC,YAAY,CAAC;EAC5D,MAAM,CAACsB,cAAc,EAAEC,iBAAiB,CAAC,GAAGvD,QAAQ,CAAmB,EAAE,CAAC;EAC1E,MAAM,CAACwD,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGzD,QAAQ,CAEtE,EAAE,CAAC;EACL,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAoB,EAAE,CAAC;;EAEjE;EACA;EACA;EACA,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GACzC7D,QAAQ,CAAmBkB,kBAAkB,CAAC;EAChD,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAc,EAAE,CAAC;EACjE,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAc,EAAE,CAAC;EACjE,MAAMkE,cAAwC,GAAG;IAC/CC,UAAU,EAAE,CAAC;IACbC,eAAe,EAAE,GAAG;IACpBC,UAAU,EAAE,GAAG;IACfC,OAAO,EAAE,gBAAgB;IACzBC,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,GAAG;IACTC,UAAU,EAAE;EACd,CAAC;EAED,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GACrC3E,QAAQ,CAA2BkE,cAAc,CAAC;EAEpD,MAAMU,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFzB,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI0B,KAAiC,GACnC,MAAM9B,gBAAgB,CAAC6B,WAAW,CAACjC,QAAQ,CAACmC,EAAE,CAAC;MACjD,IAAID,KAAK,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACzBf,eAAe,CAACY,KAAK,CAACE,IAAI,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE,CAAC;IAEjB9B,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAM+B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF/B,UAAU,CAAC,IAAI,CAAC;MAChB,IAAIG,cAA4C,GAC9C,MAAMP,gBAAgB,CAACmC,iBAAiB,CAACvC,QAAQ,CAACmC,EAAE,CAAC;MACvD,IAAIxB,cAAc,CAAC6B,IAAI,CAACH,MAAM,GAAG,CAAC,EAAE;QAClCzB,iBAAiB,CAACD,cAAc,CAAC6B,IAAI,CAAC;MACxC;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE,CAAC;IAEjB9B,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMiC,cAAc,GAAGnE,GAAG,CAACoE,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IACxCnB,UAAU,EAAElD,GAAG,CACZsE,MAAM,CAAC,CAAC,CACRC,SAAS,CAAC,8BAA8B,CAAC,CACzCC,QAAQ,CAAC,CAAC,EAAE,2BAA2B,CAAC,CAAC;IAAA,CACzCC,QAAQ,CAAC,sBAAsB,CAAC;IACnCtB,eAAe,EAAEnD,GAAG,CACjB0E,MAAM,CAAC,CAAC,CACRC,WAAW,CAAC,CAAC,CACbF,QAAQ,CAAC,qBAAqB,CAAC,CAC/BG,IAAI,CAAC,KAAK,EAAE,qBAAqB,EAAGC,GAAG,IAAKA,GAAG,IAAI,GAAG,CAAC;IAC1DzB,UAAU,EAAEpD,GAAG,CACZ0E,MAAM,CAAC,CAAC,CACRC,WAAW,CAAC,CAAC,CACbF,QAAQ,CAAC,sBAAsB,CAAC,CAChCG,IAAI,CAAC,KAAK,EAAE,sBAAsB,EAAGC,GAAG,IAAKA,GAAG,IAAI,GAAG;EAC5D,CAAC,CAAC;EAEF/F,SAAS,CAAC,MAAM;IACd6E,WAAW,CAAC,CAAC;IACbM,iBAAiB,CAAC,CAAC;IACnBa,uBAAuB,CAAC,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI;MACF5C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM6C,oBAAiD,GACrD,MAAMjD,gBAAgB,CAACkD,YAAY,CAACtD,QAAQ,CAACmC,EAAE,CAAC;MAClDoB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,oBAAoB,CAACjB,IAAI,CAAC;MACzDhB,eAAe,CAACiC,oBAAoB,CAACjB,IAAI,CAAC;IAC5C,CAAC,CAAC,OAAOE,KAAK,EAAE,CAAC;IAEjB9B,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMiD,kCAAkC,GAAG,MAAO/B,UAAkB,IAAK;IACvE,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMkD,eAA8C,GAClD,MAAMpD,aAAa,CAACqD,KAAK,CAAC3D,QAAQ,CAACmC,EAAE,EAAET,UAAU,CAAC;MACpD6B,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEE,eAAe,CAAC;MAC/D1C,YAAY,CAAC0C,eAAe,CAACtB,IAAI,CAAC;IACpC,CAAC,CAAC,OAAOE,KAAK,EAAE,CAAC;IAEjB9B,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMoD,qBAAqB,GAAG,MAAOC,MAAgC,IAAK;IACxE,IAAI;MACF,MAAMC,OAAO,GAAG,MAAMrB,cAAc,CAACqB,OAAO,CAACD,MAAM,CAAC;MACpD,IAAIC,OAAO,EAAE;QACXL,kCAAkC,CAACI,MAAM,CAACnC,UAAU,CAAC;MACvD;IACF,CAAC,CAAC,OAAOY,KAAU,EAAE,CAAC;EACxB,CAAC;EAED,MAAMyB,SAAS,GAAG,MAAOF,MAAgC,IAAK;IAC5D,IAAI;MACFrD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwD,kBAAkB,GAAG;QACzB,kBAAkB,EAAEH,MAAM,CAACpC,eAAe;QAC1C,mBAAmB,EAAEoC,MAAM,CAACrC,UAAU;QACtC,mBAAmB,EAAEqC,MAAM,CAACnC;MAC9B,CAAC;MAED,MAAMgC,eAA8C,GAClD,MAAMpD,aAAa,CAAC2D,SAAS,CAACD,kBAAkB,CAAC;MAEnD,IAAIN,eAAe,CAACQ,OAAO,EAAE;QAC3BzD,cAAc,CAACtB,aAAa,CAACgF,OAAO,EAAET,eAAe,CAACU,OAAO,EAAE,IAAI,CAAC;QACpER,qBAAqB,CAACC,MAAM,CAAC;MAC/B,CAAC,MAAM;QACLpD,cAAc,CAACtB,aAAa,CAACkF,KAAK,EAAEX,eAAe,CAACU,OAAO,EAAE,IAAI,CAAC;MACpE;IACF,CAAC,CAAC,OAAO9B,KAAU,EAAE;MAAA,IAAAgC,eAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACnBjE,UAAU,CAAC,KAAK,CAAC;MACjB,IAAI8B,KAAK,aAALA,KAAK,gBAAAgC,eAAA,GAALhC,KAAK,CAAEoC,QAAQ,cAAAJ,eAAA,gBAAAC,oBAAA,GAAfD,eAAA,CAAiB9B,IAAI,cAAA+B,oBAAA,gBAAAC,qBAAA,GAArBD,oBAAA,CAAuB/B,IAAI,cAAAgC,qBAAA,gBAAAC,sBAAA,GAA3BD,qBAAA,CAA6BlC,KAAK,cAAAmC,sBAAA,eAAlCA,sBAAA,CAAoCE,OAAO,EAAE;QAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QAC/CtE,cAAc,CACZtB,aAAa,CAACkF,KAAK,EACnB/B,KAAK,aAALA,KAAK,wBAAAsC,gBAAA,GAALtC,KAAK,CAAEoC,QAAQ,cAAAE,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBpC,IAAI,cAAAqC,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuBrC,IAAI,cAAAsC,sBAAA,wBAAAC,sBAAA,GAA3BD,sBAAA,CAA6BxC,KAAK,cAAAyC,sBAAA,uBAAlCA,sBAAA,CAAoCJ,OAAO,CAAC,CAAC,CAAC,CAACK,MAAM,EACrD,IACF,CAAC;MACH,CAAC,MAAM;QACLvE,cAAc,CACZtB,aAAa,CAACkF,KAAK,EACnBjF,gBAAgB,CAAC6F,uBAAuB,EACxC,IACF,CAAC;MACH;IACF;EACF,CAAC;EAED,MAAMC,wBAAwB,GAAIC,KAGjC,IAAK;IAAAxF,EAAA;IACJ,MAAM,CAACyF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhI,QAAQ,CAAU,KAAK,CAAC;IAExE,oBACEkC,OAAA,CAACvB,KAAK;MAACsH,IAAI,EAAE,CAAE;MAAAC,QAAA,gBACbhG,OAAA,CAACzB,IAAI;QAAC0H,OAAO,EAAC,UAAU;QAACC,SAAS,EAAC,iCAAiC;QAAAF,QAAA,eAClEhG,OAAA,CAACxB,WAAW;UAAC0H,SAAS,EAAC,MAAM;UAAAF,QAAA,eAC3BhG,OAAA,CAACjC,GAAG;YAAAiI,QAAA,gBACFhG,OAAA;cAAIkG,SAAS,EAAC,KAAK;cAAAF,QAAA,EAAEJ,KAAK,CAACO,UAAU,CAACC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpDxG,OAAA,CAACjC,GAAG;cAAAiI,QAAA,gBACFhG,OAAA,CAACtB,IAAI;gBAACwH,SAAS,EAAC,EAAE;gBAAAF,QAAA,eAChBhG,OAAA,CAACrB,QAAQ;kBAACuH,SAAS,EAAC,MAAM;kBAAAF,QAAA,eACxBhG,OAAA,CAAC/B,UAAU;oBAACiI,SAAS,EAAC,cAAc;oBAAAF,QAAA,gBAClChG,OAAA;sBAAAgG,QAAA,EAAG;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,EACdZ,KAAK,CAACO,UAAU,CAACM,MAAM,EAAC,GAC3B;kBAAA;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,EACNE,OAAO,CAAChG,QAAQ,IAAIA,QAAQ,CAACiG,UAAU,CAAC,iBACvC3G,OAAA,CAACjC,GAAG;gBAACmI,SAAS,EAAC,YAAY;gBAAAF,QAAA,eACzBhG,OAAA,CAAChC,MAAM;kBACLkI,SAAS,EAAC,QAAQ;kBAClBU,OAAO,EAAEA,CAAA,KAAMd,mBAAmB,CAAC,IAAI,CAAE;kBACzCG,OAAO,EAAC,WAAW;kBACnBY,SAAS,eAAE7G,OAAA,CAACP,gBAAgB;oBAAA4G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAR,QAAA,EACjC;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPxG,OAAA,CAACT,aAAa;QACZuH,SAAS,eACP9G,OAAA,CAACN,4BAA4B;UAC3BqH,cAAc,EAAEnB,KAAK,CAACO,UAAW;UACjCa,WAAW,EAAEA,CAAA,KAAMlB,mBAAmB,CAAC,CAACD,gBAAgB;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CACF;QACDS,MAAM,EAAEpB,gBAAiB;QACzBqB,QAAQ,EAAEA,CAAA,KAAMpB,mBAAmB,CAAC,CAACD,gBAAgB;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA,GAxCiBZ,KAAK,CAACuB,KAAK;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAyCzB,CAAC;EAEZ,CAAC;EAACpG,EAAA,CAlDIuF,wBAAwB;EAoD9B,oBACE3F,OAAA;IAAAgG,QAAA,eACEhG,OAAA,CAACjC,GAAG;MAAAiI,QAAA,eACFhG,OAAA,CAACjC,GAAG;QAAAiI,QAAA,eACFhG,OAAA,CAACnB,iBAAiB;UAAAmH,QAAA,eAChBhG,OAAA,CAACjC,GAAG;YAACmI,SAAS,EAAC,UAAU;YAAAF,QAAA,gBACvBhG,OAAA,CAAClB,MAAM;cACLsI,kBAAkB;cAClB5E,aAAa,EAAE;gBAAE,GAAGA;cAAc,CAAE;cACpC6E,gBAAgB,EAAEnE,cAAe;cACjCoE,QAAQ,EAAEA,CAAChD,MAAM,EAAE;gBAAEiD;cAAc,CAAC,KAAK;gBACvClD,qBAAqB,CAACC,MAAM,CAAC;cAC/B,CAAE;cAAA0B,QAAA,EAEDA,CAAC;gBACA1B,MAAM;gBACNkD,MAAM;gBACNC,OAAO;gBACPC,YAAY;gBACZC,UAAU;gBACVC,YAAY;gBACZC,aAAa;gBACbC,WAAW;gBACXC,YAAY;gBACZxD;gBACA;cACF,CAAC,kBACCvE,OAAA;gBAAMsH,QAAQ,EAAEM,YAAa;gBAACI,OAAO,EAAEF,WAAY;gBAAA9B,QAAA,gBACjDhG,OAAA,CAACjC,GAAG;kBAACmI,SAAS,EAAC,mBAAmB;kBAAAF,QAAA,gBAChChG,OAAA;oBAAIkG,SAAS,EAAC,uBAAuB;oBAAAF,QAAA,EAAC;kBAEtC;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACJE,OAAO,CAAChG,QAAQ,IAAIA,QAAQ,CAACuH,cAAc,CAAC,iBAC3CjI,OAAA,CAAChC,MAAM;oBACL4I,OAAO,EAAEA,CAAA,KAAMpC,SAAS,CAACF,MAAM,CAAE;oBACjC4B,SAAS,EACP5B,MAAM,CAACnC,UAAU,KAAK,GAAG,GACrB,wBAAwB,GACxB,gBACL;oBACD+F,EAAE,EAAE;sBACFC,SAAS,EAAE,MAAM;sBAAE;sBACnBC,KAAK,EAAE;oBACT,CAAE;oBACFvB,SAAS,eACP7G,OAAA;sBACEqI,KAAK,EAAC,4BAA4B;sBAClCC,OAAO,EAAC,WAAW;sBACnBF,KAAK,EAAC,MAAM;sBACZG,MAAM,EAAC,MAAM;sBAAAvC,QAAA,gBAEbhG,OAAA;wBACEwI,IAAI,EAAC,SAAS;wBACdC,CAAC,EAAC;sBAA0Q;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7Q,CAAC,eACFxG,OAAA;wBACEwI,IAAI,EAAC,SAAS;wBACdC,CAAC,EAAC;sBAAoK;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvK,CAAC,eACFxG,OAAA;wBACEwI,IAAI,EAAC,SAAS;wBACdC,CAAC,EAAC;sBAAgK;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnK,CAAC,eACFxG,OAAA;wBACEwI,IAAI,EAAC,SAAS;wBACdC,CAAC,EAAC;sBAAiM;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN;oBACDkC,QAAQ,EAAEpE,MAAM,CAACnC,UAAU,KAAK,GAAI;oBAAA6D,QAAA,EACrC;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNxG,OAAA,CAAC1B,IAAI;kBAACqK,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAA5C,QAAA,gBACzBhG,OAAA,CAAC1B,IAAI;oBAACuK,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACC,EAAE,EAAE,CAAE;oBAACC,EAAE,EAAE,CAAE;oBAAAhD,QAAA,eAC9BhG,OAAA,CAAC3B,WAAW;sBACV4H,OAAO,EAAC,QAAQ;sBAChBgD,SAAS;sBACTlG,KAAK,EAAE2D,OAAO,CACZ/G,KAAK,CAAC6H,MAAM,EAAE,YAAY,CAAC,IACzB7H,KAAK,CAAC8H,OAAO,EAAE,YAAY,CAC/B,CAAE;sBAAAzB,QAAA,gBAEFhG,OAAA,CAAC5B,UAAU;wBAACwE,EAAE,EAAC,iCAAiC;wBAAAoD,QAAA,EAAC;sBAEjD;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAEbxG,OAAA,CAAC9B,MAAM;wBACL+K,SAAS;wBACTrG,EAAE,EAAC,YAAY;wBACfsG,KAAK,EAAC,UAAU;wBAChBC,KAAK,EAAE7E,MAAM,CAACrC,UAAU,CAACmH,QAAQ,CAAC,CAAE;wBACpCC,QAAQ,EAAGC,GAAsB,IAAK;0BACpC/H,2BAA2B,CACzBH,cAAc,CAACmI,MAAM,CAClBC,CAAiB,IAChBA,CAAC,CAACvH,UAAU,KAAK,CAACqH,GAAG,CAACG,MAAM,CAACN,KACjC,CACF,CAAC;0BACDtB,aAAa,CAAC,YAAY,EAAE,CAACyB,GAAG,CAACG,MAAM,CAACN,KAAK,CAAC;0BAC9CtB,aAAa,CAAC,iBAAiB,EAAE,GAAG,CAAC;0BACrCA,aAAa,CAAC,YAAY,EAAE,GAAG,CAAC;0BAChClG,kBAAkB,CAAC;4BAAE,GAAG3C;0BAAmB,CAAC,CAAC;wBAC/C,CAAE;wBACF0K,MAAM,EAAE/B,UAAW;wBACnBO,EAAE,EAAE;0BACFyB,eAAe,EAAE,mBAAmB;0BACpCC,YAAY,EAAE;wBAChB,CAAE;wBAAA5D,QAAA,gBAEFhG,OAAA,CAAC7B,QAAQ;0BAACgL,KAAK,EAAE,CAAE;0BAAAnD,QAAA,EAAC;wBAAM;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAU,CAAC,EACpC1E,YAAY,IACXA,YAAY,CAAC+H,GAAG,CAAEC,QAAmB,iBACnC9J,OAAA,CAAC7B,QAAQ;0BAEPgL,KAAK,EAAEW,QAAQ,CAAClH,EAAE,CAACwG,QAAQ,CAAC,CAAE;0BAAApD,QAAA,EAE7B8D,QAAQ,CAACC;wBAAY,GAHjBD,QAAQ,CAAClH,EAAE;0BAAAyD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAIR,CACX,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACTxG,OAAA,CAACpB,cAAc;wBAAAoH,QAAA,EACZyB,OAAO,CAACxF,UAAU,IAAIuF,MAAM,CAACvF,UAAU,GACpCuF,MAAM,CAACvF,UAAU,GACjB;sBAAE;wBAAAoE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACPxG,OAAA,CAAC1B,IAAI;oBAACuK,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACC,EAAE,EAAE,CAAE;oBAACC,EAAE,EAAE,CAAE;oBAAAhD,QAAA,eAC9BhG,OAAA,CAAC3B,WAAW;sBACV4H,OAAO,EAAC,QAAQ;sBAChBgD,SAAS;sBACTlG,KAAK,EAAE2D,OAAO,CACZ/G,KAAK,CAAC6H,MAAM,EAAE,iBAAiB,CAAC,IAC9B7H,KAAK,CAAC8H,OAAO,EAAE,iBAAiB,CACpC,CAAE;sBAAAzB,QAAA,gBAEFhG,OAAA,CAAC5B,UAAU;wBAACwE,EAAE,EAAC,iCAAiC;wBAAAoD,QAAA,EAAC;sBAEjD;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAEbxG,OAAA,CAAC9B,MAAM;wBACL+K,SAAS;wBACTrG,EAAE,EAAC,iBAAiB;wBACpBsG,KAAK,EAAC,OAAO;wBACbC,KAAK,EAAE7E,MAAM,CAACpC,eAAe,IAAI,EAAG,CAAC;wBAAA;wBACrCmH,QAAQ,EAAGC,GAAsB,IAAK;0BACpC,MAAMU,aAAa,GAAGV,GAAG,CAACG,MAAM,CAACN,KAAK;0BACtCtB,aAAa,CAAC,iBAAiB,EAAEmC,aAAa,CAAC,CAAC,CAAC;0BACjDnC,aAAa,CAAC,YAAY,EAAE,GAAG,CAAC;0BAChClG,kBAAkB,CAAC;4BAAE,GAAG3C;0BAAmB,CAAC,CAAC;wBAC/C,CAAE;wBACF0K,MAAM,EAAE/B,UAAW;wBACnBO,EAAE,EAAE;0BACFyB,eAAe,EAAE,mBAAmB;0BACpCC,YAAY,EAAE;wBAChB,CAAE;wBAAA5D,QAAA,gBAEFhG,OAAA,CAAC7B,QAAQ;0BAACgL,KAAK,EAAC,GAAG;0BAAAnD,QAAA,EAAC;wBAAM;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAU,CAAC,EACpCpF,cAAc,CACZmI,MAAM,CACJC,CAAiB,IAChBA,CAAC,CAACvH,UAAU,KAAKqC,MAAM,CAACrC,UAC5B,CAAC,CACA4H,GAAG,CAAEI,aAA6B,iBACjCjK,OAAA,CAAC7B,QAAQ;0BAEPgL,KAAK,EAAEc,aAAa,CAACC,SAAU;0BAAAlE,QAAA,EAE9BiE,aAAa,CAACE;wBAAW,GAHrBF,aAAa,CAACC,SAAS;0BAAA7D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAIpB,CACX,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACTxG,OAAA,CAACpB,cAAc;wBAAAoH,QAAA,EACZyB,OAAO,CAACvF,eAAe,IAAIsF,MAAM,CAACtF,eAAe,GAC9CsF,MAAM,CAACtF,eAAe,GACtB;sBAAE;wBAAAmE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACPxG,OAAA,CAAC1B,IAAI;oBAACuK,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACC,EAAE,EAAE,CAAE;oBAACC,EAAE,EAAE,CAAE;oBAAAhD,QAAA,eAC9BhG,OAAA,CAAC3B,WAAW;sBACV4H,OAAO,EAAC,QAAQ;sBAChBgD,SAAS;sBACTlG,KAAK,EAAE2D,OAAO,CACZ/G,KAAK,CAAC6H,MAAM,EAAE,YAAY,CAAC,IACzB7H,KAAK,CAAC8H,OAAO,EAAE,YAAY,CAC/B,CAAE;sBAAAzB,QAAA,gBAEFhG,OAAA,CAAC5B,UAAU;wBAACwE,EAAE,EAAC,iCAAiC;wBAAAoD,QAAA,EAAC;sBAEjD;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAEbxG,OAAA,CAAC9B,MAAM;wBACL+K,SAAS;wBACTrG,EAAE,EAAC,YAAY;wBACfsG,KAAK,EAAC,UAAU;wBAChBC,KAAK,EAAE7E,MAAM,CAACnC,UAAU,CAACiH,QAAQ,CAAC,CAAE;wBACpCC,QAAQ,EAAGC,GAAsB,IAAK;0BACpCzB,aAAa,CAAC,YAAY,EAAEyB,GAAG,CAACG,MAAM,CAACN,KAAK,CAAC;0BAC7CxH,kBAAkB,CAAC;4BAAE,GAAG3C;0BAAmB,CAAC,CAAC;wBAC/C,CAAE;wBACF0K,MAAM,EAAE/B,UAAW;wBACnBO,EAAE,EAAE;0BACFyB,eAAe,EAAE,mBAAmB;0BACpCC,YAAY,EAAE;wBAChB,CAAE;wBAAA5D,QAAA,gBAEFhG,OAAA,CAAC7B,QAAQ;0BAACgL,KAAK,EAAE,GAAI;0BAAAnD,QAAA,EAAC;wBAAM;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAU,CAAC,EACtC5E,YAAY,CACV2H,MAAM,CACJC,CAAY,IACXA,CAAC,CAACY,YAAY,KAAK9F,MAAM,CAACpC,eAC9B,CAAC,CACA2H,GAAG,CAAEQ,QAAmB,iBACvBrK,OAAA,CAAC7B,QAAQ;0BAEPgL,KAAK,EAAEkB,QAAQ,CAACC,aAAc;0BAAAtE,QAAA,EAE7BqE,QAAQ,CAACE;wBAAe,GAHpBF,QAAQ,CAACC,aAAa;0BAAAjE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAInB,CACX,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACTxG,OAAA,CAACpB,cAAc;wBAAAoH,QAAA,EACZyB,OAAO,CAACtF,UAAU,IAAIqF,MAAM,CAACrF,UAAU,GACpCqF,MAAM,CAACrF,UAAU,GACjB;sBAAE;wBAAAkE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACPxG,OAAA,CAAC1B,IAAI;oBAACuK,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACC,EAAE,EAAE,CAAE;oBAACC,EAAE,EAAE,CAAE;oBAAAhD,QAAA,eAC9BhG,OAAA,CAAChC,MAAM;sBACLkI,SAAS,EAAC,gBAAgB;sBAC1BsE,IAAI,EAAC,QAAQ;sBACbvE,OAAO,EAAC,WAAW;sBACnBgD,SAAS;sBAAAjD,QAAA,EACV;oBAED;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACTxG,OAAA,CAACjC,GAAG;cAAAiI,QAAA,eACFhG,OAAA,CAACjC,GAAG;gBAAAiI,QAAA,eAIFhG,OAAA,CAACvB,KAAK;kBAACkK,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAA5C,QAAA,EACzBxE,SAAS,CAACsB,MAAM,GAAG,CAAC,IACnBtB,SAAS,CAACqI,GAAG,CACX,CAACzD,QAAyB,EAAEe,KAAa,kBACvCnH,OAAA,CAAC2F,wBAAwB;oBAEvBQ,UAAU,EAAEC,QAAS;oBACrBe,KAAK,EAAEA;kBAAM,GAFR,SAAS,GAAGA,KAAK;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGvB,CAEL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrG,GAAA,CA9dIF,KAAmC;EAAA,QACtBhB,WAAW,EAEGC,WAAW;AAAA;AAAAuL,EAAA,GAHtCxK,KAAmC;AAgezC,eAAeA,KAAK;AAAC,IAAAwK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}