const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

/**
 * Request/Response Logging Middleware
 * Logs all incoming requests and outgoing responses with detailed information
 */

const requestLogger = (req, res, next) => {
  // Generate unique request ID
  const requestId = uuidv4();
  req.requestId = requestId;
  
  // Record start time
  const startTime = Date.now();
  
  // Log incoming request
  logger.logRequest(req, requestId);
  
  // Store original res.json and res.send methods
  const originalJson = res.json;
  const originalSend = res.send;
  
  // Override res.json to capture response data
  res.json = function(data) {
    // Log response data (sanitized)
    const responseTime = Date.now() - startTime;
    
    // Log the response
    logger.logResponse(res, requestId, responseTime);
    
    // Log response body if in debug mode
    if (process.env.APP_LOG_LEVEL === 'DEBUG') {
      logger.debug('Response Body', {
        requestId: requestId,
        responseData: sanitizeResponseData(data)
      });
    }
    
    // Call original method
    return originalJson.call(this, data);
  };
  
  // Override res.send to capture response data
  res.send = function(data) {
    const responseTime = Date.now() - startTime;
    
    // Log the response
    logger.logResponse(res, requestId, responseTime);
    
    // Log response body if in debug mode
    if (process.env.APP_LOG_LEVEL === 'DEBUG') {
      logger.debug('Response Body', {
        requestId: requestId,
        responseData: sanitizeResponseData(data)
      });
    }
    
    // Call original method
    return originalSend.call(this, data);
  };
  
  // Handle response finish event
  res.on('finish', () => {
    const responseTime = Date.now() - startTime;
    
    // Log final response metrics
    logger.http('Request Completed', {
      requestId: requestId,
      method: req.method,
      url: req.originalUrl || req.url,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      contentLength: res.get('Content-Length') || 0,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress
    });
  });
  
  // Handle errors
  res.on('error', (error) => {
    logger.error('Response Error', {
      requestId: requestId,
      method: req.method,
      url: req.originalUrl || req.url,
      error: error.message,
      stack: error.stack
    });
  });
  
  next();
};

/**
 * Sanitize response data to remove sensitive information
 */
function sanitizeResponseData(data) {
  if (!data || typeof data !== 'object') return data;
  
  try {
    const sanitized = JSON.parse(JSON.stringify(data));
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'accessToken', 'refreshToken'];
    
    function recursiveSanitize(obj) {
      if (Array.isArray(obj)) {
        return obj.map(item => recursiveSanitize(item));
      } else if (obj && typeof obj === 'object') {
        const sanitizedObj = {};
        for (const [key, value] of Object.entries(obj)) {
          if (sensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
            sanitizedObj[key] = '[REDACTED]';
          } else {
            sanitizedObj[key] = recursiveSanitize(value);
          }
        }
        return sanitizedObj;
      }
      return obj;
    }
    
    return recursiveSanitize(sanitized);
  } catch (error) {
    return '[UNABLE_TO_SANITIZE]';
  }
}

/**
 * Error logging middleware
 */
const errorLogger = (error, req, res, next) => {
  logger.error('Unhandled Error', {
    requestId: req.requestId,
    method: req.method,
    url: req.originalUrl || req.url,
    error: error.message,
    stack: error.stack,
    userId: req.user?.id || 'anonymous'
  });
  
  next(error);
};

/**
 * Controller action logger helper
 */
const logControllerAction = (controller, action) => {
  return (req, res, next) => {
    logger.logControllerAction(controller, action, req.requestId, {
      userId: req.user?.id || 'anonymous',
      params: req.params,
      query: req.query
    });
    next();
  };
};

/**
 * Database operation logger helper
 */
const logDatabaseOperation = (operation, table, additionalData = {}) => {
  return (requestId) => {
    logger.logDatabase(operation, table, requestId, additionalData);
  };
};

module.exports = {
  requestLogger,
  errorLogger,
  logControllerAction,
  logDatabaseOperation
};
