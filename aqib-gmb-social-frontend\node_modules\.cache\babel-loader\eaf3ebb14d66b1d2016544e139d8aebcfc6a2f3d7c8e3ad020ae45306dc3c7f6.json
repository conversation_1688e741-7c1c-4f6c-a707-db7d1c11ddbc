{"ast": null, "code": "import { USER_ROLES } from \"../constants/endPoints.constant\";\nimport HttpHelperService from \"../services/httpHelper.service\";\nimport { AUTH_REQUESTED, AUTH_SUCCESS, AUTH_ERROR, AUTH_LOGOUT, AUTH_UNAUTHORIZED } from \"../constants/reducer.constant\";\nimport ApplicationHelperService from \"../services/helperService\";\nconst _applicationHelperService = new ApplicationHelperService({});\nexport const authInitiate = (payload, setLoading) => async dispatch => {\n  try {\n    const _httpHelperService = new HttpHelperService(dispatch);\n    setLoading(true); // Start loading\n    dispatch({\n      type: AUTH_REQUESTED,\n      payload: payload\n    });\n    _httpHelperService.login(payload).then(async response => {\n      let isLoginSuccess = response.message === \"Success\" && response.result !== null;\n      if (isLoginSuccess) {\n        const roleAccessResponse = await _httpHelperService.get(`${USER_ROLES(response.result.id)}`);\n        localStorage.setItem(\"MyLocoBiz_UserInfo\", JSON.stringify(response));\n        dispatch({\n          type: AUTH_SUCCESS,\n          payload: {\n            ...response,\n            rbAccess: roleAccessResponse.list[0]\n          }\n        });\n      } else {\n        dispatch({\n          type: AUTH_ERROR,\n          payload: response\n        });\n      }\n    }).catch(error => {\n      var _error$response, _error$response$data;\n      console.log(error);\n      dispatch({\n        type: AUTH_ERROR,\n        payload: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || \"Login failed\"\n      });\n    }).finally(() => {\n      setLoading(false); // Always stop loading\n    });\n  } catch (error) {\n    dispatch({\n      type: AUTH_ERROR,\n      payload: error\n    });\n    setLoading(false); // In case of try-block error\n  }\n};\nexport const updateUserData = payload => async dispatch => {\n  try {\n    dispatch({\n      type: AUTH_SUCCESS,\n      payload: payload\n    });\n  } catch (error) {\n    dispatch({\n      type: AUTH_ERROR,\n      payload: error\n    });\n  }\n};\nexport const logOut = () => dispatch => {\n  localStorage.removeItem(\"MyLocoBiz_UserInfo\");\n  dispatch({\n    type: AUTH_LOGOUT,\n    payload: null\n  });\n};\nexport const sessionExpired = () => dispatch => {\n  localStorage.removeItem(\"MyLocoBiz_UserInfo\");\n  dispatch({\n    type: AUTH_UNAUTHORIZED,\n    payload: {\n      message: \"\"\n    }\n  });\n};", "map": {"version": 3, "names": ["USER_ROLES", "HttpHelperService", "AUTH_REQUESTED", "AUTH_SUCCESS", "AUTH_ERROR", "AUTH_LOGOUT", "AUTH_UNAUTHORIZED", "ApplicationHelperService", "_applicationHelperService", "authInitiate", "payload", "setLoading", "dispatch", "_httpHelperService", "type", "login", "then", "response", "isLoginSuccess", "message", "result", "roleAccessResponse", "get", "id", "localStorage", "setItem", "JSON", "stringify", "rbAccess", "list", "catch", "error", "_error$response", "_error$response$data", "console", "log", "data", "finally", "updateUserData", "logOut", "removeItem", "sessionExpired"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/actions/auth.actions.tsx"], "sourcesContent": ["import { <PERSON><PERSON>GIN, USER_ROLES } from \"../constants/endPoints.constant\";\nimport { ILoginModel } from \"../interfaces/request/ILoginModel\";\nimport HttpHelperService from \"../services/httpHelper.service\";\nimport { useContext, createRef } from \"react\";\nimport {\n  AUTH_REQUESTED,\n  AUTH_SUCCESS,\n  AUTH_ERROR,\n  AUTH_LOGOUT,\n  AUTH_UNAUTHORIZED,\n} from \"../constants/reducer.constant\";\nimport axios from \"axios\";\nimport { Action, Dispatch } from \"redux\";\nimport { AnyAction } from \"redux\";\nimport ApplicationHelperService from \"../services/helperService\";\nimport { ISignInResponseModel } from \"../interfaces/response/ISignInResponseModel\";\n\nconst _applicationHelperService = new ApplicationHelperService({});\n\nexport const authInitiate =\n  (payload: ILoginModel, setLoading: (isLoading: boolean) => void) =>\n  async (dispatch: Dispatch<Action>) => {\n    try {\n      const _httpHelperService = new HttpHelperService(dispatch);\n      setLoading(true); // Start loading\n      dispatch({\n        type: AUTH_REQUESTED,\n        payload: payload,\n      });\n\n      _httpHelperService\n        .login(payload)\n        .then(async (response: ISignInResponseModel) => {\n          let isLoginSuccess =\n            response.message === \"Success\" && response.result !== null;\n\n          if (isLoginSuccess) {\n            const roleAccessResponse = await _httpHelperService.get(\n              `${USER_ROLES(response.result.id)}`\n            );\n\n            localStorage.setItem(\n              \"MyLocoBiz_UserInfo\",\n              JSON.stringify(response)\n            );\n\n            dispatch({\n              type: AUTH_SUCCESS,\n              payload: { ...response, rbAccess: roleAccessResponse.list[0] },\n            });\n          } else {\n            dispatch({\n              type: AUTH_ERROR,\n              payload: response,\n            });\n          }\n        })\n        .catch((error) => {\n          console.log(error);\n          dispatch({\n            type: AUTH_ERROR,\n            payload: error.response?.data?.message || \"Login failed\",\n          });\n        })\n        .finally(() => {\n          setLoading(false); // Always stop loading\n        });\n    } catch (error) {\n      dispatch({\n        type: AUTH_ERROR,\n        payload: error,\n      });\n      setLoading(false); // In case of try-block error\n    }\n  };\n\nexport const updateUserData =\n  (payload: any) => async (dispatch: Dispatch<Action>) => {\n    try {\n      dispatch({\n        type: AUTH_SUCCESS,\n        payload: payload,\n      });\n    } catch (error) {\n      dispatch({\n        type: AUTH_ERROR,\n        payload: error,\n      });\n    }\n  };\n\nexport const logOut = () => (dispatch: Dispatch) => {\n  localStorage.removeItem(\"MyLocoBiz_UserInfo\");\n  dispatch({\n    type: AUTH_LOGOUT,\n    payload: null,\n  });\n};\n\nexport const sessionExpired = () => (dispatch: Dispatch) => {\n  localStorage.removeItem(\"MyLocoBiz_UserInfo\");\n  dispatch({\n    type: AUTH_UNAUTHORIZED,\n    payload: { message: \"\" },\n  });\n};\n"], "mappings": "AAAA,SAAgBA,UAAU,QAAQ,iCAAiC;AAEnE,OAAOC,iBAAiB,MAAM,gCAAgC;AAE9D,SACEC,cAAc,EACdC,YAAY,EACZC,UAAU,EACVC,WAAW,EACXC,iBAAiB,QACZ,+BAA+B;AAItC,OAAOC,wBAAwB,MAAM,2BAA2B;AAGhE,MAAMC,yBAAyB,GAAG,IAAID,wBAAwB,CAAC,CAAC,CAAC,CAAC;AAElE,OAAO,MAAME,YAAY,GACvBA,CAACC,OAAoB,EAAEC,UAAwC,KAC/D,MAAOC,QAA0B,IAAK;EACpC,IAAI;IACF,MAAMC,kBAAkB,GAAG,IAAIZ,iBAAiB,CAACW,QAAQ,CAAC;IAC1DD,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAClBC,QAAQ,CAAC;MACPE,IAAI,EAAEZ,cAAc;MACpBQ,OAAO,EAAEA;IACX,CAAC,CAAC;IAEFG,kBAAkB,CACfE,KAAK,CAACL,OAAO,CAAC,CACdM,IAAI,CAAC,MAAOC,QAA8B,IAAK;MAC9C,IAAIC,cAAc,GAChBD,QAAQ,CAACE,OAAO,KAAK,SAAS,IAAIF,QAAQ,CAACG,MAAM,KAAK,IAAI;MAE5D,IAAIF,cAAc,EAAE;QAClB,MAAMG,kBAAkB,GAAG,MAAMR,kBAAkB,CAACS,GAAG,CACrD,GAAGtB,UAAU,CAACiB,QAAQ,CAACG,MAAM,CAACG,EAAE,CAAC,EACnC,CAAC;QAEDC,YAAY,CAACC,OAAO,CAClB,oBAAoB,EACpBC,IAAI,CAACC,SAAS,CAACV,QAAQ,CACzB,CAAC;QAEDL,QAAQ,CAAC;UACPE,IAAI,EAAEX,YAAY;UAClBO,OAAO,EAAE;YAAE,GAAGO,QAAQ;YAAEW,QAAQ,EAAEP,kBAAkB,CAACQ,IAAI,CAAC,CAAC;UAAE;QAC/D,CAAC,CAAC;MACJ,CAAC,MAAM;QACLjB,QAAQ,CAAC;UACPE,IAAI,EAAEV,UAAU;UAChBM,OAAO,EAAEO;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,CACDa,KAAK,CAAEC,KAAK,IAAK;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MAChBC,OAAO,CAACC,GAAG,CAACJ,KAAK,CAAC;MAClBnB,QAAQ,CAAC;QACPE,IAAI,EAAEV,UAAU;QAChBM,OAAO,EAAE,EAAAsB,eAAA,GAAAD,KAAK,CAACd,QAAQ,cAAAe,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBI,IAAI,cAAAH,oBAAA,uBAApBA,oBAAA,CAAsBd,OAAO,KAAI;MAC5C,CAAC,CAAC;IACJ,CAAC,CAAC,CACDkB,OAAO,CAAC,MAAM;MACb1B,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC;EACN,CAAC,CAAC,OAAOoB,KAAK,EAAE;IACdnB,QAAQ,CAAC;MACPE,IAAI,EAAEV,UAAU;MAChBM,OAAO,EAAEqB;IACX,CAAC,CAAC;IACFpB,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;EACrB;AACF,CAAC;AAEH,OAAO,MAAM2B,cAAc,GACxB5B,OAAY,IAAK,MAAOE,QAA0B,IAAK;EACtD,IAAI;IACFA,QAAQ,CAAC;MACPE,IAAI,EAAEX,YAAY;MAClBO,OAAO,EAAEA;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOqB,KAAK,EAAE;IACdnB,QAAQ,CAAC;MACPE,IAAI,EAAEV,UAAU;MAChBM,OAAO,EAAEqB;IACX,CAAC,CAAC;EACJ;AACF,CAAC;AAEH,OAAO,MAAMQ,MAAM,GAAGA,CAAA,KAAO3B,QAAkB,IAAK;EAClDY,YAAY,CAACgB,UAAU,CAAC,oBAAoB,CAAC;EAC7C5B,QAAQ,CAAC;IACPE,IAAI,EAAET,WAAW;IACjBK,OAAO,EAAE;EACX,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAM+B,cAAc,GAAGA,CAAA,KAAO7B,QAAkB,IAAK;EAC1DY,YAAY,CAACgB,UAAU,CAAC,oBAAoB,CAAC;EAC7C5B,QAAQ,CAAC;IACPE,IAAI,EAAER,iBAAiB;IACvBI,OAAO,EAAE;MAAES,OAAO,EAAE;IAAG;EACzB,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}