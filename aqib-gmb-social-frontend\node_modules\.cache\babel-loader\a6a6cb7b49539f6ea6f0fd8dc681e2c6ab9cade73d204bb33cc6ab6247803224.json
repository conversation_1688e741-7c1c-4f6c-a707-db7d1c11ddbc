{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\serviceAreaList\\\\serviceAreaList.component.tsx\";\nimport React from \"react\";\nimport { Box, Grid, Card, CardContent, Typography } from \"@mui/material\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ServiceAreaList = ({\n  placeInfos\n}) => {\n  const handleOpenMap = placeId => {\n    const url = `https://www.google.com/maps/place/?q=place_id:${placeId}`;\n    window.open(url, \"_blank\");\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      sx: {\n        mb: 2\n      },\n      children: \"Service Areas\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: placeInfos.map((place, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: \"100%\"\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                sx: {\n                  mr: 2\n                },\n                children: place.placeName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_c = ServiceAreaList;\nexport default ServiceAreaList;\nvar _c;\n$RefreshReg$(_c, \"ServiceAreaList\");", "map": {"version": 3, "names": ["React", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "jsxDEV", "_jsxDEV", "ServiceAreaList", "placeInfos", "handleOpenMap", "placeId", "url", "window", "open", "sx", "p", "children", "variant", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "map", "place", "index", "item", "xs", "sm", "md", "height", "display", "alignItems", "justifyContent", "mr", "placeName", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/serviceAreaList/serviceAreaList.component.tsx"], "sourcesContent": ["import React from \"react\";\nimport {\n  <PERSON>,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  Button,\n} from \"@mui/material\";\nimport NearMeOutlinedIcon from \"@mui/icons-material/NearMeOutlined\";\n\ninterface PlaceInfo {\n  placeName: string;\n  placeId: string;\n}\n\ninterface ServiceAreaProps {\n  placeInfos: PlaceInfo[];\n}\n\nconst ServiceAreaList: React.FC<ServiceAreaProps> = ({ placeInfos }) => {\n  const handleOpenMap = (placeId: string) => {\n    const url = `https://www.google.com/maps/place/?q=place_id:${placeId}`;\n    window.open(url, \"_blank\");\n  };\n\n  return (\n    <Box sx={{ p: 2 }}>\n      <Typography variant=\"h6\" sx={{ mb: 2 }}>\n        Service Areas\n      </Typography>\n      <Grid container spacing={2}>\n        {placeInfos.map((place, index) => (\n          <Grid item xs={12} sm={6} md={4} key={index}>\n            <Card sx={{ height: \"100%\" }}>\n              <CardContent>\n                <Box\n                  display=\"flex\"\n                  alignItems=\"center\"\n                  justifyContent=\"space-between\"\n                >\n                  <Typography variant=\"body1\" sx={{ mr: 2 }}>\n                    {place.placeName}\n                  </Typography>\n                  {/* <Button\n                    onClick={() => handleOpenMap(place.placeId)}\n                    variant=\"outlined\"\n                    className=\"emptyBtn editIconBtn\"\n                    startIcon={<NearMeOutlinedIcon />}\n                  /> */}\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n    </Box>\n  );\n};\n\nexport default ServiceAreaList;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,QAEL,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAYvB,MAAMC,eAA2C,GAAGA,CAAC;EAAEC;AAAW,CAAC,KAAK;EACtE,MAAMC,aAAa,GAAIC,OAAe,IAAK;IACzC,MAAMC,GAAG,GAAG,iDAAiDD,OAAO,EAAE;IACtEE,MAAM,CAACC,IAAI,CAACF,GAAG,EAAE,QAAQ,CAAC;EAC5B,CAAC;EAED,oBACEL,OAAA,CAACN,GAAG;IAACc,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAChBV,OAAA,CAACF,UAAU;MAACa,OAAO,EAAC,IAAI;MAACH,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,EAAC;IAExC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbhB,OAAA,CAACL,IAAI;MAACsB,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAR,QAAA,EACxBR,UAAU,CAACiB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC3BrB,OAAA,CAACL,IAAI;QAAC2B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eAC9BV,OAAA,CAACJ,IAAI;UAACY,EAAE,EAAE;YAAEkB,MAAM,EAAE;UAAO,CAAE;UAAAhB,QAAA,eAC3BV,OAAA,CAACH,WAAW;YAAAa,QAAA,eACVV,OAAA,CAACN,GAAG;cACFiC,OAAO,EAAC,MAAM;cACdC,UAAU,EAAC,QAAQ;cACnBC,cAAc,EAAC,eAAe;cAAAnB,QAAA,eAE9BV,OAAA,CAACF,UAAU;gBAACa,OAAO,EAAC,OAAO;gBAACH,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAApB,QAAA,EACvCU,KAAK,CAACW;cAAS;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GAnB6BK,KAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBrC,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACgB,EAAA,GAtCI/B,eAA2C;AAwCjD,eAAeA,eAAe;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}