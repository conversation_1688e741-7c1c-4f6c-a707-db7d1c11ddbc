{"ast": null, "code": "import { combineReducers } from \"redux\";\nimport { configureStore } from \"@reduxjs/toolkit\";\nimport { persistStore, persistReducer } from \"redux-persist\";\nimport storage from \"redux-persist/lib/storage\";\nimport authReducer from \"../reducers/auth.reducer\";\nimport userPreferencesReducer from \"../reducers/userPreferences.reducer\";\nconst persistConfig = {\n  key: \"root\",\n  storage\n};\nconst defaultMiddlewareConfig = {\n  serializableCheck: false\n};\nfunction configureAppStore(initialState = {}) {\n  const rootReducer = combineReducers({\n    authReducer,\n    userPreferencesReducer\n  });\n  const persistedReducer = persistReducer(persistConfig, rootReducer);\n  const store = configureStore({\n    reducer: persistReducer({\n      key: \"root\",\n      debug: true,\n      storage\n    }, persistedReducer),\n    middleware: getDefaultMiddleware => getDefaultMiddleware(defaultMiddlewareConfig)\n  });\n  console.log(\"initialState\", store.getState());\n  const persistor = persistStore(store, null, () => {\n    console.log(\"restoredState\", store.getState());\n  });\n  return {\n    store,\n    persistor\n  };\n}\nexport default configureAppStore;", "map": {"version": 3, "names": ["combineReducers", "configureStore", "persistStore", "persistReducer", "storage", "authReducer", "userPreferencesReducer", "persistConfig", "key", "defaultMiddlewareConfig", "serializableCheck", "configureAppStore", "initialState", "rootReducer", "persistedReducer", "store", "reducer", "debug", "middleware", "getDefaultMiddleware", "console", "log", "getState", "persistor"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/store/index.tsx"], "sourcesContent": ["import { combineReducers } from \"redux\";\nimport { configureStore } from \"@reduxjs/toolkit\";\nimport { persistStore, persistReducer } from \"redux-persist\";\nimport storage from \"redux-persist/lib/storage\";\nimport authReducer from \"../reducers/auth.reducer\";\nimport lookupReducer from \"../reducers/lookup.reducer\";\nimport userPreferencesReducer from \"../reducers/userPreferences.reducer\";\n\nconst persistConfig = {\n  key: \"root\",\n  storage,\n};\n\nconst defaultMiddlewareConfig = {\n  serializableCheck: false,\n};\n\nfunction configureAppStore(initialState = {}) {\n  const rootReducer = combineReducers({\n    authReducer,\n    userPreferencesReducer,\n  });\n  const persistedReducer = persistReducer(persistConfig, rootReducer);\n  const store = configureStore({\n    reducer: persistReducer(\n      {\n        key: \"root\",\n        debug: true,\n        storage,\n      },\n      persistedReducer\n    ),\n    middleware: (getDefaultMiddleware) =>\n      getDefaultMiddleware(defaultMiddlewareConfig),\n  });\n  console.log(\"initialState\", store.getState());\n\n  const persistor = persistStore(store, null, () => {\n    console.log(\"restoredState\", store.getState());\n  });\n\n  return { store, persistor };\n}\n\nexport default configureAppStore;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,OAAO;AACvC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,EAAEC,cAAc,QAAQ,eAAe;AAC5D,OAAOC,OAAO,MAAM,2BAA2B;AAC/C,OAAOC,WAAW,MAAM,0BAA0B;AAElD,OAAOC,sBAAsB,MAAM,qCAAqC;AAExE,MAAMC,aAAa,GAAG;EACpBC,GAAG,EAAE,MAAM;EACXJ;AACF,CAAC;AAED,MAAMK,uBAAuB,GAAG;EAC9BC,iBAAiB,EAAE;AACrB,CAAC;AAED,SAASC,iBAAiBA,CAACC,YAAY,GAAG,CAAC,CAAC,EAAE;EAC5C,MAAMC,WAAW,GAAGb,eAAe,CAAC;IAClCK,WAAW;IACXC;EACF,CAAC,CAAC;EACF,MAAMQ,gBAAgB,GAAGX,cAAc,CAACI,aAAa,EAAEM,WAAW,CAAC;EACnE,MAAME,KAAK,GAAGd,cAAc,CAAC;IAC3Be,OAAO,EAAEb,cAAc,CACrB;MACEK,GAAG,EAAE,MAAM;MACXS,KAAK,EAAE,IAAI;MACXb;IACF,CAAC,EACDU,gBACF,CAAC;IACDI,UAAU,EAAGC,oBAAoB,IAC/BA,oBAAoB,CAACV,uBAAuB;EAChD,CAAC,CAAC;EACFW,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEN,KAAK,CAACO,QAAQ,CAAC,CAAC,CAAC;EAE7C,MAAMC,SAAS,GAAGrB,YAAY,CAACa,KAAK,EAAE,IAAI,EAAE,MAAM;IAChDK,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEN,KAAK,CAACO,QAAQ,CAAC,CAAC,CAAC;EAChD,CAAC,CAAC;EAEF,OAAO;IAAEP,KAAK;IAAEQ;EAAU,CAAC;AAC7B;AAEA,eAAeZ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}