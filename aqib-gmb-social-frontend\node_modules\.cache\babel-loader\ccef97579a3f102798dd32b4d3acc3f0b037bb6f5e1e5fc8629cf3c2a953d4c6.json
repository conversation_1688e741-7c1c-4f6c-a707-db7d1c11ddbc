{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\createPost\\\\createPostTemplates\\\\cards\\\\testimonialCard1\\\\testimonialCard1.component.tsx\";\nimport React from \"react\";\nimport { Box, Typography } from \"@mui/material\";\nimport RatingsStar from \"../../../../ratingsStar/ratingsStar.component\";\nimport UserAvatar from \"../../../../userAvatar/userAvatar.component\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestimonialCard1 = props => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    ref: props.divRef,\n    sx: {\n      height: \"100%\",\n      width: \"100%\",\n      padding: \"30px\",\n      // backgroundColor: `${props.templateConfig.backgroundColor}`, // Yellow background\n      background: `${props.templateConfig.backgroundColor}`,\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      textAlign: \"center\",\n      boxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.2)\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        height: \"300px\",\n        width: \"300px\",\n        padding: \"30px\",\n        backgroundColor: \"#ffffff\",\n        borderRadius: \"50%\",\n        position: \"relative\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        textAlign: \"center\",\n        boxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.2)\",\n        overflow: \"hidden\"\n      },\n      children: [props.templateConfig.showAvatar && /*#__PURE__*/_jsxDEV(UserAvatar, {\n        profileImage: props.templateConfig.reviewerImage,\n        fullname: props.templateConfig.reviewerName,\n        style: {\n          width: 60,\n          height: 60,\n          margin: \"0 auto 10px\",\n          background: \"linear-gradient(45deg, #42A5F5, #64B5F6)\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 11\n      }, this), props.templateConfig.showRating && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"center\",\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(RatingsStar, {\n          starRating: props.templateConfig.starRating\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          fontSize: \"14px\",\n          lineHeight: \"1.6\",\n          color: `${props.templateConfig.fontColor}`\n        },\n        children: props.templateConfig.comment\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          fontSize: \"16px\",\n          fontWeight: 600,\n          marginTop: \"20px\",\n          color: \"#0056A6\"\n        },\n        children: [\"\\u2014 \", props.templateConfig.reviewerName]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_c = TestimonialCard1;\nexport default TestimonialCard1;\nvar _c;\n$RefreshReg$(_c, \"TestimonialCard1\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "RatingsStar", "UserAvatar", "jsxDEV", "_jsxDEV", "TestimonialCard1", "props", "ref", "divRef", "sx", "height", "width", "padding", "background", "templateConfig", "backgroundColor", "display", "flexDirection", "alignItems", "justifyContent", "textAlign", "boxShadow", "children", "borderRadius", "position", "overflow", "showAvatar", "profileImage", "reviewerImage", "fullname", "reviewerName", "style", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showRating", "mb", "starRating", "variant", "fontSize", "lineHeight", "color", "fontColor", "comment", "fontWeight", "marginTop", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/createPost/createPostTemplates/cards/testimonialCard1/testimonialCard1.component.tsx"], "sourcesContent": ["import React from \"react\";\nimport { Box, Typography } from \"@mui/material\";\nimport { IPostTemplateConfig } from \"../../../../../types/IPostTemplateConfig\";\nimport { ref } from \"yup\";\nimport RatingsStar from \"../../../../ratingsStar/ratingsStar.component\";\nimport UserAvatar from \"../../../../userAvatar/userAvatar.component\";\n\nconst TestimonialCard1 = (props: {\n  templateConfig: IPostTemplateConfig;\n  divRef: any;\n}) => {\n  return (\n    <Box\n      ref={props.divRef}\n      sx={{\n        height: \"100%\",\n        width: \"100%\",\n        padding: \"30px\",\n        // backgroundColor: `${props.templateConfig.backgroundColor}`, // Yellow background\n        background: `${props.templateConfig.backgroundColor}`,\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        textAlign: \"center\",\n        boxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.2)\",\n      }}\n    >\n      <Box\n        sx={{\n          height: \"300px\",\n          width: \"300px\",\n          padding: \"30px\",\n          backgroundColor: \"#ffffff\",\n          borderRadius: \"50%\",\n          position: \"relative\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          alignItems: \"center\",\n          justifyContent: \"center\",\n          textAlign: \"center\",\n          boxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.2)\",\n          overflow: \"hidden\",\n        }}\n      >\n        {/* Avatar */}\n        {props.templateConfig.showAvatar && (\n          <UserAvatar\n            profileImage={props.templateConfig.reviewerImage}\n            fullname={props.templateConfig.reviewerName}\n            style={{\n              width: 60,\n              height: 60,\n              margin: \"0 auto 10px\",\n              background: \"linear-gradient(45deg, #42A5F5, #64B5F6)\",\n            }}\n          />\n        )}\n\n        {/* Star Rating */}\n        {props.templateConfig.showRating && (\n          <Box\n            sx={{\n              display: \"flex\",\n              justifyContent: \"center\",\n              mb: 2,\n            }}\n          >\n            <RatingsStar starRating={props.templateConfig.starRating} />\n          </Box>\n        )}\n\n        {/* Testimonial Text */}\n        <Typography\n          variant=\"body1\"\n          sx={{\n            fontSize: \"14px\",\n            lineHeight: \"1.6\",\n            color: `${props.templateConfig.fontColor}`,\n          }}\n        >\n          {props.templateConfig.comment}\n        </Typography>\n\n        {/* Author Section */}\n        <Typography\n          variant=\"h6\"\n          sx={{\n            fontSize: \"16px\",\n            fontWeight: 600,\n            marginTop: \"20px\",\n            color: \"#0056A6\",\n          }}\n        >\n          — {props.templateConfig.reviewerName}\n        </Typography>\n      </Box>\n    </Box>\n  );\n};\n\nexport default TestimonialCard1;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,QAAQ,eAAe;AAG/C,OAAOC,WAAW,MAAM,+CAA+C;AACvE,OAAOC,UAAU,MAAM,6CAA6C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,gBAAgB,GAAIC,KAGzB,IAAK;EACJ,oBACEF,OAAA,CAACL,GAAG;IACFQ,GAAG,EAAED,KAAK,CAACE,MAAO;IAClBC,EAAE,EAAE;MACFC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,MAAM;MACf;MACAC,UAAU,EAAE,GAAGP,KAAK,CAACQ,cAAc,CAACC,eAAe,EAAE;MACrDC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE;IACb,CAAE;IAAAC,QAAA,eAEFlB,OAAA,CAACL,GAAG;MACFU,EAAE,EAAE;QACFC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,MAAM;QACfG,eAAe,EAAE,SAAS;QAC1BQ,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE,UAAU;QACpBR,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE,iCAAiC;QAC5CI,QAAQ,EAAE;MACZ,CAAE;MAAAH,QAAA,GAGDhB,KAAK,CAACQ,cAAc,CAACY,UAAU,iBAC9BtB,OAAA,CAACF,UAAU;QACTyB,YAAY,EAAErB,KAAK,CAACQ,cAAc,CAACc,aAAc;QACjDC,QAAQ,EAAEvB,KAAK,CAACQ,cAAc,CAACgB,YAAa;QAC5CC,KAAK,EAAE;UACLpB,KAAK,EAAE,EAAE;UACTD,MAAM,EAAE,EAAE;UACVsB,MAAM,EAAE,aAAa;UACrBnB,UAAU,EAAE;QACd;MAAE;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF,EAGA9B,KAAK,CAACQ,cAAc,CAACuB,UAAU,iBAC9BjC,OAAA,CAACL,GAAG;QACFU,EAAE,EAAE;UACFO,OAAO,EAAE,MAAM;UACfG,cAAc,EAAE,QAAQ;UACxBmB,EAAE,EAAE;QACN,CAAE;QAAAhB,QAAA,eAEFlB,OAAA,CAACH,WAAW;UAACsC,UAAU,EAAEjC,KAAK,CAACQ,cAAc,CAACyB;QAAW;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CACN,eAGDhC,OAAA,CAACJ,UAAU;QACTwC,OAAO,EAAC,OAAO;QACf/B,EAAE,EAAE;UACFgC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBC,KAAK,EAAE,GAAGrC,KAAK,CAACQ,cAAc,CAAC8B,SAAS;QAC1C,CAAE;QAAAtB,QAAA,EAEDhB,KAAK,CAACQ,cAAc,CAAC+B;MAAO;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eAGbhC,OAAA,CAACJ,UAAU;QACTwC,OAAO,EAAC,IAAI;QACZ/B,EAAE,EAAE;UACFgC,QAAQ,EAAE,MAAM;UAChBK,UAAU,EAAE,GAAG;UACfC,SAAS,EAAE,MAAM;UACjBJ,KAAK,EAAE;QACT,CAAE;QAAArB,QAAA,GACH,SACG,EAAChB,KAAK,CAACQ,cAAc,CAACgB,YAAY;MAAA;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACY,EAAA,GA5FI3C,gBAAgB;AA8FtB,eAAeA,gBAAgB;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}