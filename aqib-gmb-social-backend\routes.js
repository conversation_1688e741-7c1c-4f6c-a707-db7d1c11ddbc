const express = require("express");
const router = express.Router();

//Routes
const authRoutes = require("./routes/auth");
const userRoutes = require("./routes/user");
const gmbRoutes = require("./routes/gmb");
const businessRoutes = require("./routes/business");
const locationsRoutes = require("./routes/locations");
const reviewRoutes = require("./routes/reviews");
const reviewSettingsRoutes = require("./routes/reviewSettings");
const QandARoutes = require("./routes/QandA");
const roleRoutes = require("./routes/role");
const performanceRoutes = require("./routes/locationMetrics");
const postRoutes = require("./routes/post");
const geoGridRoutes = require("./routes/geoGrid");
const manageAssetsRoutes = require("./routes/manageAssets");

router.get("/", async (req, res) => {
  res.send({ app_version: process.env.APP_VER_PREFIX });
});

router.use("/auth", authRoutes);
router.use("/user", userRoutes);
router.use("/gmb", gmbRoutes);
router.use("/business", businessRoutes);
router.use("/locations", locationsRoutes);
router.use("/gmb-reviews", reviewRoutes);
router.use("/review-settings", reviewSettingsRoutes);
router.use("/gmb-QandA", QandARoutes);
router.use("/role", roleRoutes);
router.use("/performance", performanceRoutes);
router.use("/post", postRoutes);
router.use("/geo-grid", geoGridRoutes);
router.use("/manage-assets", manageAssetsRoutes);

module.exports = router;
