{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\categoryDisplay\\\\categoryDisplay.component.tsx\";\nimport React from \"react\";\nimport { Card, CardContent, Typography, Accordion, AccordionSummary, AccordionDetails, Chip } from \"@mui/material\";\nimport ExpandMoreIcon from \"@mui/icons-material/ExpandMore\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoryDisplay = props => {\n  const {\n    primaryCategory,\n    additionalCategories\n  } = props.categories;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [primaryCategory && primaryCategory.serviceTypes && /*#__PURE__*/_jsxDEV(Card, {\n      className: \"\",\n      variant: \"outlined\",\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: [\"Primary Category: \", primaryCategory.displayName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n          expanded: true,\n          className: \"commonBorderCard\",\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 45\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              children: \"Service Types\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                flexWrap: \"wrap\",\n                gap: \"8px\"\n              },\n              children: primaryCategory && primaryCategory.serviceTypes && primaryCategory.serviceTypes.map(service => /*#__PURE__*/_jsxDEV(Chip, {\n                label: service.displayName,\n                variant: \"outlined\"\n              }, service.serviceTypeId, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n          expanded: true,\n          className: \"commonBorderCard\",\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 45\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              children: \"More Hours Types\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                flexWrap: \"wrap\",\n                gap: \"8px\"\n              },\n              children: primaryCategory.moreHoursTypes.map(hours => /*#__PURE__*/_jsxDEV(Chip, {\n                label: hours.displayName,\n                variant: \"outlined\"\n              }, hours.hoursTypeId, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 9\n    }, this), additionalCategories && /*#__PURE__*/_jsxDEV(Card, {\n      variant: \"outlined\",\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Additional Categories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            flexWrap: \"wrap\",\n            gap: \"8px\"\n          },\n          children: additionalCategories.map((category, idx) => /*#__PURE__*/_jsxDEV(Chip, {\n            label: category.displayName,\n            variant: \"outlined\"\n          }, category.displayName, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_c = CategoryDisplay;\nexport default CategoryDisplay;\nvar _c;\n$RefreshReg$(_c, \"CategoryDisplay\");", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Accordion", "AccordionSummary", "AccordionDetails", "Chip", "ExpandMoreIcon", "jsxDEV", "_jsxDEV", "CategoryDisplay", "props", "primaryCategory", "additionalCategories", "categories", "children", "serviceTypes", "className", "variant", "sx", "mb", "gutterBottom", "displayName", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "expanded", "expandIcon", "style", "display", "flexWrap", "gap", "map", "service", "label", "serviceTypeId", "moreHoursTypes", "hours", "hoursTypeId", "category", "idx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/categoryDisplay/categoryDisplay.component.tsx"], "sourcesContent": ["import React from \"react\";\nimport {\n  Card,\n  CardContent,\n  Typography,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  List,\n  ListItem,\n  ListItemText,\n  Divider,\n  Chip,\n} from \"@mui/material\";\nimport ExpandMoreIcon from \"@mui/icons-material/ExpandMore\";\n\nconst CategoryDisplay = (props: { categories: any }) => {\n  const { primaryCategory, additionalCategories } = props.categories;\n\n  return (\n    <div>\n      {/* Primary Category */}\n      {primaryCategory && primaryCategory.serviceTypes && (\n        <Card className=\"\" variant=\"outlined\" sx={{ mb: 4 }}>\n          <CardContent>\n            <Typography variant=\"h5\" gutterBottom>\n              Primary Category: {primaryCategory.displayName}\n            </Typography>\n\n            <Accordion expanded className=\"commonBorderCard\">\n              <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n                <Typography>Service Types</Typography>\n              </AccordionSummary>\n              <AccordionDetails>\n                <div style={{ display: \"flex\", flexWrap: \"wrap\", gap: \"8px\" }}>\n                  {primaryCategory &&\n                    primaryCategory.serviceTypes &&\n                    primaryCategory.serviceTypes.map((service: any) => (\n                      <Chip\n                        key={service.serviceTypeId}\n                        label={service.displayName}\n                        variant=\"outlined\"\n                      />\n                    ))}\n                </div>\n              </AccordionDetails>\n            </Accordion>\n\n            <Accordion expanded className=\"commonBorderCard\">\n              <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n                <Typography>More Hours Types</Typography>\n              </AccordionSummary>\n              <AccordionDetails>\n                <div style={{ display: \"flex\", flexWrap: \"wrap\", gap: \"8px\" }}>\n                  {primaryCategory.moreHoursTypes.map((hours: any) => (\n                    <Chip\n                      key={hours.hoursTypeId}\n                      label={hours.displayName}\n                      variant=\"outlined\"\n                    />\n                  ))}\n                </div>\n              </AccordionDetails>\n            </Accordion>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Additional Categories */}\n      {additionalCategories && (\n        <Card variant=\"outlined\" sx={{ mb: 4 }}>\n          <CardContent>\n            <Typography variant=\"h5\" gutterBottom>\n              Additional Categories\n            </Typography>\n            <div style={{ display: \"flex\", flexWrap: \"wrap\", gap: \"8px\" }}>\n              {additionalCategories.map((category: any, idx: number) => (\n                <Chip\n                  key={category.displayName}\n                  label={category.displayName}\n                  variant=\"outlined\"\n                />\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* {additionalCategories.map((category: any, idx: number) => (\n        <Card key={category.name} sx={{ mb: 3 }}>\n          <CardContent>\n            <Typography variant=\"h6\">{category.displayName}</Typography>\n            <Divider sx={{ my: 1 }} />\n            <List dense>\n              {category.moreHoursTypes.map((hours: any) => (\n                <ListItem key={hours.hoursTypeId}>\n                  <ListItemText primary={hours.displayName} />\n                </ListItem>\n              ))}\n            </List>\n          </CardContent>\n        </Card>\n      ))} */}\n    </div>\n  );\n};\n\nexport default CategoryDisplay;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAKhBC,IAAI,QACC,eAAe;AACtB,OAAOC,cAAc,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,eAAe,GAAIC,KAA0B,IAAK;EACtD,MAAM;IAAEC,eAAe;IAAEC;EAAqB,CAAC,GAAGF,KAAK,CAACG,UAAU;EAElE,oBACEL,OAAA;IAAAM,QAAA,GAEGH,eAAe,IAAIA,eAAe,CAACI,YAAY,iBAC9CP,OAAA,CAACT,IAAI;MAACiB,SAAS,EAAC,EAAE;MAACC,OAAO,EAAC,UAAU;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eAClDN,OAAA,CAACR,WAAW;QAAAc,QAAA,gBACVN,OAAA,CAACP,UAAU;UAACgB,OAAO,EAAC,IAAI;UAACG,YAAY;UAAAN,QAAA,GAAC,oBAClB,EAACH,eAAe,CAACU,WAAW;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eAEbjB,OAAA,CAACN,SAAS;UAACwB,QAAQ;UAACV,SAAS,EAAC,kBAAkB;UAAAF,QAAA,gBAC9CN,OAAA,CAACL,gBAAgB;YAACwB,UAAU,eAAEnB,OAAA,CAACF,cAAc;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAX,QAAA,eAC/CN,OAAA,CAACP,UAAU;cAAAa,QAAA,EAAC;YAAa;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACnBjB,OAAA,CAACJ,gBAAgB;YAAAU,QAAA,eACfN,OAAA;cAAKoB,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,QAAQ,EAAE,MAAM;gBAAEC,GAAG,EAAE;cAAM,CAAE;cAAAjB,QAAA,EAC3DH,eAAe,IACdA,eAAe,CAACI,YAAY,IAC5BJ,eAAe,CAACI,YAAY,CAACiB,GAAG,CAAEC,OAAY,iBAC5CzB,OAAA,CAACH,IAAI;gBAEH6B,KAAK,EAAED,OAAO,CAACZ,WAAY;gBAC3BJ,OAAO,EAAC;cAAU,GAFbgB,OAAO,CAACE,aAAa;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAG3B,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEZjB,OAAA,CAACN,SAAS;UAACwB,QAAQ;UAACV,SAAS,EAAC,kBAAkB;UAAAF,QAAA,gBAC9CN,OAAA,CAACL,gBAAgB;YAACwB,UAAU,eAAEnB,OAAA,CAACF,cAAc;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAX,QAAA,eAC/CN,OAAA,CAACP,UAAU;cAAAa,QAAA,EAAC;YAAgB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACnBjB,OAAA,CAACJ,gBAAgB;YAAAU,QAAA,eACfN,OAAA;cAAKoB,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,QAAQ,EAAE,MAAM;gBAAEC,GAAG,EAAE;cAAM,CAAE;cAAAjB,QAAA,EAC3DH,eAAe,CAACyB,cAAc,CAACJ,GAAG,CAAEK,KAAU,iBAC7C7B,OAAA,CAACH,IAAI;gBAEH6B,KAAK,EAAEG,KAAK,CAAChB,WAAY;gBACzBJ,OAAO,EAAC;cAAU,GAFboB,KAAK,CAACC,WAAW;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGvB,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP,EAGAb,oBAAoB,iBACnBJ,OAAA,CAACT,IAAI;MAACkB,OAAO,EAAC,UAAU;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACrCN,OAAA,CAACR,WAAW;QAAAc,QAAA,gBACVN,OAAA,CAACP,UAAU;UAACgB,OAAO,EAAC,IAAI;UAACG,YAAY;UAAAN,QAAA,EAAC;QAEtC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjB,OAAA;UAAKoB,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,QAAQ,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAM,CAAE;UAAAjB,QAAA,EAC3DF,oBAAoB,CAACoB,GAAG,CAAC,CAACO,QAAa,EAAEC,GAAW,kBACnDhC,OAAA,CAACH,IAAI;YAEH6B,KAAK,EAAEK,QAAQ,CAAClB,WAAY;YAC5BJ,OAAO,EAAC;UAAU,GAFbsB,QAAQ,CAAClB,WAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAG1B,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAiBE,CAAC;AAEV,CAAC;AAACgB,EAAA,GAzFIhC,eAAe;AA2FrB,eAAeA,eAAe;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}