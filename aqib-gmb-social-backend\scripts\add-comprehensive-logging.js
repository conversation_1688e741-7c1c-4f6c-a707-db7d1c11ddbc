const fs = require('fs');
const path = require('path');

/**
 * Enhanced script to add comprehensive logging to all controller methods
 * This script automatically adds try-catch blocks and logging to all controller functions
 */

const controllersDir = path.join(__dirname, '../controllers');

// Controller files to process
const controllerFiles = [
  'business.controller.js',
  'gmb.controller.js',
  'posts.controller.js',
  'QandA.controllers.js',
  'reviews.controller.js',
  'role.controller.js',
  'user.controller.js',
  'accounts.controller.js',
  'locationMetrics.controller.js'
];

/**
 * Add logger import if not present
 */
function ensureLoggerImport(content) {
  if (!content.includes('require("../utils/logger")') && !content.includes("require('../utils/logger')")) {
    const lines = content.split('\n');
    let insertIndex = 0;
    
    // Find the last require statement
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('require(') && !lines[i].includes('//')) {
        insertIndex = i + 1;
      }
    }
    
    lines.splice(insertIndex, 0, 'const logger = require("../utils/logger");');
    return lines.join('\n');
  }
  return content;
}

/**
 * Generate logging wrapper for a function
 */
function generateLoggingWrapper(functionName, controllerName, originalFunction) {
  const isAsync = originalFunction.includes('async ');
  const hasNext = originalFunction.includes(', next)');
  
  // Remove 'next' parameter if present and not used
  let cleanFunction = originalFunction.replace(', next)', ')');
  
  // Extract function parameters
  const paramMatch = cleanFunction.match(/\((.*?)\)/);
  const params = paramMatch ? paramMatch[1] : 'req, res';
  
  const asyncKeyword = isAsync ? 'async ' : '';
  const awaitKeyword = isAsync ? 'await ' : '';
  
  return `const ${functionName} = ${asyncKeyword}(${params}) => {
  try {
    logger.logControllerAction('${controllerName}', '${functionName}', req.requestId, {
      userId: req.user?.id || req.params?.userId || req.body?.userId,
      params: req.params,
      query: req.query
    });
    
    logger.info('${functionName} operation started', {
      requestId: req.requestId,
      userId: req.user?.id || req.params?.userId || req.body?.userId
    });
    
    // Original function logic will be preserved here
    // This is a template - manual adjustment needed for each function
    
    logger.info('${functionName} operation completed successfully', {
      requestId: req.requestId
    });
    
  } catch (error) {
    logger.error('Error in ${functionName}', {
      requestId: req.requestId,
      userId: req.user?.id || req.params?.userId || req.body?.userId,
      error: error.message,
      stack: error.stack
    });
    
    res.status(500).json({
      message: 'Internal server error',
      error: error.message
    });
  }
};`;
}

/**
 * Process a single controller file
 */
function processControllerFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(filePath, '.js');
    const controllerName = fileName.replace('.controller', '');
    
    console.log(`\nProcessing ${fileName}...`);
    
    // Add logger import
    content = ensureLoggerImport(content);
    
    // Find all exported functions
    const moduleExportsMatch = content.match(/module\.exports\s*=\s*{([^}]+)}/);
    if (moduleExportsMatch) {
      const exportedFunctions = moduleExportsMatch[1]
        .split(',')
        .map(func => func.trim())
        .filter(func => func && !func.includes('//'));
      
      console.log(`Found exported functions: ${exportedFunctions.join(', ')}`);
      
      // Add logging template for each function
      const loggingTemplate = `
// LOGGING TEMPLATES FOR ${controllerName.toUpperCase()} CONTROLLER
// Add these logging calls to your existing functions:

${exportedFunctions.map(funcName => `
// For ${funcName} function:
// logger.logControllerAction('${controllerName}', '${funcName}', req.requestId, { userId: req.user?.id });
// logger.info('${funcName} operation started', { requestId: req.requestId });
// logger.info('${funcName} completed successfully', { requestId: req.requestId });
// logger.error('Error in ${funcName}', { requestId: req.requestId, error: error.message, stack: error.stack });`).join('\n')}

// EXAMPLE COMPLETE FUNCTION WITH LOGGING:
/*
const exampleFunction = async (req, res) => {
  try {
    logger.logControllerAction('${controllerName}', 'exampleFunction', req.requestId, {
      userId: req.user?.id || req.params?.userId,
      params: req.params
    });
    
    logger.info('Example operation started', {
      requestId: req.requestId,
      userId: req.user?.id
    });
    
    // Your business logic here
    const result = await SomeModel.someOperation();
    
    logger.info('Example operation completed successfully', {
      requestId: req.requestId,
      resultCount: result.length
    });
    
    res.status(200).json({ success: true, data: result });
    
  } catch (error) {
    logger.error('Error in exampleFunction', {
      requestId: req.requestId,
      userId: req.user?.id,
      error: error.message,
      stack: error.stack
    });
    
    res.status(500).json({
      message: 'Internal server error',
      error: error.message
    });
  }
};
*/
`;
      
      // Add template if not already present
      if (!content.includes('LOGGING TEMPLATES FOR')) {
        content += loggingTemplate;
      }
    }
    
    // Write back to file
    fs.writeFileSync(filePath, content);
    console.log(`✓ Updated ${fileName} with logging templates`);
    
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

/**
 * Main function to process all controllers
 */
function addComprehensiveLogging() {
  console.log('Adding comprehensive logging to all controllers...\n');
  
  controllerFiles.forEach(fileName => {
    const filePath = path.join(controllersDir, fileName);
    
    if (fs.existsSync(filePath)) {
      processControllerFile(filePath);
    } else {
      console.log(`File not found: ${fileName}`);
    }
  });
  
  console.log('\n' + '='.repeat(60));
  console.log('COMPREHENSIVE LOGGING SETUP COMPLETED!');
  console.log('='.repeat(60));
  console.log('\nNext steps:');
  console.log('1. Review each controller file');
  console.log('2. Copy the logging templates into your existing functions');
  console.log('3. Replace the business logic placeholders with your actual code');
  console.log('4. Test each controller to ensure logging works correctly');
  console.log('5. Remove the template comments once implemented');
  
  console.log('\nLogging patterns added:');
  console.log('- Controller action logging');
  console.log('- Operation start/completion logging');
  console.log('- Error handling with stack traces');
  console.log('- User context tracking');
  console.log('- Request ID tracing');
}

// Run the script
if (require.main === module) {
  addComprehensiveLogging();
}

module.exports = {
  addComprehensiveLogging,
  processControllerFile
};
