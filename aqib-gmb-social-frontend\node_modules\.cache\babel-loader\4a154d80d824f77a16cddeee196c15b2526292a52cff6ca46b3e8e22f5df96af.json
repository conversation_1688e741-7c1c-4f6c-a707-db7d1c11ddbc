{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\createPost\\\\createPostTemplates\\\\cards\\\\testimonialCard5\\\\testimonialCard5.component.tsx\";\nimport React from \"react\";\nimport { Box, Typography, Container, CssBaseline } from \"@mui/material\";\nimport UserAvatar from \"../../../../userAvatar/userAvatar.component\";\nimport RatingsStar from \"../../../../ratingsStar/ratingsStar.component\";\n\n//Css Import\nimport \"../testimonialCard5/testimonialCard5.component.style.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestimonialCard5 = props => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      background: `${props.templateConfig.backgroundColor} url(${require(\"../../../../../assets/feedbackBackgrouns/5.png\")}) no-repeat center/cover`,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\",\n      backgroundRepeat: \"no-repeat\",\n      color: \"#fff\",\n      textAlign: \"center\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      height: \"100%\",\n      width: \"100%\",\n      border: 1,\n      borderColor: \"black\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          className: \"testimonialInnerFive\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: \"quoteMark\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: require(\"../../../../../assets/feedbackBackgrouns/5Quote.png\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"marB20\",\n              children: props.templateConfig.showRating && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  justifyContent: \"center\",\n                  mb: 2\n                },\n                children: /*#__PURE__*/_jsxDEV(RatingsStar, {\n                  starRating: props.templateConfig.starRating\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              mb: 2,\n              sx: {\n                color: `${props.templateConfig.fontColor}`\n              },\n              children: props.templateConfig.comment\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"testmonialUserInfo\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: props.templateConfig.showAvatar && /*#__PURE__*/_jsxDEV(UserAvatar, {\n                  profileImage: props.templateConfig.reviewerImage,\n                  fullname: props.templateConfig.reviewerName,\n                  style: {\n                    width: 60,\n                    height: 60,\n                    margin: \"0 auto 10px\",\n                    background: \"linear-gradient(45deg, #42A5F5, #64B5F6)\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  className: \"testmonialUserName\",\n                  children: props.templateConfig.reviewerName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_c = TestimonialCard5;\nexport default TestimonialCard5;\nvar _c;\n$RefreshReg$(_c, \"TestimonialCard5\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Container", "CssBaseline", "UserAvatar", "RatingsStar", "jsxDEV", "_jsxDEV", "TestimonialCard5", "props", "sx", "background", "templateConfig", "backgroundColor", "require", "backgroundSize", "backgroundPosition", "backgroundRepeat", "color", "textAlign", "display", "flexDirection", "justifyContent", "alignItems", "height", "width", "border", "borderColor", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "src", "showRating", "mb", "starRating", "variant", "fontColor", "comment", "showAvatar", "profileImage", "reviewerImage", "fullname", "reviewerName", "style", "margin", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/createPost/createPostTemplates/cards/testimonialCard5/testimonialCard5.component.tsx"], "sourcesContent": ["import React from \"react\";\nimport {\n  Box,\n  Typography,\n  Avatar,\n  Rating,\n  Container,\n  CssBaseline,\n} from \"@mui/material\";\nimport { IPostTemplateConfig } from \"../../../../../types/IPostTemplateConfig\";\nimport { ref } from \"yup\";\nimport StarIcon from \"@mui/icons-material/Star\";\nimport UserAvatar from \"../../../../userAvatar/userAvatar.component\";\nimport RatingsStar from \"../../../../ratingsStar/ratingsStar.component\";\n\n//Css Import\nimport \"../testimonialCard5/testimonialCard5.component.style.css\";\n\nconst TestimonialCard5 = (props: {\n  templateConfig: IPostTemplateConfig;\n  divRef: any;\n}) => {\n  return (\n    <Box\n      sx={{\n        background: `${\n          props.templateConfig.backgroundColor\n        } url(${require(\"../../../../../assets/feedbackBackgrouns/5.png\")}) no-repeat center/cover`,\n        backgroundSize: \"cover\",\n        backgroundPosition: \"center\",\n        backgroundRepeat: \"no-repeat\",\n        color: \"#fff\",\n        textAlign: \"center\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        height: \"100%\",\n        width: \"100%\",\n        border: 1,\n        borderColor: \"black\",\n      }}\n    >\n      <Container>\n        <CssBaseline />\n        <Box>\n          {/* Main Content */}\n          <Box className=\"testimonialInnerFive\">\n            <Box className=\"quoteMark\">\n              <img\n                src={require(\"../../../../../assets/feedbackBackgrouns/5Quote.png\")}\n              />\n            </Box>\n            {/* Avatar and Name */}\n            <Box>\n              {/* Rating */}\n              <Box className=\"marB20\">\n                {props.templateConfig.showRating && (\n                  <Box\n                    sx={{\n                      display: \"flex\",\n                      justifyContent: \"center\",\n                      mb: 2,\n                    }}\n                  >\n                    <RatingsStar starRating={props.templateConfig.starRating} />\n                  </Box>\n                )}\n              </Box>\n              {/* Review Text */}\n              <Typography\n                variant=\"body2\"\n                mb={2}\n                sx={{\n                  color: `${props.templateConfig.fontColor}`,\n                }}\n              >\n                {props.templateConfig.comment}\n              </Typography>\n              <Box className=\"testmonialUserInfo\">\n                <Box>\n                  {props.templateConfig.showAvatar && (\n                    <UserAvatar\n                      profileImage={props.templateConfig.reviewerImage}\n                      fullname={props.templateConfig.reviewerName}\n                      style={{\n                        width: 60,\n                        height: 60,\n                        margin: \"0 auto 10px\",\n                        background: \"linear-gradient(45deg, #42A5F5, #64B5F6)\",\n                      }}\n                    />\n                  )}\n                </Box>\n                <Box>\n                  <Typography className=\"testmonialUserName\">\n                    {props.templateConfig.reviewerName}\n                  </Typography>\n                </Box>\n              </Box>\n            </Box>\n          </Box>\n        </Box>\n      </Container>\n    </Box>\n  );\n};\n\nexport default TestimonialCard5;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EAGVC,SAAS,EACTC,WAAW,QACN,eAAe;AAItB,OAAOC,UAAU,MAAM,6CAA6C;AACpE,OAAOC,WAAW,MAAM,+CAA+C;;AAEvE;AACA,OAAO,0DAA0D;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,gBAAgB,GAAIC,KAGzB,IAAK;EACJ,oBACEF,OAAA,CAACP,GAAG;IACFU,EAAE,EAAE;MACFC,UAAU,EAAE,GACVF,KAAK,CAACG,cAAc,CAACC,eAAe,QAC9BC,OAAO,CAAC,gDAAgD,CAAC,0BAA0B;MAC3FC,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE,WAAW;MAC7BC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,QAAQ;MACnBC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,CAAC;MACTC,WAAW,EAAE;IACf,CAAE;IAAAC,QAAA,eAEFrB,OAAA,CAACL,SAAS;MAAA0B,QAAA,gBACRrB,OAAA,CAACJ,WAAW;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfzB,OAAA,CAACP,GAAG;QAAA4B,QAAA,eAEFrB,OAAA,CAACP,GAAG;UAACiC,SAAS,EAAC,sBAAsB;UAAAL,QAAA,gBACnCrB,OAAA,CAACP,GAAG;YAACiC,SAAS,EAAC,WAAW;YAAAL,QAAA,eACxBrB,OAAA;cACE2B,GAAG,EAAEpB,OAAO,CAAC,qDAAqD;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENzB,OAAA,CAACP,GAAG;YAAA4B,QAAA,gBAEFrB,OAAA,CAACP,GAAG;cAACiC,SAAS,EAAC,QAAQ;cAAAL,QAAA,EACpBnB,KAAK,CAACG,cAAc,CAACuB,UAAU,iBAC9B5B,OAAA,CAACP,GAAG;gBACFU,EAAE,EAAE;kBACFU,OAAO,EAAE,MAAM;kBACfE,cAAc,EAAE,QAAQ;kBACxBc,EAAE,EAAE;gBACN,CAAE;gBAAAR,QAAA,eAEFrB,OAAA,CAACF,WAAW;kBAACgC,UAAU,EAAE5B,KAAK,CAACG,cAAc,CAACyB;gBAAW;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENzB,OAAA,CAACN,UAAU;cACTqC,OAAO,EAAC,OAAO;cACfF,EAAE,EAAE,CAAE;cACN1B,EAAE,EAAE;gBACFQ,KAAK,EAAE,GAAGT,KAAK,CAACG,cAAc,CAAC2B,SAAS;cAC1C,CAAE;cAAAX,QAAA,EAEDnB,KAAK,CAACG,cAAc,CAAC4B;YAAO;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACbzB,OAAA,CAACP,GAAG;cAACiC,SAAS,EAAC,oBAAoB;cAAAL,QAAA,gBACjCrB,OAAA,CAACP,GAAG;gBAAA4B,QAAA,EACDnB,KAAK,CAACG,cAAc,CAAC6B,UAAU,iBAC9BlC,OAAA,CAACH,UAAU;kBACTsC,YAAY,EAAEjC,KAAK,CAACG,cAAc,CAAC+B,aAAc;kBACjDC,QAAQ,EAAEnC,KAAK,CAACG,cAAc,CAACiC,YAAa;kBAC5CC,KAAK,EAAE;oBACLrB,KAAK,EAAE,EAAE;oBACTD,MAAM,EAAE,EAAE;oBACVuB,MAAM,EAAE,aAAa;oBACrBpC,UAAU,EAAE;kBACd;gBAAE;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNzB,OAAA,CAACP,GAAG;gBAAA4B,QAAA,eACFrB,OAAA,CAACN,UAAU;kBAACgC,SAAS,EAAC,oBAAoB;kBAAAL,QAAA,EACvCnB,KAAK,CAACG,cAAc,CAACiC;gBAAY;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACgB,EAAA,GAxFIxC,gBAAgB;AA0FtB,eAAeA,gBAAgB;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}