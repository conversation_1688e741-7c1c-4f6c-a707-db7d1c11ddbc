const User = require("../models/user.models");
const logger = require("../utils/logger");
var async = require("async");

const welcome = async (req, res) => {
  try {
    logger.logControllerAction("user", "welcome", req.requestId);
    logger.info("User welcome endpoint accessed", { requestId: req.requestId });
    res.send({ message: "User Home Page" });
  } catch (error) {
    logger.error("Error in user welcome", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res
      .status(500)
      .json({ message: "Internal server error", error: error.message });
  }
};

const createUser = async (req, res, next) => {
  const roleId = req.body.roleId;
  const name = req.body.name;
  const mobile = req.body.mobile;
  const email = req.body.email;
  const mobileVerified = req.body.mobileVerified
    ? req.body.mobileVerified
    : null;
  const emailVerified = req.body.emailVerified ? req.body.emailVerified : null;
  const password = req.body.password;
  const statusId = req.body.statusId;

  const newUser = {
    roleId: roleId,
    name: name,
    mobile: mobile,
    email: email,
    mobileVerified: mobileVerified,
    emailVerified: emailVerified,
    password: password,
    statusId: statusId,
    businessId: req.body.businessId,
  };
  User.Insert(newUser)
    .then((result) => {
      res.status(201).json({
        message: "User Created!",
        response: result.results,
        userId: result.userId,
      });
    })
    .catch((error) => {
      res.status(404).json({ message: "Users Not Created!", error: error });
    });
};

const createAssignUserLocation = async (req, res, next) => {
  try {
    const userId = req.body.userId;
    const gmbLocationIds = req.body.locationId; // Ensure this is correctly named in the request
    const statusId = req.body.statusId;

    if (!Array.isArray(gmbLocationIds)) {
      return res
        .status(400)
        .json({ error: "gmbLocationIds should be an array" });
    }
    var locationData = [];

    for (let locationId of gmbLocationIds) {
      locationData.push({
        userId: userId,
        gmbLocationId: locationId,
        statusId: statusId,
      });
    }

    if (locationData.length > 0) {
      User.AssignUserLocation(locationData);
    }

    res.status(200).json({ message: "User locations assigned successfully" });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: "Internal server error" });
  }
};

const userList = (req, res, next) => {
  const userId = req.params.userId;
  User.fetchAll(userId)
    .then((response) => {
      res.status(201).json({ message: "User List!", list: response });
    })
    .catch((error) => {
      res.status(404).json({ message: "Users Not Found!", error: error });
    });
};

const userListpaginated = (req, res, next) => {
  const pageNo = req.query.pageNo;
  const offset = req.query.offset;
  const userId = req.query.userId;
  User.fetchUsersPaginated(userId, pageNo, offset)
    .then((response) => {
      res.status(201).json({ message: "User List!", ...response });
    })
    .catch((error) => {
      res.status(404).json({ message: "Users Not Found!", error: error });
    });
};

const userLocationList = (req, res, next) => {
  const userId = req.params.id;
  User.fetchUserLocation(userId)
    .then((response) => {
      res.status(201).json({ message: "User Location List!", list: response });
    })
    .catch((error) => {
      res
        .status(404)
        .json({ message: " User Location Not Found!", error: error });
    });
};

const deleteUser = (req, res, next) => {
  const id = req.params.id;
  User.deleteById(id)
    .then((response) => {
      res.status(201).json({ message: "User Deleted!", list: response });
    })
    .catch((error) => {
      res.status(404).json({
        message: "Could not perfrom operation due to error!",
        error: error,
      });
    });
};

const updateUser = (req, res, next) => {
  const newData = {
    id: req.params.id,
    roleId: req.body.roleId || null,
    name: req.body.name || null,
    mobile: req.body.mobile || null,
    email: req.body.email || null,
    mobileVerified: req.body.mobileVerified || null,
    emailVerified: req.body.emailVerified || null,
    password: req.body.password || null,
    statusId: req.body.statusId || null,
  };
  User.updateById(newData)
    .then((response) => {
      res.status(201).json({ message: "User Updated!", list: response });
    })
    .catch((error) => {
      res.status(404).json({
        message: "Could not perfrom operation due to error!",
        error: error,
      });
    });
};

const updatePassword = (req, res, next) => {
  const newData = {
    id: req.params.id,
    password: req.body.password || null,
  };
  User.updatePasswordById(newData)
    .then((response) => {
      res.status(201).json({ message: "User Updated!", list: response });
    })
    .catch((error) => {
      res.status(404).json({
        message: "Could not perfrom operation due to error!",
        error: error,
      });
    });
};

const enableDisableUser = (req, res, next) => {
  const newData = {
    id: req.params.id,
    isActive: req.body.isActive,
  };
  User.enableDisableUser(newData)
    .then((response) => {
      res.status(201).json({ message: "User Updated!", list: response });
    })
    .catch((error) => {
      res.status(404).json({
        message: "Could not perfrom operation due to error!",
        error: error,
      });
    });
};

const updateUserLocation = async (req, res, next) => {
  try {
    const userId = req.body.userId;
    const gmbLocationIds = Array.isArray(req.body.locationId)
      ? req.body.locationId
      : [req.body.locationId]; // Ensure it's an array
    const statusId = req.body.statusId;

    if (!userId || !gmbLocationIds.length || !statusId) {
      return res.status(400).json({ error: "LocationId is required" });
    }
    const result = await User.updateAssignUserLocation(userId, gmbLocationIds, {
      statusId,
    });

    res.status(200).json({ message: "Locations updated successfully", result });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: error });
  }
};

const updateUserCombined = async (req, res, next) => {
  try {
    // 1. Validate password and confirmPassword match if provided
    if (req.body.password && req.body.password !== req.body.confirmPassword) {
      return res.status(400).json({
        message: "Password and confirm password do not match",
        error: "Password mismatch",
      });
    }

    // 2. Update user basic information
    const userId = req.body.userId;
    const userData = {
      id: userId,
      roleId: req.body.roleId || null,
      name: req.body.name || null,
      mobile: req.body.mobile || null,
      email: req.body.email || null,
      mobileVerified: req.body.mobileVerified || null,
      emailVerified: req.body.emailVerified || null,
      statusId: req.body.statusId || null,
      password: req.body.password || null,
    };

    const userUpdateResult = await User.updateById(userData);

    // 3. Update user location assignments if locationId is provided
    let locationUpdateResult = null;
    if (
      req.body.locationId &&
      Array.isArray(req.body.locationId) &&
      req.body.locationId.length > 0
    ) {
      locationUpdateResult = await User.updateAssignUserLocation(
        userId,
        req.body.locationId,
        { statusId: req.body.statusId }
      );
    }

    res.status(200).json({
      message: "User updated successfully",
      userUpdate: userUpdateResult,
      locationUpdate: locationUpdateResult
        ? "Locations updated successfully"
        : "No location updates",
    });
  } catch (error) {
    console.error("Error in combined user update:", error);
    res.status(500).json({
      message: "Could not perform operation due to error!",
      error: error,
    });
  }
};

module.exports = {
  welcome,
  createUser,
  createAssignUserLocation,
  userList,
  userLocationList,
  deleteUser,
  updateUser,
  updateUserLocation,
  enableDisableUser,
  userListpaginated,
  updatePassword,
  updateUserCombined,
};
