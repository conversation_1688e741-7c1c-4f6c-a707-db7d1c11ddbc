{"timestamp":"2025-06-03T05:51:33.910Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T00:21:36.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T05:55:30.795Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T00:25:32.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:00:42.585Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T00:30:44.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:00:55.963Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T00:30:58.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:01:08.591Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T00:31:10.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:01:20.205Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T00:31:22.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:01:36.207Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T00:31:38.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:02:00.777Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T00:32:02.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:02:13.820Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T00:32:15.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:02:48.889Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T00:32:51.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:02:57.317Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T00:32:59.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:20:44.276Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T06:38:16.679Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d4d4dd4d-a850-4f10-95df-736d7d747a71","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-06-03T06:38:16.690Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"d4d4dd4d-a850-4f10-95df-736d7d747a71","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-06-03T06:38:16.904Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"d4d4dd4d-a850-4f10-95df-736d7d747a71","userId":52,"email":"<EMAIL>"}
{"timestamp":"2025-06-03T06:38:23.692Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"4581ed0a-9c08-4911-b5b5-3c80d1b16fd5","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-03T06:38:23.694Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"4581ed0a-9c08-4911-b5b5-3c80d1b16fd5","userId":"52"}
{"timestamp":"2025-06-03T06:38:23.783Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"4581ed0a-9c08-4911-b5b5-3c80d1b16fd5","userId":"52","businessCount":5}
{"timestamp":"2025-06-03T06:38:34.837Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"0359609d-7a3e-4235-aca1-bdcacaf655a2","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T06:38:47.123Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8f307246-b1c6-466e-800b-c8e85d79850d","controller":"ManageAssets","action":"uploadAssets"}
{"timestamp":"2025-06-03T06:38:47.200Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"business-assets/aqib-softech-2-45/98905777-51f4-4721-8517-ceb7ca7a39bd.png","mimeType":"image/png","bucketName":"gmb-social-assets"}
{"timestamp":"2025-06-03T06:38:49.744Z","level":"ERROR","message":"Error uploading file to S3","environment":"DEVELOPMENT","s3Key":"business-assets/aqib-softech-2-45/98905777-51f4-4721-8517-ceb7ca7a39bd.png","error":"The specified bucket does not exist","stack":"NoSuchBucket: The specified bucket does not exist\n    at Request.extractError (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\services\\s3.js:757:35)\n    at Request.callListeners (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\sequential_executor.js:106:20)\n    at Request.emit (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\sequential_executor.js:78:10)\n    at Request.emit (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\request.js:686:14)\n    at Request.transition (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\request.js:22:10)\n    at AcceptorStateMachine.runTo (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\state_machine.js:14:12)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\state_machine.js:26:10\n    at Request.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\request.js:38:9)\n    at Request.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\request.js:688:12)\n    at Request.callListeners (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\sequential_executor.js:116:18)"}
{"timestamp":"2025-06-03T06:38:49.746Z","level":"INFO","message":"Assets upload completed","environment":"DEVELOPMENT","businessId":"45","uploadedCount":0,"errorCount":1}
{"timestamp":"2025-06-03T06:38:49.788Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"bdbb56c6-1b56-4265-a5f1-07790c40f6e1","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T06:39:12.141Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"176f87fa-6c6d-4bc8-9d28-aca640266872","controller":"ManageAssets","action":"uploadAssets"}
{"timestamp":"2025-06-03T06:39:12.218Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"business-assets/aqib-softech-2-45/9d748e80-1c96-471c-8e75-206596751c5c.jpg","mimeType":"image/jpeg","bucketName":"gmb-social-assets"}
{"timestamp":"2025-06-03T06:39:14.591Z","level":"ERROR","message":"Error uploading file to S3","environment":"DEVELOPMENT","s3Key":"business-assets/aqib-softech-2-45/9d748e80-1c96-471c-8e75-206596751c5c.jpg","error":"The specified bucket does not exist","stack":"NoSuchBucket: The specified bucket does not exist\n    at Request.extractError (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\services\\s3.js:757:35)\n    at Request.callListeners (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\sequential_executor.js:106:20)\n    at Request.emit (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\sequential_executor.js:78:10)\n    at Request.emit (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\request.js:686:14)\n    at Request.transition (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\request.js:22:10)\n    at AcceptorStateMachine.runTo (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\state_machine.js:14:12)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\state_machine.js:26:10\n    at Request.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\request.js:38:9)\n    at Request.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\request.js:688:12)\n    at Request.callListeners (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\sequential_executor.js:116:18)"}
{"timestamp":"2025-06-03T06:39:14.592Z","level":"INFO","message":"Assets upload completed","environment":"DEVELOPMENT","businessId":"45","uploadedCount":0,"errorCount":1}
{"timestamp":"2025-06-03T06:39:14.675Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"86c16350-c495-4774-a24d-1a200f0ea757","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T06:46:21.309Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:16:23.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:46:32.393Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T06:46:32.628Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"07ca637e-cffe-45fd-97c2-5db8e43167be","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-03T06:46:32.631Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"07ca637e-cffe-45fd-97c2-5db8e43167be","userId":"52"}
{"timestamp":"2025-06-03T01:16:34.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:46:32.784Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"07ca637e-cffe-45fd-97c2-5db8e43167be","userId":"52","businessCount":5}
{"timestamp":"2025-06-03T06:46:44.529Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"2ec416ef-4cc4-46d2-a208-0dd989ff1156","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T06:46:45.371Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e96e7d6f-cb96-40dd-949a-bf61ed2db7ba","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T06:46:46.169Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d5e859e6-2e6d-4f6a-b6b8-94f92288bcdc","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T06:47:34.943Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:17:37.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:47:50.395Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:17:52.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:48:05.371Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:18:07.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:48:20.010Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:18:22.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:48:32.164Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:18:34.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:52:09.281Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:22:11.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:52:48.805Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6a8260e3-ad93-47b0-87eb-c0a3cd1a4157","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-03T06:52:48.807Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"6a8260e3-ad93-47b0-87eb-c0a3cd1a4157","userId":"52"}
{"timestamp":"2025-06-03T06:52:48.865Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"6a8260e3-ad93-47b0-87eb-c0a3cd1a4157","userId":"52","businessCount":5}
{"timestamp":"2025-06-03T06:52:50.816Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ae8f7faf-8128-46d0-9ef2-c335f4151ad6","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T06:52:59.209Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ded3ccd8-ea5b-40bf-a14e-098fb8be79b6","controller":"ManageAssets","action":"uploadAssets"}
{"timestamp":"2025-06-03T06:52:59.315Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"business-assets/aqib-softech-2-45/40f83218-4177-4fe8-ad05-7a6b5dcf59be.jpg","mimeType":"image/jpeg","bucketName":"gmb-social-assets"}
{"timestamp":"2025-06-03T06:53:02.675Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"business-assets/aqib-softech-2-45/40f83218-4177-4fe8-ad05-7a6b5dcf59be.jpg","location":"https://gmb-social-assets.s3.amazonaws.com/business-assets/aqib-softech-2-45/40f83218-4177-4fe8-ad05-7a6b5dcf59be.jpg","etag":"\"b2061ed4b5654060cabc1967d84a5f86\""}
{"timestamp":"2025-06-03T06:53:02.701Z","level":"ERROR","message":"Database query failed in ManageAssets.Insert","environment":"DEVELOPMENT","error":"Column 'user_id' cannot be null","stack":"Error: Column 'user_id' cannot be null\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:728:17)\n    at Query.execute (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:481:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:97:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:104:25)\n    at Socket.emit (node:events:514:28)\n    at Socket.emit (node:domain:488:12)\n    at addChunk (node:internal/streams/readable:376:12)\n    at readableAddChunk (node:internal/streams/readable:349:9)","asset":{"businessId":"45","fileName":"40f83218-4177-4fe8-ad05-7a6b5dcf59be.jpg","originalFileName":"download.jpg","fileType":"image","fileSize":909909,"s3Key":"business-assets/aqib-softech-2-45/40f83218-4177-4fe8-ad05-7a6b5dcf59be.jpg","s3Url":"https://gmb-social-assets.s3.amazonaws.com/business-assets/aqib-softech-2-45/40f83218-4177-4fe8-ad05-7a6b5dcf59be.jpg","mimeType":"image/jpeg"}}
{"timestamp":"2025-06-03T06:53:02.703Z","level":"ERROR","message":"Error processing file upload","environment":"DEVELOPMENT","fileName":"download.jpg","error":"Column 'user_id' cannot be null","stack":"Error: Column 'user_id' cannot be null\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:728:17)\n    at Query.execute (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:481:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:97:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:104:25)\n    at Socket.emit (node:events:514:28)\n    at Socket.emit (node:domain:488:12)\n    at addChunk (node:internal/streams/readable:376:12)\n    at readableAddChunk (node:internal/streams/readable:349:9)"}
{"timestamp":"2025-06-03T06:53:02.705Z","level":"INFO","message":"Assets upload completed","environment":"DEVELOPMENT","businessId":"45","uploadedCount":0,"errorCount":1}
{"timestamp":"2025-06-03T06:53:02.754Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8d8d851a-2075-4b95-91ba-e13fb0b10d2e","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T06:53:28.447Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"5d5cca42-4b8e-42b8-9e11-ac752f7c73c0","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-03T06:53:28.450Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"5d5cca42-4b8e-42b8-9e11-ac752f7c73c0","userId":"52"}
{"timestamp":"2025-06-03T06:53:28.521Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"5d5cca42-4b8e-42b8-9e11-ac752f7c73c0","userId":"52","businessCount":5}
{"timestamp":"2025-06-03T06:53:34.145Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"06159d3c-99b1-47e2-8fa7-88490a726527","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T06:53:41.484Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e4512274-40a9-498e-a148-1b5c580ccc38","controller":"ManageAssets","action":"uploadAssets"}
{"timestamp":"2025-06-03T06:53:41.586Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"business-assets/asian-andrology-59/1fb0d5ba-7b98-47bd-854c-e5a905b3869b.jpg","mimeType":"image/jpeg","bucketName":"gmb-social-assets"}
{"timestamp":"2025-06-03T06:53:46.328Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"business-assets/asian-andrology-59/1fb0d5ba-7b98-47bd-854c-e5a905b3869b.jpg","location":"https://gmb-social-assets.s3.amazonaws.com/business-assets/asian-andrology-59/1fb0d5ba-7b98-47bd-854c-e5a905b3869b.jpg","etag":"\"b2061ed4b5654060cabc1967d84a5f86\""}
{"timestamp":"2025-06-03T06:53:46.352Z","level":"ERROR","message":"Database query failed in ManageAssets.Insert","environment":"DEVELOPMENT","error":"Column 'user_id' cannot be null","stack":"Error: Column 'user_id' cannot be null\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:728:17)\n    at Query.execute (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:481:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:97:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:104:25)\n    at Socket.emit (node:events:514:28)\n    at Socket.emit (node:domain:488:12)\n    at addChunk (node:internal/streams/readable:376:12)\n    at readableAddChunk (node:internal/streams/readable:349:9)","asset":{"businessId":"59","fileName":"1fb0d5ba-7b98-47bd-854c-e5a905b3869b.jpg","originalFileName":"download.jpg","fileType":"image","fileSize":909909,"s3Key":"business-assets/asian-andrology-59/1fb0d5ba-7b98-47bd-854c-e5a905b3869b.jpg","s3Url":"https://gmb-social-assets.s3.amazonaws.com/business-assets/asian-andrology-59/1fb0d5ba-7b98-47bd-854c-e5a905b3869b.jpg","mimeType":"image/jpeg"}}
{"timestamp":"2025-06-03T06:53:46.353Z","level":"ERROR","message":"Error processing file upload","environment":"DEVELOPMENT","fileName":"download.jpg","error":"Column 'user_id' cannot be null","stack":"Error: Column 'user_id' cannot be null\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:728:17)\n    at Query.execute (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:481:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:97:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:104:25)\n    at Socket.emit (node:events:514:28)\n    at Socket.emit (node:domain:488:12)\n    at addChunk (node:internal/streams/readable:376:12)\n    at readableAddChunk (node:internal/streams/readable:349:9)"}
{"timestamp":"2025-06-03T06:53:46.355Z","level":"INFO","message":"Assets upload completed","environment":"DEVELOPMENT","businessId":"59","uploadedCount":0,"errorCount":1}
{"timestamp":"2025-06-03T06:53:46.415Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"a5e573be-9c95-4733-8a21-49ea2f33882b","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T06:54:37.081Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:24:39.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T06:54:46.514Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:24:48.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:01:45.638Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f3eec5d9-eeac-4339-a0d2-4cdea3f9eabd","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-03T07:01:45.640Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"f3eec5d9-eeac-4339-a0d2-4cdea3f9eabd","userId":"52"}
{"timestamp":"2025-06-03T07:01:45.721Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"f3eec5d9-eeac-4339-a0d2-4cdea3f9eabd","userId":"52","businessCount":5}
{"timestamp":"2025-06-03T07:01:49.068Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"92bd01ec-2820-42cb-b0b7-4da50f305a24","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T07:01:56.000Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"a9604548-7711-4d39-b879-03cbc6669994","controller":"ManageAssets","action":"uploadAssets"}
{"timestamp":"2025-06-03T07:01:56.113Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"business-assets/asian-andrology-59/a555f797-999c-41b3-b905-6b3417c7198b.jpg","mimeType":"image/jpeg","bucketName":"gmb-social-assets"}
{"timestamp":"2025-06-03T07:01:58.579Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"business-assets/asian-andrology-59/a555f797-999c-41b3-b905-6b3417c7198b.jpg","location":"https://gmb-social-assets.s3.amazonaws.com/business-assets/asian-andrology-59/a555f797-999c-41b3-b905-6b3417c7198b.jpg","etag":"\"b2061ed4b5654060cabc1967d84a5f86\""}
{"timestamp":"2025-06-03T07:01:58.627Z","level":"INFO","message":"Asset record inserted successfully","environment":"DEVELOPMENT","assetId":1,"businessId":"59","fileName":"a555f797-999c-41b3-b905-6b3417c7198b.jpg"}
{"timestamp":"2025-06-03T07:01:58.630Z","level":"INFO","message":"Assets upload completed","environment":"DEVELOPMENT","businessId":"59","userId":"52","uploadedCount":1,"errorCount":0}
{"timestamp":"2025-06-03T07:01:58.687Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"766f2e70-7805-41c7-a437-5fc34b1e52d4","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T07:03:44.762Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"739f1995-61d4-45e5-90aa-cd839dea5fc7","controller":"ManageAssets","action":"uploadAssets"}
{"timestamp":"2025-06-03T07:04:00.706Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b3bab33a-1f09-4a5c-94b5-97eb53f20501","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-03T07:04:00.710Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"b3bab33a-1f09-4a5c-94b5-97eb53f20501","userId":"52"}
{"timestamp":"2025-06-03T07:04:00.786Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"b3bab33a-1f09-4a5c-94b5-97eb53f20501","userId":"52","businessCount":5}
{"timestamp":"2025-06-03T07:05:01.191Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"db84b270-57c5-485b-b0d2-da6abff14175","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T07:05:04.998Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"2f1d773c-2719-4fa2-949a-e01b6499f6bf","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T07:05:12.937Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"feee9396-774f-4964-a406-3bb4aa7fc7d9","controller":"ManageAssets","action":"uploadAssets"}
{"timestamp":"2025-06-03T07:05:13.047Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"business-assets/healthonus-techonologies-57/5fcbe2e9-4af6-4342-b644-3573f2f3c745.mp4","mimeType":"video/mp4","bucketName":"gmb-social-assets"}
{"timestamp":"2025-06-03T07:05:19.896Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"business-assets/healthonus-techonologies-57/5fcbe2e9-4af6-4342-b644-3573f2f3c745.mp4","location":"https://gmb-social-assets.s3.amazonaws.com/business-assets/healthonus-techonologies-57/5fcbe2e9-4af6-4342-b644-3573f2f3c745.mp4","etag":"\"1250e2a8c9c0d1830003df96d0283dd8-4\""}
{"timestamp":"2025-06-03T07:05:19.942Z","level":"INFO","message":"Asset record inserted successfully","environment":"DEVELOPMENT","assetId":2,"businessId":"57","fileName":"5fcbe2e9-4af6-4342-b644-3573f2f3c745.mp4"}
{"timestamp":"2025-06-03T07:05:19.943Z","level":"INFO","message":"Assets upload completed","environment":"DEVELOPMENT","businessId":"57","userId":"52","uploadedCount":1,"errorCount":0}
{"timestamp":"2025-06-03T07:05:19.988Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"82fd03b1-c274-44f2-a65d-e8f05c4085f5","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T07:09:46.911Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:39:49.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:10:05.906Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:40:08.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:10:22.654Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:40:24.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:10:33.336Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:40:35.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:11:21.516Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:41:23.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:11:45.374Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:41:47.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:12:04.626Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:42:06.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:12:23.562Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:42:25.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:12:36.785Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:42:38.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:15:24.129Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d635562b-ff39-4a69-bcb4-4b57866e05a1","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-03T07:15:24.136Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"d635562b-ff39-4a69-bcb4-4b57866e05a1","userId":"52"}
{"timestamp":"2025-06-03T07:15:24.174Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b865c9be-3217-4dc0-b1af-5a3839cff16b","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T07:15:24.250Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"d635562b-ff39-4a69-bcb4-4b57866e05a1","userId":"52","businessCount":5}
{"timestamp":"2025-06-03T07:15:36.332Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9164dca0-ad2d-4ed1-aaf5-4e100fc292db","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T07:15:36.344Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b2d2f4e5-6891-4ac5-b6ee-3111702b8416","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-03T07:15:36.352Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"b2d2f4e5-6891-4ac5-b6ee-3111702b8416","userId":"52"}
{"timestamp":"2025-06-03T07:15:36.461Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"b2d2f4e5-6891-4ac5-b6ee-3111702b8416","userId":"52","businessCount":5}
{"timestamp":"2025-06-03T07:20:24.655Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:50:26.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:20:38.452Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:50:40.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:24:58.317Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:55:00.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:25:08.708Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:55:10.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:25:59.636Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:56:01.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:26:14.103Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:56:16.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:26:28.714Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T01:56:30.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:59:45.244Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:29:47.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T07:59:47.833Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"1970ac5f-ba3d-4b65-9367-ed41b3cc929d","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-06-03T07:59:47.835Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"1970ac5f-ba3d-4b65-9367-ed41b3cc929d","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-06-03T07:59:48.050Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"1970ac5f-ba3d-4b65-9367-ed41b3cc929d","userId":52,"email":"<EMAIL>"}
{"timestamp":"2025-06-03T07:59:52.343Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"30151307-996e-4492-8a4b-316c4afa307e","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-03T07:59:52.345Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"30151307-996e-4492-8a4b-316c4afa307e","userId":"52"}
{"timestamp":"2025-06-03T07:59:52.450Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"30151307-996e-4492-8a4b-316c4afa307e","userId":"52","businessCount":5}
{"timestamp":"2025-06-03T07:59:54.471Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6d3b12f8-eca9-441e-91df-0b7b195aaafc","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T08:00:01.703Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e82d58c3-54d2-4c3a-8cb5-7598649bf9a4","controller":"ManageAssets","action":"uploadAssets"}
{"timestamp":"2025-06-03T08:00:13.709Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ebf057b5-33cc-4949-8b78-52b91cd7ab8c","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-03T08:00:13.714Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"ebf057b5-33cc-4949-8b78-52b91cd7ab8c","userId":"52"}
{"timestamp":"2025-06-03T08:00:13.803Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"ebf057b5-33cc-4949-8b78-52b91cd7ab8c","userId":"52","businessCount":5}
{"timestamp":"2025-06-03T08:00:15.153Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d1c9bee5-bbe9-4d45-b2c8-eb1956e7def3","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T08:00:21.003Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"02d874c0-8bce-459b-ac70-b89f51522776","controller":"ManageAssets","action":"uploadAssets"}
{"timestamp":"2025-06-03T08:00:32.765Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"5ff2baac-477a-48fa-b300-ec2f8a411df8","controller":"ManageAssets","action":"uploadAssets"}
{"timestamp":"2025-06-03T08:01:55.363Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:31:57.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:02:05.981Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:32:08.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:02:23.716Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:32:25.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:03:03.773Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:33:05.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:03:19.075Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:33:21.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:10:27.715Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:40:29.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:10:51.022Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:40:53.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:11:02.858Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:41:05.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:11:16.163Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:41:18.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:13:16.873Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:43:19.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:13:26.658Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:43:28.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:13:40.442Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:43:42.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:13:49.003Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:43:51.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:20:50.934Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"0c6b0b27-2aa6-4754-a94d-6e3e87a8f50e","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-03T08:20:51.102Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"0c6b0b27-2aa6-4754-a94d-6e3e87a8f50e","userId":"52"}
{"timestamp":"2025-06-03T08:20:51.286Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"0c6b0b27-2aa6-4754-a94d-6e3e87a8f50e","userId":"52","businessCount":5}
{"timestamp":"2025-06-03T08:20:55.532Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b58d90a7-21c1-4f03-b81b-cfd517e4b9ae","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T08:20:57.908Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"453340ee-5790-479a-b056-170299641a4e","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T08:21:07.765Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"82d4c6a3-01ee-48fa-9e49-0ea8e133003c","controller":"ManageAssets","action":"uploadAssets"}
{"timestamp":"2025-06-03T08:21:07.818Z","level":"INFO","message":"Storage validation debug","environment":"DEVELOPMENT","businessId":"57","maxUploadSizeMB":1024,"maxUploadSizeBytes":**********,"currentTotalSize":17839845,"newFilesTotalSize":909909,"totalAfterUpload":18749754,"wouldExceed":false}
{"timestamp":"2025-06-03T08:21:07.849Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"business-assets/healthonus-techonologies-57/92bd7bf8-bacd-482f-8fc9-ad0cab26bbf5.jpg","mimeType":"image/jpeg","bucketName":"gmb-social-assets"}
{"timestamp":"2025-06-03T08:21:10.883Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"business-assets/healthonus-techonologies-57/92bd7bf8-bacd-482f-8fc9-ad0cab26bbf5.jpg","location":"https://gmb-social-assets.s3.amazonaws.com/business-assets/healthonus-techonologies-57/92bd7bf8-bacd-482f-8fc9-ad0cab26bbf5.jpg","etag":"\"b2061ed4b5654060cabc1967d84a5f86\""}
{"timestamp":"2025-06-03T08:21:10.884Z","level":"INFO","message":"Generating image thumbnail","environment":"DEVELOPMENT","fileName":"download.jpg"}
{"timestamp":"2025-06-03T08:21:10.886Z","level":"INFO","message":"Image thumbnail generation temporarily disabled","environment":"DEVELOPMENT","businessId":"57","originalFileName":"download.jpg","imageSize":909909}
{"timestamp":"2025-06-03T08:21:10.923Z","level":"INFO","message":"Asset record inserted successfully","environment":"DEVELOPMENT","assetId":3,"businessId":"57","fileName":"92bd7bf8-bacd-482f-8fc9-ad0cab26bbf5.jpg"}
{"timestamp":"2025-06-03T08:21:10.925Z","level":"INFO","message":"Assets upload completed","environment":"DEVELOPMENT","businessId":"57","userId":"52","uploadedCount":1,"errorCount":0}
{"timestamp":"2025-06-03T08:21:10.984Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"049217eb-9c12-4b13-8669-93285439162a","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T08:21:21.850Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9f80d192-d00b-4882-a795-75e1dcf86d10","controller":"ManageAssets","action":"uploadAssets"}
{"timestamp":"2025-06-03T08:21:21.909Z","level":"INFO","message":"Storage validation debug","environment":"DEVELOPMENT","businessId":"57","maxUploadSizeMB":1024,"maxUploadSizeBytes":**********,"currentTotalSize":18749754,"newFilesTotalSize":17839845,"totalAfterUpload":36589599,"wouldExceed":false}
{"timestamp":"2025-06-03T08:21:21.942Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"business-assets/healthonus-techonologies-57/0d874106-f26b-46de-a2f2-f6778c4536e1.mp4","mimeType":"video/mp4","bucketName":"gmb-social-assets"}
{"timestamp":"2025-06-03T08:21:31.814Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"business-assets/healthonus-techonologies-57/0d874106-f26b-46de-a2f2-f6778c4536e1.mp4","location":"https://gmb-social-assets.s3.amazonaws.com/business-assets/healthonus-techonologies-57/0d874106-f26b-46de-a2f2-f6778c4536e1.mp4","etag":"\"1250e2a8c9c0d1830003df96d0283dd8-4\""}
{"timestamp":"2025-06-03T08:21:31.816Z","level":"INFO","message":"Generating video thumbnail","environment":"DEVELOPMENT","fileName":"file_example_MP4_1920_18MG.mp4"}
{"timestamp":"2025-06-03T08:21:31.817Z","level":"INFO","message":"Video thumbnail generation temporarily disabled","environment":"DEVELOPMENT","businessId":"57","originalFileName":"file_example_MP4_1920_18MG.mp4","videoSize":17839845}
{"timestamp":"2025-06-03T08:21:31.859Z","level":"INFO","message":"Asset record inserted successfully","environment":"DEVELOPMENT","assetId":4,"businessId":"57","fileName":"0d874106-f26b-46de-a2f2-f6778c4536e1.mp4"}
{"timestamp":"2025-06-03T08:21:31.861Z","level":"INFO","message":"Assets upload completed","environment":"DEVELOPMENT","businessId":"57","userId":"52","uploadedCount":1,"errorCount":0}
{"timestamp":"2025-06-03T08:21:31.905Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"305e4083-5c20-41b0-90b5-cbf561ed421f","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T08:26:52.898Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:56:55.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:27:04.153Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:57:06.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:27:21.463Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:57:23.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:28:00.762Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:58:02.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:28:13.433Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:58:15.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:28:28.406Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:58:30.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:28:48.531Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T02:58:50.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:32:55.667Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T03:02:57.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:33:03.993Z","level":"INFO","message":"Starting video thumbnail generation","environment":"DEVELOPMENT","businessId":57,"originalFileName":"test-video.mp4","videoSize":32}
{"timestamp":"2025-06-03T08:33:04.774Z","level":"ERROR","message":"Error generating thumbnail with ffmpeg","environment":"DEVELOPMENT","error":"ffmpeg exited with code 1: C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_a78ba224-3e90-44f5-be87-31d978d0ecc1.mp4: Invalid data found when processing input\n","stack":"Error: ffmpeg exited with code 1: C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_a78ba224-3e90-44f5-be87-31d978d0ecc1.mp4: Invalid data found when processing input\n\n    at ChildProcess.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\fluent-ffmpeg\\lib\\processor.js:180:22)\n    at ChildProcess.emit (node:events:514:28)\n    at ChildProcess.emit (node:domain:488:12)\n    at ChildProcess._handle.onexit (node:internal/child_process:294:12)"}
{"timestamp":"2025-06-03T08:33:04.778Z","level":"ERROR","message":"Error generating video thumbnail","environment":"DEVELOPMENT","error":"ffmpeg exited with code 1: C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_a78ba224-3e90-44f5-be87-31d978d0ecc1.mp4: Invalid data found when processing input\n","stack":"Error: ffmpeg exited with code 1: C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_a78ba224-3e90-44f5-be87-31d978d0ecc1.mp4: Invalid data found when processing input\n\n    at ChildProcess.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\fluent-ffmpeg\\lib\\processor.js:180:22)\n    at ChildProcess.emit (node:events:514:28)\n    at ChildProcess.emit (node:domain:488:12)\n    at ChildProcess._handle.onexit (node:internal/child_process:294:12)","businessId":57,"originalFileName":"test-video.mp4"}
{"timestamp":"2025-06-03T08:33:04.785Z","level":"INFO","message":"Starting image thumbnail generation","environment":"DEVELOPMENT","businessId":57,"originalFileName":"test-image.jpg","imageSize":24}
{"timestamp":"2025-06-03T08:33:04.795Z","level":"ERROR","message":"Error generating image thumbnail","environment":"DEVELOPMENT","error":"Input buffer has corrupt header: VipsJpeg: premature end of JPEG image","stack":"Error: Input buffer has corrupt header: VipsJpeg: premature end of JPEG image\n    at Sharp.toBuffer (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\sharp\\lib\\output.js:163:17)\n    at Object.generateImageThumbnail (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\videoThumbnail.service.js:176:8)\n    at testVideoThumbnail (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\test-video-thumbnail.js:59:53)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","businessId":57,"originalFileName":"test-image.jpg"}
{"timestamp":"2025-06-03T08:33:21.267Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T03:03:23.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:33:32.624Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T03:03:34.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:33:48.611Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-03T03:03:50.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-03T08:34:27.535Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"4dbe8d79-35dd-4b13-a902-66a44ec75906","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-03T08:34:27.537Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"4dbe8d79-35dd-4b13-a902-66a44ec75906","userId":"52"}
{"timestamp":"2025-06-03T08:34:27.597Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"4dbe8d79-35dd-4b13-a902-66a44ec75906","userId":"52","businessCount":5}
{"timestamp":"2025-06-03T08:34:29.084Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ec5f956f-6be4-45fb-8470-39ad873c75c4","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T08:34:39.873Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"279072e6-eeb2-4666-b724-3d31d9a159c9","controller":"ManageAssets","action":"uploadAssets"}
{"timestamp":"2025-06-03T08:34:39.955Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"business-assets/healthonus-techonologies-57/069ca465-a97a-406d-80a2-811bc0cfa619.mp4","mimeType":"video/mp4","bucketName":"gmb-social-assets"}
{"timestamp":"2025-06-03T08:34:51.485Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"business-assets/healthonus-techonologies-57/069ca465-a97a-406d-80a2-811bc0cfa619.mp4","location":"https://gmb-social-assets.s3.amazonaws.com/business-assets/healthonus-techonologies-57/069ca465-a97a-406d-80a2-811bc0cfa619.mp4","etag":"\"1250e2a8c9c0d1830003df96d0283dd8-4\""}
{"timestamp":"2025-06-03T08:34:51.489Z","level":"INFO","message":"Generating video thumbnail","environment":"DEVELOPMENT","fileName":"file_example_MP4_1920_18MG.mp4"}
{"timestamp":"2025-06-03T08:34:51.492Z","level":"INFO","message":"Starting video thumbnail generation","environment":"DEVELOPMENT","businessId":"57","originalFileName":"file_example_MP4_1920_18MG.mp4","videoSize":17839845}
{"timestamp":"2025-06-03T08:34:51.899Z","level":"INFO","message":"Thumbnail generated successfully","environment":"DEVELOPMENT"}
{"timestamp":"2025-06-03T08:34:51.930Z","level":"INFO","message":"Uploading file to S3","environment":"DEVELOPMENT","s3Key":"business-assets/healthonus-techonologies-57/file_example_MP4_1920_18MG_thumbnail.jpg","mimeType":"image/jpeg","bucketName":"gmb-social-assets"}
{"timestamp":"2025-06-03T08:34:53.063Z","level":"INFO","message":"File uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"business-assets/healthonus-techonologies-57/file_example_MP4_1920_18MG_thumbnail.jpg","location":"https://gmb-social-assets.s3.amazonaws.com/business-assets/healthonus-techonologies-57/file_example_MP4_1920_18MG_thumbnail.jpg","etag":"\"45723756d315b85e3f7e4e219698038e\""}
{"timestamp":"2025-06-03T08:34:53.080Z","level":"INFO","message":"Video thumbnail uploaded successfully","environment":"DEVELOPMENT","thumbnailS3Key":"business-assets/healthonus-techonologies-57/file_example_MP4_1920_18MG_thumbnail.jpg","thumbnailUrl":"https://gmb-social-assets.s3.amazonaws.com/business-assets/healthonus-techonologies-57/file_example_MP4_1920_18MG_thumbnail.jpg"}
{"timestamp":"2025-06-03T08:34:53.120Z","level":"INFO","message":"Asset record inserted successfully","environment":"DEVELOPMENT","assetId":5,"businessId":"57","fileName":"069ca465-a97a-406d-80a2-811bc0cfa619.mp4"}
{"timestamp":"2025-06-03T08:34:53.122Z","level":"INFO","message":"Assets upload completed","environment":"DEVELOPMENT","businessId":"57","userId":"52","uploadedCount":1,"errorCount":0}
{"timestamp":"2025-06-03T08:34:53.159Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"84e84887-bb58-47a8-9f05-2c1e9808d63c","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T08:35:03.040Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6d8ae8ab-511b-4b04-98cc-5476970121a9","controller":"ManageAssets","action":"deleteAsset"}
{"timestamp":"2025-06-03T08:35:03.108Z","level":"INFO","message":"Asset soft deleted successfully","environment":"DEVELOPMENT","assetId":"4","deletedBy":"52"}
{"timestamp":"2025-06-03T08:35:03.111Z","level":"INFO","message":"Deleting file from S3","environment":"DEVELOPMENT","s3Key":"business-assets/healthonus-techonologies-57/0d874106-f26b-46de-a2f2-f6778c4536e1.mp4","bucketName":"gmb-social-assets"}
{"timestamp":"2025-06-03T08:35:04.772Z","level":"INFO","message":"File deleted successfully from S3","environment":"DEVELOPMENT","s3Key":"business-assets/healthonus-techonologies-57/0d874106-f26b-46de-a2f2-f6778c4536e1.mp4"}
{"timestamp":"2025-06-03T08:35:04.805Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"10e68d4c-c820-4ded-ac73-69c76ee6e617","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T08:35:07.644Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e86ee75a-d6d2-4b7a-84c6-d8f42b5140e7","controller":"ManageAssets","action":"deleteAsset"}
{"timestamp":"2025-06-03T08:35:07.696Z","level":"INFO","message":"Asset soft deleted successfully","environment":"DEVELOPMENT","assetId":"2","deletedBy":"52"}
{"timestamp":"2025-06-03T08:35:07.697Z","level":"INFO","message":"Deleting file from S3","environment":"DEVELOPMENT","s3Key":"business-assets/healthonus-techonologies-57/5fcbe2e9-4af6-4342-b644-3573f2f3c745.mp4","bucketName":"gmb-social-assets"}
{"timestamp":"2025-06-03T08:35:08.536Z","level":"INFO","message":"File deleted successfully from S3","environment":"DEVELOPMENT","s3Key":"business-assets/healthonus-techonologies-57/5fcbe2e9-4af6-4342-b644-3573f2f3c745.mp4"}
{"timestamp":"2025-06-03T08:35:08.559Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"c4fe9c9e-c793-4cd7-89ab-1e4d99dc63fc","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T08:36:40.674Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e5ebc3ce-c556-42b1-a01c-c683f284f371","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T08:36:41.629Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"385aa26a-8b96-430d-8cf3-b2485eddf3cc","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T08:36:57.950Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"c7275f77-4fd2-4d25-aea1-dce0715a0c92","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-06-03T08:37:45.149Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b59dcb5e-7104-4a06-a077-918bef33067f","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-03T08:37:45.150Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"b59dcb5e-7104-4a06-a077-918bef33067f","userId":"52"}
{"timestamp":"2025-06-03T08:37:45.221Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"b59dcb5e-7104-4a06-a077-918bef33067f","userId":"52","businessCount":5}
{"timestamp":"2025-06-03T08:37:47.994Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"dfd32752-9ac5-41ea-9f13-8d2467bec67a","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-03T08:37:47.995Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"dfd32752-9ac5-41ea-9f13-8d2467bec67a","userId":"52"}
{"timestamp":"2025-06-03T08:37:48.060Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"dfd32752-9ac5-41ea-9f13-8d2467bec67a","userId":"52","businessCount":5}
