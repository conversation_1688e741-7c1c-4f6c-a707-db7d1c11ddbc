const { google } = require("googleapis");
require("dotenv").config();
const axios = require("axios");

async function getBusinessAccountId() {
  //   const postsResponse = await axios.get(
  //     `https://mybusiness.googleapis.com/v4/accounts/118014306973614125801/locations/6511310875720024037/localPosts`,
  //     {
  //       headers: {
  //         Authorization: `Bearer 1//0gUso-mUjRE9WCgYIARAAGBASNwF-L9Ir_984jxiM6GSyjOVxjGhvSYdyG3AY18rVaZwPJMpd6PPLdQTGu3Jwpj-VT3xgloDp9vc`,
  //         Accept: "application/json",
  //       },
  //     }
  //   );
  //   res.json(postsResponse.data.localPosts || []);

  try {
    const response = await axios.get(
      "https://www.googleapis.com/oauth2/v1/tokeninfo",
      {
        headers: {
          Authorization: `Bearer ******************************************************************************************************************************************************************************************************************************`,
        },
      }
    );

    console.log("Token Info:", response.data);
  } catch (error) {
    console.error(
      "Error checking token:",
      error.response ? error.response.data : error.message
    );
  }
}

getBusinessAccountId();
