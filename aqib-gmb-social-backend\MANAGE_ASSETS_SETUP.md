# Manage Assets Feature Setup Guide

This guide provides step-by-step instructions for setting up the Manage Assets functionality in the aqib-gmb-social project.

## Overview

The Manage Assets feature allows users to:

- Upload images and videos to AWS S3
- View uploaded assets in a gallery format
- Delete assets with confirmation dialogs
- Zoom images and play videos
- Manage storage limits per business
- Track file sizes and usage

## Backend Setup

### 1. Install Dependencies

```bash
cd aqib-gmb-social-backend
npm install aws-sdk multer-s3
```

### 2. Configure Environment Variables

The following AWS credentials have been added to `.env.development`:

```env
# AWS S3 Configuration
APP_AWS_ACCESS_KEY_ID=********************
APP_AWS_SECRET_ACCESS_KEY=h9O3B/VDX4/5CrgM36zTA/+5GOTksjCy768l9r2H
APP_AWS_REGION=us-east-1
APP_AWS_S3_BUCKET=gmb-social-assets
```

### 3. Setup Database

Run the database migration to create required tables:

```bash
npm run setup:manage-assets
```

This will:

- Add `max_upload_size_mb` column to `gmb_businesses_master` table (default: 1024 MB)
- Create `business_assets` table for tracking uploaded files
- Update existing records with default values

**Note:** If you previously ran the setup and the column was created in the wrong table, run the fix script:

```bash
npm run fix:manage-assets-table
```

This will move the `max_upload_size_mb` column from `user_business` to `gmb_businesses_master` table where it belongs (storage limits should be per business, not per user-business relationship).

### 4. Database Schema Changes

#### gmb_businesses_master table

- Added `max_upload_size_mb INT DEFAULT 1024` column

#### business_assets table (new)

```sql
CREATE TABLE business_assets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    business_id INT NOT NULL,
    user_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    original_file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    s3_key VARCHAR(500) NOT NULL,
    s3_url VARCHAR(1000) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    -- Foreign keys and indexes included
);
```

### 5. API Endpoints

The following endpoints are now available:

- `GET /v1/manage-assets/` - Welcome endpoint
- `POST /v1/manage-assets/upload/{businessId}` - Upload assets
- `GET /v1/manage-assets/business/{businessId}` - Get assets with pagination
- `GET /v1/manage-assets/asset/{assetId}` - Get single asset
- `DELETE /v1/manage-assets/asset/{assetId}` - Delete asset
- `PUT /v1/manage-assets/business/{businessId}/max-size` - Update max upload size

### 6. File Storage Structure

Files are stored in S3 with the following structure:

```
business-assets/
  └── {business-name-with-id}/
      ├── {uuid}.jpg
      ├── {uuid}.mp4
      └── ...
```

Business names are sanitized (spaces and special characters replaced with hyphens).

## Frontend Setup

### 1. New Components Created

- **ManageAssets Screen** (`/src/screens/manageAssets/`)

  - Main screen with business selection, storage info, and file management

- **FileUploadComponent** (`/src/components/fileUpload/`)

  - Drag & drop file upload with validation and preview

- **FileGalleryComponent** (`/src/components/fileGallery/`)

  - Grid display of uploaded assets with action buttons

- **FileViewerComponent** (`/src/components/fileViewer/`)

  - Modal for viewing images (with zoom) and playing videos

- **ConfirmDeleteComponent** (`/src/components/confirmDelete/`)
  - Reusable confirmation dialog for delete operations

### 2. Service Layer

- **ManageAssetsService** (`/src/services/manageAssets/`)
  - API calls and utility functions for file management

### 3. Menu Integration

- Added "Manage Assets" menu item to left navigation
- Available to users with BusinessManagement permission
- Uses CloudUpload icon

### 4. Routing

- Added `/manage-assets` route in App.tsx

## Features

### File Upload

- Drag & drop interface
- Multiple file selection
- File type validation (images and videos only)
- File size validation (100MB per file)
- Storage limit checking
- Upload progress indication

### File Management

- Grid view of uploaded assets
- Image thumbnails and video placeholders
- File information display (size, type, upload date)
- Hover actions (view, delete)
- Pagination support

### File Viewing

- Image zoom functionality
- Video playback
- Fullscreen mode
- Download option
- File metadata display

### Storage Management

- Visual storage usage indicator
- Configurable storage limits per business
- Real-time usage calculation
- Warning when approaching limits

### Supported File Types

**Images:**

- JPEG, JPG, PNG, GIF, WebP

**Videos:**

- MP4, AVI, MOV, WMV, FLV, WebM

## Security Features

- File type validation on both frontend and backend
- File size limits (100MB per file, configurable total per business)
- S3 bucket with public read access for viewing
- Soft delete for assets (status field)
- User authentication required for all operations

## AWS S3 Configuration

### Bucket Setup

1. Create S3 bucket named `gmb-social-assets`
2. Configure bucket policy for public read access
3. Enable CORS for web access
4. Set up lifecycle policies if needed

### Recommended Bucket Policy

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::gmb-social-assets/*"
    }
  ]
}
```

## Testing

### Backend Testing

1. Test database migration: `npm run setup:manage-assets`
2. Start server: `npm run devStart`
3. Test API endpoints using Swagger UI at `/api-docs`

### Frontend Testing

1. Start frontend development server
2. Navigate to `/manage-assets`
3. Test file upload, viewing, and deletion
4. Verify storage limit enforcement

## Troubleshooting

### Common Issues

1. **AWS Credentials Error**

   - Verify AWS credentials in `.env.development`
   - Check S3 bucket permissions

2. **Database Connection Error**

   - Verify database credentials in `.env.development`
   - Ensure database is accessible

3. **File Upload Fails**

   - Check file size and type restrictions
   - Verify storage limits
   - Check S3 bucket configuration

4. **Images/Videos Not Loading**
   - Verify S3 bucket public read permissions
   - Check CORS configuration

## Next Steps

1. Set up AWS S3 bucket with proper permissions
2. Test the complete upload and view workflow
3. Configure storage limits per business as needed
4. Add monitoring and logging for file operations
5. Consider implementing file compression for large images
6. Add batch operations (select multiple files for deletion)

## Support

For issues or questions, refer to:

- API documentation at `/api-docs`
- Database setup logs
- Application logs in `/logs` directory
