const Business = require("../models/business.models");
const logger = require("../utils/logger");

const welcome = async (req, res) => {
  try {
    logger.logControllerAction("business", "welcome", req.requestId);

    const response = {
      message: `Business Home Page`,
    };

    logger.info("Business welcome endpoint accessed", {
      requestId: req.requestId,
    });

    res.send(response);
  } catch (error) {
    logger.error("Error in business welcome", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res
      .status(500)
      .json({ message: "Internal server error", error: error.message });
  }
};

const addBusiness = async (req, res) => {
  try {
    logger.logControllerAction("business", "addBusiness", req.requestId, {
      userId: req.body.userId,
      businessName: req.body.businessName,
    });

    const userId = req.body.userId;
    const businessName = req.body.businessName;
    const businessEmail = req.body.businessEmail;
    const businessUrl = req.body.businessUrl;
    const statusId = req.body.statusId;
    const createdBy = req.body.createdBy;
    const updatedBy = req.body.updatedBy;

    const newBusiness = {
      userId: userId,
      businessName: businessName,
      businessEmail: businessEmail,
      businessUrl: businessUrl,
      statusId: statusId,
      createdBy: createdBy,
      updatedBy: updatedBy,
    };

    logger.info("Creating new business", {
      requestId: req.requestId,
      userId: userId,
      businessName: businessName,
      businessEmail: businessEmail,
    });

    const result = await Business.Insert(newBusiness);

    logger.info("Business created successfully", {
      requestId: req.requestId,
      businessId: result.insertId,
      businessName: businessName,
    });

    res.status(201).json({ message: "Business Created!", response: result });
  } catch (error) {
    logger.error("Error creating business", {
      requestId: req.requestId,
      userId: req.body.userId,
      businessName: req.body.businessName,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      message: "Business Not Created!",
      error: error.message,
    });
  }
};

const businessList = async (req, res) => {
  try {
    const userId = req.params.userId;

    logger.logControllerAction("business", "businessList", req.requestId, {
      userId: userId,
    });

    logger.info("Fetching business list", {
      requestId: req.requestId,
      userId: userId,
    });

    const response = await Business.fetchAll(userId);

    logger.info("Business list fetched successfully", {
      requestId: req.requestId,
      userId: userId,
      businessCount: response.length,
    });

    res.status(200).json({ message: "Business List!", list: response });
  } catch (error) {
    logger.error("Error fetching business list", {
      requestId: req.requestId,
      userId: req.params.userId,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      message: "Business Not Found!",
      error: error.message,
    });
  }
};

const businessListPaginated = (req, res, next) => {
  const pageNo = req.query.pageNo;
  const offset = req.query.offset;
  Business.fetchBusinessPaginated(req.params.userId, pageNo, offset)
    .then((response) => {
      res.status(201).json({ message: "Business List!", ...response });
    })
    .catch((error) => {
      res.status(404).json({ message: "Business Not Found!", error: error });
    });
};

const deleteBusiness = (req, res, next) => {
  const id = req.params.id;
  Business.deleteById(id)
    .then((response) => {
      res.status(201).json({ message: "Business Deleted!", list: response });
    })
    .catch((error) => {
      res.status(404).json({
        message: "Could not perfrom operation due to error!",
        error: error,
      });
    });
};

const updateBusiness = (req, res, next) => {
  const newData = {
    id: req.params.id,
    userId: req.body.userId,
    businessName: req.body.businessName,
    businessEmail: req.body.businessEmail,
    businessUrl: req.body.businessUrl,
    statusId: req.body.statusId,
    createdBy: req.body.createdBy,
    updatedBy: req.body.updatedBy,
  };
  Business.updateById(newData)
    .then((response) => {
      res.status(201).json({ message: "Business Updated!", list: response });
    })
    .catch((error) => {
      res.status(404).json({
        message: "Could not perfrom operation due to error!",
        error: error,
      });
    });
};

const enableDisableBusiness = (req, res, next) => {
  const newData = {
    id: req.params.id,
    isActive: req.body.isActive,
  };
  Business.enableDisableBusiness(newData)
    .then((response) => {
      res.status(201).json({ message: "Business Updated!", list: response });
    })
    .catch((error) => {
      res.status(404).json({
        message: "Could not perfrom operation due to error!",
        error: error,
      });
    });
};

module.exports = {
  welcome,
  addBusiness,
  businessList,
  deleteBusiness,
  updateBusiness,
  enableDisableBusiness,
  businessListPaginated,
};
