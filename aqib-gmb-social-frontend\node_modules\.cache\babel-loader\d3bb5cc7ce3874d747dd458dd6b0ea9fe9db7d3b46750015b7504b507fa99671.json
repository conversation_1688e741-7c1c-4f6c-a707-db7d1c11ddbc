{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\mediaGallery\\\\mediaGallery.component.tsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useEffect, useState } from \"react\";\nimport { Card, CardContent, Typography, ImageList, ImageListItem, Divider, Box } from \"@mui/material\";\nimport { useDispatch } from \"react-redux\";\nimport LocationService from \"../../services/location/location.service\";\nimport { LoadingContext } from \"../../context/loading.context\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MediaGallery = props => {\n  _s();\n  const dispatch = useDispatch();\n  const _locationService = new LocationService(dispatch);\n  const [grouped, setGrouped] = useState(null);\n  const {\n    setLoading\n  } = useContext(LoadingContext);\n  useEffect(() => {\n    getData();\n    async function getData() {\n      setLoading(true);\n      const groupedItems = {};\n      const promises = props.mediaItems.map(async item => {\n        var _item$locationAssocia;\n        const category = ((_item$locationAssocia = item.locationAssociation) === null || _item$locationAssocia === void 0 ? void 0 : _item$locationAssocia.category) || \"UNCATEGORIZED\";\n        const base64Image = await _locationService.getGoogleImageBase64(item.thumbnailUrl);\n        if (!groupedItems[category]) groupedItems[category] = [];\n        groupedItems[category].push({\n          ...item,\n          base64Image: base64Image.base64\n        });\n      });\n      await Promise.all(promises); // Wait for all async tasks to finish\n      setGrouped(groupedItems);\n      setLoading(false);\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        gutterBottom: true,\n        children: \"Media Gallery\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), grouped && Object.entries(grouped).map(([category, items]) => /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            textTransform: \"capitalize\",\n            fontWeight: 600,\n            fontSize: 16\n          },\n          children: category.replace(/_/g, \" \").toLowerCase()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ImageList, {\n          cols: 5,\n          gap: 12,\n          children: items.map((item, index) => /*#__PURE__*/_jsxDEV(ImageListItem, {\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: item.base64Image,\n              alt: `${category}-${index}-${item.base64Image}`,\n              loading: \"eager\",\n              style: {\n                borderRadius: 8,\n                objectFit: \"cover\",\n                width: \"100%\",\n                height: \"100%\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 21\n            }, this)\n          }, item.name, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 15\n        }, this)]\n      }, category, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_s(MediaGallery, \"4q66har3I4Ec+QaCeA1/EU/hQv0=\", false, function () {\n  return [useDispatch];\n});\n_c = MediaGallery;\nexport default MediaGallery;\nvar _c;\n$RefreshReg$(_c, \"MediaGallery\");", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useState", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "ImageList", "ImageListItem", "Divider", "Box", "useDispatch", "LocationService", "LoadingContext", "jsxDEV", "_jsxDEV", "MediaGallery", "props", "_s", "dispatch", "_locationService", "grouped", "setGrouped", "setLoading", "getData", "groupedItems", "promises", "mediaItems", "map", "item", "_item$locationAssocia", "category", "locationAssociation", "base64Image", "getGoogleImageBase64", "thumbnailUrl", "push", "base64", "Promise", "all", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mb", "Object", "entries", "items", "textTransform", "fontWeight", "fontSize", "replace", "toLowerCase", "cols", "gap", "index", "src", "alt", "loading", "style", "borderRadius", "objectFit", "width", "height", "name", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/mediaGallery/mediaGallery.component.tsx"], "sourcesContent": ["import React, { useContext, useEffect, useState } from \"react\";\nimport {\n  <PERSON>,\n  CardContent,\n  Typography,\n  ImageList,\n  ImageListItem,\n  Divider,\n  Box,\n} from \"@mui/material\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport LocationService from \"../../services/location/location.service\";\nimport { LoadingContext } from \"../../context/loading.context\";\n\nconst MediaGallery = (props: { mediaItems: any }) => {\n  const dispatch = useDispatch();\n  const _locationService = new LocationService(dispatch);\n  const [grouped, setGrouped] = useState<Record<string, any[]> | null>(null);\n  const { setLoading } = useContext(LoadingContext);\n  useEffect(() => {\n    getData();\n\n    async function getData() {\n      setLoading(true);\n      const groupedItems: Record<string, any[]> = {};\n\n      const promises = props.mediaItems.map(async (item: any) => {\n        const category = item.locationAssociation?.category || \"UNCATEGORIZED\";\n        const base64Image = await _locationService.getGoogleImageBase64(\n          item.thumbnailUrl\n        );\n        if (!groupedItems[category]) groupedItems[category] = [];\n        groupedItems[category].push({\n          ...item,\n          base64Image: base64Image.base64,\n        });\n      });\n\n      await Promise.all(promises); // Wait for all async tasks to finish\n      setGrouped(groupedItems);\n      setLoading(false);\n    }\n  }, []);\n\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h5\" gutterBottom>\n          Media Gallery\n        </Typography>\n        <Divider sx={{ mb: 2 }} />\n\n        {grouped &&\n          Object.entries(grouped).map(([category, items]) => (\n            <Box key={category} sx={{ mb: 4 }}>\n              <Typography\n                variant=\"h6\"\n                gutterBottom\n                sx={{\n                  textTransform: \"capitalize\",\n                  fontWeight: 600,\n                  fontSize: 16,\n                }}\n              >\n                {category.replace(/_/g, \" \").toLowerCase()}\n              </Typography>\n              <ImageList cols={5} gap={12}>\n                {(items as any).map((item: any, index: number) => (\n                  <ImageListItem key={item.name}>\n                    <img\n                      src={item.base64Image}\n                      alt={`${category}-${index}-${item.base64Image}`}\n                      loading=\"eager\"\n                      style={{\n                        borderRadius: 8,\n                        objectFit: \"cover\",\n                        width: \"100%\",\n                        height: \"100%\",\n                      }}\n                    />\n                  </ImageListItem>\n                ))}\n              </ImageList>\n            </Box>\n          ))}\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default MediaGallery;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC9D,SACEC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,aAAa,EACbC,OAAO,EACPC,GAAG,QACE,eAAe;AACtB,SAASC,WAAW,QAAqB,aAAa;AACtD,OAAOC,eAAe,MAAM,0CAA0C;AACtE,SAASC,cAAc,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,YAAY,GAAIC,KAA0B,IAAK;EAAAC,EAAA;EACnD,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,gBAAgB,GAAG,IAAIR,eAAe,CAACO,QAAQ,CAAC;EACtD,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAA+B,IAAI,CAAC;EAC1E,MAAM;IAAEoB;EAAW,CAAC,GAAGtB,UAAU,CAACY,cAAc,CAAC;EACjDX,SAAS,CAAC,MAAM;IACdsB,OAAO,CAAC,CAAC;IAET,eAAeA,OAAOA,CAAA,EAAG;MACvBD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAME,YAAmC,GAAG,CAAC,CAAC;MAE9C,MAAMC,QAAQ,GAAGT,KAAK,CAACU,UAAU,CAACC,GAAG,CAAC,MAAOC,IAAS,IAAK;QAAA,IAAAC,qBAAA;QACzD,MAAMC,QAAQ,GAAG,EAAAD,qBAAA,GAAAD,IAAI,CAACG,mBAAmB,cAAAF,qBAAA,uBAAxBA,qBAAA,CAA0BC,QAAQ,KAAI,eAAe;QACtE,MAAME,WAAW,GAAG,MAAMb,gBAAgB,CAACc,oBAAoB,CAC7DL,IAAI,CAACM,YACP,CAAC;QACD,IAAI,CAACV,YAAY,CAACM,QAAQ,CAAC,EAAEN,YAAY,CAACM,QAAQ,CAAC,GAAG,EAAE;QACxDN,YAAY,CAACM,QAAQ,CAAC,CAACK,IAAI,CAAC;UAC1B,GAAGP,IAAI;UACPI,WAAW,EAAEA,WAAW,CAACI;QAC3B,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,MAAMC,OAAO,CAACC,GAAG,CAACb,QAAQ,CAAC,CAAC,CAAC;MAC7BJ,UAAU,CAACG,YAAY,CAAC;MACxBF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,oBACER,OAAA,CAACX,IAAI;IAAAoC,QAAA,eACHzB,OAAA,CAACV,WAAW;MAAAmC,QAAA,gBACVzB,OAAA,CAACT,UAAU;QAACmC,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/B,OAAA,CAACN,OAAO;QAACsC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAEzBzB,OAAO,IACN4B,MAAM,CAACC,OAAO,CAAC7B,OAAO,CAAC,CAACO,GAAG,CAAC,CAAC,CAACG,QAAQ,EAAEoB,KAAK,CAAC,kBAC5CpC,OAAA,CAACL,GAAG;QAAgBqC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAChCzB,OAAA,CAACT,UAAU;UACTmC,OAAO,EAAC,IAAI;UACZC,YAAY;UACZK,EAAE,EAAE;YACFK,aAAa,EAAE,YAAY;YAC3BC,UAAU,EAAE,GAAG;YACfC,QAAQ,EAAE;UACZ,CAAE;UAAAd,QAAA,EAEDT,QAAQ,CAACwB,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;QAAC;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACb/B,OAAA,CAACR,SAAS;UAACkD,IAAI,EAAE,CAAE;UAACC,GAAG,EAAE,EAAG;UAAAlB,QAAA,EACxBW,KAAK,CAASvB,GAAG,CAAC,CAACC,IAAS,EAAE8B,KAAa,kBAC3C5C,OAAA,CAACP,aAAa;YAAAgC,QAAA,eACZzB,OAAA;cACE6C,GAAG,EAAE/B,IAAI,CAACI,WAAY;cACtB4B,GAAG,EAAE,GAAG9B,QAAQ,IAAI4B,KAAK,IAAI9B,IAAI,CAACI,WAAW,EAAG;cAChD6B,OAAO,EAAC,OAAO;cACfC,KAAK,EAAE;gBACLC,YAAY,EAAE,CAAC;gBACfC,SAAS,EAAE,OAAO;gBAClBC,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE;cACV;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAXgBjB,IAAI,CAACuC,IAAI;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYd,CAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA,GA5BJf,QAAQ;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6Bb,CACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAAC5B,EAAA,CA1EIF,YAAY;EAAA,QACCL,WAAW;AAAA;AAAA0D,EAAA,GADxBrD,YAAY;AA4ElB,eAAeA,YAAY;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}