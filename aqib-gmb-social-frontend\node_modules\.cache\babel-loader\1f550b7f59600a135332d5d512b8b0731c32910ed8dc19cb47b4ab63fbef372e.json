{"ast": null, "code": "import { UPDATE_MENU_STATE, TOGGLED_MENU_ITEM } from \"../constants/reducer.constant\";\nconst initialState = {\n  menuOpened: false,\n  toggledMenuId: \"\"\n};\nconst userPreferencesReducer = (prevState = initialState, action) => {\n  switch (action.type) {\n    case UPDATE_MENU_STATE:\n      return {\n        ...prevState,\n        menuOpened: action.payload\n      };\n    case TOGGLED_MENU_ITEM:\n      return {\n        ...prevState,\n        toggledMenuId: action.payload\n      };\n    default:\n      return prevState;\n  }\n};\nexport default userPreferencesReducer;", "map": {"version": 3, "names": ["UPDATE_MENU_STATE", "TOGGLED_MENU_ITEM", "initialState", "menuOpened", "toggledMenuId", "userPreferencesReducer", "prevState", "action", "type", "payload"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/reducers/userPreferences.reducer.tsx"], "sourcesContent": ["import {\n  AUTH_REQUESTED,\n  AUTH_SUCCESS,\n  AUTH_LOGOUT,\n  AUTH_ERROR,\n  AUTH_UNAUTHORIZED,\n  UPDATE_MENU_STATE,\n  TOGGLED_MENU_ITEM,\n} from \"../constants/reducer.constant\";\nimport { IRoleBasedAccessResponseModel } from \"../interfaces/response/IRoleBasedAccessResponseModel\";\nimport { ILoggedInUserResponseModel } from \"../interfaces/response/ISignInResponseModel\";\n\ninterface UserPreferencesState {\n  menuOpened: boolean;\n  toggledMenuId: string;\n}\n\nconst initialState: UserPreferencesState = {\n  menuOpened: false,\n  toggledMenuId: \"\",\n};\n\nconst userPreferencesReducer = (prevState = initialState, action: any) => {\n  switch (action.type) {\n    case UPDATE_MENU_STATE:\n      return {\n        ...prevState,\n        menuOpened: action.payload,\n      };\n    case TOGGLED_MENU_ITEM:\n      return {\n        ...prevState,\n        toggledMenuId: action.payload,\n      };\n    default:\n      return prevState;\n  }\n};\n\nexport default userPreferencesReducer;\n"], "mappings": "AAAA,SAMEA,iBAAiB,EACjBC,iBAAiB,QACZ,+BAA+B;AAStC,MAAMC,YAAkC,GAAG;EACzCC,UAAU,EAAE,KAAK;EACjBC,aAAa,EAAE;AACjB,CAAC;AAED,MAAMC,sBAAsB,GAAGA,CAACC,SAAS,GAAGJ,YAAY,EAAEK,MAAW,KAAK;EACxE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKR,iBAAiB;MACpB,OAAO;QACL,GAAGM,SAAS;QACZH,UAAU,EAAEI,MAAM,CAACE;MACrB,CAAC;IACH,KAAKR,iBAAiB;MACpB,OAAO;QACL,GAAGK,SAAS;QACZF,aAAa,EAAEG,MAAM,CAACE;MACxB,CAAC;IACH;MACE,OAAOH,SAAS;EACpB;AACF,CAAC;AAED,eAAeD,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}