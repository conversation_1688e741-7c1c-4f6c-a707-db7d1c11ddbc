{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\userAvatar\\\\userAvatar.component.tsx\",\n  _s = $RefreshSig$();\nimport Avatar from \"@mui/material/Avatar\";\nimport { useEffect, useState } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserAvatar = props => {\n  _s();\n  const [avatarSrc, setAvatarSrc] = useState(\"\");\n  const getAvatarText = fullname => {\n    var _nameParts$, _nameParts;\n    const nameParts = fullname.trim().split(/\\s+/);\n    const firstInitial = ((_nameParts$ = nameParts[0]) === null || _nameParts$ === void 0 ? void 0 : _nameParts$.charAt(0).toUpperCase()) || \"\";\n    const lastInitial = ((_nameParts = nameParts[nameParts.length - 1]) === null || _nameParts === void 0 ? void 0 : _nameParts.charAt(0).toUpperCase()) || \"\";\n    return `${firstInitial}${lastInitial}` || \"NA\";\n  };\n  useEffect(() => {\n    const fetchImageAsBase64 = async () => {\n      try {\n        if (props.profileImage) {\n          const response = await fetch(props.profileImage, {\n            mode: \"no-cors\"\n          });\n          const blob = await response.blob();\n          const reader = new FileReader();\n          reader.onloadend = () => {\n            setAvatarSrc(reader.result);\n          };\n          reader.readAsDataURL(blob);\n        }\n      } catch (error) {}\n    };\n    if (props.profileImage) {\n      fetchImageAsBase64();\n    }\n  }, []);\n  return avatarSrc ? /*#__PURE__*/_jsxDEV(Avatar, {\n    src: avatarSrc,\n    sx: {\n      ...props.style\n    },\n    alt: \"Avatar\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this) : /*#__PURE__*/_jsxDEV(Avatar, {\n    sx: {\n      ...props.style\n    },\n    children: getAvatarText(props.fullname)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n_s(UserAvatar, \"/J6Yg/KhRr00ebSemk8fMIn864U=\");\n_c = UserAvatar;\nexport default UserAvatar;\nvar _c;\n$RefreshReg$(_c, \"UserAvatar\");", "map": {"version": 3, "names": ["Avatar", "useEffect", "useState", "jsxDEV", "_jsxDEV", "UserAvatar", "props", "_s", "avatarSrc", "setAvatarSrc", "getAvatarText", "fullname", "_nameParts$", "_nameParts", "nameParts", "trim", "split", "firstInitial", "char<PERSON>t", "toUpperCase", "lastInitial", "length", "fetchImageAsBase64", "profileImage", "response", "fetch", "mode", "blob", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "error", "src", "sx", "style", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/userAvatar/userAvatar.component.tsx"], "sourcesContent": ["import Avatar from \"@mui/material/Avatar\";\nimport { useEffect, useState } from \"react\";\n\nconst UserAvatar = (props: {\n  fullname: string;\n  style?: React.CSSProperties;\n  profileImage?: string;\n}) => {\n  const [avatarSrc, setAvatarSrc] = useState<string>(\"\");\n\n  const getAvatarText = (fullname: string): string => {\n    const nameParts = fullname.trim().split(/\\s+/);\n    const firstInitial = nameParts[0]?.charAt(0).toUpperCase() || \"\";\n    const lastInitial =\n      nameParts[nameParts.length - 1]?.charAt(0).toUpperCase() || \"\";\n    return `${firstInitial}${lastInitial}` || \"NA\";\n  };\n\n  useEffect(() => {\n    const fetchImageAsBase64 = async () => {\n      try {\n        if (props.profileImage) {\n          const response = await fetch(props.profileImage, { mode: \"no-cors\" });\n          const blob = await response.blob();\n          const reader = new FileReader();\n          reader.onloadend = () => {\n            setAvatarSrc(reader.result as string);\n          };\n          reader.readAsDataURL(blob);\n        }\n      } catch (error) {}\n    };\n\n    if (props.profileImage) {\n      fetchImageAsBase64();\n    }\n  }, []);\n\n  return avatarSrc ? (\n    <Avatar src={avatarSrc} sx={{ ...props.style }} alt=\"Avatar\"></Avatar>\n  ) : (\n    <Avatar sx={{ ...props.style }}>{getAvatarText(props.fullname)}</Avatar>\n  );\n};\n\nexport default UserAvatar;\n"], "mappings": ";;AAAA,OAAOA,MAAM,MAAM,sBAAsB;AACzC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,UAAU,GAAIC,KAInB,IAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGP,QAAQ,CAAS,EAAE,CAAC;EAEtD,MAAMQ,aAAa,GAAIC,QAAgB,IAAa;IAAA,IAAAC,WAAA,EAAAC,UAAA;IAClD,MAAMC,SAAS,GAAGH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC;IAC9C,MAAMC,YAAY,GAAG,EAAAL,WAAA,GAAAE,SAAS,CAAC,CAAC,CAAC,cAAAF,WAAA,uBAAZA,WAAA,CAAcM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI,EAAE;IAChE,MAAMC,WAAW,GACf,EAAAP,UAAA,GAAAC,SAAS,CAACA,SAAS,CAACO,MAAM,GAAG,CAAC,CAAC,cAAAR,UAAA,uBAA/BA,UAAA,CAAiCK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI,EAAE;IAChE,OAAO,GAAGF,YAAY,GAAGG,WAAW,EAAE,IAAI,IAAI;EAChD,CAAC;EAEDnB,SAAS,CAAC,MAAM;IACd,MAAMqB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACF,IAAIhB,KAAK,CAACiB,YAAY,EAAE;UACtB,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACnB,KAAK,CAACiB,YAAY,EAAE;YAAEG,IAAI,EAAE;UAAU,CAAC,CAAC;UACrE,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACG,IAAI,CAAC,CAAC;UAClC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;UAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;YACvBrB,YAAY,CAACmB,MAAM,CAACG,MAAgB,CAAC;UACvC,CAAC;UACDH,MAAM,CAACI,aAAa,CAACL,IAAI,CAAC;QAC5B;MACF,CAAC,CAAC,OAAOM,KAAK,EAAE,CAAC;IACnB,CAAC;IAED,IAAI3B,KAAK,CAACiB,YAAY,EAAE;MACtBD,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOd,SAAS,gBACdJ,OAAA,CAACJ,MAAM;IAACkC,GAAG,EAAE1B,SAAU;IAAC2B,EAAE,EAAE;MAAE,GAAG7B,KAAK,CAAC8B;IAAM,CAAE;IAACC,GAAG,EAAC;EAAQ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAS,CAAC,gBAEtErC,OAAA,CAACJ,MAAM;IAACmC,EAAE,EAAE;MAAE,GAAG7B,KAAK,CAAC8B;IAAM,CAAE;IAAAM,QAAA,EAAEhC,aAAa,CAACJ,KAAK,CAACK,QAAQ;EAAC;IAAA2B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAS,CACxE;AACH,CAAC;AAAClC,EAAA,CAxCIF,UAAU;AAAAsC,EAAA,GAAVtC,UAAU;AA0ChB,eAAeA,UAAU;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}