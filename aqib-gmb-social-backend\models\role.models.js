const pool = require("../config/db");
const RoleType = require("../constants/dbConstants");

module.exports = class Role {
  static async fetchAll(userId) {
    try {
      const userData = await pool.query("SELECT * FROM users WHERE id = ?", [
        userId,
      ]);
      if (userData[0].roleId === RoleType.Admin) {
        const results = await pool.query("SELECT * FROM roles");
        return results;
      } else {
        const results = await pool.query(
          "SELECT * FROM roles WHERE id NOT IN (1)"
        );
        return results;
      }
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async fetchById(userId) {
    try {
      const userData = await pool.query("SELECT * FROM users WHERE id = ?", [
        userId,
      ]);
      const userRoles = await pool.query("SELECT * FROM roles WHERE id = ?", [
        userData[0].roleId,
      ]);
      return userRoles;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async updateById(fieldName, value, id) {
    try {
      const result = await pool.query(
        `UPDATE roles SET ${fieldName} = ? WHERE id = ?`,
        [value, id]
      );
      return result;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
};
