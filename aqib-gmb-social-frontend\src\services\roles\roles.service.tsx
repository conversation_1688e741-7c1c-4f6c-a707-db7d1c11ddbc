import { Component, Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import {
  LIST_OF_LOCATIONS,
  LIST_OF_ROLE,
  UPDATE_ROLE,
} from "../../constants/endPoints.constant";
import { Action } from "redux";

class RolesService {
  _httpHelperService;
  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  roleList = async (userId: number) => {
    return await this._httpHelperService.get(`${LIST_OF_ROLE}/${userId}`);
  };

  updateRole = async (roleId: number | undefined, request: any) => {
    return await this._httpHelperService.put(
      `${UPDATE_ROLE}/${roleId}`,
      request
    );
  };
}

export default RolesService;
