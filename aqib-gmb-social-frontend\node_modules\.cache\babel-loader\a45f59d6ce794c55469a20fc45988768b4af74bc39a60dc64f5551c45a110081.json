{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\unAuthorized\\\\notFoundPage.component.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { Button } from \"@mui/material\";\nimport ErrorOutlineIcon from \"@mui/icons-material/ErrorOutline\";\nimport { useDispatch } from \"react-redux\";\nimport { logOut } from \"../../actions/auth.actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UnAuthorized = ({\n  title\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const logoutUser = () => dispatch(logOut());\n  const [seconds, setSeconds] = useState(5);\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setSeconds(prev => prev - 1);\n    }, 1000);\n    const timeout = setTimeout(() => {\n      logoutUser();\n    }, 5 * 1000);\n    return () => {\n      clearInterval(interval);\n      clearTimeout(timeout);\n    };\n  }, [navigate]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: \"flex\",\n      height: \"100vh\",\n      backgroundColor: \"#f8f9fa\"\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flexGrow: 1,\n        display: \"flex\",\n        flexDirection: \"column\",\n        justifyContent: \"center\",\n        alignItems: \"center\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(ErrorOutlineIcon, {\n        style: {\n          fontSize: 80,\n          color: \"var(--secondaryColor)\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          color: \"var(--secondaryColor)\",\n          margin: \"1rem 0\"\n        },\n        children: \"Un-Authorized / No Access\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: \"#777\"\n        },\n        children: [\"You will be logged out in \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: seconds\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 37\n        }, this), \" second\", seconds !== 1 ? \"s\" : \"\", \"...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"tableActionBtn\",\n        onClick: () => logoutUser(),\n        children: \"Go to Login Now\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(UnAuthorized, \"Ellb34Db0zJ8yF3cWpBkDvCaTn0=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = UnAuthorized;\nexport default UnAuthorized;\nvar _c;\n$RefreshReg$(_c, \"UnAuthorized\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useNavigate", "<PERSON><PERSON>", "ErrorOutlineIcon", "useDispatch", "logOut", "jsxDEV", "_jsxDEV", "UnAuthorized", "title", "_s", "navigate", "dispatch", "logoutUser", "seconds", "setSeconds", "interval", "setInterval", "prev", "timeout", "setTimeout", "clearInterval", "clearTimeout", "style", "display", "height", "backgroundColor", "children", "flexGrow", "flexDirection", "justifyContent", "alignItems", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "className", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/unAuthorized/notFoundPage.component.tsx"], "sourcesContent": ["import React, { useEffect, useState, FunctionComponent } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { Button } from \"@mui/material\";\nimport ErrorOutlineIcon from \"@mui/icons-material/ErrorOutline\";\nimport PageProps from \"../../models/PageProps.interface\";\nimport { useDispatch } from \"react-redux\";\nimport { logOut } from \"../../actions/auth.actions\";\n\nconst UnAuthorized: FunctionComponent<PageProps> = ({ title }) => {\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const logoutUser = () => dispatch<any>(logOut());\n  const [seconds, setSeconds] = useState<number>(5);\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setSeconds((prev) => prev - 1);\n    }, 1000);\n\n    const timeout = setTimeout(() => {\n      logoutUser();\n    }, 5 * 1000);\n\n    return () => {\n      clearInterval(interval);\n      clearTimeout(timeout);\n    };\n  }, [navigate]);\n\n  return (\n    <div\n      style={{ display: \"flex\", height: \"100vh\", backgroundColor: \"#f8f9fa\" }}\n    >\n      {/* Main content */}\n      <div\n        style={{\n          flexGrow: 1,\n          display: \"flex\",\n          flexDirection: \"column\",\n          justifyContent: \"center\",\n          alignItems: \"center\",\n        }}\n      >\n        <ErrorOutlineIcon\n          style={{ fontSize: 80, color: \"var(--secondaryColor)\" }}\n        />\n        <h1 style={{ color: \"var(--secondaryColor)\", margin: \"1rem 0\" }}>\n          Un-Authorized / No Access\n        </h1>\n        <p style={{ color: \"#777\" }}>\n          You will be logged out in <strong>{seconds}</strong> second\n          {seconds !== 1 ? \"s\" : \"\"}...\n        </p>\n        <Button className=\"tableActionBtn\" onClick={() => logoutUser()}>\n          Go to Login Now\n        </Button>\n      </div>\n    </div>\n  );\n};\n\nexport default UnAuthorized;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAA2B,OAAO;AACrE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,gBAAgB,MAAM,kCAAkC;AAE/D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,YAA0C,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAChE,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,UAAU,GAAGA,CAAA,KAAMD,QAAQ,CAAMP,MAAM,CAAC,CAAC,CAAC;EAChD,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAS,CAAC,CAAC;EAEjDD,SAAS,CAAC,MAAM;IACd,MAAMiB,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCF,UAAU,CAAEG,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;IAChC,CAAC,EAAE,IAAI,CAAC;IAER,MAAMC,OAAO,GAAGC,UAAU,CAAC,MAAM;MAC/BP,UAAU,CAAC,CAAC;IACd,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;IAEZ,OAAO,MAAM;MACXQ,aAAa,CAACL,QAAQ,CAAC;MACvBM,YAAY,CAACH,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACR,QAAQ,CAAC,CAAC;EAEd,oBACEJ,OAAA;IACEgB,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,MAAM,EAAE,OAAO;MAAEC,eAAe,EAAE;IAAU,CAAE;IAAAC,QAAA,eAGxEpB,OAAA;MACEgB,KAAK,EAAE;QACLK,QAAQ,EAAE,CAAC;QACXJ,OAAO,EAAE,MAAM;QACfK,aAAa,EAAE,QAAQ;QACvBC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE;MACd,CAAE;MAAAJ,QAAA,gBAEFpB,OAAA,CAACJ,gBAAgB;QACfoB,KAAK,EAAE;UAAES,QAAQ,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAwB;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACF9B,OAAA;QAAIgB,KAAK,EAAE;UAAEU,KAAK,EAAE,uBAAuB;UAAEK,MAAM,EAAE;QAAS,CAAE;QAAAX,QAAA,EAAC;MAEjE;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL9B,OAAA;QAAGgB,KAAK,EAAE;UAAEU,KAAK,EAAE;QAAO,CAAE;QAAAN,QAAA,GAAC,4BACD,eAAApB,OAAA;UAAAoB,QAAA,EAASb;QAAO;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,WACpD,EAACvB,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,KAC5B;MAAA;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ9B,OAAA,CAACL,MAAM;QAACqC,SAAS,EAAC,gBAAgB;QAACC,OAAO,EAAEA,CAAA,KAAM3B,UAAU,CAAC,CAAE;QAAAc,QAAA,EAAC;MAEhE;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CAnDIF,YAA0C;EAAA,QAC7BP,WAAW,EACXG,WAAW;AAAA;AAAAqC,EAAA,GAFxBjC,YAA0C;AAqDhD,eAAeA,YAAY;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}