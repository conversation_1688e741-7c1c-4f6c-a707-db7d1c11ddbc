{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\fileUpload\\\\fileUpload.component.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from \"react\";\nimport { Box, Card, CardContent, Typography, Button, Alert, LinearProgress, Chip, IconButton, List, ListItem, ListItemText, ListItemSecondaryAction } from \"@mui/material\";\nimport CloudUploadIcon from \"@mui/icons-material/CloudUpload\";\nimport DeleteIcon from \"@mui/icons-material/Delete\";\nimport ImageIcon from \"@mui/icons-material/Image\";\nimport VideoLibraryIcon from \"@mui/icons-material/VideoLibrary\";\nimport { FileUtils } from \"../../utils/fileUtils\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FileUploadComponent = ({\n  onFileUpload,\n  uploading,\n  maxSizeMB,\n  currentUsageMB\n}) => {\n  _s();\n  const [selectedFiles, setSelectedFiles] = useState([]);\n  const [dragOver, setDragOver] = useState(false);\n  const fileInputRef = useRef(null);\n  const handleFileSelect = files => {\n    if (!files) return;\n    const newFiles = [];\n    const remainingSpace = maxSizeMB * 1024 * 1024 - currentUsageMB * 1024 * 1024;\n    Array.from(files).forEach(file => {\n      const validation = FileUtils.validateFile(file, 100); // 100MB per file limit\n\n      if (!validation.valid) {\n        newFiles.push({\n          file,\n          error: validation.error\n        });\n        return;\n      }\n\n      // Check if adding this file would exceed storage limit\n      const totalSelectedSize = selectedFiles.reduce((sum, f) => sum + f.file.size, 0);\n      if (totalSelectedSize + file.size > remainingSpace) {\n        newFiles.push({\n          file,\n          error: \"Would exceed storage limit\"\n        });\n        return;\n      }\n\n      // Create preview for images\n      if (FileUtils.isImage(file.type)) {\n        const reader = new FileReader();\n        reader.onload = e => {\n          const fileIndex = newFiles.findIndex(f => f.file === file);\n          if (fileIndex !== -1) {\n            var _e$target;\n            newFiles[fileIndex].preview = (_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result;\n            setSelectedFiles([...selectedFiles, ...newFiles]);\n          }\n        };\n        reader.readAsDataURL(file);\n        newFiles.push({\n          file\n        });\n      } else {\n        newFiles.push({\n          file\n        });\n      }\n    });\n    if (newFiles.some(f => !FileUtils.isImage(f.file.type))) {\n      setSelectedFiles([...selectedFiles, ...newFiles]);\n    }\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    setDragOver(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setDragOver(false);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setDragOver(false);\n    handleFileSelect(e.dataTransfer.files);\n  };\n  const handleFileInputChange = e => {\n    handleFileSelect(e.target.files);\n  };\n  const removeFile = index => {\n    const newFiles = [...selectedFiles];\n    newFiles.splice(index, 1);\n    setSelectedFiles(newFiles);\n  };\n  const handleUpload = () => {\n    const validFiles = selectedFiles.filter(f => !f.error);\n    if (validFiles.length === 0) return;\n    const fileList = new DataTransfer();\n    validFiles.forEach(f => fileList.items.add(f.file));\n    onFileUpload(fileList.files);\n    setSelectedFiles([]);\n  };\n  const getTotalSize = () => {\n    return selectedFiles.reduce((sum, f) => sum + f.file.size, 0);\n  };\n  const getValidFilesCount = () => {\n    return selectedFiles.filter(f => !f.error).length;\n  };\n  const remainingSpaceMB = maxSizeMB - currentUsageMB;\n  const canUpload = getValidFilesCount() > 0 && !uploading;\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      marginBottom: 3\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Upload Assets\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), remainingSpaceMB <= 0 && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          marginBottom: 2\n        },\n        children: \"Storage limit reached. Please delete some files or increase storage limit.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 11\n      }, this), remainingSpaceMB > 0 && remainingSpaceMB < 100 && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        sx: {\n          marginBottom: 2\n        },\n        children: [\"Low storage space remaining: \", remainingSpaceMB.toFixed(2), \" MB\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        className: `upload-zone ${dragOver ? \"dragover\" : \"\"}`,\n        onDragOver: handleDragOver,\n        onDragLeave: handleDragLeave,\n        onDrop: handleDrop,\n        onClick: () => {\n          var _fileInputRef$current;\n          return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n        },\n        sx: {\n          border: \"2px dashed #ccc\",\n          borderRadius: 2,\n          padding: 4,\n          textAlign: \"center\",\n          cursor: remainingSpaceMB > 0 ? \"pointer\" : \"not-allowed\",\n          backgroundColor: dragOver ? \"#e3f2fd\" : \"transparent\",\n          borderColor: dragOver ? \"#1976d2\" : \"#ccc\",\n          opacity: remainingSpaceMB > 0 ? 1 : 0.5,\n          transition: \"all 0.3s ease\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(CloudUploadIcon, {\n          sx: {\n            fontSize: 48,\n            color: \"#1976d2\",\n            marginBottom: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: remainingSpaceMB > 0 ? \"Drop files here or click to browse\" : \"Storage limit reached\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          gutterBottom: true,\n          children: \"Supported formats: JPEG, PNG, GIF, WebP, MP4, AVI, MOV, WMV, FLV, WebM\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Maximum file size: 100 MB per file\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        ref: fileInputRef,\n        type: \"file\",\n        multiple: true,\n        accept: \"image/*,video/*\",\n        onChange: handleFileInputChange,\n        style: {\n          display: \"none\"\n        },\n        disabled: remainingSpaceMB <= 0\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), selectedFiles.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          marginTop: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: [\"Selected Files (\", getValidFilesCount(), \" valid,\", \" \", ManageAssetsService.formatFileSize(getTotalSize()), \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          dense: true,\n          children: selectedFiles.map((fileWithPreview, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n            divider: true,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                flex: 1\n              },\n              children: [ManageAssetsService.isImage(fileWithPreview.file.type) ? /*#__PURE__*/_jsxDEV(ImageIcon, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(VideoLibraryIcon, {\n                color: \"secondary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  flex: 1\n                },\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: fileWithPreview.file.name,\n                  secondary: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: ManageAssetsService.formatFileSize(fileWithPreview.file.size)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: ManageAssetsService.getFileTypeDisplay(fileWithPreview.file.type),\n                      size: \"small\",\n                      color: ManageAssetsService.isImage(fileWithPreview.file.type) ? \"primary\" : \"secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 29\n                    }, this), fileWithPreview.error && /*#__PURE__*/_jsxDEV(Chip, {\n                      label: fileWithPreview.error,\n                      size: \"small\",\n                      color: \"error\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 21\n              }, this), fileWithPreview.preview && /*#__PURE__*/_jsxDEV(Box, {\n                component: \"img\",\n                src: fileWithPreview.preview,\n                alt: \"Preview\",\n                sx: {\n                  width: 50,\n                  height: 50,\n                  objectFit: \"cover\",\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                edge: \"end\",\n                onClick: () => removeFile(index),\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            marginTop: 2,\n            display: \"flex\",\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleUpload,\n            disabled: !canUpload,\n            startIcon: /*#__PURE__*/_jsxDEV(CloudUploadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 28\n            }, this),\n            children: [\"Upload \", getValidFilesCount(), \" File\", getValidFilesCount() !== 1 ? \"s\" : \"\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: () => setSelectedFiles([]),\n            disabled: uploading,\n            children: \"Clear All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this), uploading && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            marginTop: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            gutterBottom: true,\n            children: \"Uploading files...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this);\n};\n_s(FileUploadComponent, \"i2w3eYJS4AgGSVv6vEiqXmYEm2w=\");\n_c = FileUploadComponent;\nexport default FileUploadComponent;\nvar _c;\n$RefreshReg$(_c, \"FileUploadComponent\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "LinearProgress", "Chip", "IconButton", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "CloudUploadIcon", "DeleteIcon", "ImageIcon", "VideoLibraryIcon", "FileUtils", "jsxDEV", "_jsxDEV", "FileUploadComponent", "onFileUpload", "uploading", "maxSizeMB", "currentUsageMB", "_s", "selectedFiles", "setSelectedFiles", "dragOver", "setDragOver", "fileInputRef", "handleFileSelect", "files", "newFiles", "remainingSpace", "Array", "from", "for<PERSON>ach", "file", "validation", "validateFile", "valid", "push", "error", "totalSelectedSize", "reduce", "sum", "f", "size", "isImage", "type", "reader", "FileReader", "onload", "e", "fileIndex", "findIndex", "_e$target", "preview", "target", "result", "readAsDataURL", "some", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "handleFileInputChange", "removeFile", "index", "splice", "handleUpload", "validFiles", "filter", "length", "fileList", "DataTransfer", "items", "add", "getTotalSize", "getValidFilesCount", "remainingSpaceMB", "canUpload", "sx", "marginBottom", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "toFixed", "className", "onDragOver", "onDragLeave", "onDrop", "onClick", "_fileInputRef$current", "current", "click", "border", "borderRadius", "padding", "textAlign", "cursor", "backgroundColor", "borderColor", "opacity", "transition", "fontSize", "color", "ref", "multiple", "accept", "onChange", "style", "display", "disabled", "marginTop", "ManageAssetsService", "formatFileSize", "dense", "map", "fileWithPreview", "divider", "alignItems", "gap", "flex", "primary", "name", "secondary", "label", "getFileTypeDisplay", "component", "src", "alt", "width", "height", "objectFit", "edge", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/fileUpload/fileUpload.component.tsx"], "sourcesContent": ["import React, { useState, useRef, DragEvent } from \"react\";\nimport {\n  <PERSON>,\n  <PERSON>,\n  CardContent,\n  <PERSON><PERSON><PERSON>,\n  Button,\n  Alert,\n  LinearProgress,\n  Chip,\n  IconButton,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n} from \"@mui/material\";\nimport CloudUploadIcon from \"@mui/icons-material/CloudUpload\";\nimport DeleteIcon from \"@mui/icons-material/Delete\";\nimport ImageIcon from \"@mui/icons-material/Image\";\nimport VideoLibraryIcon from \"@mui/icons-material/VideoLibrary\";\nimport { FileUtils } from \"../../utils/fileUtils\";\n\ninterface FileUploadComponentProps {\n  onFileUpload: (files: FileList) => void;\n  uploading: boolean;\n  maxSizeMB: number;\n  currentUsageMB: number;\n}\n\ninterface FileWithPreview {\n  file: File;\n  preview?: string;\n  error?: string;\n}\n\nconst FileUploadComponent: React.FC<FileUploadComponentProps> = ({\n  onFileUpload,\n  uploading,\n  maxSizeMB,\n  currentUsageMB,\n}) => {\n  const [selectedFiles, setSelectedFiles] = useState<FileWithPreview[]>([]);\n  const [dragOver, setDragOver] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleFileSelect = (files: FileList | null) => {\n    if (!files) return;\n\n    const newFiles: FileWithPreview[] = [];\n    const remainingSpace =\n      maxSizeMB * 1024 * 1024 - currentUsageMB * 1024 * 1024;\n\n    Array.from(files).forEach((file) => {\n      const validation = FileUtils.validateFile(file, 100); // 100MB per file limit\n\n      if (!validation.valid) {\n        newFiles.push({\n          file,\n          error: validation.error,\n        });\n        return;\n      }\n\n      // Check if adding this file would exceed storage limit\n      const totalSelectedSize = selectedFiles.reduce(\n        (sum, f) => sum + f.file.size,\n        0\n      );\n      if (totalSelectedSize + file.size > remainingSpace) {\n        newFiles.push({\n          file,\n          error: \"Would exceed storage limit\",\n        });\n        return;\n      }\n\n      // Create preview for images\n      if (FileUtils.isImage(file.type)) {\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          const fileIndex = newFiles.findIndex((f) => f.file === file);\n          if (fileIndex !== -1) {\n            newFiles[fileIndex].preview = e.target?.result as string;\n            setSelectedFiles([...selectedFiles, ...newFiles]);\n          }\n        };\n        reader.readAsDataURL(file);\n        newFiles.push({ file });\n      } else {\n        newFiles.push({ file });\n      }\n    });\n\n    if (newFiles.some((f) => !FileUtils.isImage(f.file.type))) {\n      setSelectedFiles([...selectedFiles, ...newFiles]);\n    }\n  };\n\n  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    setDragOver(true);\n  };\n\n  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    setDragOver(false);\n  };\n\n  const handleDrop = (e: DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    setDragOver(false);\n    handleFileSelect(e.dataTransfer.files);\n  };\n\n  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    handleFileSelect(e.target.files);\n  };\n\n  const removeFile = (index: number) => {\n    const newFiles = [...selectedFiles];\n    newFiles.splice(index, 1);\n    setSelectedFiles(newFiles);\n  };\n\n  const handleUpload = () => {\n    const validFiles = selectedFiles.filter((f) => !f.error);\n    if (validFiles.length === 0) return;\n\n    const fileList = new DataTransfer();\n    validFiles.forEach((f) => fileList.items.add(f.file));\n\n    onFileUpload(fileList.files);\n    setSelectedFiles([]);\n  };\n\n  const getTotalSize = () => {\n    return selectedFiles.reduce((sum, f) => sum + f.file.size, 0);\n  };\n\n  const getValidFilesCount = () => {\n    return selectedFiles.filter((f) => !f.error).length;\n  };\n\n  const remainingSpaceMB = maxSizeMB - currentUsageMB;\n  const canUpload = getValidFilesCount() > 0 && !uploading;\n\n  return (\n    <Card sx={{ marginBottom: 3 }}>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom>\n          Upload Assets\n        </Typography>\n\n        {remainingSpaceMB <= 0 && (\n          <Alert severity=\"error\" sx={{ marginBottom: 2 }}>\n            Storage limit reached. Please delete some files or increase storage\n            limit.\n          </Alert>\n        )}\n\n        {remainingSpaceMB > 0 && remainingSpaceMB < 100 && (\n          <Alert severity=\"warning\" sx={{ marginBottom: 2 }}>\n            Low storage space remaining: {remainingSpaceMB.toFixed(2)} MB\n          </Alert>\n        )}\n\n        <Box\n          className={`upload-zone ${dragOver ? \"dragover\" : \"\"}`}\n          onDragOver={handleDragOver}\n          onDragLeave={handleDragLeave}\n          onDrop={handleDrop}\n          onClick={() => fileInputRef.current?.click()}\n          sx={{\n            border: \"2px dashed #ccc\",\n            borderRadius: 2,\n            padding: 4,\n            textAlign: \"center\",\n            cursor: remainingSpaceMB > 0 ? \"pointer\" : \"not-allowed\",\n            backgroundColor: dragOver ? \"#e3f2fd\" : \"transparent\",\n            borderColor: dragOver ? \"#1976d2\" : \"#ccc\",\n            opacity: remainingSpaceMB > 0 ? 1 : 0.5,\n            transition: \"all 0.3s ease\",\n          }}\n        >\n          <CloudUploadIcon\n            sx={{ fontSize: 48, color: \"#1976d2\", marginBottom: 2 }}\n          />\n          <Typography variant=\"h6\" gutterBottom>\n            {remainingSpaceMB > 0\n              ? \"Drop files here or click to browse\"\n              : \"Storage limit reached\"}\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n            Supported formats: JPEG, PNG, GIF, WebP, MP4, AVI, MOV, WMV, FLV,\n            WebM\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Maximum file size: 100 MB per file\n          </Typography>\n        </Box>\n\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          multiple\n          accept=\"image/*,video/*\"\n          onChange={handleFileInputChange}\n          style={{ display: \"none\" }}\n          disabled={remainingSpaceMB <= 0}\n        />\n\n        {selectedFiles.length > 0 && (\n          <Box sx={{ marginTop: 3 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Selected Files ({getValidFilesCount()} valid,{\" \"}\n              {ManageAssetsService.formatFileSize(getTotalSize())})\n            </Typography>\n\n            <List dense>\n              {selectedFiles.map((fileWithPreview, index) => (\n                <ListItem key={index} divider>\n                  <Box\n                    sx={{\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 2,\n                      flex: 1,\n                    }}\n                  >\n                    {ManageAssetsService.isImage(fileWithPreview.file.type) ? (\n                      <ImageIcon color=\"primary\" />\n                    ) : (\n                      <VideoLibraryIcon color=\"secondary\" />\n                    )}\n\n                    <Box sx={{ flex: 1 }}>\n                      <ListItemText\n                        primary={fileWithPreview.file.name}\n                        secondary={\n                          <Box\n                            sx={{\n                              display: \"flex\",\n                              alignItems: \"center\",\n                              gap: 1,\n                            }}\n                          >\n                            <span>\n                              {ManageAssetsService.formatFileSize(\n                                fileWithPreview.file.size\n                              )}\n                            </span>\n                            <Chip\n                              label={ManageAssetsService.getFileTypeDisplay(\n                                fileWithPreview.file.type\n                              )}\n                              size=\"small\"\n                              color={\n                                ManageAssetsService.isImage(\n                                  fileWithPreview.file.type\n                                )\n                                  ? \"primary\"\n                                  : \"secondary\"\n                              }\n                            />\n                            {fileWithPreview.error && (\n                              <Chip\n                                label={fileWithPreview.error}\n                                size=\"small\"\n                                color=\"error\"\n                              />\n                            )}\n                          </Box>\n                        }\n                      />\n                    </Box>\n\n                    {fileWithPreview.preview && (\n                      <Box\n                        component=\"img\"\n                        src={fileWithPreview.preview}\n                        alt=\"Preview\"\n                        sx={{\n                          width: 50,\n                          height: 50,\n                          objectFit: \"cover\",\n                          borderRadius: 1,\n                        }}\n                      />\n                    )}\n                  </Box>\n\n                  <ListItemSecondaryAction>\n                    <IconButton\n                      edge=\"end\"\n                      onClick={() => removeFile(index)}\n                      size=\"small\"\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  </ListItemSecondaryAction>\n                </ListItem>\n              ))}\n            </List>\n\n            <Box sx={{ marginTop: 2, display: \"flex\", gap: 2 }}>\n              <Button\n                variant=\"contained\"\n                onClick={handleUpload}\n                disabled={!canUpload}\n                startIcon={<CloudUploadIcon />}\n              >\n                Upload {getValidFilesCount()} File\n                {getValidFilesCount() !== 1 ? \"s\" : \"\"}\n              </Button>\n\n              <Button\n                variant=\"outlined\"\n                onClick={() => setSelectedFiles([])}\n                disabled={uploading}\n              >\n                Clear All\n              </Button>\n            </Box>\n\n            {uploading && (\n              <Box sx={{ marginTop: 2 }}>\n                <Typography variant=\"body2\" gutterBottom>\n                  Uploading files...\n                </Typography>\n                <LinearProgress />\n              </Box>\n            )}\n          </Box>\n        )}\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default FileUploadComponent;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAmB,OAAO;AAC1D,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,cAAc,EACdC,IAAI,EACJC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,QAClB,eAAe;AACtB,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,SAASC,SAAS,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAelD,MAAMC,mBAAuD,GAAGA,CAAC;EAC/DC,YAAY;EACZC,SAAS;EACTC,SAAS;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAoB,EAAE,CAAC;EACzE,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMgC,YAAY,GAAG/B,MAAM,CAAmB,IAAI,CAAC;EAEnD,MAAMgC,gBAAgB,GAAIC,KAAsB,IAAK;IACnD,IAAI,CAACA,KAAK,EAAE;IAEZ,MAAMC,QAA2B,GAAG,EAAE;IACtC,MAAMC,cAAc,GAClBX,SAAS,GAAG,IAAI,GAAG,IAAI,GAAGC,cAAc,GAAG,IAAI,GAAG,IAAI;IAExDW,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC,CAACK,OAAO,CAAEC,IAAI,IAAK;MAClC,MAAMC,UAAU,GAAGtB,SAAS,CAACuB,YAAY,CAACF,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;;MAEtD,IAAI,CAACC,UAAU,CAACE,KAAK,EAAE;QACrBR,QAAQ,CAACS,IAAI,CAAC;UACZJ,IAAI;UACJK,KAAK,EAAEJ,UAAU,CAACI;QACpB,CAAC,CAAC;QACF;MACF;;MAEA;MACA,MAAMC,iBAAiB,GAAGlB,aAAa,CAACmB,MAAM,CAC5C,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACT,IAAI,CAACU,IAAI,EAC7B,CACF,CAAC;MACD,IAAIJ,iBAAiB,GAAGN,IAAI,CAACU,IAAI,GAAGd,cAAc,EAAE;QAClDD,QAAQ,CAACS,IAAI,CAAC;UACZJ,IAAI;UACJK,KAAK,EAAE;QACT,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAI1B,SAAS,CAACgC,OAAO,CAACX,IAAI,CAACY,IAAI,CAAC,EAAE;QAChC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;UACrB,MAAMC,SAAS,GAAGtB,QAAQ,CAACuB,SAAS,CAAET,CAAC,IAAKA,CAAC,CAACT,IAAI,KAAKA,IAAI,CAAC;UAC5D,IAAIiB,SAAS,KAAK,CAAC,CAAC,EAAE;YAAA,IAAAE,SAAA;YACpBxB,QAAQ,CAACsB,SAAS,CAAC,CAACG,OAAO,IAAAD,SAAA,GAAGH,CAAC,CAACK,MAAM,cAAAF,SAAA,uBAARA,SAAA,CAAUG,MAAgB;YACxDjC,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAE,GAAGO,QAAQ,CAAC,CAAC;UACnD;QACF,CAAC;QACDkB,MAAM,CAACU,aAAa,CAACvB,IAAI,CAAC;QAC1BL,QAAQ,CAACS,IAAI,CAAC;UAAEJ;QAAK,CAAC,CAAC;MACzB,CAAC,MAAM;QACLL,QAAQ,CAACS,IAAI,CAAC;UAAEJ;QAAK,CAAC,CAAC;MACzB;IACF,CAAC,CAAC;IAEF,IAAIL,QAAQ,CAAC6B,IAAI,CAAEf,CAAC,IAAK,CAAC9B,SAAS,CAACgC,OAAO,CAACF,CAAC,CAACT,IAAI,CAACY,IAAI,CAAC,CAAC,EAAE;MACzDvB,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAE,GAAGO,QAAQ,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAM8B,cAAc,GAAIT,CAA4B,IAAK;IACvDA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClBnC,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMoC,eAAe,GAAIX,CAA4B,IAAK;IACxDA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClBnC,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMqC,UAAU,GAAIZ,CAA4B,IAAK;IACnDA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClBnC,WAAW,CAAC,KAAK,CAAC;IAClBE,gBAAgB,CAACuB,CAAC,CAACa,YAAY,CAACnC,KAAK,CAAC;EACxC,CAAC;EAED,MAAMoC,qBAAqB,GAAId,CAAsC,IAAK;IACxEvB,gBAAgB,CAACuB,CAAC,CAACK,MAAM,CAAC3B,KAAK,CAAC;EAClC,CAAC;EAED,MAAMqC,UAAU,GAAIC,KAAa,IAAK;IACpC,MAAMrC,QAAQ,GAAG,CAAC,GAAGP,aAAa,CAAC;IACnCO,QAAQ,CAACsC,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IACzB3C,gBAAgB,CAACM,QAAQ,CAAC;EAC5B,CAAC;EAED,MAAMuC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG/C,aAAa,CAACgD,MAAM,CAAE3B,CAAC,IAAK,CAACA,CAAC,CAACJ,KAAK,CAAC;IACxD,IAAI8B,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;IAE7B,MAAMC,QAAQ,GAAG,IAAIC,YAAY,CAAC,CAAC;IACnCJ,UAAU,CAACpC,OAAO,CAAEU,CAAC,IAAK6B,QAAQ,CAACE,KAAK,CAACC,GAAG,CAAChC,CAAC,CAACT,IAAI,CAAC,CAAC;IAErDjB,YAAY,CAACuD,QAAQ,CAAC5C,KAAK,CAAC;IAC5BL,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,MAAMqD,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAOtD,aAAa,CAACmB,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACT,IAAI,CAACU,IAAI,EAAE,CAAC,CAAC;EAC/D,CAAC;EAED,MAAMiC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAOvD,aAAa,CAACgD,MAAM,CAAE3B,CAAC,IAAK,CAACA,CAAC,CAACJ,KAAK,CAAC,CAACgC,MAAM;EACrD,CAAC;EAED,MAAMO,gBAAgB,GAAG3D,SAAS,GAAGC,cAAc;EACnD,MAAM2D,SAAS,GAAGF,kBAAkB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC3D,SAAS;EAExD,oBACEH,OAAA,CAAClB,IAAI;IAACmF,EAAE,EAAE;MAAEC,YAAY,EAAE;IAAE,CAAE;IAAAC,QAAA,eAC5BnE,OAAA,CAACjB,WAAW;MAAAoF,QAAA,gBACVnE,OAAA,CAAChB,UAAU;QAACoF,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZV,gBAAgB,IAAI,CAAC,iBACpB/D,OAAA,CAACd,KAAK;QAACwF,QAAQ,EAAC,OAAO;QAACT,EAAE,EAAE;UAAEC,YAAY,EAAE;QAAE,CAAE;QAAAC,QAAA,EAAC;MAGjD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,EAEAV,gBAAgB,GAAG,CAAC,IAAIA,gBAAgB,GAAG,GAAG,iBAC7C/D,OAAA,CAACd,KAAK;QAACwF,QAAQ,EAAC,SAAS;QAACT,EAAE,EAAE;UAAEC,YAAY,EAAE;QAAE,CAAE;QAAAC,QAAA,GAAC,+BACpB,EAACJ,gBAAgB,CAACY,OAAO,CAAC,CAAC,CAAC,EAAC,KAC5D;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,eAEDzE,OAAA,CAACnB,GAAG;QACF+F,SAAS,EAAE,eAAenE,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAG;QACvDoE,UAAU,EAAEjC,cAAe;QAC3BkC,WAAW,EAAEhC,eAAgB;QAC7BiC,MAAM,EAAEhC,UAAW;QACnBiC,OAAO,EAAEA,CAAA;UAAA,IAAAC,qBAAA;UAAA,QAAAA,qBAAA,GAAMtE,YAAY,CAACuE,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,KAAK,CAAC,CAAC;QAAA,CAAC;QAC7ClB,EAAE,EAAE;UACFmB,MAAM,EAAE,iBAAiB;UACzBC,YAAY,EAAE,CAAC;UACfC,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,QAAQ;UACnBC,MAAM,EAAEzB,gBAAgB,GAAG,CAAC,GAAG,SAAS,GAAG,aAAa;UACxD0B,eAAe,EAAEhF,QAAQ,GAAG,SAAS,GAAG,aAAa;UACrDiF,WAAW,EAAEjF,QAAQ,GAAG,SAAS,GAAG,MAAM;UAC1CkF,OAAO,EAAE5B,gBAAgB,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG;UACvC6B,UAAU,EAAE;QACd,CAAE;QAAAzB,QAAA,gBAEFnE,OAAA,CAACN,eAAe;UACduE,EAAE,EAAE;YAAE4B,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAE5B,YAAY,EAAE;UAAE;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACFzE,OAAA,CAAChB,UAAU;UAACoF,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAF,QAAA,EAClCJ,gBAAgB,GAAG,CAAC,GACjB,oCAAoC,GACpC;QAAuB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACbzE,OAAA,CAAChB,UAAU;UAACoF,OAAO,EAAC,OAAO;UAAC0B,KAAK,EAAC,gBAAgB;UAACzB,YAAY;UAAAF,QAAA,EAAC;QAGhE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzE,OAAA,CAAChB,UAAU;UAACoF,OAAO,EAAC,OAAO;UAAC0B,KAAK,EAAC,gBAAgB;UAAA3B,QAAA,EAAC;QAEnD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENzE,OAAA;QACE+F,GAAG,EAAEpF,YAAa;QAClBoB,IAAI,EAAC,MAAM;QACXiE,QAAQ;QACRC,MAAM,EAAC,iBAAiB;QACxBC,QAAQ,EAAEjD,qBAAsB;QAChCkD,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAO,CAAE;QAC3BC,QAAQ,EAAEtC,gBAAgB,IAAI;MAAE;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,EAEDlE,aAAa,CAACiD,MAAM,GAAG,CAAC,iBACvBxD,OAAA,CAACnB,GAAG;QAACoF,EAAE,EAAE;UAAEqC,SAAS,EAAE;QAAE,CAAE;QAAAnC,QAAA,gBACxBnE,OAAA,CAAChB,UAAU;UAACoF,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,GAAC,kBAC3B,EAACL,kBAAkB,CAAC,CAAC,EAAC,SAAO,EAAC,GAAG,EAChDyC,mBAAmB,CAACC,cAAc,CAAC3C,YAAY,CAAC,CAAC,CAAC,EAAC,GACtD;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbzE,OAAA,CAACV,IAAI;UAACmH,KAAK;UAAAtC,QAAA,EACR5D,aAAa,CAACmG,GAAG,CAAC,CAACC,eAAe,EAAExD,KAAK,kBACxCnD,OAAA,CAACT,QAAQ;YAAaqH,OAAO;YAAAzC,QAAA,gBAC3BnE,OAAA,CAACnB,GAAG;cACFoF,EAAE,EAAE;gBACFmC,OAAO,EAAE,MAAM;gBACfS,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE;cACR,CAAE;cAAA5C,QAAA,GAEDoC,mBAAmB,CAACzE,OAAO,CAAC6E,eAAe,CAACxF,IAAI,CAACY,IAAI,CAAC,gBACrD/B,OAAA,CAACJ,SAAS;gBAACkG,KAAK,EAAC;cAAS;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE7BzE,OAAA,CAACH,gBAAgB;gBAACiG,KAAK,EAAC;cAAW;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACtC,eAEDzE,OAAA,CAACnB,GAAG;gBAACoF,EAAE,EAAE;kBAAE8C,IAAI,EAAE;gBAAE,CAAE;gBAAA5C,QAAA,eACnBnE,OAAA,CAACR,YAAY;kBACXwH,OAAO,EAAEL,eAAe,CAACxF,IAAI,CAAC8F,IAAK;kBACnCC,SAAS,eACPlH,OAAA,CAACnB,GAAG;oBACFoF,EAAE,EAAE;sBACFmC,OAAO,EAAE,MAAM;sBACfS,UAAU,EAAE,QAAQ;sBACpBC,GAAG,EAAE;oBACP,CAAE;oBAAA3C,QAAA,gBAEFnE,OAAA;sBAAAmE,QAAA,EACGoC,mBAAmB,CAACC,cAAc,CACjCG,eAAe,CAACxF,IAAI,CAACU,IACvB;oBAAC;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG,CAAC,eACPzE,OAAA,CAACZ,IAAI;sBACH+H,KAAK,EAAEZ,mBAAmB,CAACa,kBAAkB,CAC3CT,eAAe,CAACxF,IAAI,CAACY,IACvB,CAAE;sBACFF,IAAI,EAAC,OAAO;sBACZiE,KAAK,EACHS,mBAAmB,CAACzE,OAAO,CACzB6E,eAAe,CAACxF,IAAI,CAACY,IACvB,CAAC,GACG,SAAS,GACT;oBACL;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,EACDkC,eAAe,CAACnF,KAAK,iBACpBxB,OAAA,CAACZ,IAAI;sBACH+H,KAAK,EAAER,eAAe,CAACnF,KAAM;sBAC7BK,IAAI,EAAC,OAAO;sBACZiE,KAAK,EAAC;oBAAO;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CACF;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAELkC,eAAe,CAACpE,OAAO,iBACtBvC,OAAA,CAACnB,GAAG;gBACFwI,SAAS,EAAC,KAAK;gBACfC,GAAG,EAAEX,eAAe,CAACpE,OAAQ;gBAC7BgF,GAAG,EAAC,SAAS;gBACbtD,EAAE,EAAE;kBACFuD,KAAK,EAAE,EAAE;kBACTC,MAAM,EAAE,EAAE;kBACVC,SAAS,EAAE,OAAO;kBAClBrC,YAAY,EAAE;gBAChB;cAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENzE,OAAA,CAACP,uBAAuB;cAAA0E,QAAA,eACtBnE,OAAA,CAACX,UAAU;gBACTsI,IAAI,EAAC,KAAK;gBACV3C,OAAO,EAAEA,CAAA,KAAM9B,UAAU,CAACC,KAAK,CAAE;gBACjCtB,IAAI,EAAC,OAAO;gBAAAsC,QAAA,eAEZnE,OAAA,CAACL,UAAU;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC;UAAA,GA/EbtB,KAAK;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgFV,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPzE,OAAA,CAACnB,GAAG;UAACoF,EAAE,EAAE;YAAEqC,SAAS,EAAE,CAAC;YAAEF,OAAO,EAAE,MAAM;YAAEU,GAAG,EAAE;UAAE,CAAE;UAAA3C,QAAA,gBACjDnE,OAAA,CAACf,MAAM;YACLmF,OAAO,EAAC,WAAW;YACnBY,OAAO,EAAE3B,YAAa;YACtBgD,QAAQ,EAAE,CAACrC,SAAU;YACrB4D,SAAS,eAAE5H,OAAA,CAACN,eAAe;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAN,QAAA,GAChC,SACQ,EAACL,kBAAkB,CAAC,CAAC,EAAC,OAC7B,EAACA,kBAAkB,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;UAAA;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAETzE,OAAA,CAACf,MAAM;YACLmF,OAAO,EAAC,UAAU;YAClBY,OAAO,EAAEA,CAAA,KAAMxE,gBAAgB,CAAC,EAAE,CAAE;YACpC6F,QAAQ,EAAElG,SAAU;YAAAgE,QAAA,EACrB;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELtE,SAAS,iBACRH,OAAA,CAACnB,GAAG;UAACoF,EAAE,EAAE;YAAEqC,SAAS,EAAE;UAAE,CAAE;UAAAnC,QAAA,gBACxBnE,OAAA,CAAChB,UAAU;YAACoF,OAAO,EAAC,OAAO;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEzC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzE,OAAA,CAACb,cAAc;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAACnE,EAAA,CA9SIL,mBAAuD;AAAA4H,EAAA,GAAvD5H,mBAAuD;AAgT7D,eAAeA,mBAAmB;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}