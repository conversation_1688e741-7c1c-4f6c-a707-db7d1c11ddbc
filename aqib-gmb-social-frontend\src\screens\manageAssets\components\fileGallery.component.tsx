import React from "react";
import {
  Box,
  Card,
  CardContent,
  CardMedia,
  Typography,
  IconButton,
  Grid,
  Chip,
  Tooltip,
} from "@mui/material";
import {
  Visibility as VisibilityIcon,
  Delete as DeleteIcon,
  Image as ImageIcon,
  VideoFile as VideoIcon,
  PlayCircle as PlayIcon,
} from "@mui/icons-material";

interface IAsset {
  id: number;
  business_id: number;
  user_id: number;
  file_name: string;
  original_file_name: string;
  file_type: "image" | "video";
  file_size: number;
  s3_key: string;
  s3_url: string;
  mime_type: string;
  upload_date: string;
  status: string;
  uploaded_by_name?: string;
  thumbnail_s3_url?: string;
}

interface FileGalleryComponentProps {
  assets: IAsset[];
  onViewAsset: (asset: IAsset) => void;
  onDeleteAsset: (asset: IAsset) => void;
  formatFileSize: (bytes: number) => string;
}

const FileGalleryComponent: React.FC<FileGalleryComponentProps> = ({
  assets,
  onViewAsset,
  onDeleteAsset,
  formatFileSize,
}) => {
  const getPreviewUrl = (asset: IAsset): string => {
    // Use thumbnail if available, otherwise use original for images
    if (asset.thumbnail_s3_url) {
      return asset.thumbnail_s3_url;
    }

    // For images without thumbnails, use the original
    if (asset.file_type === "image") {
      return asset.s3_url;
    }

    // For videos without thumbnails, return empty (will show placeholder)
    return "";
  };

  const getFileTypeIcon = (asset: IAsset) => {
    return asset.file_type === "image" ? <ImageIcon /> : <VideoIcon />;
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (assets.length === 0) {
    return (
      <Card>
        <CardContent>
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            minHeight={200}
            textAlign="center"
          >
            <ImageIcon sx={{ fontSize: 64, color: "#ccc", marginBottom: 2 }} />
            <Typography variant="h6" color="text.secondary">
              No assets uploaded yet
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Upload your first image or video to get started
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Assets Gallery ({assets.length} items)
        </Typography>

        <Grid container spacing={2}>
          {assets.map((asset) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={asset.id}>
              <Card
                sx={{
                  height: "100%",
                  display: "flex",
                  flexDirection: "column",
                  position: "relative",
                  "&:hover": {
                    boxShadow: 4,
                  },
                }}
              >
                {/* Preview Image/Thumbnail */}
                <Box
                  sx={{
                    position: "relative",
                    paddingTop: "75%", // 4:3 aspect ratio
                    backgroundColor: "#f5f5f5",
                    overflow: "hidden",
                  }}
                >
                  {getPreviewUrl(asset) ? (
                    <CardMedia
                      component="img"
                      image={getPreviewUrl(asset)}
                      alt={asset.original_file_name}
                      sx={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                    />
                  ) : (
                    // Placeholder for videos without thumbnails
                    <Box
                      sx={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        width: "100%",
                        height: "100%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        backgroundColor: "#e0e0e0",
                      }}
                    >
                      <VideoIcon sx={{ fontSize: 48, color: "#999" }} />
                    </Box>
                  )}

                  {/* Video Play Overlay */}
                  {asset.file_type === "video" && (
                    <Box
                      sx={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        color: "white",
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        borderRadius: "50%",
                        padding: 1,
                      }}
                    >
                      <PlayIcon sx={{ fontSize: 32 }} />
                    </Box>
                  )}

                  {/* File Type Badge */}
                  <Chip
                    icon={getFileTypeIcon(asset)}
                    label={asset.file_type.toUpperCase()}
                    size="small"
                    sx={{
                      position: "absolute",
                      top: 8,
                      left: 8,
                      backgroundColor: "rgba(0, 0, 0, 0.7)",
                      color: "white",
                    }}
                  />
                </Box>

                {/* Asset Info */}
                <CardContent sx={{ flexGrow: 1, padding: 2 }}>
                  <Tooltip title={asset.original_file_name}>
                    <Typography
                      variant="subtitle2"
                      noWrap
                      sx={{ fontWeight: "bold", marginBottom: 1 }}
                    >
                      {asset.original_file_name}
                    </Typography>
                  </Tooltip>

                  <Typography
                    variant="body2"
                    color="text.secondary"
                    gutterBottom
                  >
                    {formatFileSize(asset.file_size)}
                  </Typography>

                  <Typography
                    variant="caption"
                    color="text.secondary"
                    display="block"
                  >
                    {formatDate(asset.upload_date)}
                  </Typography>

                  {asset.uploaded_by_name && (
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      display="block"
                    >
                      by {asset.uploaded_by_name}
                    </Typography>
                  )}
                </CardContent>

                {/* Action Buttons */}
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    padding: 1,
                    borderTop: "1px solid #e0e0e0",
                  }}
                >
                  <Tooltip title="View Asset">
                    <IconButton
                      size="small"
                      onClick={() => onViewAsset(asset)}
                      color="primary"
                    >
                      <VisibilityIcon />
                    </IconButton>
                  </Tooltip>

                  <Tooltip title="Delete Asset">
                    <IconButton
                      size="small"
                      onClick={() => onDeleteAsset(asset)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Card>
            </Grid>
          ))}
        </Grid>
      </CardContent>
    </Card>
  );
};

export default FileGalleryComponent;
