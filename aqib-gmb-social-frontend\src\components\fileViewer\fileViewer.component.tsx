import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON>le,
  <PERSON>alogContent,
  <PERSON>alog<PERSON><PERSON>,
  <PERSON><PERSON>,
  Box,
  Typography,
  IconButton,
  Chip,
  Divider,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ZoomInIcon from "@mui/icons-material/ZoomIn";
import ZoomOutIcon from "@mui/icons-material/ZoomOut";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import FullscreenExitIcon from "@mui/icons-material/FullscreenExit";
import DownloadIcon from "@mui/icons-material/Download";
import { FileUtils } from "../../utils/fileUtils";

interface IAsset {
  id: number;
  business_id: number;
  user_id: number;
  file_name: string;
  original_file_name: string;
  file_type: "image" | "video";
  file_size: number;
  s3_key: string;
  s3_url: string;
  mime_type: string;
  upload_date: string;
  status: string;
  uploaded_by_name?: string;
}

interface FileViewerComponentProps {
  asset: IAsset | null;
  open: boolean;
  onClose: () => void;
}

const FileViewerComponent: React.FC<FileViewerComponentProps> = ({
  asset,
  open,
  onClose,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [zoom, setZoom] = useState(1);
  const [fullscreen, setFullscreen] = useState(false);

  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev - 0.25, 0.25));
  };

  const handleFullscreen = () => {
    setFullscreen(!fullscreen);
  };

  const handleDownload = () => {
    if (!asset) return;

    const link = document.createElement("a");
    link.href = asset.s3_url;
    link.download = asset.original_file_name;
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleClose = () => {
    setZoom(1);
    setFullscreen(false);
    onClose();
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (!asset) return null;

  const isImage = FileUtils.isImage(asset.mime_type);
  const isVideo = FileUtils.isVideo(asset.mime_type);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth={fullscreen ? false : "lg"}
      fullWidth
      fullScreen={fullscreen}
      PaperProps={{
        sx: {
          backgroundColor: fullscreen ? "#000" : "background.paper",
          ...(fullscreen && {
            margin: 0,
            maxHeight: "100vh",
            maxWidth: "100vw",
          }),
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: 2,
          backgroundColor: fullscreen
            ? "rgba(0, 0, 0, 0.8)"
            : "background.paper",
          color: fullscreen ? "white" : "text.primary",
        }}
      >
        <Typography variant="h6" component="div" sx={{ flex: 1 }}>
          {asset.original_file_name}
        </Typography>

        <Box sx={{ display: "flex", gap: 1 }}>
          {isImage && (
            <>
              <IconButton
                onClick={handleZoomOut}
                disabled={zoom <= 0.25}
                sx={{ color: fullscreen ? "white" : "inherit" }}
              >
                <ZoomOutIcon />
              </IconButton>

              <IconButton
                onClick={handleZoomIn}
                disabled={zoom >= 3}
                sx={{ color: fullscreen ? "white" : "inherit" }}
              >
                <ZoomInIcon />
              </IconButton>
            </>
          )}

          <IconButton
            onClick={handleFullscreen}
            sx={{ color: fullscreen ? "white" : "inherit" }}
          >
            {fullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
          </IconButton>

          <IconButton
            onClick={handleDownload}
            sx={{ color: fullscreen ? "white" : "inherit" }}
          >
            <DownloadIcon />
          </IconButton>

          <IconButton
            onClick={handleClose}
            sx={{ color: fullscreen ? "white" : "inherit" }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent
        sx={{
          padding: 0,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: fullscreen ? "#000" : "background.paper",
          minHeight: fullscreen ? "calc(100vh - 120px)" : "400px",
          overflow: "auto",
        }}
      >
        {isImage && (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: "100%",
              height: "100%",
              overflow: "auto",
              padding: 2,
            }}
          >
            <img
              src={asset.s3_url}
              alt={asset.original_file_name}
              style={{
                maxWidth: "100%",
                maxHeight: "100%",
                transform: `scale(${zoom})`,
                transition: "transform 0.2s ease",
                cursor: zoom > 1 ? "grab" : "default",
              }}
              onLoad={() => {
                // Reset zoom when image loads
                if (zoom !== 1) setZoom(1);
              }}
            />
          </Box>
        )}

        {isVideo && (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: "100%",
              height: "100%",
              padding: 2,
            }}
          >
            <video
              controls
              style={{
                maxWidth: "100%",
                maxHeight: "100%",
                backgroundColor: "#000",
              }}
              preload="metadata"
            >
              <source src={asset.s3_url} type={asset.mime_type} />
              Your browser does not support the video tag.
            </video>
          </Box>
        )}
      </DialogContent>

      {!fullscreen && (
        <DialogActions
          sx={{
            flexDirection: "column",
            alignItems: "stretch",
            padding: 2,
            gap: 2,
          }}
        >
          <Divider />

          <Box
            sx={{ display: "flex", flexWrap: "wrap", gap: 1, marginBottom: 1 }}
          >
            <Chip
              label={asset.file_type.toUpperCase()}
              color={isImage ? "primary" : "secondary"}
              size="small"
            />
            <Chip
              label={FileUtils.formatFileSize(asset.file_size)}
              variant="outlined"
              size="small"
            />
            <Chip label={asset.mime_type} variant="outlined" size="small" />
          </Box>

          <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
            <Typography variant="body2" color="text.secondary">
              <strong>Uploaded:</strong> {formatDate(asset.upload_date)}
            </Typography>

            {asset.uploaded_by_name && (
              <Typography variant="body2" color="text.secondary">
                <strong>Uploaded by:</strong> {asset.uploaded_by_name}
              </Typography>
            )}

            <Typography variant="body2" color="text.secondary">
              <strong>File ID:</strong> {asset.id}
            </Typography>
          </Box>

          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              marginTop: 1,
            }}
          >
            <Button
              variant="outlined"
              onClick={handleDownload}
              startIcon={<DownloadIcon />}
            >
              Download
            </Button>

            <Button variant="contained" onClick={handleClose}>
              Close
            </Button>
          </Box>
        </DialogActions>
      )}
    </Dialog>
  );
};

export default FileViewerComponent;
