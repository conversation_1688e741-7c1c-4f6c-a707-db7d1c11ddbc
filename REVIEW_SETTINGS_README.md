# Review Settings Feature

This document provides comprehensive information about the newly implemented Review Settings feature for the GMB Social application.

## Overview

The Review Settings feature allows users to:

- Create and manage star rating-based reply templates (1-5 stars)
- Configure automatic reply settings for reviews
- Set business hours for auto-replies
- Map templates to specific businesses
- Preview templates before saving

## Architecture

### Backend Components

#### Database Schema

- **reply_templates**: Stores reply templates with star ratings
- **business_reply_templates**: Maps templates to businesses
- **auto_reply_settings**: Stores auto-reply configuration per business

#### API Endpoints

- `GET /api/review-settings/templates/:userId` - Get all templates for a user
- `POST /api/review-settings/templates/:userId` - Create a new template
- `PUT /api/review-settings/templates/:userId/:templateId` - Update a template
- `DELETE /api/review-settings/templates/:userId/:templateId` - Delete a template
- `GET /api/review-settings/auto-reply/:businessId` - Get auto-reply settings
- `PUT /api/review-settings/auto-reply/:businessId` - Update auto-reply settings

### Frontend Components

#### Main Screen

- **Location**: `src/screens/reviewManagement/reviewSettings/reviewSettings.screen.tsx`
- **Features**: Tabbed interface, business selection, template management

#### Sub-Components

- **CreateEditTemplate**: Template creation and editing with preview
- **AutoReplySettings**: Auto-reply configuration with business hours

## Installation & Setup

### 1. Database Setup

The database configuration is read from `.env.development` file which contains AWS RDS MySQL connection details.

#### Option A: Test Database Connection First (Recommended)

```bash
cd aqib-gmb-social-backend
node database/test_connection.js
```

#### Option B: Quick Setup (Creates tables without foreign keys if dependencies missing)

```bash
cd aqib-gmb-social-backend
node database/setup_review_settings.js
```

#### Option C: Full Migration (Requires all dependency tables to exist)

```bash
cd aqib-gmb-social-backend
node database/run_review_settings_migration.js
```

### 2. Backend Setup

The backend routes are automatically included when you start the server:

```bash
cd aqib-gmb-social-backend
npm start
```

### 3. Frontend Setup

The frontend components are automatically included in the build:

```bash
cd aqib-gmb-social-frontend
npm start
```

### 4. Database Configuration

The application uses the following environment variables from `.env.development`:

- `APP_DB_HOST`: AWS RDS MySQL host
- `APP_DB_USER`: Database username
- `APP_DB_PASSWORD`: Database password
- `APP_DB_NAME`: Database name
- `APP_PORT`: Application port (default: 3000)

## Testing

### Backend API Testing

Use the provided test script to verify API functionality:

```bash
cd aqib-gmb-social-backend
node test_review_settings_api.js
```

### Manual Testing Steps

1. **Access the Feature**:

   - Navigate to Review Management → Review Settings
   - Select a business from the dropdown

2. **Test Template Management**:

   - Create templates for different star ratings (1-5)
   - Edit existing templates
   - Set default templates
   - Delete templates

3. **Test Auto-Reply Settings**:
   - Enable/disable auto-reply
   - Select star ratings for auto-reply
   - Configure delay settings
   - Set business hours

## Usage Guide

### Creating Reply Templates

1. Go to Review Settings → Reply Templates tab
2. Click "Create Template"
3. Fill in the form:
   - **Template Name**: Descriptive name for the template
   - **Star Rating**: Select 1-5 stars
   - **Template Content**: Write your reply message
   - **Set as Default**: Toggle to make this the default for the star rating
4. Preview your template
5. Save the template

### Configuring Auto-Reply

1. Go to Review Settings → Auto-Reply Settings tab
2. Enable auto-reply toggle
3. Select which star ratings should trigger auto-replies
4. Set delay (in minutes) before sending replies
5. Optionally enable business hours restriction
6. Set business hours if enabled
7. Save settings

### Template Variables

You can use these placeholders in your templates:

- `{customerName}` - Customer's name
- `{businessName}` - Your business name
- `{rating}` - Star rating number

## API Reference

### Create Template

```http
POST /api/review-settings/templates/:userId
Content-Type: application/json

{
  "starRating": 5,
  "templateName": "Thank You - 5 Star",
  "templateContent": "Thank you for your wonderful review!",
  "isDefault": true,
  "businessId": 1
}
```

### Update Auto-Reply Settings

```http
PUT /api/review-settings/auto-reply/:businessId
Content-Type: application/json

{
  "is_enabled": true,
  "enabled_star_ratings": [4, 5],
  "delay_minutes": 30,
  "only_business_hours": true,
  "business_hours_start": "09:00:00",
  "business_hours_end": "17:00:00"
}
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**:

   - Ensure MySQL is running
   - Check database credentials in `.env.development`

2. **Templates Not Loading**:

   - Verify user has businesses associated
   - Check browser console for API errors

3. **Auto-Reply Not Working**:
   - Ensure auto-reply is enabled
   - Check that star ratings are selected
   - Verify business hours if enabled

### Debug Mode

Enable debug logging by setting environment variable:

```bash
DEBUG=review-settings npm start
```

## Security Considerations

- All API endpoints require authentication
- Users can only access their own templates
- Business-template mappings are validated
- SQL injection protection via parameterized queries

## Performance Notes

- Templates are cached per user session
- Database queries use appropriate indexes
- Pagination support for large template lists

## Future Enhancements

Potential improvements for future versions:

- Template categories and tags
- Advanced scheduling options
- Template analytics and usage statistics
- Bulk template operations
- Template sharing between users

## Support

For issues or questions regarding the Review Settings feature:

1. Check this documentation
2. Review the test scripts for API usage examples
3. Check the browser console for frontend errors
4. Review server logs for backend issues
