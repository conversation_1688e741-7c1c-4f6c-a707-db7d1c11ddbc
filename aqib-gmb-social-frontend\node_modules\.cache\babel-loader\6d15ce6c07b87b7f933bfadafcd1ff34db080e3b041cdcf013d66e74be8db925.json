{"ast": null, "code": "export let ToastSeverity = /*#__PURE__*/function (ToastSeverity) {\n  ToastSeverity[\"Error\"] = \"error\";\n  ToastSeverity[\"Warning\"] = \"warning\";\n  ToastSeverity[\"Info\"] = \"info\";\n  ToastSeverity[\"Success\"] = \"success\";\n  return ToastSeverity;\n}({});\nexport const POST_TEMPLATE_CONFIG = {\n  backgroundColor: \"linear-gradient(to right, #ff512f, #dd2476)\",\n  showRating: true,\n  showAvatar: true,\n  font: \"Arial\",\n  fontStyle: \"Normal\",\n  fontColor: \"#000000\",\n  comment: \"\",\n  reviewerName: \"\",\n  starRating: \"\"\n};\nexport const TOPIC_TYPES = {\n  Offer: \"OFFER\",\n  WhatsNew: \"STANDARD\",\n  Event: \"EVENT\"\n  // Informative: \"STANDARD\",\n  // Standard: \"STANDARD\",\n};\nexport const EVENT_TYPES = {\n  Call: \"CALL\",\n  Book: \"BOOK\",\n  Order: \"ORDER\",\n  Shop: \"SHOP\",\n  SignUp: \"SIGN_UP\",\n  LearnMore: \"LEARN_MORE\"\n};\nexport const MISSING_INFORMATION = {\n  PhoneNumber: {\n    key: \"PHONE_NUMBER\",\n    title: \"Phone Number\"\n  },\n  Address: {\n    key: \"ADDRESS\",\n    title: \"Address\"\n  },\n  WebsiteLink: {\n    key: \"WEBSITE_LINK\",\n    title: \"Website Link\"\n  },\n  AddCategories: {\n    key: \"ADD_CATEGORIES\",\n    title: \"Add Categories\"\n  },\n  MenuLink: {\n    key: \"MENU_LINK\",\n    title: \"Menu Link\"\n  },\n  MenuItems: {\n    key: \"MENU_ITEMS\",\n    title: \"Menu Items\"\n  },\n  OpeningDate: {\n    key: \"OPENING_DATE\",\n    title: \"Opening Date\"\n  },\n  OpeningHours: {\n    key: \"OPENING_HOURS\",\n    title: \"Opening Hours\"\n  },\n  Attributes: {\n    key: \"ATTRIBUTES\",\n    title: \"Attributes\"\n  },\n  Photos: {\n    key: \"PHOTOS\",\n    title: \"Photos\"\n  },\n  Videos: {\n    key: \"VIDEOS\",\n    title: \"Videos\"\n  },\n  BusinessLogo: {\n    key: \"BUSINESS_LOGO\",\n    title: \"Business Logo\"\n  },\n  MenuPhotos: {\n    key: \"MENU_PHOTOS\",\n    title: \"Menu Photos\"\n  },\n  SocialLinks: {\n    key: \"SOCIAL_LINKS\",\n    title: \"Social Links\"\n  },\n  Posts: {\n    key: \"POSTS\",\n    title: \"Posts\"\n  },\n  Services: {\n    key: \"SERVICES\",\n    title: \"Services\"\n  },\n  Products: {\n    key: \"PRODUCTS\",\n    title: \"Products\"\n  },\n  ReviewLink: {\n    key: \"REVIEWLINK\",\n    title: \"Review Link\"\n  },\n  QandA: {\n    key: \"QANDA\",\n    title: \"Q&A\"\n  },\n  AutoReply: {\n    key: \"AUTO_REPLY\",\n    title: \"Auto Reply\"\n  },\n  ProfileProtection: {\n    key: \"PROFILE_PROTECTION\",\n    title: \"Profile Protection\"\n  }\n};", "map": {"version": 3, "names": ["ToastSeverity", "POST_TEMPLATE_CONFIG", "backgroundColor", "showRating", "showAvatar", "font", "fontStyle", "fontColor", "comment", "reviewerName", "starRating", "TOPIC_TYPES", "Offer", "WhatsNew", "Event", "EVENT_TYPES", "Call", "Book", "Order", "Shop", "SignUp", "LearnMore", "MISSING_INFORMATION", "PhoneNumber", "key", "title", "Address", "WebsiteLink", "AddCategories", "MenuLink", "MenuItems", "OpeningDate", "OpeningHours", "Attributes", "Photos", "Videos", "BusinessLogo", "MenuPhotos", "SocialLinks", "Posts", "Services", "Products", "ReviewLink", "QandA", "AutoReply", "ProfileProtection"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/constants/application.constant.tsx"], "sourcesContent": ["import { title } from \"process\";\nimport { IPostTemplateConfig } from \"../types/IPostTemplateConfig\";\n\nexport enum ToastSeverity {\n  Error = \"error\",\n  Warning = \"warning\",\n  Info = \"info\",\n  Success = \"success\",\n}\n\nexport const POST_TEMPLATE_CONFIG: IPostTemplateConfig = {\n  backgroundColor: \"linear-gradient(to right, #ff512f, #dd2476)\",\n  showRating: true,\n  showAvatar: true,\n  font: \"Arial\",\n  fontStyle: \"Normal\",\n  fontColor: \"#000000\",\n  comment: \"\",\n  reviewerName: \"\",\n  starRating: \"\",\n};\n\nexport const TOPIC_TYPES = {\n  Offer: \"OFFER\",\n  WhatsNew: \"STANDARD\",\n  Event: \"EVENT\",\n  // Informative: \"STANDARD\",\n  // Standard: \"STANDARD\",\n};\n\nexport const EVENT_TYPES = {\n  Call: \"CALL\",\n  Book: \"BOOK\",\n  Order: \"ORDER\",\n  Shop: \"SHOP\",\n  SignUp: \"SIGN_UP\",\n  LearnMore: \"LEARN_MORE\",\n};\n\nexport const MISSING_INFORMATION = {\n  PhoneNumber: { key: \"PHONE_NUMBER\", title: \"Phone Number\" },\n  Address: { key: \"ADDRESS\", title: \"Address\" },\n  WebsiteLink: { key: \"WEBSITE_LINK\", title: \"Website Link\" },\n  AddCategories: { key: \"ADD_CATEGORIES\", title: \"Add Categories\" },\n  MenuLink: { key: \"MENU_LINK\", title: \"Menu Link\" },\n  MenuItems: { key: \"MENU_ITEMS\", title: \"Menu Items\" },\n  OpeningDate: { key: \"OPENING_DATE\", title: \"Opening Date\" },\n  OpeningHours: { key: \"OPENING_HOURS\", title: \"Opening Hours\" },\n  Attributes: { key: \"ATTRIBUTES\", title: \"Attributes\" },\n  Photos: { key: \"PHOTOS\", title: \"Photos\" },\n  Videos: { key: \"VIDEOS\", title: \"Videos\" },\n  BusinessLogo: { key: \"BUSINESS_LOGO\", title: \"Business Logo\" },\n  MenuPhotos: { key: \"MENU_PHOTOS\", title: \"Menu Photos\" },\n  SocialLinks: { key: \"SOCIAL_LINKS\", title: \"Social Links\" },\n  Posts: { key: \"POSTS\", title: \"Posts\" },\n  Services: { key: \"SERVICES\", title: \"Services\" },\n  Products: { key: \"PRODUCTS\", title: \"Products\" },\n  ReviewLink: { key: \"REVIEWLINK\", title: \"Review Link\" },\n  QandA: { key: \"QANDA\", title: \"Q&A\" },\n  AutoReply: { key: \"AUTO_REPLY\", title: \"Auto Reply\" },\n  ProfileProtection: { key: \"PROFILE_PROTECTION\", title: \"Profile Protection\" },\n};\n"], "mappings": "AAGA,WAAYA,aAAa,0BAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAA,OAAbA,aAAa;AAAA;AAOzB,OAAO,MAAMC,oBAAyC,GAAG;EACvDC,eAAe,EAAE,6CAA6C;EAC9DC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE,QAAQ;EACnBC,SAAS,EAAE,SAAS;EACpBC,OAAO,EAAE,EAAE;EACXC,YAAY,EAAE,EAAE;EAChBC,UAAU,EAAE;AACd,CAAC;AAED,OAAO,MAAMC,WAAW,GAAG;EACzBC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE;EACP;EACA;AACF,CAAC;AAED,OAAO,MAAMC,WAAW,GAAG;EACzBC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,SAAS;EACjBC,SAAS,EAAE;AACb,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAG;EACjCC,WAAW,EAAE;IAAEC,GAAG,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAe,CAAC;EAC3DC,OAAO,EAAE;IAAEF,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC;EAC7CE,WAAW,EAAE;IAAEH,GAAG,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAe,CAAC;EAC3DG,aAAa,EAAE;IAAEJ,GAAG,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAiB,CAAC;EACjEI,QAAQ,EAAE;IAAEL,GAAG,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC;EAClDK,SAAS,EAAE;IAAEN,GAAG,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC;EACrDM,WAAW,EAAE;IAAEP,GAAG,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAe,CAAC;EAC3DO,YAAY,EAAE;IAAER,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAgB,CAAC;EAC9DQ,UAAU,EAAE;IAAET,GAAG,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC;EACtDS,MAAM,EAAE;IAAEV,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC;EAC1CU,MAAM,EAAE;IAAEX,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC;EAC1CW,YAAY,EAAE;IAAEZ,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAgB,CAAC;EAC9DY,UAAU,EAAE;IAAEb,GAAG,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAc,CAAC;EACxDa,WAAW,EAAE;IAAEd,GAAG,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAe,CAAC;EAC3Dc,KAAK,EAAE;IAAEf,GAAG,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC;EACvCe,QAAQ,EAAE;IAAEhB,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC;EAChDgB,QAAQ,EAAE;IAAEjB,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC;EAChDiB,UAAU,EAAE;IAAElB,GAAG,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAc,CAAC;EACvDkB,KAAK,EAAE;IAAEnB,GAAG,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAM,CAAC;EACrCmB,SAAS,EAAE;IAAEpB,GAAG,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC;EACrDoB,iBAAiB,EAAE;IAAErB,GAAG,EAAE,oBAAoB;IAAEC,KAAK,EAAE;EAAqB;AAC9E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}