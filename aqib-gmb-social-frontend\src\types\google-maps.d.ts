// Google Maps API TypeScript declarations
declare global {
  interface Window {
    google: {
      maps: {
        places: {
          AutocompleteService: new () => {
            getPlacePredictions: (
              request: {
                input: string;
                types?: string[];
                componentRestrictions?: { country?: string };
              },
              callback: (
                predictions: Array<{
                  place_id: string;
                  description: string;
                  types: string[];
                }> | null,
                status: string
              ) => void
            ) => void;
          };
          PlacesService: new (
            attrContainer: HTMLElement | google.maps.Map
          ) => {
            getDetails: (
              request: { placeId: string; fields: string[] },
              callback: (
                place: {
                  place_id: string;
                  name: string;
                  geometry: {
                    location: {
                      lat: () => number;
                      lng: () => number;
                    };
                  };
                  formatted_address: string;
                } | null,
                status: string
              ) => void
            ) => void;
          };
          PlacesServiceStatus: {
            OK: string;
            ZERO_RESULTS: string;
            OVER_QUERY_LIMIT: string;
            REQUEST_DENIED: string;
            INVALID_REQUEST: string;
            NOT_FOUND: string;
          };
        };
        Map: new (
          mapDiv: HTMLElement,
          opts?: {
            center: { lat: number; lng: number };
            zoom: number;
          }
        ) => google.maps.Map;
        Marker: new (opts: {
          position: { lat: number; lng: number };
          map: google.maps.Map;
          title?: string;
        }) => google.maps.Marker;
        InfoWindow: new (opts?: {
          content: string;
        }) => google.maps.InfoWindow;
        LatLng: new (lat: number, lng: number) => google.maps.LatLng;
        event: {
          addListener: (
            instance: any,
            eventName: string,
            handler: () => void
          ) => void;
        };
      };
    };
  }
}

export {};
