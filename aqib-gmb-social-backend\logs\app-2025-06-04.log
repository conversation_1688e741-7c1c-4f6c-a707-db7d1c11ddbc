{"timestamp":"2025-06-04T07:02:11.940Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"2a2ffbff-d834-4a93-bef7-38770e121856","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-04T07:02:11.950Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"2a2ffbff-d834-4a93-bef7-38770e121856","userId":"52"}
{"timestamp":"2025-06-04T07:02:12.204Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"2a2ffbff-d834-4a93-bef7-38770e121856","userId":"52","businessCount":5}
{"timestamp":"2025-06-04T07:02:18.270Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6b3357ac-048f-4921-9a5b-02e7a0f5760f","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-06-04T07:54:09.567Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"123ebf87-bfe8-4d7b-89f1-47f67ef821bb","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-04T07:54:09.570Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"123ebf87-bfe8-4d7b-89f1-47f67ef821bb","userId":"52"}
{"timestamp":"2025-06-04T07:54:09.630Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"123ebf87-bfe8-4d7b-89f1-47f67ef821bb","userId":"52","businessCount":5}
{"timestamp":"2025-06-04T07:54:12.709Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d66d5100-614a-4194-bd7c-942d736eca88","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-06-04T07:56:31.862Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T02:26:32.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T07:56:44.128Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ec2ea18a-9d8a-48f2-be5d-f5c33b64936d","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-04T07:56:44.131Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"ec2ea18a-9d8a-48f2-be5d-f5c33b64936d","userId":"52"}
{"timestamp":"2025-06-04T07:56:44.206Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"ec2ea18a-9d8a-48f2-be5d-f5c33b64936d","userId":"52","businessCount":5}
{"timestamp":"2025-06-04T07:56:46.297Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"22aa2ed3-7a71-4dd8-8169-456dd4acb7e1","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-06-04T07:57:36.605Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8e286091-cd6d-432f-a4ea-16ea634fed39","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-04T07:57:36.607Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"8e286091-cd6d-432f-a4ea-16ea634fed39","userId":"52"}
{"timestamp":"2025-06-04T07:57:36.688Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"8e286091-cd6d-432f-a4ea-16ea634fed39","userId":"52","businessCount":5}
{"timestamp":"2025-06-04T07:57:40.537Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6439010f-8f2f-4b3f-808c-0b816743b9cf","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-06-04T07:58:14.250Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"250e931c-bec0-41f4-abdf-bc7c0f416240","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-04T07:58:14.251Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"250e931c-bec0-41f4-abdf-bc7c0f416240","userId":"52"}
{"timestamp":"2025-06-04T07:58:14.258Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"1e82595e-32a4-4816-83b8-ecf2b9c08101","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-06-04T07:58:14.319Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"250e931c-bec0-41f4-abdf-bc7c0f416240","userId":"52","businessCount":5}
{"timestamp":"2025-06-04T07:59:04.304Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"03fb637b-1a62-4318-9d44-1ae5635c3757","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-04T07:59:04.305Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"03fb637b-1a62-4318-9d44-1ae5635c3757","userId":"52"}
{"timestamp":"2025-06-04T07:59:04.346Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"03fb637b-1a62-4318-9d44-1ae5635c3757","userId":"52","businessCount":5}
{"timestamp":"2025-06-04T07:59:06.270Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"0d2a866c-d2fa-4a55-b801-277a72ae98c1","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-06-04T07:59:10.277Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"bb464599-31bf-42b0-b7e0-0d77feae9259","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-06-04T08:00:25.082Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8e808453-469c-49c4-beb5-388ec85b0544","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-06-04T08:00:25.084Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"8e808453-469c-49c4-beb5-388ec85b0544","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-06-04T08:00:25.253Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"8e808453-469c-49c4-beb5-388ec85b0544","userId":52,"email":"<EMAIL>"}
{"timestamp":"2025-06-04T08:00:27.303Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9e8f3622-6e03-4bb3-9138-f663c172cd6f","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-04T08:00:27.304Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"9e8f3622-6e03-4bb3-9138-f663c172cd6f","userId":"52"}
{"timestamp":"2025-06-04T08:00:27.311Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"34d96612-2e95-4271-9eba-cb88942fd07e","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-06-04T08:00:27.382Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"9e8f3622-6e03-4bb3-9138-f663c172cd6f","userId":"52","businessCount":5}
{"timestamp":"2025-06-04T08:01:23.879Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T02:31:24.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T08:01:44.700Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T02:31:45.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T08:01:58.819Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T02:31:58.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T08:02:17.181Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T02:32:17.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T08:02:33.093Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T02:32:33.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T08:02:46.788Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T02:32:46.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T08:03:15.219Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T02:33:15.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T08:20:57.849Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T02:50:58.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T08:23:17.730Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T02:53:18.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T08:40:45.861Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T03:10:46.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T08:41:19.961Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T03:11:20.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T08:41:42.887Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T03:11:43.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T08:42:06.824Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T03:12:07.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T08:42:16.395Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T03:12:16.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T08:55:57.977Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T03:25:58.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T09:02:04.457Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T03:32:04.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T19:03:02.934Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T13:33:03.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T19:03:03.711Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"eb71cc6a-1096-4937-8859-a6f0796a5109","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-04T19:03:03.713Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"eb71cc6a-1096-4937-8859-a6f0796a5109","userId":"52"}
{"timestamp":"2025-06-04T19:03:03.809Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"eb71cc6a-1096-4937-8859-a6f0796a5109","userId":"52","businessCount":5}
{"timestamp":"2025-06-04T19:03:07.421Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"64ad0b78-a7b4-4850-9355-0324ff2d28e0","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-06-04T19:03:28.539Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"c54b5073-4b11-40ec-8537-ee39dc4e8c30","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-04T19:03:28.542Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"c54b5073-4b11-40ec-8537-ee39dc4e8c30","userId":"52"}
{"timestamp":"2025-06-04T19:03:28.550Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"2e7f2797-3498-4ec8-a389-64c90bff0cef","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-06-04T19:03:28.620Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"c54b5073-4b11-40ec-8537-ee39dc4e8c30","userId":"52","businessCount":5}
{"timestamp":"2025-06-04T19:05:39.033Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d202fb2d-6fb3-452e-92d8-286e04772aff","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-04T19:05:39.036Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"d202fb2d-6fb3-452e-92d8-286e04772aff","userId":"52"}
{"timestamp":"2025-06-04T19:05:39.049Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d94b3140-7ea9-4b80-ab16-766f2b8bf75f","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-06-04T19:05:39.089Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"d202fb2d-6fb3-452e-92d8-286e04772aff","userId":"52","businessCount":5}
{"timestamp":"2025-06-04T19:14:21.993Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T13:44:21.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T19:14:51.343Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T13:44:51.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T19:15:44.105Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T13:45:43.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T19:15:54.562Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T13:45:54.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T19:16:04.795Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T13:46:04.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T19:16:28.476Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T13:46:28.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T19:23:07.006Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"1b92ddb9-232d-4ec9-b7d0-332b0a1e3463","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-04T19:23:07.008Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"1b92ddb9-232d-4ec9-b7d0-332b0a1e3463","userId":"52"}
{"timestamp":"2025-06-04T19:23:07.093Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"1b92ddb9-232d-4ec9-b7d0-332b0a1e3463","userId":"52","businessCount":5}
{"timestamp":"2025-06-04T19:23:26.209Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"013885a8-877d-4fa6-9117-96f44b7b3df0","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-06-04T19:23:34.301Z","level":"INFO","message":"Uploading post image to S3 temp folder","environment":"DEVELOPMENT","s3Key":"temp/post-images/52/*************-download.jpg","mimeType":"image/jpeg","userId":52}
{"timestamp":"2025-06-04T19:23:43.098Z","level":"ERROR","message":"Error uploading post image to S3","environment":"DEVELOPMENT","s3Key":"temp/post-images/52/*************-download.jpg","error":"Missing required key 'Bucket' in params","stack":"MissingRequiredParameter: Missing required key 'Bucket' in params\n    at ParamValidator.fail (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\param_validator.js:50:37)\n    at ParamValidator.validateStructure (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\param_validator.js:62:14)\n    at ParamValidator.validateMember (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\param_validator.js:89:21)\n    at ParamValidator.validate (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\param_validator.js:34:10)\n    at Request.VALIDATE_PARAMETERS (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\event_listeners.js:166:42)\n    at Request.callListeners (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\sequential_executor.js:106:20)\n    at callNextListener (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\sequential_executor.js:96:12)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\event_listeners.js:120:11\n    at finish (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\config.js:396:7)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\aws-sdk\\lib\\config.js:438:9"}
{"timestamp":"2025-06-04T19:26:03.341Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T13:56:03.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T19:28:25.257Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-06-04T13:58:25.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-06-04T19:30:25.380Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9e16d956-00a0-45e2-8d98-c0494cfe172d","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-04T19:30:25.382Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"9e16d956-00a0-45e2-8d98-c0494cfe172d","userId":"52"}
{"timestamp":"2025-06-04T19:30:25.388Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"10f05d4d-b99b-4b78-bf61-30a098d4c07a","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-06-04T19:30:25.459Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"9e16d956-00a0-45e2-8d98-c0494cfe172d","userId":"52","businessCount":5}
{"timestamp":"2025-06-04T19:30:44.625Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"4a32824b-6002-4896-9db8-261c2852815e","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-06-04T19:30:44.627Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"4a32824b-6002-4896-9db8-261c2852815e","userId":"52"}
{"timestamp":"2025-06-04T19:30:44.677Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"4a32824b-6002-4896-9db8-261c2852815e","userId":"52","businessCount":5}
{"timestamp":"2025-06-04T19:30:47.282Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"c66e5b38-6079-43ac-a855-5752be727070","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-06-04T19:30:55.585Z","level":"INFO","message":"Uploading post image to S3 temp folder","environment":"DEVELOPMENT","s3Key":"temp/post-images/52/*************-download.jpg","mimeType":"image/jpeg","userId":52,"bucketName":"gmb-social-assets"}
{"timestamp":"2025-06-04T19:30:58.174Z","level":"INFO","message":"Post image uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"temp/post-images/52/*************-download.jpg","location":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/52/*************-download.jpg","etag":"\"b2061ed4b5654060cabc1967d84a5f86\""}
