{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\fileViewer\\\\fileViewer.component.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Box, Typography, IconButton, Chip, Divider, useTheme, useMediaQuery } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport ZoomInIcon from \"@mui/icons-material/ZoomIn\";\nimport ZoomOutIcon from \"@mui/icons-material/ZoomOut\";\nimport FullscreenIcon from \"@mui/icons-material/Fullscreen\";\nimport FullscreenExitIcon from \"@mui/icons-material/FullscreenExit\";\nimport DownloadIcon from \"@mui/icons-material/Download\";\nimport { FileUtils } from \"../../utils/fileUtils\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FileViewerComponent = ({\n  asset,\n  open,\n  onClose\n}) => {\n  _s();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down(\"md\"));\n  const [zoom, setZoom] = useState(1);\n  const [fullscreen, setFullscreen] = useState(false);\n  const handleZoomIn = () => {\n    setZoom(prev => Math.min(prev + 0.25, 3));\n  };\n  const handleZoomOut = () => {\n    setZoom(prev => Math.max(prev - 0.25, 0.25));\n  };\n  const handleFullscreen = () => {\n    setFullscreen(!fullscreen);\n  };\n  const handleDownload = () => {\n    if (!asset) return;\n    const link = document.createElement(\"a\");\n    link.href = asset.s3_url;\n    link.download = asset.original_file_name;\n    link.target = \"_blank\";\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n  const handleClose = () => {\n    setZoom(1);\n    setFullscreen(false);\n    onClose();\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\"\n    });\n  };\n  if (!asset) return null;\n  const isImage = FileUtils.isImage(asset.mime_type);\n  const isVideo = FileUtils.isVideo(asset.mime_type);\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: handleClose,\n    maxWidth: fullscreen ? false : \"lg\",\n    fullWidth: true,\n    fullScreen: fullscreen,\n    PaperProps: {\n      sx: {\n        backgroundColor: fullscreen ? \"#000\" : \"background.paper\",\n        ...(fullscreen && {\n          margin: 0,\n          maxHeight: \"100vh\",\n          maxWidth: \"100vw\"\n        })\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      sx: {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        padding: 2,\n        backgroundColor: fullscreen ? \"rgba(0, 0, 0, 0.8)\" : \"background.paper\",\n        color: fullscreen ? \"white\" : \"text.primary\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        component: \"div\",\n        sx: {\n          flex: 1\n        },\n        children: asset.original_file_name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          gap: 1\n        },\n        children: [isImage && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: handleZoomOut,\n            disabled: zoom <= 0.25,\n            sx: {\n              color: fullscreen ? \"white\" : \"inherit\"\n            },\n            children: /*#__PURE__*/_jsxDEV(ZoomOutIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: handleZoomIn,\n            disabled: zoom >= 3,\n            sx: {\n              color: fullscreen ? \"white\" : \"inherit\"\n            },\n            children: /*#__PURE__*/_jsxDEV(ZoomInIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleFullscreen,\n          sx: {\n            color: fullscreen ? \"white\" : \"inherit\"\n          },\n          children: fullscreen ? /*#__PURE__*/_jsxDEV(FullscreenExitIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 27\n          }, this) : /*#__PURE__*/_jsxDEV(FullscreenIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 52\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleDownload,\n          sx: {\n            color: fullscreen ? \"white\" : \"inherit\"\n          },\n          children: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleClose,\n          sx: {\n            color: fullscreen ? \"white\" : \"inherit\"\n          },\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      sx: {\n        padding: 0,\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        backgroundColor: fullscreen ? \"#000\" : \"background.paper\",\n        minHeight: fullscreen ? \"calc(100vh - 120px)\" : \"400px\",\n        overflow: \"auto\"\n      },\n      children: [isImage && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"center\",\n          width: \"100%\",\n          height: \"100%\",\n          overflow: \"auto\",\n          padding: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: asset.s3_url,\n          alt: asset.original_file_name,\n          style: {\n            maxWidth: \"100%\",\n            maxHeight: \"100%\",\n            transform: `scale(${zoom})`,\n            transition: \"transform 0.2s ease\",\n            cursor: zoom > 1 ? \"grab\" : \"default\"\n          },\n          onLoad: () => {\n            // Reset zoom when image loads\n            if (zoom !== 1) setZoom(1);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this), isVideo && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"center\",\n          width: \"100%\",\n          height: \"100%\",\n          padding: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(\"video\", {\n          controls: true,\n          style: {\n            maxWidth: \"100%\",\n            maxHeight: \"100%\",\n            backgroundColor: \"#000\"\n          },\n          preload: \"metadata\",\n          children: [/*#__PURE__*/_jsxDEV(\"source\", {\n            src: asset.s3_url,\n            type: asset.mime_type\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this), \"Your browser does not support the video tag.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), !fullscreen && /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        flexDirection: \"column\",\n        alignItems: \"stretch\",\n        padding: 2,\n        gap: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          flexWrap: \"wrap\",\n          gap: 1,\n          marginBottom: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Chip, {\n          label: asset.file_type.toUpperCase(),\n          color: isImage ? \"primary\" : \"secondary\",\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: FileUtils.formatFileSize(asset.file_size),\n          variant: \"outlined\",\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: asset.mime_type,\n          variant: \"outlined\",\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          flexDirection: \"column\",\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Uploaded:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this), \" \", formatDate(asset.upload_date)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this), asset.uploaded_by_name && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Uploaded by:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 17\n          }, this), \" \", asset.uploaded_by_name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"File ID:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this), \" \", asset.id]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          marginTop: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          onClick: handleDownload,\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 26\n          }, this),\n          children: \"Download\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleClose,\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this);\n};\n_s(FileViewerComponent, \"8S4a/RcXRzatajGxP3McyKLKEJg=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = FileViewerComponent;\nexport default FileViewerComponent;\nvar _c;\n$RefreshReg$(_c, \"FileViewerComponent\");", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Box", "Typography", "IconButton", "Chip", "Divider", "useTheme", "useMediaQuery", "CloseIcon", "ZoomInIcon", "ZoomOutIcon", "FullscreenIcon", "FullscreenExitIcon", "DownloadIcon", "FileUtils", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FileViewerComponent", "asset", "open", "onClose", "_s", "theme", "isMobile", "breakpoints", "down", "zoom", "setZoom", "fullscreen", "setFullscreen", "handleZoomIn", "prev", "Math", "min", "handleZoomOut", "max", "handleFullscreen", "handleDownload", "link", "document", "createElement", "href", "s3_url", "download", "original_file_name", "target", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleClose", "formatDate", "dateString", "date", "Date", "toLocaleString", "year", "month", "day", "hour", "minute", "isImage", "mime_type", "isVideo", "max<PERSON><PERSON><PERSON>", "fullWidth", "fullScreen", "PaperProps", "sx", "backgroundColor", "margin", "maxHeight", "children", "display", "justifyContent", "alignItems", "padding", "color", "variant", "component", "flex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "onClick", "disabled", "flexDirection", "minHeight", "overflow", "width", "height", "src", "alt", "style", "transform", "transition", "cursor", "onLoad", "controls", "preload", "type", "flexWrap", "marginBottom", "label", "file_type", "toUpperCase", "size", "formatFileSize", "file_size", "upload_date", "uploaded_by_name", "id", "marginTop", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/fileViewer/fileViewer.component.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport {\n  <PERSON><PERSON>,\n  <PERSON><PERSON>T<PERSON>le,\n  <PERSON>alogContent,\n  <PERSON>alog<PERSON><PERSON>,\n  <PERSON><PERSON>,\n  Box,\n  Typography,\n  IconButton,\n  Chip,\n  Divider,\n  useTheme,\n  useMediaQuery,\n} from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport ZoomInIcon from \"@mui/icons-material/ZoomIn\";\nimport ZoomOutIcon from \"@mui/icons-material/ZoomOut\";\nimport FullscreenIcon from \"@mui/icons-material/Fullscreen\";\nimport FullscreenExitIcon from \"@mui/icons-material/FullscreenExit\";\nimport DownloadIcon from \"@mui/icons-material/Download\";\nimport { FileUtils } from \"../../utils/fileUtils\";\n\ninterface IAsset {\n  id: number;\n  business_id: number;\n  user_id: number;\n  file_name: string;\n  original_file_name: string;\n  file_type: \"image\" | \"video\";\n  file_size: number;\n  s3_key: string;\n  s3_url: string;\n  mime_type: string;\n  upload_date: string;\n  status: string;\n  uploaded_by_name?: string;\n}\n\ninterface FileViewerComponentProps {\n  asset: IAsset | null;\n  open: boolean;\n  onClose: () => void;\n}\n\nconst FileViewerComponent: React.FC<FileViewerComponentProps> = ({\n  asset,\n  open,\n  onClose,\n}) => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down(\"md\"));\n  const [zoom, setZoom] = useState(1);\n  const [fullscreen, setFullscreen] = useState(false);\n\n  const handleZoomIn = () => {\n    setZoom((prev) => Math.min(prev + 0.25, 3));\n  };\n\n  const handleZoomOut = () => {\n    setZoom((prev) => Math.max(prev - 0.25, 0.25));\n  };\n\n  const handleFullscreen = () => {\n    setFullscreen(!fullscreen);\n  };\n\n  const handleDownload = () => {\n    if (!asset) return;\n\n    const link = document.createElement(\"a\");\n    link.href = asset.s3_url;\n    link.download = asset.original_file_name;\n    link.target = \"_blank\";\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  const handleClose = () => {\n    setZoom(1);\n    setFullscreen(false);\n    onClose();\n  };\n\n  const formatDate = (dateString: string): string => {\n    const date = new Date(dateString);\n    return date.toLocaleString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  if (!asset) return null;\n\n  const isImage = FileUtils.isImage(asset.mime_type);\n  const isVideo = FileUtils.isVideo(asset.mime_type);\n\n  return (\n    <Dialog\n      open={open}\n      onClose={handleClose}\n      maxWidth={fullscreen ? false : \"lg\"}\n      fullWidth\n      fullScreen={fullscreen}\n      PaperProps={{\n        sx: {\n          backgroundColor: fullscreen ? \"#000\" : \"background.paper\",\n          ...(fullscreen && {\n            margin: 0,\n            maxHeight: \"100vh\",\n            maxWidth: \"100vw\",\n          }),\n        },\n      }}\n    >\n      <DialogTitle\n        sx={{\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          padding: 2,\n          backgroundColor: fullscreen\n            ? \"rgba(0, 0, 0, 0.8)\"\n            : \"background.paper\",\n          color: fullscreen ? \"white\" : \"text.primary\",\n        }}\n      >\n        <Typography variant=\"h6\" component=\"div\" sx={{ flex: 1 }}>\n          {asset.original_file_name}\n        </Typography>\n\n        <Box sx={{ display: \"flex\", gap: 1 }}>\n          {isImage && (\n            <>\n              <IconButton\n                onClick={handleZoomOut}\n                disabled={zoom <= 0.25}\n                sx={{ color: fullscreen ? \"white\" : \"inherit\" }}\n              >\n                <ZoomOutIcon />\n              </IconButton>\n\n              <IconButton\n                onClick={handleZoomIn}\n                disabled={zoom >= 3}\n                sx={{ color: fullscreen ? \"white\" : \"inherit\" }}\n              >\n                <ZoomInIcon />\n              </IconButton>\n            </>\n          )}\n\n          <IconButton\n            onClick={handleFullscreen}\n            sx={{ color: fullscreen ? \"white\" : \"inherit\" }}\n          >\n            {fullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}\n          </IconButton>\n\n          <IconButton\n            onClick={handleDownload}\n            sx={{ color: fullscreen ? \"white\" : \"inherit\" }}\n          >\n            <DownloadIcon />\n          </IconButton>\n\n          <IconButton\n            onClick={handleClose}\n            sx={{ color: fullscreen ? \"white\" : \"inherit\" }}\n          >\n            <CloseIcon />\n          </IconButton>\n        </Box>\n      </DialogTitle>\n\n      <DialogContent\n        sx={{\n          padding: 0,\n          display: \"flex\",\n          flexDirection: \"column\",\n          alignItems: \"center\",\n          justifyContent: \"center\",\n          backgroundColor: fullscreen ? \"#000\" : \"background.paper\",\n          minHeight: fullscreen ? \"calc(100vh - 120px)\" : \"400px\",\n          overflow: \"auto\",\n        }}\n      >\n        {isImage && (\n          <Box\n            sx={{\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              width: \"100%\",\n              height: \"100%\",\n              overflow: \"auto\",\n              padding: 2,\n            }}\n          >\n            <img\n              src={asset.s3_url}\n              alt={asset.original_file_name}\n              style={{\n                maxWidth: \"100%\",\n                maxHeight: \"100%\",\n                transform: `scale(${zoom})`,\n                transition: \"transform 0.2s ease\",\n                cursor: zoom > 1 ? \"grab\" : \"default\",\n              }}\n              onLoad={() => {\n                // Reset zoom when image loads\n                if (zoom !== 1) setZoom(1);\n              }}\n            />\n          </Box>\n        )}\n\n        {isVideo && (\n          <Box\n            sx={{\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              width: \"100%\",\n              height: \"100%\",\n              padding: 2,\n            }}\n          >\n            <video\n              controls\n              style={{\n                maxWidth: \"100%\",\n                maxHeight: \"100%\",\n                backgroundColor: \"#000\",\n              }}\n              preload=\"metadata\"\n            >\n              <source src={asset.s3_url} type={asset.mime_type} />\n              Your browser does not support the video tag.\n            </video>\n          </Box>\n        )}\n      </DialogContent>\n\n      {!fullscreen && (\n        <DialogActions\n          sx={{\n            flexDirection: \"column\",\n            alignItems: \"stretch\",\n            padding: 2,\n            gap: 2,\n          }}\n        >\n          <Divider />\n\n          <Box\n            sx={{ display: \"flex\", flexWrap: \"wrap\", gap: 1, marginBottom: 1 }}\n          >\n            <Chip\n              label={asset.file_type.toUpperCase()}\n              color={isImage ? \"primary\" : \"secondary\"}\n              size=\"small\"\n            />\n            <Chip\n              label={FileUtils.formatFileSize(asset.file_size)}\n              variant=\"outlined\"\n              size=\"small\"\n            />\n            <Chip label={asset.mime_type} variant=\"outlined\" size=\"small\" />\n          </Box>\n\n          <Box sx={{ display: \"flex\", flexDirection: \"column\", gap: 1 }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              <strong>Uploaded:</strong> {formatDate(asset.upload_date)}\n            </Typography>\n\n            {asset.uploaded_by_name && (\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                <strong>Uploaded by:</strong> {asset.uploaded_by_name}\n              </Typography>\n            )}\n\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              <strong>File ID:</strong> {asset.id}\n            </Typography>\n          </Box>\n\n          <Box\n            sx={{\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              marginTop: 1,\n            }}\n          >\n            <Button\n              variant=\"outlined\"\n              onClick={handleDownload}\n              startIcon={<DownloadIcon />}\n            >\n              Download\n            </Button>\n\n            <Button variant=\"contained\" onClick={handleClose}>\n              Close\n            </Button>\n          </Box>\n        </DialogActions>\n      )}\n    </Dialog>\n  );\n};\n\nexport default FileViewerComponent;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,QAAQ,EACRC,aAAa,QACR,eAAe;AACtB,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,SAASC,SAAS,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAwBlD,MAAMC,mBAAuD,GAAGA,CAAC;EAC/DC,KAAK;EACLC,IAAI;EACJC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,KAAK,GAAGlB,QAAQ,CAAC,CAAC;EACxB,MAAMmB,QAAQ,GAAGlB,aAAa,CAACiB,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5D,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMqC,YAAY,GAAGA,CAAA,KAAM;IACzBH,OAAO,CAAEI,IAAI,IAAKC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;EAC7C,CAAC;EAED,MAAMG,aAAa,GAAGA,CAAA,KAAM;IAC1BP,OAAO,CAAEI,IAAI,IAAKC,IAAI,CAACG,GAAG,CAACJ,IAAI,GAAG,IAAI,EAAE,IAAI,CAAC,CAAC;EAChD,CAAC;EAED,MAAMK,gBAAgB,GAAGA,CAAA,KAAM;IAC7BP,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMS,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACnB,KAAK,EAAE;IAEZ,MAAMoB,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGvB,KAAK,CAACwB,MAAM;IACxBJ,IAAI,CAACK,QAAQ,GAAGzB,KAAK,CAAC0B,kBAAkB;IACxCN,IAAI,CAACO,MAAM,GAAG,QAAQ;IACtBN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,IAAI,CAAC;IAC/BA,IAAI,CAACU,KAAK,CAAC,CAAC;IACZT,QAAQ,CAACO,IAAI,CAACG,WAAW,CAACX,IAAI,CAAC;EACjC,CAAC;EAED,MAAMY,WAAW,GAAGA,CAAA,KAAM;IACxBvB,OAAO,CAAC,CAAC,CAAC;IACVE,aAAa,CAAC,KAAK,CAAC;IACpBT,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAM+B,UAAU,GAAIC,UAAkB,IAAa;IACjD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,cAAc,CAAC,OAAO,EAAE;MAClCC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,IAAI,CAAC1C,KAAK,EAAE,OAAO,IAAI;EAEvB,MAAM2C,OAAO,GAAGjD,SAAS,CAACiD,OAAO,CAAC3C,KAAK,CAAC4C,SAAS,CAAC;EAClD,MAAMC,OAAO,GAAGnD,SAAS,CAACmD,OAAO,CAAC7C,KAAK,CAAC4C,SAAS,CAAC;EAElD,oBACEhD,OAAA,CAACpB,MAAM;IACLyB,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAE8B,WAAY;IACrBc,QAAQ,EAAEpC,UAAU,GAAG,KAAK,GAAG,IAAK;IACpCqC,SAAS;IACTC,UAAU,EAAEtC,UAAW;IACvBuC,UAAU,EAAE;MACVC,EAAE,EAAE;QACFC,eAAe,EAAEzC,UAAU,GAAG,MAAM,GAAG,kBAAkB;QACzD,IAAIA,UAAU,IAAI;UAChB0C,MAAM,EAAE,CAAC;UACTC,SAAS,EAAE,OAAO;UAClBP,QAAQ,EAAE;QACZ,CAAC;MACH;IACF,CAAE;IAAAQ,QAAA,gBAEF1D,OAAA,CAACnB,WAAW;MACVyE,EAAE,EAAE;QACFK,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,CAAC;QACVP,eAAe,EAAEzC,UAAU,GACvB,oBAAoB,GACpB,kBAAkB;QACtBiD,KAAK,EAAEjD,UAAU,GAAG,OAAO,GAAG;MAChC,CAAE;MAAA4C,QAAA,gBAEF1D,OAAA,CAACd,UAAU;QAAC8E,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,KAAK;QAACX,EAAE,EAAE;UAAEY,IAAI,EAAE;QAAE,CAAE;QAAAR,QAAA,EACtDtD,KAAK,CAAC0B;MAAkB;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAEbtE,OAAA,CAACf,GAAG;QAACqE,EAAE,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEY,GAAG,EAAE;QAAE,CAAE;QAAAb,QAAA,GAClCX,OAAO,iBACN/C,OAAA,CAAAE,SAAA;UAAAwD,QAAA,gBACE1D,OAAA,CAACb,UAAU;YACTqF,OAAO,EAAEpD,aAAc;YACvBqD,QAAQ,EAAE7D,IAAI,IAAI,IAAK;YACvB0C,EAAE,EAAE;cAAES,KAAK,EAAEjD,UAAU,GAAG,OAAO,GAAG;YAAU,CAAE;YAAA4C,QAAA,eAEhD1D,OAAA,CAACN,WAAW;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEbtE,OAAA,CAACb,UAAU;YACTqF,OAAO,EAAExD,YAAa;YACtByD,QAAQ,EAAE7D,IAAI,IAAI,CAAE;YACpB0C,EAAE,EAAE;cAAES,KAAK,EAAEjD,UAAU,GAAG,OAAO,GAAG;YAAU,CAAE;YAAA4C,QAAA,eAEhD1D,OAAA,CAACP,UAAU;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,eACb,CACH,eAEDtE,OAAA,CAACb,UAAU;UACTqF,OAAO,EAAElD,gBAAiB;UAC1BgC,EAAE,EAAE;YAAES,KAAK,EAAEjD,UAAU,GAAG,OAAO,GAAG;UAAU,CAAE;UAAA4C,QAAA,EAE/C5C,UAAU,gBAAGd,OAAA,CAACJ,kBAAkB;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGtE,OAAA,CAACL,cAAc;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eAEbtE,OAAA,CAACb,UAAU;UACTqF,OAAO,EAAEjD,cAAe;UACxB+B,EAAE,EAAE;YAAES,KAAK,EAAEjD,UAAU,GAAG,OAAO,GAAG;UAAU,CAAE;UAAA4C,QAAA,eAEhD1D,OAAA,CAACH,YAAY;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEbtE,OAAA,CAACb,UAAU;UACTqF,OAAO,EAAEpC,WAAY;UACrBkB,EAAE,EAAE;YAAES,KAAK,EAAEjD,UAAU,GAAG,OAAO,GAAG;UAAU,CAAE;UAAA4C,QAAA,eAEhD1D,OAAA,CAACR,SAAS;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdtE,OAAA,CAAClB,aAAa;MACZwE,EAAE,EAAE;QACFQ,OAAO,EAAE,CAAC;QACVH,OAAO,EAAE,MAAM;QACfe,aAAa,EAAE,QAAQ;QACvBb,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBL,eAAe,EAAEzC,UAAU,GAAG,MAAM,GAAG,kBAAkB;QACzD6D,SAAS,EAAE7D,UAAU,GAAG,qBAAqB,GAAG,OAAO;QACvD8D,QAAQ,EAAE;MACZ,CAAE;MAAAlB,QAAA,GAEDX,OAAO,iBACN/C,OAAA,CAACf,GAAG;QACFqE,EAAE,EAAE;UACFK,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,cAAc,EAAE,QAAQ;UACxBiB,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdF,QAAQ,EAAE,MAAM;UAChBd,OAAO,EAAE;QACX,CAAE;QAAAJ,QAAA,eAEF1D,OAAA;UACE+E,GAAG,EAAE3E,KAAK,CAACwB,MAAO;UAClBoD,GAAG,EAAE5E,KAAK,CAAC0B,kBAAmB;UAC9BmD,KAAK,EAAE;YACL/B,QAAQ,EAAE,MAAM;YAChBO,SAAS,EAAE,MAAM;YACjByB,SAAS,EAAE,SAAStE,IAAI,GAAG;YAC3BuE,UAAU,EAAE,qBAAqB;YACjCC,MAAM,EAAExE,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG;UAC9B,CAAE;UACFyE,MAAM,EAAEA,CAAA,KAAM;YACZ;YACA,IAAIzE,IAAI,KAAK,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;UAC5B;QAAE;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAEArB,OAAO,iBACNjD,OAAA,CAACf,GAAG;QACFqE,EAAE,EAAE;UACFK,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,cAAc,EAAE,QAAQ;UACxBiB,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdhB,OAAO,EAAE;QACX,CAAE;QAAAJ,QAAA,eAEF1D,OAAA;UACEsF,QAAQ;UACRL,KAAK,EAAE;YACL/B,QAAQ,EAAE,MAAM;YAChBO,SAAS,EAAE,MAAM;YACjBF,eAAe,EAAE;UACnB,CAAE;UACFgC,OAAO,EAAC,UAAU;UAAA7B,QAAA,gBAElB1D,OAAA;YAAQ+E,GAAG,EAAE3E,KAAK,CAACwB,MAAO;YAAC4D,IAAI,EAAEpF,KAAK,CAAC4C;UAAU;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gDAEtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,EAEf,CAACxD,UAAU,iBACVd,OAAA,CAACjB,aAAa;MACZuE,EAAE,EAAE;QACFoB,aAAa,EAAE,QAAQ;QACvBb,UAAU,EAAE,SAAS;QACrBC,OAAO,EAAE,CAAC;QACVS,GAAG,EAAE;MACP,CAAE;MAAAb,QAAA,gBAEF1D,OAAA,CAACX,OAAO;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEXtE,OAAA,CAACf,GAAG;QACFqE,EAAE,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAE8B,QAAQ,EAAE,MAAM;UAAElB,GAAG,EAAE,CAAC;UAAEmB,YAAY,EAAE;QAAE,CAAE;QAAAhC,QAAA,gBAEnE1D,OAAA,CAACZ,IAAI;UACHuG,KAAK,EAAEvF,KAAK,CAACwF,SAAS,CAACC,WAAW,CAAC,CAAE;UACrC9B,KAAK,EAAEhB,OAAO,GAAG,SAAS,GAAG,WAAY;UACzC+C,IAAI,EAAC;QAAO;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACFtE,OAAA,CAACZ,IAAI;UACHuG,KAAK,EAAE7F,SAAS,CAACiG,cAAc,CAAC3F,KAAK,CAAC4F,SAAS,CAAE;UACjDhC,OAAO,EAAC,UAAU;UAClB8B,IAAI,EAAC;QAAO;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACFtE,OAAA,CAACZ,IAAI;UAACuG,KAAK,EAAEvF,KAAK,CAAC4C,SAAU;UAACgB,OAAO,EAAC,UAAU;UAAC8B,IAAI,EAAC;QAAO;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eAENtE,OAAA,CAACf,GAAG;QAACqE,EAAE,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEe,aAAa,EAAE,QAAQ;UAAEH,GAAG,EAAE;QAAE,CAAE;QAAAb,QAAA,gBAC5D1D,OAAA,CAACd,UAAU;UAAC8E,OAAO,EAAC,OAAO;UAACD,KAAK,EAAC,gBAAgB;UAAAL,QAAA,gBAChD1D,OAAA;YAAA0D,QAAA,EAAQ;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACjC,UAAU,CAACjC,KAAK,CAAC6F,WAAW,CAAC;QAAA;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,EAEZlE,KAAK,CAAC8F,gBAAgB,iBACrBlG,OAAA,CAACd,UAAU;UAAC8E,OAAO,EAAC,OAAO;UAACD,KAAK,EAAC,gBAAgB;UAAAL,QAAA,gBAChD1D,OAAA;YAAA0D,QAAA,EAAQ;UAAY;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAClE,KAAK,CAAC8F,gBAAgB;QAAA;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CACb,eAEDtE,OAAA,CAACd,UAAU;UAAC8E,OAAO,EAAC,OAAO;UAACD,KAAK,EAAC,gBAAgB;UAAAL,QAAA,gBAChD1D,OAAA;YAAA0D,QAAA,EAAQ;UAAQ;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAClE,KAAK,CAAC+F,EAAE;QAAA;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENtE,OAAA,CAACf,GAAG;QACFqE,EAAE,EAAE;UACFK,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BwC,SAAS,EAAE;QACb,CAAE;QAAA1C,QAAA,gBAEF1D,OAAA,CAAChB,MAAM;UACLgF,OAAO,EAAC,UAAU;UAClBQ,OAAO,EAAEjD,cAAe;UACxB8E,SAAS,eAAErG,OAAA,CAACH,YAAY;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAZ,QAAA,EAC7B;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETtE,OAAA,CAAChB,MAAM;UAACgF,OAAO,EAAC,WAAW;UAACQ,OAAO,EAAEpC,WAAY;UAAAsB,QAAA,EAAC;QAElD;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAChB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAAC/D,EAAA,CA7QIJ,mBAAuD;EAAA,QAK7Cb,QAAQ,EACLC,aAAa;AAAA;AAAA+G,EAAA,GAN1BnG,mBAAuD;AA+Q7D,eAAeA,mBAAmB;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}