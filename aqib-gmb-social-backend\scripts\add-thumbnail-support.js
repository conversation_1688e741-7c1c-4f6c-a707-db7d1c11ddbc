const pool = require("../config/db");
const logger = require("../utils/logger");

/**
 * Add thumbnail support to business_assets table
 * This script adds thumbnail-related columns to the existing table
 */

async function addThumbnailSupport() {
  try {
    console.log("🚀 Adding thumbnail support to ManageAssets...");

    // Test database connection first
    console.log("🔍 Testing database connection...");
    try {
      await pool.query("SELECT 1 as test");
      console.log("✅ Database connection successful");
    } catch (dbError) {
      console.error("❌ Database connection failed:", dbError.message);
      console.log("💡 Please ensure:");
      console.log("   - Database server is running");
      console.log("   - Database credentials are correct in .env.development");
      console.log("   - Network connectivity to database server");
      throw dbError;
    }

    // SQL commands to add thumbnail columns
    const sqlCommands = [
      // Add thumbnail columns to business_assets table
      `ALTER TABLE business_assets
       ADD COLUMN thumbnail_s3_key VARCHAR(500) NULL COMMENT 'S3 key for thumbnail image',
       ADD COLUMN thumbnail_s3_url VARCHAR(1000) NULL COMMENT 'S3 URL for thumbnail image',
       ADD COLUMN thumbnail_file_name VARCHAR(255) NULL COMMENT 'Thumbnail file name',
       ADD COLUMN thumbnail_size BIGINT NULL COMMENT 'Thumbnail file size in bytes'`,

      // Add index for thumbnail queries
      `ALTER TABLE business_assets
       ADD INDEX idx_business_assets_thumbnail (thumbnail_s3_key)`,
    ];

    // Execute each SQL command
    for (let i = 0; i < sqlCommands.length; i++) {
      const sql = sqlCommands[i];
      console.log(`📝 Executing SQL command ${i + 1}/${sqlCommands.length}...`);

      try {
        await pool.query(sql);
        console.log(`✅ Command ${i + 1} executed successfully`);
      } catch (error) {
        if (error.code === "ER_DUP_FIELDNAME") {
          console.log(`⚠️  Column already exists, skipping command ${i + 1}`);
        } else if (error.code === "ER_DUP_KEYNAME") {
          console.log(`⚠️  Index already exists, skipping command ${i + 1}`);
        } else {
          throw error;
        }
      }
    }

    console.log("🎉 Thumbnail support added successfully!");
    console.log("");
    console.log("📋 Summary of changes:");
    console.log("- Added thumbnail_s3_key column");
    console.log("- Added thumbnail_s3_url column");
    console.log("- Added thumbnail_file_name column");
    console.log("- Added thumbnail_size column");
    console.log("- Added index for thumbnail queries");
    console.log("");
    console.log("✅ ManageAssets now supports video thumbnails!");
  } catch (error) {
    console.error("❌ Error adding thumbnail support:", error.message);
    console.error("Stack trace:", error.stack);
    process.exit(1);
  }
}

// Run the setup
if (require.main === module) {
  addThumbnailSupport().then(() => {
    process.exit(0);
  });
}

module.exports = { addThumbnailSupport };
