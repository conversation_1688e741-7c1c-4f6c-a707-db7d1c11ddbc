{"ast": null, "code": "import { TOGGLED_MENU_ITEM, UPDATE_MENU_STATE } from \"../constants/reducer.constant\";\nexport const openMenu = payload => async dispatch => {\n  dispatch({\n    type: UPDATE_MENU_STATE,\n    payload: payload\n  });\n};\nexport const toggleMenu = payload => async dispatch => {\n  dispatch({\n    type: TOGGLED_MENU_ITEM,\n    payload: payload\n  });\n};", "map": {"version": 3, "names": ["TOGGLED_MENU_ITEM", "UPDATE_MENU_STATE", "openMenu", "payload", "dispatch", "type", "toggleMenu"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/actions/userPreferences.actions.tsx"], "sourcesContent": ["import {\n  TOGGLED_MENU_ITEM,\n  UPDATE_MENU_STATE,\n} from \"../constants/reducer.constant\";\nimport { Action, Dispatch } from \"redux\";\n\nexport const openMenu =\n  (payload: boolean) => async (dispatch: Dispatch<Action>) => {\n    dispatch({\n      type: UPDATE_MENU_STATE,\n      payload: payload,\n    });\n  };\n\nexport const toggleMenu =\n  (payload: string | null) => async (dispatch: Dispatch<Action>) => {\n    dispatch({\n      type: TOGGLED_MENU_ITEM,\n      payload: payload,\n    });\n  };\n"], "mappings": "AAAA,SACEA,iBAAiB,EACjBC,iBAAiB,QACZ,+BAA+B;AAGtC,OAAO,MAAMC,QAAQ,GAClBC,OAAgB,IAAK,MAAOC,QAA0B,IAAK;EAC1DA,QAAQ,CAAC;IACPC,IAAI,EAAEJ,iBAAiB;IACvBE,OAAO,EAAEA;EACX,CAAC,CAAC;AACJ,CAAC;AAEH,OAAO,MAAMG,UAAU,GACpBH,OAAsB,IAAK,MAAOC,QAA0B,IAAK;EAChEA,QAAQ,CAAC;IACPC,IAAI,EAAEL,iBAAiB;IACvBG,OAAO,EAAEA;EACX,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}