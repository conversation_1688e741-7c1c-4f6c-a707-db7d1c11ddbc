import * as React from "react";
import { FunctionComponent, useContext, useEffect, useState } from "react";
import PageProps from "../../models/PageProps.interface";

//Widgets
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import { InputAdornment } from "@mui/material";
import FormControlLabel from "@mui/material/FormControlLabel";
import Checkbox from "@mui/material/Checkbox";
import Link from "@mui/material/Link";
import Button from "@mui/material/Button";
import { FormHelperText, Typography } from "@mui/material";

import FormControl from "@mui/material/FormControl";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import IconButton from "@mui/material/IconButton";
import FilledInput from "@mui/material/FilledInput";
import InputLabel from "@mui/material/InputLabel";

//Icons
import MailOutlinedIcon from "@mui/icons-material/MailOutlined";
import VpnKeyOutlinedIcon from "@mui/icons-material/VpnKeyOutlined";

//Css Import
import "../signIn/signIn.screen.style.css";
import * as yup from "yup";
import { Formik } from "formik";
import { ILoginModel } from "../../interfaces/request/ILoginModel";
import { useDispatch } from "react-redux";
import { authInitiate } from "../../actions/auth.actions";
import { ILoginRequestModel } from "../../interfaces/ILoginRequestModel";
import { LoadingContext } from "../../context/loading.context";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import { MessageConstants } from "../../constants/message.constant";

const SignIn: FunctionComponent<PageProps> = ({ title }) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const { setToastConfig, setOpen } = useContext(ToastContext);
  const handleClickShowPassword = () => setShowPassword((show) => !show);

  const handleMouseDownPassword = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    event.preventDefault();
  };

  const handleMouseUpPassword = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    event.preventDefault();
  };

  const { setLoading } = useContext(LoadingContext);
  const dispatch = useDispatch();
  const loginUser = (
    payload: ILoginModel,
    setLoading: (loading: boolean) => void
  ) => dispatch<any>(authInitiate(payload, setLoading));
  const [rememberMe, setRememberMe] = useState(false);
  const InitialValues: ILoginRequestModel = {
    email: "",
    password: "",
  };

  const [loginCreds, setLoginCreds] =
    useState<ILoginRequestModel>(InitialValues);

  useEffect(() => {
    document.title = title;
    const savedRememberMe = localStorage.getItem("rememberMe") === "true";
    const savedCredentials = localStorage.getItem("savedCreds");
    if (savedCredentials && savedRememberMe) {
      setLoginCreds(JSON.parse(savedCredentials));
    }
  }, []);

  const SignInSchema = yup.object().shape({
    email: yup
      .string()
      .email("Please enter a valid email")
      .required("This field is required"),
    password: yup.string().required("Password is required"),
  });

  const _handleSignIn = async (values: ILoginModel, formikHelpers: any) => {
    try {
      if (rememberMe) {
        localStorage.setItem("savedCreds", JSON.stringify(values));
        localStorage.setItem("rememberMe", "true");
      } else {
        localStorage.removeItem("savedCreds");
        localStorage.removeItem("rememberMe");
      }
      const isValid = await SignInSchema.isValid(values);
      console.log(isValid);
      if (isValid) {
        formikHelpers.setSubmitting(true);
        loginUser(values, setLoading);
      }
    } catch (error: any) {
      console.log(error);
      setToastConfig(ToastSeverity.Error, error.response.data.error, true);
    } finally {
      formikHelpers.setSubmitting(false);
    }
  };

  return (
    <Formik
      initialValues={{ ...loginCreds }}
      validationSchema={SignInSchema}
      onSubmit={(values, formikHelpers) => _handleSignIn(values, formikHelpers)}
      enableReinitialize
    >
      {({
        values,
        errors,
        touched,
        handleChange,
        handleBlur,
        handleSubmit,
        isSubmitting,
        isValid,
        /* and other goodies */
      }) => (
        <form onSubmit={handleSubmit} className="height100">
          <div className="height100">
            <Box className="height100">
              <Grid container spacing={2} className="height100 marT0">
                <Grid item xs={12} md={6} lg={6} className="pad0">
                  <Box className="accountLeft">
                    <img
                      alt="MyLocoBiz - Login"
                      className="width100"
                      src={require("../../assets/login/loginLeftSlider.png")}
                    />
                  </Box>
                </Grid>
                <Grid item xs={12} md={6} lg={6} className="pad0">
                  <Box className="accountRight">
                    <Box className="accountTopPart">
                      <Typography className="commonTitle welcomeTitle">
                        Welcome to
                      </Typography>
                      <Box className="accountLogo">
                        <img
                          alt="MyLocoBiz - Logo"
                          className="width100"
                          src={require("../../assets/common/Logo.png")}
                        />
                      </Box>
                    </Box>
                    <Box className="accountBodyPart">
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={12} lg={12}>
                          {/* <Box className="commonInput">
                            <TextField
                              id="email"
                              className="width100"
                              label="Email"
                              type="email"
                              variant="filled"
                              onChange={handleChange}
                              value={values.email}
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <EmailIcon />
                                  </InputAdornment>
                                ),
                              }}
                            />
                            {errors.email && touched.email && (
                              <FormHelperText className="errorMessage">
                                {errors.email}
                              </FormHelperText>
                            )}
                          </Box> */}
                          <Box className="commonInput">
                            <MailOutlinedIcon />
                            <TextField
                              id="email"
                              className="width100"
                              label="Email"
                              type="email"
                              variant="filled"
                              onChange={handleChange}
                              value={values.email}
                            />
                            {errors.email && touched.email && (
                              <FormHelperText className="errorMessage">
                                {errors.email}
                              </FormHelperText>
                            )}
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={12} lg={12}>
                          <Box className="commonInput">
                            <VpnKeyOutlinedIcon />
                            <TextField
                              id="password"
                              className="width100"
                              label="Password"
                              type={showPassword ? "text" : "password"}
                              variant="filled"
                              onChange={handleChange}
                              value={values.password}
                              InputProps={{
                                disableUnderline: true,
                                endAdornment: (
                                  <InputAdornment position="end">
                                    <IconButton
                                      onClick={handleClickShowPassword}
                                      edge="end"
                                    >
                                      {showPassword ? (
                                        <VisibilityOff />
                                      ) : (
                                        <Visibility />
                                      )}
                                    </IconButton>
                                  </InputAdornment>
                                ),
                              }}
                            />
                            {errors.password && touched.password && (
                              <FormHelperText className="errorMessage">
                                {errors.password}
                              </FormHelperText>
                            )}
                          </Box>
                        </Grid>
                        <Grid item xs={6} md={6} lg={6}>
                          <FormControlLabel
                            control={<Checkbox />}
                            label="Remember Me"
                            checked={rememberMe}
                            onChange={() => setRememberMe(!rememberMe)}
                          />
                        </Grid>
                        {/* <Grid item xs={6} md={6} lg={6}>
                          <Link href="/forgot-password" className="floatR lh42">
                            Forgot Password
                          </Link>
                        </Grid> */}
                        <Grid item xs={12} md={12} lg={12}>
                          <Button
                            variant="contained"
                            className="primaryFillBtn width100"
                            type="submit"
                          >
                            Login
                          </Button>
                        </Grid>
                      </Grid>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </div>
        </form>
      )}
    </Formik>
  );
};

export default SignIn;
