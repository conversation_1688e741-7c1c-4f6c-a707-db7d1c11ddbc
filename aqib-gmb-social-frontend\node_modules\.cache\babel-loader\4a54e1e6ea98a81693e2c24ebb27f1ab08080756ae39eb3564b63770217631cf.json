{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useCallback, useEffect, useMemo, useState } from \"react\";\nimport \"@fontsource/barlow\";\nimport \"./App.css\";\nimport { LoadingContext } from \"./context/loading.context\";\nimport Box from \"@mui/material/Box\";\nimport { Routes, Route, useNavigate, useLocation } from \"react-router-dom\";\nimport SignIn from \"./screens/signIn/signIn.screen\";\nimport Dashboard from \"./screens/dashboard/dashboard.screen\";\nimport Roles from \"./screens/userManagement/roles/roles.screen\";\nimport ForgotPassword from \"./screens/forgotPassword/forgotPassword.screen\";\nimport Users from \"./screens/userManagement/users/users.screen\";\nimport Analytics from \"./screens/analytics/analytics.screen\";\nimport QandA from \"./screens/qanda/qanda.screen\";\nimport ManageBusiness from \"./screens/businessManagement/manageBusiness/manageBusiness.screen\";\nimport ManageReviews from \"./screens/reviewManagement/manageReviews/manageReviews.screen\";\nimport ReviewSettings from \"./screens/reviewManagement/reviewSettings/reviewSettings.screen\";\nimport Help from \"./screens/help/help.screen\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport Snackbar from \"@mui/material/Snackbar\";\nimport MuiAlert from \"@mui/material/Alert\";\nimport { ToastSeverity } from \"./constants/toastSeverity.constant\";\nimport { ToastContext } from \"./context/toast.context\";\nimport LocalBusiness from \"./screens/businessManagement/localBusiness/localBusiness.screen\";\nimport BusinessSummary from \"./screens/businessManagement/businessSummary/businessSummary.screen\";\nimport CreateSocialPost from \"./screens/createSocialPost/createSocialPost.screen\";\nimport GMBCallback from \"./screens/businessManagement/callback/callback.screen\";\nimport PostsListing from \"./screens/posts/listing/PostListing.screen\";\nimport { sessionExpired } from \"./actions/auth.actions\";\nimport NotFoundPage from \"./components/notFoundPage/notFoundPage.component\";\nimport DashboardV2 from \"./screens/dashboardV2/dashboardV2.screen\";\nimport UnAuthorized from \"./components/unAuthorized/notFoundPage.component\";\nimport BusinessCategoryScreen from \"./screens/businessCategory/businessCategory.screen\";\nimport DemoScreen from \"./screens/businessCategory/demo.screen\";\nimport ServicesDemoScreen from \"./screens/businessCategory/servicesDemo.screen\";\nimport GeoGridScreen from \"./screens/geoGrid/geoGrid.screen\";\nimport Loader from \"./components/loader/loader.component\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [loading, setIsLoading] = useState(false);\n  const [activeMenuItem, setMenuItem] = useState(\"\");\n  const [message, setMessage] = useState(\"\");\n  const [open, setIsOpen] = useState(false);\n  const [toastConfig, setConfig] = useState({\n    severity: \"\",\n    message: \"\",\n    open: false\n  });\n  const [appToastConfig, setAppToastConfig] = useState({\n    severity: \"\",\n    message: \"\",\n    open: false\n  });\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const location = useLocation();\n  const {\n    userInfo,\n    isUnAuthorised,\n    success,\n    loginMessage\n  } = useSelector(state => state.authReducer);\n  const dispatchSessionExpired = () => dispatch(sessionExpired());\n  const setLoading = useCallback(loading => {\n    return setIsLoading(loading);\n  }, [loading]);\n  const loadingMemo = useMemo(() => ({\n    setLoading,\n    loading\n  }), [loading]);\n\n  // const preferencesMemo = useMemo(\n  //   () => ({\n  //     setActiveMenuItem,\n  //     activeMenuItem,\n  //   }),\n  //   [activeMenuItem]\n  // );\n\n  const setActiveMenuItem = useCallback(item => {\n    return setMenuItem(item);\n  }, [activeMenuItem]);\n  const getTitle = screen => {\n    const title = `${\"My Loco Biz\"} | ${screen}`;\n    return title;\n  };\n  useEffect(() => {\n    if (userInfo && userInfo.id > 0) {\n      console.log(location.pathname);\n\n      // if(userInfo.user.isPasswordReset) {\n      //   navigate(\"/forgotPassword\");\n      // }\n      if (location.pathname === \"/login\" || location.pathname === \"/\") {\n        navigate(\"/home\");\n      }\n    } else {\n      if (loginMessage) {\n        setConfig({\n          message: loginMessage,\n          severity: ToastSeverity.Error,\n          open: true\n        });\n      }\n      navigate(\"/\");\n    }\n  }, [userInfo, success]);\n  useEffect(() => {\n    if (isUnAuthorised) {\n      dispatchSessionExpired();\n      setConfig({\n        message: \"Your session expired, Please login again\",\n        severity: ToastSeverity.Error,\n        open: true\n      });\n    }\n  }, [isUnAuthorised]);\n  const Alert = /*#__PURE__*/React.forwardRef(function Alert(props, ref) {\n    return /*#__PURE__*/_jsxDEV(MuiAlert, {\n      elevation: 6,\n      ref: ref,\n      variant: \"filled\",\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 12\n    }, this);\n  });\n  const handleClose = (event, reason) => {\n    if (reason === \"clickaway\") {\n      return;\n    }\n    setConfig({\n      ...toastConfig,\n      open: false\n    });\n  };\n  const setOpen = useCallback(open => {\n    return setIsOpen(open);\n  }, [open]);\n  const setToastMessage = useCallback(message => {\n    return setMessage(message);\n  }, [open]);\n  const setToastConfig = useCallback((severity, message, open) => {\n    return setConfig({\n      severity,\n      message,\n      open\n    });\n  }, [toastConfig]);\n  const toastMemo = useMemo(() => ({\n    open,\n    setOpen,\n    message,\n    setToastMessage,\n    toastConfig,\n    setToastConfig\n  }), [open, message, toastConfig]);\n  return /*#__PURE__*/_jsxDEV(LoadingContext.Provider, {\n    value: loadingMemo,\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      className: \"App height100\",\n      children: /*#__PURE__*/_jsxDEV(ToastContext.Provider, {\n        value: toastMemo,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          className: \"page_loader\",\n          style: {\n            display: loadingMemo.loading ? \"flex\" : \"none\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(SignIn, {\n              title: getTitle(\"Home\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/forgot-password\",\n            element: /*#__PURE__*/_jsxDEV(ForgotPassword, {\n              title: getTitle(\"Forgot Password\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/post-management/create-social-post\",\n            element: /*#__PURE__*/_jsxDEV(CreateSocialPost, {\n              title: getTitle(\"Create Post\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/home\",\n            element: /*#__PURE__*/_jsxDEV(DashboardV2, {\n              title: getTitle(\"Dashboard\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/home2\",\n            element: /*#__PURE__*/_jsxDEV(Dashboard, {\n              title: getTitle(\"Dashboard\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/analytics\",\n            element: /*#__PURE__*/_jsxDEV(Analytics, {\n              title: getTitle(\"Analytics\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/q-and-a\",\n            element: /*#__PURE__*/_jsxDEV(QandA, {\n              title: getTitle(\"Q&A\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user-management/users\",\n            element: /*#__PURE__*/_jsxDEV(Users, {\n              title: getTitle(\"Users\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/roles-management/roles\",\n            element: /*#__PURE__*/_jsxDEV(Roles, {\n              title: getTitle(\"Roles\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/business-management/manage-business\",\n            element: /*#__PURE__*/_jsxDEV(ManageBusiness, {\n              title: getTitle(\"Manage Business\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/business-management/business-summary/:businessId/:accountId/:locationId\",\n            element: /*#__PURE__*/_jsxDEV(BusinessSummary, {\n              title: getTitle(\"Business Summary\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/business-management/local-business\",\n            element: /*#__PURE__*/_jsxDEV(LocalBusiness, {\n              title: getTitle(\"Local Business\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/business-management/gmb/callback\",\n            element: /*#__PURE__*/_jsxDEV(GMBCallback, {\n              title: getTitle(\"Callback\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/review-management/manage-reviews\",\n            element: /*#__PURE__*/_jsxDEV(ManageReviews, {\n              title: getTitle(\"Manage Reviews\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/review-management/review-settings\",\n            element: /*#__PURE__*/_jsxDEV(ReviewSettings, {\n              title: getTitle(\"Review Settings\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/post-management/posts\",\n            element: /*#__PURE__*/_jsxDEV(PostsListing, {\n              title: getTitle(\"Posts\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/business-management/categories\",\n            element: /*#__PURE__*/_jsxDEV(BusinessCategoryScreen, {\n              title: getTitle(\"Business Categories\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/demo/business-category\",\n            element: /*#__PURE__*/_jsxDEV(DemoScreen, {\n              title: getTitle(\"Business Category Demo\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/demo/services\",\n            element: /*#__PURE__*/_jsxDEV(ServicesDemoScreen, {\n              title: getTitle(\"Add Services Demo\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/geo-grid\",\n            element: /*#__PURE__*/_jsxDEV(GeoGridScreen, {\n              title: getTitle(\"Google Geo Grid\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/help\",\n            element: /*#__PURE__*/_jsxDEV(Help, {\n              title: getTitle(\"Manage Reviews\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(NotFoundPage, {\n              title: getTitle(\"Not Found Page\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/un-authorized\",\n            element: /*#__PURE__*/_jsxDEV(UnAuthorized, {\n              title: getTitle(\"Un Authorized Access\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), toastConfig && /*#__PURE__*/_jsxDEV(Snackbar, {\n          open: toastConfig.open,\n          autoHideDuration: 2000,\n          onClose: handleClose,\n          children: /*#__PURE__*/_jsxDEV(Alert, {\n            onClose: handleClose,\n            severity: toastConfig.severity,\n            sx: {\n              width: \"100%\"\n            },\n            children: toastConfig.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 192,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"I87sw/8CIMqLOHUeiFrfTNwsCWE=\", false, function () {\n  return [useNavigate, useDispatch, useLocation, useSelector];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useCallback", "useEffect", "useMemo", "useState", "LoadingContext", "Box", "Routes", "Route", "useNavigate", "useLocation", "SignIn", "Dashboard", "Roles", "ForgotPassword", "Users", "Analytics", "QandA", "ManageBusiness", "ManageReviews", "ReviewSettings", "Help", "useDispatch", "useSelector", "Snackbar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ToastSeverity", "ToastContext", "LocalBusiness", "BusinessSummary", "CreateSocialPost", "GMBCallback", "PostsListing", "sessionExpired", "NotFoundPage", "DashboardV2", "UnAuthorized", "BusinessCategoryScreen", "DemoScreen", "ServicesDemoScreen", "GeoGridScreen", "Loader", "jsxDEV", "_jsxDEV", "App", "_s", "loading", "setIsLoading", "activeMenuItem", "setMenuItem", "message", "setMessage", "open", "setIsOpen", "toastConfig", "setConfig", "severity", "appToastConfig", "setAppToastConfig", "navigate", "dispatch", "location", "userInfo", "isUnAuthorised", "success", "loginMessage", "state", "authReducer", "dispatchSessionExpired", "setLoading", "loadingMemo", "setActiveMenuItem", "item", "getTitle", "screen", "title", "id", "console", "log", "pathname", "Error", "<PERSON><PERSON>", "forwardRef", "props", "ref", "elevation", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleClose", "event", "reason", "<PERSON><PERSON><PERSON>", "setToastMessage", "setToastConfig", "toastMemo", "Provider", "value", "children", "className", "style", "display", "path", "element", "autoHideDuration", "onClose", "sx", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/App.tsx"], "sourcesContent": ["import React, {\r\n  use<PERSON>allback,\r\n  useContext,\r\n  useEffect,\r\n  useMemo,\r\n  useState,\r\n} from \"react\";\r\nimport \"@fontsource/barlow\";\r\nimport logo from \"./logo.svg\";\r\nimport \"./App.css\";\r\nimport Button from \"@mui/material/Button\";\r\nimport TextField from \"@mui/material/TextField\";\r\nimport { ThreeCircles } from \"react-loader-spinner\";\r\nimport { LoadingContext } from \"./context/loading.context\";\r\nimport Box from \"@mui/material/Box\";\r\nimport { PreferencesContext } from \"./context/preferences.context\";\r\nimport { Routes, Route, useNavigate, useLocation } from \"react-router-dom\";\r\nimport SignIn from \"./screens/signIn/signIn.screen\";\r\nimport { theme } from \"./theme\";\r\nimport Dashboard from \"./screens/dashboard/dashboard.screen\";\r\nimport Roles from \"./screens/userManagement/roles/roles.screen\";\r\nimport ForgotPassword from \"./screens/forgotPassword/forgotPassword.screen\";\r\nimport Users from \"./screens/userManagement/users/users.screen\";\r\nimport Analytics from \"./screens/analytics/analytics.screen\";\r\nimport QandA from \"./screens/qanda/qanda.screen\";\r\nimport ManageBusiness from \"./screens/businessManagement/manageBusiness/manageBusiness.screen\";\r\nimport ManageReviews from \"./screens/reviewManagement/manageReviews/manageReviews.screen\";\r\nimport ReviewSettings from \"./screens/reviewManagement/reviewSettings/reviewSettings.screen\";\r\nimport Help from \"./screens/help/help.screen\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport Snackbar from \"@mui/material/Snackbar\";\r\nimport MuiAlert, { AlertProps } from \"@mui/material/Alert\";\r\nimport { ToastSeverity } from \"./constants/toastSeverity.constant\";\r\nimport { ToastContext } from \"./context/toast.context\";\r\nimport LocalBusiness from \"./screens/businessManagement/localBusiness/localBusiness.screen\";\r\nimport BusinessSummary from \"./screens/businessManagement/businessSummary/businessSummary.screen\";\r\nimport CreateSocialPost from \"./screens/createSocialPost/createSocialPost.screen\";\r\nimport GMBCallback from \"./screens/businessManagement/callback/callback.screen\";\r\nimport PostsListing from \"./screens/posts/listing/PostListing.screen\";\r\nimport { sessionExpired } from \"./actions/auth.actions\";\r\nimport NotFoundPage from \"./components/notFoundPage/notFoundPage.component\";\r\nimport DashboardV2 from \"./screens/dashboardV2/dashboardV2.screen\";\r\nimport UnAuthorized from \"./components/unAuthorized/notFoundPage.component\";\r\nimport BusinessCategoryScreen from \"./screens/businessCategory/businessCategory.screen\";\r\nimport DemoScreen from \"./screens/businessCategory/demo.screen\";\r\nimport ServicesDemoScreen from \"./screens/businessCategory/servicesDemo.screen\";\r\nimport GeoGridScreen from \"./screens/geoGrid/geoGrid.screen\";\r\nimport Loader from \"./components/loader/loader.component\";\r\n\r\nfunction App() {\r\n  const [loading, setIsLoading] = useState(false);\r\n  const [activeMenuItem, setMenuItem] = useState<string>(\"\");\r\n  const [message, setMessage] = useState<string>(\"\");\r\n  const [open, setIsOpen] = useState<boolean>(false);\r\n  const [toastConfig, setConfig] = useState<any>({\r\n    severity: \"\",\r\n    message: \"\",\r\n    open: false,\r\n  });\r\n  const [appToastConfig, setAppToastConfig] = useState<any>({\r\n    severity: \"\",\r\n    message: \"\",\r\n    open: false,\r\n  });\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const location = useLocation();\r\n  const { userInfo, isUnAuthorised, success, loginMessage } = useSelector(\r\n    (state: any) => state.authReducer\r\n  );\r\n  const dispatchSessionExpired = () => dispatch<any>(sessionExpired());\r\n  const setLoading = useCallback(\r\n    (loading: boolean) => {\r\n      return setIsLoading(loading);\r\n    },\r\n    [loading]\r\n  );\r\n\r\n  const loadingMemo = useMemo(\r\n    () => ({\r\n      setLoading,\r\n      loading,\r\n    }),\r\n    [loading]\r\n  );\r\n\r\n  // const preferencesMemo = useMemo(\r\n  //   () => ({\r\n  //     setActiveMenuItem,\r\n  //     activeMenuItem,\r\n  //   }),\r\n  //   [activeMenuItem]\r\n  // );\r\n\r\n  const setActiveMenuItem = useCallback(\r\n    (item: string) => {\r\n      return setMenuItem(item);\r\n    },\r\n    [activeMenuItem]\r\n  );\r\n\r\n  const getTitle = (screen: string) => {\r\n    const title = `${\"My Loco Biz\"} | ${screen}`;\r\n    return title;\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (userInfo && userInfo.id > 0) {\r\n      console.log(location.pathname);\r\n\r\n      // if(userInfo.user.isPasswordReset) {\r\n      //   navigate(\"/forgotPassword\");\r\n      // }\r\n      if (location.pathname === \"/login\" || location.pathname === \"/\") {\r\n        navigate(\"/home\");\r\n      }\r\n    } else {\r\n      if (loginMessage) {\r\n        setConfig({\r\n          message: loginMessage,\r\n          severity: ToastSeverity.Error,\r\n          open: true,\r\n        });\r\n      }\r\n      navigate(\"/\");\r\n    }\r\n  }, [userInfo, success]);\r\n\r\n  useEffect(() => {\r\n    if (isUnAuthorised) {\r\n      dispatchSessionExpired();\r\n      setConfig({\r\n        message: \"Your session expired, Please login again\",\r\n        severity: ToastSeverity.Error,\r\n        open: true,\r\n      });\r\n    }\r\n  }, [isUnAuthorised]);\r\n\r\n  const Alert = React.forwardRef<HTMLDivElement, AlertProps>(function Alert(\r\n    props,\r\n    ref\r\n  ) {\r\n    return <MuiAlert elevation={6} ref={ref} variant=\"filled\" {...props} />;\r\n  });\r\n\r\n  const handleClose = (\r\n    event?: React.SyntheticEvent | Event,\r\n    reason?: string\r\n  ) => {\r\n    if (reason === \"clickaway\") {\r\n      return;\r\n    }\r\n\r\n    setConfig({ ...toastConfig, open: false });\r\n  };\r\n\r\n  const setOpen = useCallback(\r\n    (open: boolean) => {\r\n      return setIsOpen(open);\r\n    },\r\n    [open]\r\n  );\r\n\r\n  const setToastMessage = useCallback(\r\n    (message: string) => {\r\n      return setMessage(message);\r\n    },\r\n    [open]\r\n  );\r\n\r\n  const setToastConfig = useCallback(\r\n    (severity: ToastSeverity, message: string, open: boolean) => {\r\n      return setConfig({ severity, message, open });\r\n    },\r\n    [toastConfig]\r\n  );\r\n\r\n  const toastMemo = useMemo(\r\n    () => ({\r\n      open,\r\n      setOpen,\r\n      message,\r\n      setToastMessage,\r\n      toastConfig,\r\n      setToastConfig,\r\n    }),\r\n    [open, message, toastConfig]\r\n  );\r\n\r\n  return (\r\n    <LoadingContext.Provider value={loadingMemo}>\r\n      <Box className=\"App height100\">\r\n        {/* <PreferencesContext.Provider value={preferencesMemo}> */}\r\n        <ToastContext.Provider value={toastMemo}>\r\n          <Box\r\n            className=\"page_loader\"\r\n            style={{ display: loadingMemo.loading ? \"flex\" : \"none\" }}\r\n          >\r\n            <Loader />\r\n            {/* <ThreeCircles\r\n              visible={true}\r\n              height=\"100\"\r\n              width=\"100\"\r\n              color={theme.palette.primary.main}\r\n              ariaLabel=\"three-circles-loading\"\r\n              wrapperStyle={{}}\r\n              wrapperClass=\"\"\r\n            /> */}\r\n          </Box>\r\n          {/* Language Toggle Button */}\r\n\r\n          <Routes>\r\n            <Route path=\"/\" element={<SignIn title={getTitle(\"Home\")} />} />\r\n\r\n            <Route\r\n              path=\"/forgot-password\"\r\n              element={<ForgotPassword title={getTitle(\"Forgot Password\")} />}\r\n            />\r\n\r\n            <Route\r\n              path=\"/post-management/create-social-post\"\r\n              element={<CreateSocialPost title={getTitle(\"Create Post\")} />}\r\n            />\r\n\r\n            {/* Dashboard */}\r\n            <Route\r\n              path=\"/home\"\r\n              element={<DashboardV2 title={getTitle(\"Dashboard\")} />}\r\n            />\r\n\r\n            <Route\r\n              path=\"/home2\"\r\n              element={<Dashboard title={getTitle(\"Dashboard\")} />}\r\n            />\r\n\r\n            {/* Analytics */}\r\n            <Route\r\n              path=\"/analytics\"\r\n              element={<Analytics title={getTitle(\"Analytics\")} />}\r\n            />\r\n\r\n            {/* Q&A */}\r\n            <Route\r\n              path=\"/q-and-a\"\r\n              element={<QandA title={getTitle(\"Q&A\")} />}\r\n            />\r\n\r\n            {/* User Management */}\r\n            <Route\r\n              path=\"/user-management/users\"\r\n              element={<Users title={getTitle(\"Users\")} />}\r\n            />\r\n\r\n            <Route\r\n              path=\"/roles-management/roles\"\r\n              element={<Roles title={getTitle(\"Roles\")} />}\r\n            />\r\n\r\n            {/* Business Management */}\r\n            <Route\r\n              path=\"/business-management/manage-business\"\r\n              element={<ManageBusiness title={getTitle(\"Manage Business\")} />}\r\n            />\r\n\r\n            <Route\r\n              path=\"/business-management/business-summary/:businessId/:accountId/:locationId\"\r\n              element={<BusinessSummary title={getTitle(\"Business Summary\")} />}\r\n            />\r\n\r\n            <Route\r\n              path=\"/business-management/local-business\"\r\n              element={<LocalBusiness title={getTitle(\"Local Business\")} />}\r\n            />\r\n\r\n            {/* GMB Callback */}\r\n            <Route\r\n              path=\"/business-management/gmb/callback\"\r\n              element={<GMBCallback title={getTitle(\"Callback\")} />}\r\n            />\r\n\r\n            {/* Review Management */}\r\n            <Route\r\n              path=\"/review-management/manage-reviews\"\r\n              element={<ManageReviews title={getTitle(\"Manage Reviews\")} />}\r\n            />\r\n\r\n            <Route\r\n              path=\"/review-management/review-settings\"\r\n              element={<ReviewSettings title={getTitle(\"Review Settings\")} />}\r\n            />\r\n\r\n            {/* Post Management */}\r\n            <Route\r\n              path=\"/post-management/posts\"\r\n              element={<PostsListing title={getTitle(\"Posts\")} />}\r\n            />\r\n\r\n            {/* Business Category Management */}\r\n            <Route\r\n              path=\"/business-management/categories\"\r\n              element={\r\n                <BusinessCategoryScreen\r\n                  title={getTitle(\"Business Categories\")}\r\n                />\r\n              }\r\n            />\r\n\r\n            {/* Demo Screens */}\r\n            <Route\r\n              path=\"/demo/business-category\"\r\n              element={\r\n                <DemoScreen title={getTitle(\"Business Category Demo\")} />\r\n              }\r\n            />\r\n\r\n            <Route\r\n              path=\"/demo/services\"\r\n              element={\r\n                <ServicesDemoScreen title={getTitle(\"Add Services Demo\")} />\r\n              }\r\n            />\r\n\r\n            {/* Geo Grid */}\r\n            <Route\r\n              path=\"/geo-grid\"\r\n              element={<GeoGridScreen title={getTitle(\"Google Geo Grid\")} />}\r\n            />\r\n\r\n            {/* Help */}\r\n            <Route\r\n              path=\"/help\"\r\n              element={<Help title={getTitle(\"Manage Reviews\")} />}\r\n            />\r\n\r\n            {/* 404 route - MUST be last */}\r\n            <Route\r\n              path=\"*\"\r\n              element={<NotFoundPage title={getTitle(\"Not Found Page\")} />}\r\n            />\r\n\r\n            <Route\r\n              path=\"/un-authorized\"\r\n              element={\r\n                <UnAuthorized title={getTitle(\"Un Authorized Access\")} />\r\n              }\r\n            />\r\n\r\n            {/* <Route\r\n              path=\"/changePassword\"\r\n              element={\r\n                <ProtectedRoute\r\n                  allowedRoles={[\r\n                    RoleType.SuperAdmin,\r\n                    RoleType.Admin,\r\n                    RoleType.User,\r\n                    RoleType.Agent,\r\n                  ]}\r\n                >\r\n                  <ChangePassword title={getTitle(\"Change Password\")} />\r\n                </ProtectedRoute>\r\n              }\r\n            /> */}\r\n          </Routes>\r\n\r\n          {toastConfig && (\r\n            <Snackbar\r\n              open={toastConfig.open}\r\n              autoHideDuration={2000}\r\n              onClose={handleClose}\r\n            >\r\n              <Alert\r\n                onClose={handleClose}\r\n                severity={toastConfig.severity}\r\n                sx={{ width: \"100%\" }}\r\n              >\r\n                {toastConfig.message}\r\n              </Alert>\r\n            </Snackbar>\r\n          )}\r\n        </ToastContext.Provider>\r\n        {/* </PreferencesContext.Provider> */}\r\n      </Box>\r\n    </LoadingContext.Provider>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IACVC,WAAW,EAEXC,SAAS,EACTC,OAAO,EACPC,QAAQ,QACH,OAAO;AACd,OAAO,oBAAoB;AAE3B,OAAO,WAAW;AAIlB,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,OAAOC,GAAG,MAAM,mBAAmB;AAEnC,SAASC,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC1E,OAAOC,MAAM,MAAM,gCAAgC;AAEnD,OAAOC,SAAS,MAAM,sCAAsC;AAC5D,OAAOC,KAAK,MAAM,6CAA6C;AAC/D,OAAOC,cAAc,MAAM,gDAAgD;AAC3E,OAAOC,KAAK,MAAM,6CAA6C;AAC/D,OAAOC,SAAS,MAAM,sCAAsC;AAC5D,OAAOC,KAAK,MAAM,8BAA8B;AAChD,OAAOC,cAAc,MAAM,mEAAmE;AAC9F,OAAOC,aAAa,MAAM,+DAA+D;AACzF,OAAOC,cAAc,MAAM,iEAAiE;AAC5F,OAAOC,IAAI,MAAM,4BAA4B;AAC7C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,QAAQ,MAAsB,qBAAqB;AAC1D,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,YAAY,QAAQ,yBAAyB;AACtD,OAAOC,aAAa,MAAM,iEAAiE;AAC3F,OAAOC,eAAe,MAAM,qEAAqE;AACjG,OAAOC,gBAAgB,MAAM,oDAAoD;AACjF,OAAOC,WAAW,MAAM,uDAAuD;AAC/E,OAAOC,YAAY,MAAM,4CAA4C;AACrE,SAASC,cAAc,QAAQ,wBAAwB;AACvD,OAAOC,YAAY,MAAM,kDAAkD;AAC3E,OAAOC,WAAW,MAAM,0CAA0C;AAClE,OAAOC,YAAY,MAAM,kDAAkD;AAC3E,OAAOC,sBAAsB,MAAM,oDAAoD;AACvF,OAAOC,UAAU,MAAM,wCAAwC;AAC/D,OAAOC,kBAAkB,MAAM,gDAAgD;AAC/E,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,MAAM,MAAM,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,OAAO,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC4C,cAAc,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAS,EAAE,CAAC;EAClD,MAAM,CAACgD,IAAI,EAAEC,SAAS,CAAC,GAAGjD,QAAQ,CAAU,KAAK,CAAC;EAClD,MAAM,CAACkD,WAAW,EAAEC,SAAS,CAAC,GAAGnD,QAAQ,CAAM;IAC7CoD,QAAQ,EAAE,EAAE;IACZN,OAAO,EAAE,EAAE;IACXE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAM;IACxDoD,QAAQ,EAAE,EAAE;IACZN,OAAO,EAAE,EAAE;IACXE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMO,QAAQ,GAAGlD,WAAW,CAAC,CAAC;EAC9B,MAAMmD,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAMuC,QAAQ,GAAGnD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoD,QAAQ;IAAEC,cAAc;IAAEC,OAAO;IAAEC;EAAa,CAAC,GAAG1C,WAAW,CACpE2C,KAAU,IAAKA,KAAK,CAACC,WACxB,CAAC;EACD,MAAMC,sBAAsB,GAAGA,CAAA,KAAMR,QAAQ,CAAM3B,cAAc,CAAC,CAAC,CAAC;EACpE,MAAMoC,UAAU,GAAGpE,WAAW,CAC3B6C,OAAgB,IAAK;IACpB,OAAOC,YAAY,CAACD,OAAO,CAAC;EAC9B,CAAC,EACD,CAACA,OAAO,CACV,CAAC;EAED,MAAMwB,WAAW,GAAGnE,OAAO,CACzB,OAAO;IACLkE,UAAU;IACVvB;EACF,CAAC,CAAC,EACF,CAACA,OAAO,CACV,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAMyB,iBAAiB,GAAGtE,WAAW,CAClCuE,IAAY,IAAK;IAChB,OAAOvB,WAAW,CAACuB,IAAI,CAAC;EAC1B,CAAC,EACD,CAACxB,cAAc,CACjB,CAAC;EAED,MAAMyB,QAAQ,GAAIC,MAAc,IAAK;IACnC,MAAMC,KAAK,GAAG,GAAG,aAAa,MAAMD,MAAM,EAAE;IAC5C,OAAOC,KAAK;EACd,CAAC;EAEDzE,SAAS,CAAC,MAAM;IACd,IAAI4D,QAAQ,IAAIA,QAAQ,CAACc,EAAE,GAAG,CAAC,EAAE;MAC/BC,OAAO,CAACC,GAAG,CAACjB,QAAQ,CAACkB,QAAQ,CAAC;;MAE9B;MACA;MACA;MACA,IAAIlB,QAAQ,CAACkB,QAAQ,KAAK,QAAQ,IAAIlB,QAAQ,CAACkB,QAAQ,KAAK,GAAG,EAAE;QAC/DpB,QAAQ,CAAC,OAAO,CAAC;MACnB;IACF,CAAC,MAAM;MACL,IAAIM,YAAY,EAAE;QAChBV,SAAS,CAAC;UACRL,OAAO,EAAEe,YAAY;UACrBT,QAAQ,EAAE9B,aAAa,CAACsD,KAAK;UAC7B5B,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACAO,QAAQ,CAAC,GAAG,CAAC;IACf;EACF,CAAC,EAAE,CAACG,QAAQ,EAAEE,OAAO,CAAC,CAAC;EAEvB9D,SAAS,CAAC,MAAM;IACd,IAAI6D,cAAc,EAAE;MAClBK,sBAAsB,CAAC,CAAC;MACxBb,SAAS,CAAC;QACRL,OAAO,EAAE,0CAA0C;QACnDM,QAAQ,EAAE9B,aAAa,CAACsD,KAAK;QAC7B5B,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACW,cAAc,CAAC,CAAC;EAEpB,MAAMkB,KAAK,gBAAGjF,KAAK,CAACkF,UAAU,CAA6B,SAASD,KAAKA,CACvEE,KAAK,EACLC,GAAG,EACH;IACA,oBAAOzC,OAAA,CAAClB,QAAQ;MAAC4D,SAAS,EAAE,CAAE;MAACD,GAAG,EAAEA,GAAI;MAACE,OAAO,EAAC,QAAQ;MAAA,GAAKH;IAAK;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EACzE,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAGA,CAClBC,KAAoC,EACpCC,MAAe,KACZ;IACH,IAAIA,MAAM,KAAK,WAAW,EAAE;MAC1B;IACF;IAEAtC,SAAS,CAAC;MAAE,GAAGD,WAAW;MAAEF,IAAI,EAAE;IAAM,CAAC,CAAC;EAC5C,CAAC;EAED,MAAM0C,OAAO,GAAG7F,WAAW,CACxBmD,IAAa,IAAK;IACjB,OAAOC,SAAS,CAACD,IAAI,CAAC;EACxB,CAAC,EACD,CAACA,IAAI,CACP,CAAC;EAED,MAAM2C,eAAe,GAAG9F,WAAW,CAChCiD,OAAe,IAAK;IACnB,OAAOC,UAAU,CAACD,OAAO,CAAC;EAC5B,CAAC,EACD,CAACE,IAAI,CACP,CAAC;EAED,MAAM4C,cAAc,GAAG/F,WAAW,CAChC,CAACuD,QAAuB,EAAEN,OAAe,EAAEE,IAAa,KAAK;IAC3D,OAAOG,SAAS,CAAC;MAAEC,QAAQ;MAAEN,OAAO;MAAEE;IAAK,CAAC,CAAC;EAC/C,CAAC,EACD,CAACE,WAAW,CACd,CAAC;EAED,MAAM2C,SAAS,GAAG9F,OAAO,CACvB,OAAO;IACLiD,IAAI;IACJ0C,OAAO;IACP5C,OAAO;IACP6C,eAAe;IACfzC,WAAW;IACX0C;EACF,CAAC,CAAC,EACF,CAAC5C,IAAI,EAAEF,OAAO,EAAEI,WAAW,CAC7B,CAAC;EAED,oBACEX,OAAA,CAACtC,cAAc,CAAC6F,QAAQ;IAACC,KAAK,EAAE7B,WAAY;IAAA8B,QAAA,eAC1CzD,OAAA,CAACrC,GAAG;MAAC+F,SAAS,EAAC,eAAe;MAAAD,QAAA,eAE5BzD,OAAA,CAAChB,YAAY,CAACuE,QAAQ;QAACC,KAAK,EAAEF,SAAU;QAAAG,QAAA,gBACtCzD,OAAA,CAACrC,GAAG;UACF+F,SAAS,EAAC,aAAa;UACvBC,KAAK,EAAE;YAAEC,OAAO,EAAEjC,WAAW,CAACxB,OAAO,GAAG,MAAM,GAAG;UAAO,CAAE;UAAAsD,QAAA,eAE1DzD,OAAA,CAACF,MAAM;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUP,CAAC,eAGN/C,OAAA,CAACpC,MAAM;UAAA6F,QAAA,gBACLzD,OAAA,CAACnC,KAAK;YAACgG,IAAI,EAAC,GAAG;YAACC,OAAO,eAAE9D,OAAA,CAAChC,MAAM;cAACgE,KAAK,EAAEF,QAAQ,CAAC,MAAM;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEhE/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eAAE9D,OAAA,CAAC7B,cAAc;cAAC6D,KAAK,EAAEF,QAAQ,CAAC,iBAAiB;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAEF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,qCAAqC;YAC1CC,OAAO,eAAE9D,OAAA,CAACb,gBAAgB;cAAC6C,KAAK,EAAEF,QAAQ,CAAC,aAAa;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eAGF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,OAAO;YACZC,OAAO,eAAE9D,OAAA,CAACR,WAAW;cAACwC,KAAK,EAAEF,QAAQ,CAAC,WAAW;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eAEF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,QAAQ;YACbC,OAAO,eAAE9D,OAAA,CAAC/B,SAAS;cAAC+D,KAAK,EAAEF,QAAQ,CAAC,WAAW;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eAGF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,YAAY;YACjBC,OAAO,eAAE9D,OAAA,CAAC3B,SAAS;cAAC2D,KAAK,EAAEF,QAAQ,CAAC,WAAW;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eAGF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,UAAU;YACfC,OAAO,eAAE9D,OAAA,CAAC1B,KAAK;cAAC0D,KAAK,EAAEF,QAAQ,CAAC,KAAK;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAGF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,wBAAwB;YAC7BC,OAAO,eAAE9D,OAAA,CAAC5B,KAAK;cAAC4D,KAAK,EAAEF,QAAQ,CAAC,OAAO;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eAEF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,yBAAyB;YAC9BC,OAAO,eAAE9D,OAAA,CAAC9B,KAAK;cAAC8D,KAAK,EAAEF,QAAQ,CAAC,OAAO;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eAGF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,sCAAsC;YAC3CC,OAAO,eAAE9D,OAAA,CAACzB,cAAc;cAACyD,KAAK,EAAEF,QAAQ,CAAC,iBAAiB;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAEF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,0EAA0E;YAC/EC,OAAO,eAAE9D,OAAA,CAACd,eAAe;cAAC8C,KAAK,EAAEF,QAAQ,CAAC,kBAAkB;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAEF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,qCAAqC;YAC1CC,OAAO,eAAE9D,OAAA,CAACf,aAAa;cAAC+C,KAAK,EAAEF,QAAQ,CAAC,gBAAgB;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eAGF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,mCAAmC;YACxCC,OAAO,eAAE9D,OAAA,CAACZ,WAAW;cAAC4C,KAAK,EAAEF,QAAQ,CAAC,UAAU;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eAGF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,mCAAmC;YACxCC,OAAO,eAAE9D,OAAA,CAACxB,aAAa;cAACwD,KAAK,EAAEF,QAAQ,CAAC,gBAAgB;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eAEF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,oCAAoC;YACzCC,OAAO,eAAE9D,OAAA,CAACvB,cAAc;cAACuD,KAAK,EAAEF,QAAQ,CAAC,iBAAiB;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAGF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,wBAAwB;YAC7BC,OAAO,eAAE9D,OAAA,CAACX,YAAY;cAAC2C,KAAK,EAAEF,QAAQ,CAAC,OAAO;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eAGF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,iCAAiC;YACtCC,OAAO,eACL9D,OAAA,CAACN,sBAAsB;cACrBsC,KAAK,EAAEF,QAAQ,CAAC,qBAAqB;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,yBAAyB;YAC9BC,OAAO,eACL9D,OAAA,CAACL,UAAU;cAACqC,KAAK,EAAEF,QAAQ,CAAC,wBAAwB;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UACzD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACL9D,OAAA,CAACJ,kBAAkB;cAACoC,KAAK,EAAEF,QAAQ,CAAC,mBAAmB;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAC5D;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,WAAW;YAChBC,OAAO,eAAE9D,OAAA,CAACH,aAAa;cAACmC,KAAK,EAAEF,QAAQ,CAAC,iBAAiB;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eAGF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,OAAO;YACZC,OAAO,eAAE9D,OAAA,CAACtB,IAAI;cAACsD,KAAK,EAAEF,QAAQ,CAAC,gBAAgB;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eAGF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,GAAG;YACRC,OAAO,eAAE9D,OAAA,CAACT,YAAY;cAACyC,KAAK,EAAEF,QAAQ,CAAC,gBAAgB;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eAEF/C,OAAA,CAACnC,KAAK;YACJgG,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACL9D,OAAA,CAACP,YAAY;cAACuC,KAAK,EAAEF,QAAQ,CAAC,sBAAsB;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UACzD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiBI,CAAC,EAERpC,WAAW,iBACVX,OAAA,CAACnB,QAAQ;UACP4B,IAAI,EAAEE,WAAW,CAACF,IAAK;UACvBsD,gBAAgB,EAAE,IAAK;UACvBC,OAAO,EAAEhB,WAAY;UAAAS,QAAA,eAErBzD,OAAA,CAACsC,KAAK;YACJ0B,OAAO,EAAEhB,WAAY;YACrBnC,QAAQ,EAAEF,WAAW,CAACE,QAAS;YAC/BoD,EAAE,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAT,QAAA,EAErB9C,WAAW,CAACJ;UAAO;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACoB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAErB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACiB,CAAC;AAE9B;AAAC7C,EAAA,CAhVQD,GAAG;EAAA,QAeOnC,WAAW,EACXa,WAAW,EACXZ,WAAW,EACgCa,WAAW;AAAA;AAAAuF,EAAA,GAlBhElE,GAAG;AAkVZ,eAAeA,GAAG;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}