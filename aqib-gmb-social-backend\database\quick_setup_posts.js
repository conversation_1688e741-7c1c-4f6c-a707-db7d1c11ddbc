require('dotenv').config({ path: '.env.development' });
const pool = require('../config/db');

async function quickSetupPosts() {
  console.log('⚡ Quick GMB Posts Setup...');
  console.log(`📅 Timestamp: ${new Date().toISOString()}`);
  console.log();
  
  try {
    // Test database connection
    console.log('🔗 Connecting to database...');
    await pool.query('SELECT 1');
    console.log('✅ Database connection established!');
    console.log();
    
    // Create table with individual error handling
    console.log('🏗️ Creating gmb_posts table...');
    
    try {
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS gmb_posts (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          business_id INT NOT NULL,
          location_id VARCHAR(255) NOT NULL,
          account_id VARCHAR(255) NOT NULL,
          google_post_name VARCHAR(500) NOT NULL,
          bulk_post_id VARCHAR(36) NULL,
          is_bulk_post BOOLEAN DEFAULT FALSE,
          post_content JSON NOT NULL,
          post_response JSON NOT NULL,
          summary TEXT,
          topic_type VARCHAR(50),
          language_code VARCHAR(10),
          state VARCHAR(50),
          search_url TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `;
      
      await pool.query(createTableQuery);
      console.log('✅ gmb_posts table created successfully');
      
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️ gmb_posts table already exists');
      } else {
        throw error;
      }
    }
    
    // Add indexes separately with error handling
    console.log('📊 Adding indexes...');
    
    const indexes = [
      { name: 'idx_user_id', column: 'user_id' },
      { name: 'idx_business_id', column: 'business_id' },
      { name: 'idx_location_id', column: 'location_id' },
      { name: 'idx_bulk_post_id', column: 'bulk_post_id' },
      { name: 'idx_google_post_name', column: 'google_post_name' },
      { name: 'idx_is_bulk_post', column: 'is_bulk_post' },
      { name: 'idx_created_at', column: 'created_at' }
    ];
    
    for (const index of indexes) {
      try {
        await pool.query(`CREATE INDEX ${index.name} ON gmb_posts (${index.column})`);
        console.log(`✅ Added index: ${index.name}`);
      } catch (error) {
        if (error.message.includes('Duplicate key name')) {
          console.log(`ℹ️ Index ${index.name} already exists`);
        } else {
          console.log(`⚠️ Could not create index ${index.name}: ${error.message}`);
        }
      }
    }
    
    // Add composite indexes
    const compositeIndexes = [
      { name: 'idx_user_business', columns: 'user_id, business_id' },
      { name: 'idx_bulk_posts', columns: 'bulk_post_id, is_bulk_post' },
      { name: 'idx_location_posts', columns: 'location_id, created_at' }
    ];
    
    for (const index of compositeIndexes) {
      try {
        await pool.query(`CREATE INDEX ${index.name} ON gmb_posts (${index.columns})`);
        console.log(`✅ Added composite index: ${index.name}`);
      } catch (error) {
        if (error.message.includes('Duplicate key name')) {
          console.log(`ℹ️ Composite index ${index.name} already exists`);
        } else {
          console.log(`⚠️ Could not create composite index ${index.name}: ${error.message}`);
        }
      }
    }
    
    console.log();
    
    // Verify setup
    console.log('🔍 Verifying setup...');
    
    const tableCheck = await pool.query(`
      SELECT COUNT(*) as table_exists 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = 'gmb_posts'
    `, [process.env.APP_DB_NAME]);
    
    if (tableCheck[0].table_exists > 0) {
      console.log('✅ Table gmb_posts exists');
      
      // Get row count
      const countResult = await pool.query('SELECT COUNT(*) as row_count FROM gmb_posts');
      console.log(`📊 Current rows: ${countResult[0].row_count}`);
      
      // Show indexes
      const indexInfo = await pool.query('SHOW INDEX FROM gmb_posts');
      const uniqueIndexes = [...new Set(indexInfo.map(idx => idx.Key_name))];
      console.log(`📊 Total indexes: ${uniqueIndexes.length}`);
      
    } else {
      throw new Error('Table verification failed');
    }
    
    console.log();
    console.log('🎉 Quick setup completed successfully!');
    console.log();
    console.log('🎯 Next steps:');
    console.log('   1. Start the backend server: npm start');
    console.log('   2. Test the posts API: npm run test:posts-api');
    console.log('   3. Create posts through the frontend');
    console.log();
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Quick setup failed:', error.message);
    console.error('🔧 Error details:', error);
    process.exit(1);
  }
}

quickSetupPosts();
