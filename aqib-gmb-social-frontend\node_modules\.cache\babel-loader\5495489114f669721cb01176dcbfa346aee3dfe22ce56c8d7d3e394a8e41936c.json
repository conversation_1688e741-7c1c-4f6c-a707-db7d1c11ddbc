{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\charts\\\\activeJobs.charts.tsx\";\nimport React from \"react\";\nimport { Line } from \"react-chartjs-2\";\nimport { Chart, registerables } from \"chart.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChart.register(...registerables);\nexport const ActiveJobsChart = props => {\n  const data = {\n    labels: [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\"],\n    datasets: [{\n      label: props.label,\n      data: props.chartData,\n      backgroundColor: [\"rgba(255, 99, 132, 0.2)\", \"rgba(255, 159, 64, 0.2)\", \"rgba(255, 205, 86, 0.2)\", \"rgba(75, 192, 192, 0.2)\", \"rgba(54, 162, 235, 0.2)\", \"rgba(153, 102, 255, 0.2)\", \"rgba(201, 203, 207, 0.2)\"],\n      borderColor: [\"rgb(255, 99, 132)\", \"rgb(255, 159, 64)\", \"rgb(255, 205, 86)\", \"rgb(75, 192, 192)\", \"rgb(54, 162, 235)\", \"rgb(153, 102, 255)\", \"rgb(201, 203, 207)\"],\n      borderWidth: 1\n    }]\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chart-container 123\",\n    children: /*#__PURE__*/_jsxDEV(Line, {\n      data: data,\n      options: {}\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_c = ActiveJobsChart;\nvar _c;\n$RefreshReg$(_c, \"ActiveJobsChart\");", "map": {"version": 3, "names": ["React", "Line", "Chart", "registerables", "jsxDEV", "_jsxDEV", "register", "ActiveJobsChart", "props", "data", "labels", "datasets", "label", "chartData", "backgroundColor", "borderColor", "borderWidth", "className", "children", "options", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/charts/activeJobs.charts.tsx"], "sourcesContent": ["import React from \"react\";\nimport { Line, PolarArea } from \"react-chartjs-2\";\nimport { Chart, registerables } from \"chart.js\";\nChart.register(...registerables);\n\nexport const ActiveJobsChart = (props: { chartData: any; label: string }) => {\n  const data = {\n    labels: [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\"],\n    datasets: [\n      {\n        label: props.label,\n        data: props.chartData,\n        backgroundColor: [\n          \"rgba(255, 99, 132, 0.2)\",\n          \"rgba(255, 159, 64, 0.2)\",\n          \"rgba(255, 205, 86, 0.2)\",\n          \"rgba(75, 192, 192, 0.2)\",\n          \"rgba(54, 162, 235, 0.2)\",\n          \"rgba(153, 102, 255, 0.2)\",\n          \"rgba(201, 203, 207, 0.2)\",\n        ],\n        borderColor: [\n          \"rgb(255, 99, 132)\",\n          \"rgb(255, 159, 64)\",\n          \"rgb(255, 205, 86)\",\n          \"rgb(75, 192, 192)\",\n          \"rgb(54, 162, 235)\",\n          \"rgb(153, 102, 255)\",\n          \"rgb(201, 203, 207)\",\n        ],\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  return (\n    <div className=\"chart-container 123\">\n      <Line data={data} options={{}} />\n    </div>\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAmB,iBAAiB;AACjD,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAChDH,KAAK,CAACI,QAAQ,CAAC,GAAGH,aAAa,CAAC;AAEhC,OAAO,MAAMI,eAAe,GAAIC,KAAwC,IAAK;EAC3E,MAAMC,IAAI,GAAG;IACXC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;IACxEC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAEJ,KAAK,CAACI,KAAK;MAClBH,IAAI,EAAED,KAAK,CAACK,SAAS;MACrBC,eAAe,EAAE,CACf,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,0BAA0B,EAC1B,0BAA0B,CAC3B;MACDC,WAAW,EAAE,CACX,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,oBAAoB,EACpB,oBAAoB,CACrB;MACDC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,oBACEX,OAAA;IAAKY,SAAS,EAAC,qBAAqB;IAAAC,QAAA,eAClCb,OAAA,CAACJ,IAAI;MAACQ,IAAI,EAAEA,IAAK;MAACU,OAAO,EAAE,CAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9B,CAAC;AAEV,CAAC;AAACC,EAAA,GAnCWjB,eAAe;AAAA,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}