{"ast": null, "code": "import HttpHelperService from \"../httpHelper.service\";\nimport { MANAGE_ASSETS_UPLOAD, MANAGE_ASSETS_GET_BUSINESS_ASSETS, MANAGE_ASSETS_GET_ASSET, MANAGE_ASSETS_DELETE_ASSET, MA<PERSON>GE_ASSETS_UPDATE_MAX_SIZE } from \"../../constants/endPoints.constant\";\nclass ManageAssetsService {\n  constructor(dispatch) {\n    this._httpHelperService = void 0;\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  /**\n   * Upload assets for a business\n   * @param businessId - Business ID\n   * @param files - Files to upload\n   * @returns Promise with upload result\n   */\n  async uploadAssets(businessId, files) {\n    try {\n      const formData = new FormData();\n\n      // Add all files to FormData\n      Array.from(files).forEach(file => {\n        formData.append(\"files\", file);\n      });\n      return await this._httpHelperService.postFormData(MANAGE_ASSETS_UPLOAD(businessId), formData);\n    } catch (error) {\n      console.error(\"Error uploading assets:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get assets for a business with pagination\n   * @param businessId - Business ID\n   * @param page - Page number\n   * @param limit - Records per page\n   * @returns Promise with assets list\n   */\n  async getAssets(businessId, page = 1, limit = 12) {\n    try {\n      return await this._httpHelperService.get(MANAGE_ASSETS_GET_BUSINESS_ASSETS(businessId), {\n        page,\n        limit\n      });\n    } catch (error) {\n      console.error(\"Error fetching assets:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get single asset by ID\n   * @param assetId - Asset ID\n   * @returns Promise with asset data\n   */\n  async getAssetById(assetId) {\n    try {\n      return await this._httpHelperService.get(MANAGE_ASSETS_GET_ASSET(assetId));\n    } catch (error) {\n      console.error(\"Error fetching asset:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete an asset\n   * @param assetId - Asset ID\n   * @returns Promise with delete result\n   */\n  async deleteAsset(assetId) {\n    try {\n      return await this._httpHelperService.delete(MANAGE_ASSETS_DELETE_ASSET(assetId));\n    } catch (error) {\n      console.error(\"Error deleting asset:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update max upload size for a business\n   * @param businessId - Business ID\n   * @param maxSizeMB - Max size in MB\n   * @returns Promise with update result\n   */\n  async updateMaxUploadSize(businessId, maxSizeMB) {\n    try {\n      return await this._httpHelperService.put(MANAGE_ASSETS_UPDATE_MAX_SIZE(businessId), {\n        maxSizeMB\n      });\n    } catch (error) {\n      console.error(\"Error updating max upload size:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Validate file before upload\n   * @param file - File to validate\n   * @param maxSizeMB - Maximum allowed size in MB\n   * @returns Validation result\n   */\n  validateFile(file, maxSizeMB) {\n    // Check file size\n    const maxSizeBytes = maxSizeMB * 1024 * 1024;\n    if (file.size > maxSizeBytes) {\n      return {\n        valid: false,\n        error: `File size exceeds ${maxSizeMB} MB limit`\n      };\n    }\n\n    // Check file type\n    const allowedTypes = [\n    // Images\n    \"image/jpeg\", \"image/jpg\", \"image/png\", \"image/gif\", \"image/webp\",\n    // Videos\n    \"video/mp4\", \"video/avi\", \"video/mov\", \"video/wmv\", \"video/flv\", \"video/webm\"];\n    if (!allowedTypes.includes(file.type)) {\n      return {\n        valid: false,\n        error: `Unsupported file type: ${file.type}`\n      };\n    }\n    return {\n      valid: true\n    };\n  }\n\n  /**\n   * Format file size for display\n   * @param bytes - File size in bytes\n   * @returns Formatted file size string\n   */\n  formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n  }\n\n  /**\n   * Check if file is an image\n   * @param mimeType - File MIME type\n   * @returns True if file is an image\n   */\n  isImage(mimeType) {\n    return mimeType.startsWith(\"image/\");\n  }\n\n  /**\n   * Check if file is a video\n   * @param mimeType - File MIME type\n   * @returns True if file is a video\n   */\n  isVideo(mimeType) {\n    return mimeType.startsWith(\"video/\");\n  }\n\n  /**\n   * Get file type display name\n   * @param mimeType - File MIME type\n   * @returns Display name for file type\n   */\n  getFileTypeDisplay(mimeType) {\n    if (this.isImage(mimeType)) return \"Image\";\n    if (this.isVideo(mimeType)) return \"Video\";\n    return \"File\";\n  }\n\n  /**\n   * Generate thumbnail URL for video files\n   * @param videoUrl - Video URL\n   * @returns Thumbnail URL (placeholder for now)\n   */\n  getVideoThumbnail(videoUrl) {\n    // For now, return a placeholder. In production, you might want to generate\n    // actual thumbnails or use a service like AWS Lambda to create them\n    return \"/assets/video-placeholder.png\";\n  }\n}\nexport default ManageAssetsService;", "map": {"version": 3, "names": ["HttpHelperService", "MANAGE_ASSETS_UPLOAD", "MANAGE_ASSETS_GET_BUSINESS_ASSETS", "MANAGE_ASSETS_GET_ASSET", "MANAGE_ASSETS_DELETE_ASSET", "MANAGE_ASSETS_UPDATE_MAX_SIZE", "ManageAssetsService", "constructor", "dispatch", "_httpHelperService", "uploadAssets", "businessId", "files", "formData", "FormData", "Array", "from", "for<PERSON>ach", "file", "append", "postFormData", "error", "console", "getAssets", "page", "limit", "get", "getAssetById", "assetId", "deleteAsset", "delete", "updateMaxUploadSize", "maxSizeMB", "put", "validateFile", "maxSizeBytes", "size", "valid", "allowedTypes", "includes", "type", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "isImage", "mimeType", "startsWith", "isVideo", "getFileTypeDisplay", "getVideoThumbnail", "videoUrl"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/manageAssets/manageAssets.service.ts"], "sourcesContent": ["import { Dispatch } from \"react\";\nimport HttpHelperService from \"../httpHelper.service\";\nimport { Action } from \"redux\";\nimport {\n  MANAGE_ASSETS_UPLOAD,\n  MANAGE_ASSETS_GET_BUSINESS_ASSETS,\n  MANAGE_ASSETS_GET_ASSET,\n  MANAGE_ASSETS_DELETE_ASSET,\n  MANAGE_ASSETS_UPDATE_MAX_SIZE,\n} from \"../../constants/endPoints.constant\";\n\nclass ManageAssetsService {\n  _httpHelperService: HttpHelperService;\n\n  constructor(dispatch: Dispatch<Action>) {\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  /**\n   * Upload assets for a business\n   * @param businessId - Business ID\n   * @param files - Files to upload\n   * @returns Promise with upload result\n   */\n  async uploadAssets(businessId: number, files: FileList): Promise<any> {\n    try {\n      const formData = new FormData();\n\n      // Add all files to FormData\n      Array.from(files).forEach((file) => {\n        formData.append(\"files\", file);\n      });\n\n      return await this._httpHelperService.postFormData(\n        MANAGE_ASSETS_UPLOAD(businessId),\n        formData\n      );\n    } catch (error: any) {\n      console.error(\"Error uploading assets:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get assets for a business with pagination\n   * @param businessId - Business ID\n   * @param page - Page number\n   * @param limit - Records per page\n   * @returns Promise with assets list\n   */\n  async getAssets(\n    businessId: number,\n    page: number = 1,\n    limit: number = 12\n  ): Promise<any> {\n    try {\n      return await this._httpHelperService.get(\n        MANAGE_ASSETS_GET_BUSINESS_ASSETS(businessId),\n        { page, limit }\n      );\n    } catch (error: any) {\n      console.error(\"Error fetching assets:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get single asset by ID\n   * @param assetId - Asset ID\n   * @returns Promise with asset data\n   */\n  async getAssetById(assetId: number): Promise<any> {\n    try {\n      return await this._httpHelperService.get(\n        MANAGE_ASSETS_GET_ASSET(assetId)\n      );\n    } catch (error: any) {\n      console.error(\"Error fetching asset:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete an asset\n   * @param assetId - Asset ID\n   * @returns Promise with delete result\n   */\n  async deleteAsset(assetId: number): Promise<any> {\n    try {\n      return await this._httpHelperService.delete(\n        MANAGE_ASSETS_DELETE_ASSET(assetId)\n      );\n    } catch (error: any) {\n      console.error(\"Error deleting asset:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update max upload size for a business\n   * @param businessId - Business ID\n   * @param maxSizeMB - Max size in MB\n   * @returns Promise with update result\n   */\n  async updateMaxUploadSize(\n    businessId: number,\n    maxSizeMB: number\n  ): Promise<any> {\n    try {\n      return await this._httpHelperService.put(\n        MANAGE_ASSETS_UPDATE_MAX_SIZE(businessId),\n        { maxSizeMB }\n      );\n    } catch (error: any) {\n      console.error(\"Error updating max upload size:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Validate file before upload\n   * @param file - File to validate\n   * @param maxSizeMB - Maximum allowed size in MB\n   * @returns Validation result\n   */\n  validateFile(\n    file: File,\n    maxSizeMB: number\n  ): { valid: boolean; error?: string } {\n    // Check file size\n    const maxSizeBytes = maxSizeMB * 1024 * 1024;\n    if (file.size > maxSizeBytes) {\n      return {\n        valid: false,\n        error: `File size exceeds ${maxSizeMB} MB limit`,\n      };\n    }\n\n    // Check file type\n    const allowedTypes = [\n      // Images\n      \"image/jpeg\",\n      \"image/jpg\",\n      \"image/png\",\n      \"image/gif\",\n      \"image/webp\",\n      // Videos\n      \"video/mp4\",\n      \"video/avi\",\n      \"video/mov\",\n      \"video/wmv\",\n      \"video/flv\",\n      \"video/webm\",\n    ];\n\n    if (!allowedTypes.includes(file.type)) {\n      return {\n        valid: false,\n        error: `Unsupported file type: ${file.type}`,\n      };\n    }\n\n    return { valid: true };\n  }\n\n  /**\n   * Format file size for display\n   * @param bytes - File size in bytes\n   * @returns Formatted file size string\n   */\n  formatFileSize(bytes: number): string {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n  }\n\n  /**\n   * Check if file is an image\n   * @param mimeType - File MIME type\n   * @returns True if file is an image\n   */\n  isImage(mimeType: string): boolean {\n    return mimeType.startsWith(\"image/\");\n  }\n\n  /**\n   * Check if file is a video\n   * @param mimeType - File MIME type\n   * @returns True if file is a video\n   */\n  isVideo(mimeType: string): boolean {\n    return mimeType.startsWith(\"video/\");\n  }\n\n  /**\n   * Get file type display name\n   * @param mimeType - File MIME type\n   * @returns Display name for file type\n   */\n  getFileTypeDisplay(mimeType: string): string {\n    if (this.isImage(mimeType)) return \"Image\";\n    if (this.isVideo(mimeType)) return \"Video\";\n    return \"File\";\n  }\n\n  /**\n   * Generate thumbnail URL for video files\n   * @param videoUrl - Video URL\n   * @returns Thumbnail URL (placeholder for now)\n   */\n  getVideoThumbnail(videoUrl: string): string {\n    // For now, return a placeholder. In production, you might want to generate\n    // actual thumbnails or use a service like AWS Lambda to create them\n    return \"/assets/video-placeholder.png\";\n  }\n}\n\nexport default ManageAssetsService;\n"], "mappings": "AACA,OAAOA,iBAAiB,MAAM,uBAAuB;AAErD,SACEC,oBAAoB,EACpBC,iCAAiC,EACjCC,uBAAuB,EACvBC,0BAA0B,EAC1BC,6BAA6B,QACxB,oCAAoC;AAE3C,MAAMC,mBAAmB,CAAC;EAGxBC,WAAWA,CAACC,QAA0B,EAAE;IAAA,KAFxCC,kBAAkB;IAGhB,IAAI,CAACA,kBAAkB,GAAG,IAAIT,iBAAiB,CAACQ,QAAQ,CAAC;EAC3D;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAME,YAAYA,CAACC,UAAkB,EAAEC,KAAe,EAAgB;IACpE,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAE/B;MACAC,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC,CAACK,OAAO,CAAEC,IAAI,IAAK;QAClCL,QAAQ,CAACM,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;MAChC,CAAC,CAAC;MAEF,OAAO,MAAM,IAAI,CAACT,kBAAkB,CAACW,YAAY,CAC/CnB,oBAAoB,CAACU,UAAU,CAAC,EAChCE,QACF,CAAC;IACH,CAAC,CAAC,OAAOQ,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAME,SAASA,CACbZ,UAAkB,EAClBa,IAAY,GAAG,CAAC,EAChBC,KAAa,GAAG,EAAE,EACJ;IACd,IAAI;MACF,OAAO,MAAM,IAAI,CAAChB,kBAAkB,CAACiB,GAAG,CACtCxB,iCAAiC,CAACS,UAAU,CAAC,EAC7C;QAAEa,IAAI;QAAEC;MAAM,CAChB,CAAC;IACH,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMM,YAAYA,CAACC,OAAe,EAAgB;IAChD,IAAI;MACF,OAAO,MAAM,IAAI,CAACnB,kBAAkB,CAACiB,GAAG,CACtCvB,uBAAuB,CAACyB,OAAO,CACjC,CAAC;IACH,CAAC,CAAC,OAAOP,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMQ,WAAWA,CAACD,OAAe,EAAgB;IAC/C,IAAI;MACF,OAAO,MAAM,IAAI,CAACnB,kBAAkB,CAACqB,MAAM,CACzC1B,0BAA0B,CAACwB,OAAO,CACpC,CAAC;IACH,CAAC,CAAC,OAAOP,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMU,mBAAmBA,CACvBpB,UAAkB,EAClBqB,SAAiB,EACH;IACd,IAAI;MACF,OAAO,MAAM,IAAI,CAACvB,kBAAkB,CAACwB,GAAG,CACtC5B,6BAA6B,CAACM,UAAU,CAAC,EACzC;QAAEqB;MAAU,CACd,CAAC;IACH,CAAC,CAAC,OAAOX,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEa,YAAYA,CACVhB,IAAU,EACVc,SAAiB,EACmB;IACpC;IACA,MAAMG,YAAY,GAAGH,SAAS,GAAG,IAAI,GAAG,IAAI;IAC5C,IAAId,IAAI,CAACkB,IAAI,GAAGD,YAAY,EAAE;MAC5B,OAAO;QACLE,KAAK,EAAE,KAAK;QACZhB,KAAK,EAAE,qBAAqBW,SAAS;MACvC,CAAC;IACH;;IAEA;IACA,MAAMM,YAAY,GAAG;IACnB;IACA,YAAY,EACZ,WAAW,EACX,WAAW,EACX,WAAW,EACX,YAAY;IACZ;IACA,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,YAAY,CACb;IAED,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACrB,IAAI,CAACsB,IAAI,CAAC,EAAE;MACrC,OAAO;QACLH,KAAK,EAAE,KAAK;QACZhB,KAAK,EAAE,0BAA0BH,IAAI,CAACsB,IAAI;MAC5C,CAAC;IACH;IAEA,OAAO;MAAEH,KAAK,EAAE;IAAK,CAAC;EACxB;;EAEA;AACF;AACA;AACA;AACA;EACEI,cAAcA,CAACC,KAAa,EAAU;IACpC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE;;EAEA;AACF;AACA;AACA;AACA;EACEO,OAAOA,CAACC,QAAgB,EAAW;IACjC,OAAOA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC;EACtC;;EAEA;AACF;AACA;AACA;AACA;EACEC,OAAOA,CAACF,QAAgB,EAAW;IACjC,OAAOA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC;EACtC;;EAEA;AACF;AACA;AACA;AACA;EACEE,kBAAkBA,CAACH,QAAgB,EAAU;IAC3C,IAAI,IAAI,CAACD,OAAO,CAACC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC1C,IAAI,IAAI,CAACE,OAAO,CAACF,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC1C,OAAO,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;EACEI,iBAAiBA,CAACC,QAAgB,EAAU;IAC1C;IACA;IACA,OAAO,+BAA+B;EACxC;AACF;AAEA,eAAepD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}