{"ast": null, "code": "import HttpHelperService from \"../httpHelper.service\";\nimport { CREATE_BUSINESS, DELETE_BUSINESS, EDIT_BUSINESS, ENABLE_DISABLE_BUSINESS, GMB_ACCOUNTS, LIST_OF_BUSINESS, LIST_OF_BUSINESS_PAGINATED, LIST_OF_LOCATIONS } from \"../../constants/endPoints.constant\";\nclass BusinessService {\n  constructor(dispatch) {\n    this._httpHelperService = void 0;\n    this.getBusiness = async userId => {\n      return await this._httpHelperService.get(`${LIST_OF_BUSINESS}/${userId}`);\n    };\n    this.getBusinessPaginated = async (userId, paginationModel) => {\n      return await this._httpHelperService.get(`${LIST_OF_BUSINESS_PAGINATED}/${userId}?pageNo=${paginationModel.pageNo}&offset=${paginationModel.offset}`);\n    };\n    this.addBusiness = async request => {\n      return await this._httpHelperService.post(`${CREATE_BUSINESS}`, request);\n    };\n    this.updateBusiness = async (request, id) => {\n      return await this._httpHelperService.put(`${EDIT_BUSINESS}/${id}`, request);\n    };\n    this.getBusinessGroups = async userId => {\n      return await this._httpHelperService.get(`${GMB_ACCOUNTS}/${userId}`);\n    };\n    this.getLocations = async userId => {\n      return await this._httpHelperService.get(`${LIST_OF_LOCATIONS}/${userId}`);\n    };\n    this.deleteBusniess = async businessId => {\n      return await this._httpHelperService.delete(`${DELETE_BUSINESS}/${businessId}`);\n    };\n    this.enableDisableBusniess = async (businessId, request) => {\n      return await this._httpHelperService.post(`${ENABLE_DISABLE_BUSINESS}/${businessId}`, request);\n    };\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n}\nexport default BusinessService;", "map": {"version": 3, "names": ["HttpHelperService", "CREATE_BUSINESS", "DELETE_BUSINESS", "EDIT_BUSINESS", "ENABLE_DISABLE_BUSINESS", "GMB_ACCOUNTS", "LIST_OF_BUSINESS", "LIST_OF_BUSINESS_PAGINATED", "LIST_OF_LOCATIONS", "BusinessService", "constructor", "dispatch", "_httpHelperService", "getBusiness", "userId", "get", "getBusinessPaginated", "paginationModel", "pageNo", "offset", "addBusiness", "request", "post", "updateBusiness", "id", "put", "getBusinessGroups", "getLocations", "deleteBusniess", "businessId", "delete", "enableDisableBusniess"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/business/business.service.tsx"], "sourcesContent": ["import { Component, Dispatch } from \"react\";\nimport HttpHelperService from \"../httpHelper.service\";\nimport {\n  CREATE_BUSINESS,\n  DELETE_BUSINESS,\n  EDIT_BUSINESS,\n  ENABLE_DISABLE_BUSINESS,\n  GMB_ACCOUNTS,\n  LIST_OF_BUSINESS,\n  LIST_OF_BUSINESS_PAGINATED,\n  LIST_OF_LOCATIONS,\n} from \"../../constants/endPoints.constant\";\nimport { IAddBusinessRequestModel } from \"../../interfaces/request/IAddBusinessRequestModel\";\nimport { IPaginationModel } from \"../../interfaces/IPaginationModel\";\nimport { Action } from \"redux\";\n\nclass BusinessService {\n  _httpHelperService;\n  constructor(dispatch: Dispatch<Action>) {\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  getBusiness = async (userId: number) => {\n    return await this._httpHelperService.get(`${LIST_OF_BUSINESS}/${userId}`);\n  };\n\n  getBusinessPaginated = async (\n    userId: number,\n    paginationModel: IPaginationModel\n  ) => {\n    return await this._httpHelperService.get(\n      `${LIST_OF_BUSINESS_PAGINATED}/${userId}?pageNo=${paginationModel.pageNo}&offset=${paginationModel.offset}`\n    );\n  };\n\n  addBusiness = async (request: IAddBusinessRequestModel) => {\n    return await this._httpHelperService.post(`${CREATE_BUSINESS}`, request);\n  };\n\n  updateBusiness = async (request: IAddBusinessRequestModel, id: number) => {\n    return await this._httpHelperService.put(`${EDIT_BUSINESS}/${id}`, request);\n  };\n\n  getBusinessGroups = async (userId: number) => {\n    return await this._httpHelperService.get(`${GMB_ACCOUNTS}/${userId}`);\n  };\n\n  getLocations = async (userId: number) => {\n    return await this._httpHelperService.get(`${LIST_OF_LOCATIONS}/${userId}`);\n  };\n\n  deleteBusniess = async (businessId: number) => {\n    return await this._httpHelperService.delete(\n      `${DELETE_BUSINESS}/${businessId}`\n    );\n  };\n\n  enableDisableBusniess = async (businessId: number, request: any) => {\n    return await this._httpHelperService.post(\n      `${ENABLE_DISABLE_BUSINESS}/${businessId}`,\n      request\n    );\n  };\n}\n\nexport default BusinessService;\n"], "mappings": "AACA,OAAOA,iBAAiB,MAAM,uBAAuB;AACrD,SACEC,eAAe,EACfC,eAAe,EACfC,aAAa,EACbC,uBAAuB,EACvBC,YAAY,EACZC,gBAAgB,EAChBC,0BAA0B,EAC1BC,iBAAiB,QACZ,oCAAoC;AAK3C,MAAMC,eAAe,CAAC;EAEpBC,WAAWA,CAACC,QAA0B,EAAE;IAAA,KADxCC,kBAAkB;IAAA,KAKlBC,WAAW,GAAG,MAAOC,MAAc,IAAK;MACtC,OAAO,MAAM,IAAI,CAACF,kBAAkB,CAACG,GAAG,CAAC,GAAGT,gBAAgB,IAAIQ,MAAM,EAAE,CAAC;IAC3E,CAAC;IAAA,KAEDE,oBAAoB,GAAG,OACrBF,MAAc,EACdG,eAAiC,KAC9B;MACH,OAAO,MAAM,IAAI,CAACL,kBAAkB,CAACG,GAAG,CACtC,GAAGR,0BAA0B,IAAIO,MAAM,WAAWG,eAAe,CAACC,MAAM,WAAWD,eAAe,CAACE,MAAM,EAC3G,CAAC;IACH,CAAC;IAAA,KAEDC,WAAW,GAAG,MAAOC,OAAiC,IAAK;MACzD,OAAO,MAAM,IAAI,CAACT,kBAAkB,CAACU,IAAI,CAAC,GAAGrB,eAAe,EAAE,EAAEoB,OAAO,CAAC;IAC1E,CAAC;IAAA,KAEDE,cAAc,GAAG,OAAOF,OAAiC,EAAEG,EAAU,KAAK;MACxE,OAAO,MAAM,IAAI,CAACZ,kBAAkB,CAACa,GAAG,CAAC,GAAGtB,aAAa,IAAIqB,EAAE,EAAE,EAAEH,OAAO,CAAC;IAC7E,CAAC;IAAA,KAEDK,iBAAiB,GAAG,MAAOZ,MAAc,IAAK;MAC5C,OAAO,MAAM,IAAI,CAACF,kBAAkB,CAACG,GAAG,CAAC,GAAGV,YAAY,IAAIS,MAAM,EAAE,CAAC;IACvE,CAAC;IAAA,KAEDa,YAAY,GAAG,MAAOb,MAAc,IAAK;MACvC,OAAO,MAAM,IAAI,CAACF,kBAAkB,CAACG,GAAG,CAAC,GAAGP,iBAAiB,IAAIM,MAAM,EAAE,CAAC;IAC5E,CAAC;IAAA,KAEDc,cAAc,GAAG,MAAOC,UAAkB,IAAK;MAC7C,OAAO,MAAM,IAAI,CAACjB,kBAAkB,CAACkB,MAAM,CACzC,GAAG5B,eAAe,IAAI2B,UAAU,EAClC,CAAC;IACH,CAAC;IAAA,KAEDE,qBAAqB,GAAG,OAAOF,UAAkB,EAAER,OAAY,KAAK;MAClE,OAAO,MAAM,IAAI,CAACT,kBAAkB,CAACU,IAAI,CACvC,GAAGlB,uBAAuB,IAAIyB,UAAU,EAAE,EAC1CR,OACF,CAAC;IACH,CAAC;IA3CC,IAAI,CAACT,kBAAkB,GAAG,IAAIZ,iBAAiB,CAACW,QAAQ,CAAC;EAC3D;AA2CF;AAEA,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}