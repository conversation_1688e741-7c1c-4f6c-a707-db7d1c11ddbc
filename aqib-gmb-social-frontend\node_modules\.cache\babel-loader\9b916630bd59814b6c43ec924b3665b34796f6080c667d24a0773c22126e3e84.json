{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\createPost\\\\createPostTemplates\\\\cards\\\\testimonialCard2\\\\testimonialCard2.component.tsx\";\nimport React from \"react\";\nimport { Box, Typography } from \"@mui/material\";\nimport UserAvatar from \"../../../../userAvatar/userAvatar.component\";\nimport RatingsStar from \"../../../../ratingsStar/ratingsStar.component\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestimonialCard2 = props => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    ref: props.divRef,\n    sx: {\n      height: \"100%\",\n      width: \"100%\",\n      padding: 3,\n      borderRadius: 3,\n      boxShadow: \"0 4px 20px rgba(0, 0, 0, 0.1)\",\n      background: `${props.templateConfig.backgroundColor}`,\n      textAlign: \"center\",\n      margin: \"auto\"\n    },\n    children: [props.templateConfig.showRating && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        justifyContent: \"center\",\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(RatingsStar, {\n        starRating: props.templateConfig.starRating\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      sx: {\n        fontSize: \"0.9rem\",\n        mb: 3,\n        lineHeight: 1.6,\n        color: `${props.templateConfig.fontColor}`\n      },\n      children: props.templateConfig.comment\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), props.templateConfig.showAvatar && /*#__PURE__*/_jsxDEV(UserAvatar, {\n      profileImage: props.templateConfig.reviewerImage,\n      fullname: props.templateConfig.reviewerName,\n      style: {\n        width: 60,\n        height: 60,\n        margin: \"0 auto 10px\",\n        background: \"linear-gradient(45deg, #42A5F5, #64B5F6)\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      sx: {\n        fontWeight: 600,\n        fontSize: \"1rem\",\n        color: `${props.templateConfig.fontColor}`\n      },\n      children: props.templateConfig.reviewerName\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_c = TestimonialCard2;\nexport default TestimonialCard2;\nvar _c;\n$RefreshReg$(_c, \"TestimonialCard2\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "UserAvatar", "RatingsStar", "jsxDEV", "_jsxDEV", "TestimonialCard2", "props", "ref", "divRef", "sx", "height", "width", "padding", "borderRadius", "boxShadow", "background", "templateConfig", "backgroundColor", "textAlign", "margin", "children", "showRating", "display", "justifyContent", "mb", "starRating", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "lineHeight", "color", "fontColor", "comment", "showAvatar", "profileImage", "reviewerImage", "fullname", "reviewerName", "style", "fontWeight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/createPost/createPostTemplates/cards/testimonialCard2/testimonialCard2.component.tsx"], "sourcesContent": ["import React from \"react\";\nimport { Box, Typography, Avatar } from \"@mui/material\";\nimport { IPostTemplateConfig } from \"../../../../../types/IPostTemplateConfig\";\nimport { ref } from \"yup\";\nimport StarIcon from \"@mui/icons-material/Star\";\nimport UserAvatar from \"../../../../userAvatar/userAvatar.component\";\nimport RatingsStar from \"../../../../ratingsStar/ratingsStar.component\";\n\nconst TestimonialCard2 = (props: {\n  templateConfig: IPostTemplateConfig;\n  divRef: any;\n}) => {\n  return (\n    <Box\n      ref={props.divRef}\n      sx={{\n        height: \"100%\",\n        width: \"100%\",\n        padding: 3,\n        borderRadius: 3,\n        boxShadow: \"0 4px 20px rgba(0, 0, 0, 0.1)\",\n        background: `${props.templateConfig.backgroundColor}`,\n        textAlign: \"center\",\n        margin: \"auto\",\n      }}\n    >\n      {/* Stars */}\n\n      {props.templateConfig.showRating && (\n        <Box\n          sx={{\n            display: \"flex\",\n            justifyContent: \"center\",\n            mb: 2,\n          }}\n        >\n          <RatingsStar starRating={props.templateConfig.starRating} />\n        </Box>\n      )}\n\n      {/* Testimonial Text */}\n      <Typography\n        sx={{\n          fontSize: \"0.9rem\",\n          mb: 3,\n          lineHeight: 1.6,\n          color: `${props.templateConfig.fontColor}`,\n        }}\n      >\n        {props.templateConfig.comment}\n      </Typography>\n\n      {/* Avatar */}\n      {props.templateConfig.showAvatar && (\n        <UserAvatar\n          profileImage={props.templateConfig.reviewerImage}\n          fullname={props.templateConfig.reviewerName}\n          style={{\n            width: 60,\n            height: 60,\n            margin: \"0 auto 10px\",\n            background: \"linear-gradient(45deg, #42A5F5, #64B5F6)\",\n          }}\n        />\n      )}\n\n      {/* Name and Designation */}\n      <Typography\n        sx={{\n          fontWeight: 600,\n          fontSize: \"1rem\",\n          color: `${props.templateConfig.fontColor}`,\n        }}\n      >\n        {props.templateConfig.reviewerName}\n      </Typography>\n    </Box>\n  );\n};\n\nexport default TestimonialCard2;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,QAAgB,eAAe;AAIvD,OAAOC,UAAU,MAAM,6CAA6C;AACpE,OAAOC,WAAW,MAAM,+CAA+C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExE,MAAMC,gBAAgB,GAAIC,KAGzB,IAAK;EACJ,oBACEF,OAAA,CAACL,GAAG;IACFQ,GAAG,EAAED,KAAK,CAACE,MAAO;IAClBC,EAAE,EAAE;MACFC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,+BAA+B;MAC1CC,UAAU,EAAE,GAAGT,KAAK,CAACU,cAAc,CAACC,eAAe,EAAE;MACrDC,SAAS,EAAE,QAAQ;MACnBC,MAAM,EAAE;IACV,CAAE;IAAAC,QAAA,GAIDd,KAAK,CAACU,cAAc,CAACK,UAAU,iBAC9BjB,OAAA,CAACL,GAAG;MACFU,EAAE,EAAE;QACFa,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,EAAE,EAAE;MACN,CAAE;MAAAJ,QAAA,eAEFhB,OAAA,CAACF,WAAW;QAACuB,UAAU,EAAEnB,KAAK,CAACU,cAAc,CAACS;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CACN,eAGDzB,OAAA,CAACJ,UAAU;MACTS,EAAE,EAAE;QACFqB,QAAQ,EAAE,QAAQ;QAClBN,EAAE,EAAE,CAAC;QACLO,UAAU,EAAE,GAAG;QACfC,KAAK,EAAE,GAAG1B,KAAK,CAACU,cAAc,CAACiB,SAAS;MAC1C,CAAE;MAAAb,QAAA,EAEDd,KAAK,CAACU,cAAc,CAACkB;IAAO;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,EAGZvB,KAAK,CAACU,cAAc,CAACmB,UAAU,iBAC9B/B,OAAA,CAACH,UAAU;MACTmC,YAAY,EAAE9B,KAAK,CAACU,cAAc,CAACqB,aAAc;MACjDC,QAAQ,EAAEhC,KAAK,CAACU,cAAc,CAACuB,YAAa;MAC5CC,KAAK,EAAE;QACL7B,KAAK,EAAE,EAAE;QACTD,MAAM,EAAE,EAAE;QACVS,MAAM,EAAE,aAAa;QACrBJ,UAAU,EAAE;MACd;IAAE;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,eAGDzB,OAAA,CAACJ,UAAU;MACTS,EAAE,EAAE;QACFgC,UAAU,EAAE,GAAG;QACfX,QAAQ,EAAE,MAAM;QAChBE,KAAK,EAAE,GAAG1B,KAAK,CAACU,cAAc,CAACiB,SAAS;MAC1C,CAAE;MAAAb,QAAA,EAEDd,KAAK,CAACU,cAAc,CAACuB;IAAY;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACa,EAAA,GAtEIrC,gBAAgB;AAwEtB,eAAeA,gBAAgB;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}