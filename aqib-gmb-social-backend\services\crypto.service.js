const crypto = require('crypto-js');
const config = require('rc')('lambda', {
    encryption: {
        authentication: 'aqib-social-tool-2024-secretkey'
    }
});

module.exports = {
    encrypt: async (input, passwordKey) => {
        const result = crypto.AES.encrypt(input, config.encryption[passwordKey]);
        return result.toString();
    },
    decrypt: async (input, passwordKey) => {
        const result = crypto.AES.decrypt(input, config.encryption[passwordKey]);
        return result.toString(crypto.enc.Utf8);
    }
} 