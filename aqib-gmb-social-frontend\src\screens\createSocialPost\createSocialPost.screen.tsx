import {
  FunctionComponent,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import PageProps from "../../models/PageProps.interface";

//Widgets
import Link from "@mui/material/Link";
import React from "react";
import {
  <PERSON>,
  Typography,
  Tabs,
  Tab,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Card,
  CardContent,
  Paper,
  FormControl,
  Tooltip,
  LinearProgress,
  CircularProgress,
  Drawer,
} from "@mui/material";

//Css Import
import "../signIn/signIn.screen.style.css";

import * as yup from "yup";
import { Formik, FormikErrors, FormikTouched } from "formik";
import { ILoginModel } from "../../interfaces/request/ILoginModel";
import { useDispatch, useSelector } from "react-redux";
import { authInitiate } from "../../actions/auth.actions";
import SyncOutlinedIcon from "@mui/icons-material/SyncOutlined";
import CheckCircleOutlineOutlinedIcon from "@mui/icons-material/CheckCircleOutlineOutlined";
import {
  LocalizationProvider,
  MobileDateTimePicker,
} from "@mui/x-date-pickers";
// import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs"; // Day.js library
import InfoCard from "./components/InfoCard.screen";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import ImageOutlinedIcon from "@mui/icons-material/ImageOutlined";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import SubmitPost, {
  IModalWithSelect,
  ISelectionLocationWithPost,
} from "./components/submitPost.component";
import { IGoogleCreatePost } from "../../interfaces/request/IGoogleCreatePost";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import { EVENT_TYPES, TOPIC_TYPES } from "../../constants/application.constant";
import { CalendarToday } from "@mui/icons-material";
import {
  IconButton,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import CallIcon from "@mui/icons-material/Call";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import ShoppingCartCheckoutIcon from "@mui/icons-material/ShoppingCartCheckout";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import SchoolIcon from "@mui/icons-material/School";
import { Campaign, Web } from "@mui/icons-material";
import { getIn } from "formik";
import utc from "dayjs/plugin/utc";
import { CardMedia } from "@mui/material";
import { ArrowBackIos, ArrowForwardIos } from "@mui/icons-material";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import PostsService from "../../services/posts/posts.service";
import { IFileUploadResponseModel } from "../../interfaces/response/IFileUploadResponseModel";
import { styled } from "@mui/system";
import VisibilityIcon from "@mui/icons-material/Visibility";
import BlockIcon from "@mui/icons-material/Block";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import CancelIcon from "@mui/icons-material/Cancel";
import { useLocation, useNavigate } from "react-router-dom";
import { useSearchParams } from "react-router-dom";
import LocalActivityIcon from "@mui/icons-material/LocalActivity";
import ThumbUpAltIcon from "@mui/icons-material/ThumbUpAlt";
import LinkIcon from "@mui/icons-material/Link";
import ScheduleLater from "../../components/scheduleLater/scheduleLater.component";
import GenericDrawer from "../../components/genericDrawer/genericDrawer.component";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import { IExtendedPageProps } from "../../interfaces/IExtendedPageProps";

dayjs.extend(utc);

const DEBUG_MODE = !(process.env.NODE_ENV === "development");

const FormErrorDebugger = ({
  errors,
  touched,
}: {
  errors: any;
  touched: any;
}) => {
  if (!DEBUG_MODE) return null;

  const renderErrorMessage = (message: any): string => {
    if (typeof message === "string") {
      return message;
    } else if (typeof message === "object" && message !== null) {
      return JSON.stringify(message);
    }
    return String(message);
  };

  return (
    <Box
      sx={{
        position: "fixed",
        bottom: 0,
        right: 0,
        width: "300px",
        maxHeight: "300px",
        overflowY: "auto",
        backgroundColor: "rgba(255, 0, 0, 0.1)",
        padding: 2,
        zIndex: 9999,
        border: "1px solid red",
      }}
    >
      <Typography variant="subtitle2" fontWeight="bold">
        Form Errors:
      </Typography>
      {Object.keys(errors).length === 0 ? (
        <Typography variant="body2">No errors</Typography>
      ) : (
        Object.entries(errors).map(([field, message]) => (
          <Box key={field} sx={{ mb: 1 }}>
            <Typography variant="caption" fontWeight="bold">
              {field}:
            </Typography>
            <Typography variant="caption" display="block" color="error">
              {renderErrorMessage(message)}
              {touched[field] ? " (touched)" : " (not touched)"}
            </Typography>
          </Box>
        ))
      )}
    </Box>
  );
};

type PostCreationProgressIndicator = {
  percent: number;
  status: string;
};

const CreateSocialPost: FunctionComponent<IExtendedPageProps> = ({
  title,
  createPost,
}) => {
  const EventTypes = [
    {
      label: "Call",
      variant: "outlined",
      color: "primary",
      icon: "CallIcon",
      key: EVENT_TYPES.Call,
    },
    {
      label: "Book Now",
      variant: "outlined",
      color: "primary",
      icon: "CalendarMonthIcon",
      key: EVENT_TYPES.Book,
    },
    {
      label: "Order",
      variant: "outlined",
      color: "primary",
      icon: "ShoppingCartIcon",
      key: EVENT_TYPES.Order,
    },
    {
      label: "Shop",
      variant: "outlined",
      color: "primary",
      icon: "ShoppingCartIcon",
      key: EVENT_TYPES.Shop,
    },
    {
      label: "Sign Up",
      variant: "outlined",
      color: "primary",
      icon: "PersonAddIcon",
      key: EVENT_TYPES.SignUp,
    },
    {
      label: "Learn More",
      variant: "outlined",
      color: "primary",
      icon: "SchoolIcon",
      key: EVENT_TYPES.LearnMore,
    },
  ];

  const iconMap: { [key: string]: JSX.Element } = {
    CallIcon: <CallIcon />,
    CalendarMonthIcon: <CalendarMonthIcon />,
    ShoppingCartIcon: <ShoppingCartIcon />,
    ShoppingCartCheckoutIcon: <ShoppingCartCheckoutIcon />,
    PersonAddIcon: <PersonAddIcon />,
    SchoolIcon: <SchoolIcon />,
  };
  const navigate = useNavigate();
  const location = useLocation();
  const formikSchedulerRef = useRef(null);
  const fileInputRef = useRef(null);
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const [tabValue, setTabValue] = useState(1);
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  const { setToastConfig } = useContext(ToastContext);
  const [date, setDate] = React.useState(dayjs());
  const [scheduleForLater, setScheduleForLater] = useState<boolean>(false);
  const handleClosePost = () => setShowLocationSelection({ isShow: false });
  const [showLocationSelection, setShowLocationSelection] =
    useState<IModalWithSelect>({
      isShow: false,
    });

  const [uploadedImages, setUploadedImages] = useState<unknown[]>([]);
  const [showCreatePostStatus, setShowCreatePostStatus] =
    useState<boolean>(false);
  const [selectedLocations, setSelectedLocations] = useState<
    ISelectionLocationWithPost[]
  >([]);
  const _postsService = new PostsService(dispatch);

  // Generate a unique bulk post ID
  const generateBulkPostId = () => {
    return "bulk_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9);
  };
  const MIN_ALLOWED_CHARS = 1;
  const MAX_ALLOWED_CHARS = 1500;

  const [postCreationProgress, setPostCreationProgress] =
    useState<PostCreationProgressIndicator>({
      percent: 30,
      status: "",
    });

  const topic = searchParams.get("topic");
  const domainRegex =
    /^(https?:\/\/)?((([a-z\d]([a-z\d-]*[a-z\d])*)\.)+[a-z]{2,})(:\d+)?(\/[-a-z\d%_.~+]*)*(\?[;&a-z\d%_.~+=-]*)?(#[-a-z\d_]*)?$/i;

  const iconTextInputStyle = {
    paddingLeft: "14px", // Font size
  };

  const CREATE_POST_INITIAL_POST: IGoogleCreatePost = {
    languageCode: "en-US",
    summary: "",
    event: {
      title: "",
      schedule: {
        startTime: "",
        endTime: "",
      },
    },
    offer: null,
    media: [],
    topicType:
      topic &&
      [
        TOPIC_TYPES.Offer,
        TOPIC_TYPES.WhatsNew,
        TOPIC_TYPES.Event,
        // TOPIC_TYPES.Informative,
        // TOPIC_TYPES.Standard,
      ].includes(topic.toUpperCase())
        ? topic
        : "EVENT",
    callToAction: {
      actionType: EVENT_TYPES.Call,
      url: "",
    },
  };

  const [createPostInitials, setCreatePostInitials] =
    useState<IGoogleCreatePost>(CREATE_POST_INITIAL_POST);

  useEffect(() => {
    if (location.state && location.state.createPost) {
      setCreatePostInitials(location.state.createPost);
    }
    document.title = title;
  }, []);

  const checkFormValidity = async () => {
    if (formikSchedulerRef.current) {
      var isValid = await (formikSchedulerRef.current as any).validateForm();
      // (formikSchedulerRef.current as  any).validateForm().then((errors: any) => {
      //   if (Object.keys(errors).length === 0) {
      //     console.log("Form is valid!");
      //   } else {
      //     console.log("Form has errors:", errors);
      //   }
      // });
    }
  };

  type EventType = (typeof EVENT_TYPES)[keyof typeof EVENT_TYPES];

  const CreatePostSchema = yup.object().shape({
    event: yup
      .object()
      .nullable()
      .when("$topicType", (topicType, schema) => {
        if (
          topicType &&
          (topicType[0] === TOPIC_TYPES.Event ||
            topicType[0] === TOPIC_TYPES.Offer)
        ) {
          return schema.nonNullable().required("Event is required");
        }
        return schema; // Keep it nullable for other types
      })
      .shape({
        title: yup
          .string()
          .nullable()
          .transform((value) => (value === "" ? null : value))
          .when("$topicType", (topicType, schema) => {
            if (
              (topicType && topicType[0] === TOPIC_TYPES.Event) ||
              topicType[0] === TOPIC_TYPES.Offer
            ) {
              return schema.nonNullable().required("Title is required");
            }
            return schema; // Keep it nullable for other types
          })
          .test({
            name: "mandatory-check-when-event",
            message: "This field is required",
            test: function (value) {
              const { from } = this;
              const objectValues = from as any;
              const values = objectValues[objectValues.length - 1]
                .value as IGoogleCreatePost;

              // Validate only if topicType is "Event"
              if (
                values.topicType === TOPIC_TYPES.Event ||
                values.topicType === TOPIC_TYPES.Offer
              ) {
                return Boolean(value && value.trim().length > 0);
              }
              return true; // Skip validation for other types
            },
          })
          .required("Title is required"), // Required check
        schedule: yup.object().shape({
          startTime: yup
            .string()
            .nullable()
            .when("$topicType", (topicType, schema) => {
              if (
                topicType &&
                (topicType[0] === TOPIC_TYPES.Event ||
                  topicType[0] === TOPIC_TYPES.Offer)
              ) {
                return schema.nonNullable().required("Start Time is required");
              }
              return schema; // Keep it nullable for other types
            })
            .test({
              name: "mandatory-check-start-date",
              message: "StartDate is required",
              test: function (value) {
                const { from } = this;
                const objectValues = from as any;
                const values = objectValues[objectValues.length - 1]
                  .value as IGoogleCreatePost;

                // Validate only if topicType is "Event"
                if (
                  values.topicType === TOPIC_TYPES.Event ||
                  values.topicType === TOPIC_TYPES.Offer
                ) {
                  return Boolean(value && value.trim().length > 0);
                }
                return true; // Skip validation for other types
              },
            }),
          endTime: yup
            .string()
            .nullable()
            .when("$topicType", (topicType, schema) => {
              if (
                topicType &&
                (topicType[0] === TOPIC_TYPES.Event ||
                  topicType[0] === TOPIC_TYPES.Offer)
              ) {
                return schema.nonNullable().required("End Time is required");
              }
              return schema; // Keep it nullable for other types
            })
            .test({
              name: "mandatory-check-end-date",
              message: "EndDate is required",
              test: function (value) {
                const { from } = this;
                const objectValues = from as any;
                const values = objectValues[objectValues.length - 1]
                  .value as IGoogleCreatePost;

                // Validate only if topicType is "Event or Offer"
                if (
                  values.topicType === TOPIC_TYPES.Event ||
                  values.topicType === TOPIC_TYPES.Offer
                ) {
                  return Boolean(value && value.trim().length > 0);
                }
                return true; // Skip validation for other types
              },
            }),
        }),
      }),
    offer: yup
      .object()
      .nullable()
      .when("$topicType", (topicType, schema) => {
        if (topicType && topicType[0] === TOPIC_TYPES.Offer) {
          return schema.nonNullable().required("Offer is required");
        }
        return schema; // Keep it nullable for other types
      })
      .shape({
        couponCode: yup
          .string()
          .nullable()
          .when("$topicType", (topicType, schema) => {
            if (topicType && topicType[0] === TOPIC_TYPES.Offer) {
              return schema.nonNullable().required("Coupon Code is required");
            }
            return schema; // Keep it nullable for other types
          }), // Required check
        redeemOnlineUrl: yup
          .string()
          .nullable()
          .when("$topicType", (topicType, schema) => {
            if (topicType && topicType[0] === TOPIC_TYPES.Offer) {
              return schema
                .nonNullable()
                .required("Online Redeem Url is required");
            }
            return schema; // Keep it nullable for other types
          }), // Required check
        termsConditions: yup
          .string()
          .nullable()
          .when("$topicType", (topicType, schema) => {
            if (topicType && topicType[0] === TOPIC_TYPES.Offer) {
              return schema
                .nonNullable()
                .required("Terms & Conditions is required");
            }
            return schema; // Keep it nullable for other types
          }), // Required check
      }),
    summary: yup
      .string()
      .required("Summary is required")
      .test(
        "len",
        `Should me maximum of ${MAX_ALLOWED_CHARS} characters`,
        (val) => val.length <= MAX_ALLOWED_CHARS
      ),
    media: yup
      .array()
      .min(1, "At least one image is required")
      .test("fileSize", "Each file must be less than 5MB", (files) =>
        files ? files.every((file) => file.size <= 5 * 1024 * 1024) : true
      )
      .test("fileFormat", "Only JPG and PNG are allowed", (files) =>
        files
          ? files.every((file) =>
              ["image/jpeg", "image/png"].includes(file.type)
            )
          : true
      ),
    callToAction: yup
      .object()
      .nullable()
      .when("$topicType", (topicType, schema) => {
        if (topicType && topicType[0] === TOPIC_TYPES.Event) {
          return schema.nonNullable().required("Event is required for Event");
        }
        return schema; // Keep it nullable for other types
      })
      .shape({
        actionType: yup
          .string()
          .nullable()
          .when("$callToAction.actionType", (actionType, schema) => {
            if (
              actionType &&
              Object.values(EVENT_TYPES).includes(actionType[0] as EventType)
            ) {
              return schema.nonNullable().required("Action is required");
            }
            return schema; // Keep it nullable for other types
          }),
        url: yup
          .string()
          .nullable()
          .when("$callToAction.actionType", (actionType, schema) => {
            if (
              actionType &&
              actionType[0] &&
              actionType[0] !== EVENT_TYPES.Call
            ) {
              return schema
                .nonNullable()
                .matches(domainRegex, "Invalid domain format")
                .required("Url is required");
            }
            return schema; // Keep it nullable for other types
          }),
      }),
  });

  const _handlePostSubmission = async (
    values: IGoogleCreatePost,
    formikHelpers: any
  ) => {
    if (values.topicType === TOPIC_TYPES.Event) {
      formikHelpers.setTouched({
        event: {
          title: true, // Manually mark nested field as touched
          schedule: {
            startTime: true,
            endTime: true,
          },
        },
        summary: true,
      });
    } else if (values.topicType === TOPIC_TYPES.Offer) {
      formikHelpers.setTouched({
        event: {
          title: true, // Manually mark nested field as touched
          schedule: {
            startTime: true,
            endTime: true,
          },
        },
        offer: {
          couponCode: true,
          redeemOnlineUrl: true,
          termsConditions: true,
        },
        summary: true,
      });
    } else {
      formikHelpers.setTouched({
        summary: true,
      });
    }

    try {
      var response = await CreatePostSchema.validate(values, {
        abortEarly: false,
      }); // Validate form
      setShowLocationSelection({
        isShow: true,
        createPostModel: {
          googleRequest: values,
          schedule: null,
          images: uploadedImages,
        },
      });
      console.log("Form Submitted Successfully!", values);
    } catch (errors: any) {
      if (errors.inner) {
        console.log("Validation Errors:", errors.inner);
        errors.inner.forEach((error: any) => {
          console.log(`Field: ${error.path}, Message: ${error.message}`);
        });
      } else {
        console.log("Unexpected Error:", errors);
      }
    }
  };

  const handleClick = () => {
    if (fileInputRef && fileInputRef.current) {
      (fileInputRef.current as any).click(); // Trigger file input click
    }
  };

  const PostTypeTabs = (props: { value: any; onChange: any }) => (
    <Tabs
      className="whiteBg"
      value={props.value}
      onChange={props.onChange}
      sx={{ borderBottom: "1px solid #ddd" }}
    >
      <Tab
        label={
          <Box sx={{ gap: "8px" }}>
            <Box sx={{ display: "flex", alignItems: "center", gap: "50px" }}>
              <span>Google</span>
              <Button
                disabled
                variant="outlined"
                startIcon={
                  <CheckCircleOutlineOutlinedIcon color="success"></CheckCircleOutlineOutlinedIcon>
                }
                onClick={(e) => {
                  e.stopPropagation();
                }}
                sx={{ border: 0 }}
              ></Button>
            </Box>
            <Box sx={{ display: "flex", alignItems: "flex-start" }}>
              <Typography sx={{ fontSize: 10 }}>0 Locations</Typography>
            </Box>
          </Box>
        }
        value={1}
      />
      <Tab
        label={
          <Box sx={{ gap: "8px" }}>
            <Box sx={{ display: "flex", alignItems: "center", gap: "50px" }}>
              <span>Facebook</span>
              <Button
                variant="outlined"
                startIcon={<SyncOutlinedIcon color="error"></SyncOutlinedIcon>}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              ></Button>
            </Box>
            <Box sx={{ display: "flex", alignItems: "flex-start" }}>
              <Typography sx={{ fontSize: 10 }}>Connect Account</Typography>
            </Box>
          </Box>
        }
        value={2}
        disabled
      />
      <Tab
        label={
          <Box sx={{ gap: "8px" }}>
            <Box sx={{ display: "flex", alignItems: "center", gap: "50px" }}>
              <span>Instagram</span>
              <Button
                variant="outlined"
                startIcon={<SyncOutlinedIcon color="error"></SyncOutlinedIcon>}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              ></Button>
            </Box>
            <Box sx={{ display: "flex", alignItems: "flex-start" }}>
              <Typography sx={{ fontSize: 10 }}>Connect Account</Typography>
            </Box>
          </Box>
        }
        value={3}
        disabled
      />
    </Tabs>
  );

  const handleFileChange = (event: any) => {
    setUploadedImages([]);
    const files = Array.from(event.target.files);
    if (files.length > 5) {
      setToastConfig(
        ToastSeverity.Error,
        `You can only upload a maximum of 5 images.`,
        true
      );
      return;
    }

    const validFiles = files.filter((file: any) => {
      if (!["image/jpeg", "image/png"].includes(file.type)) {
        setToastConfig(
          ToastSeverity.Error,
          `Invalid file type: ${file.name}. Only JPG and PNG are allowed.`,
          true
        );
        return false;
      }
      if (file.size < 10240) {
        setToastConfig(
          ToastSeverity.Error,
          `File "${file.name}" is too small. Minimum size is 10KB.`,
          true
        );
        return false;
      }
      return true;
    });

    setUploadedImages(validFiles);
  };

  const EventDateTimePicker = (props: {
    setFieldValue: any;
    values: IGoogleCreatePost;
    errors: FormikErrors<IGoogleCreatePost>;
    touched: FormikTouched<IGoogleCreatePost>;
    setFieldTouched: any;
    showtitle: boolean;
  }) => {
    const formatDayJsToISO = (date: Dayjs): string => {
      return date.utc().format("YYYY-MM-DDTHH:mm:ss[Z]");
    };
    const {
      setFieldValue,
      values,
      errors,
      touched,
      setFieldTouched,
      showtitle,
    } = props;
    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Box sx={{ width: "100%", margin: "auto" }}>
          {showtitle && (
            <Typography variant="h6" sx={{ mb: 1 }}>
              Event Start & End Date - Time
            </Typography>
          )}

          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 2,
              bgcolor: "#ffffff",
              borderRadius: 2,
              p: 2,
              justifyContent: "space-evenly",
            }}
          >
            <FormControl fullWidth>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                {/* Start Date */}
                <MobileDateTimePicker
                  value={
                    values.event &&
                    values.event.schedule &&
                    values.event.schedule.startTime
                      ? dayjs(values.event.schedule.startTime)
                      : null
                  }
                  onChange={(newValue) => {
                    if (newValue != null) {
                      setFieldValue(
                        "event.schedule.startTime",
                        formatDayJsToISO(newValue)
                      );
                    }
                  }}
                  onClose={() =>
                    setFieldTouched("event.schedule.startTime", true)
                  }
                  minDate={dayjs()}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      error: Boolean(
                        getIn(errors, "event.schedule.startTime") &&
                          getIn(touched, "event.schedule.startTime")
                      ),
                      helperText:
                        getIn(errors, "event.schedule.startTime") &&
                        getIn(touched, "event.schedule.startTime")
                          ? getIn(errors, "event.schedule.startTime")
                          : "",
                      InputProps: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <CalendarTodayIcon />
                          </InputAdornment>
                        ),
                      },
                    },
                  }}
                  sx={{ width: "100%" }}
                  label={"Start Date"}
                />
              </Box>
            </FormControl>
            <Typography>-</Typography>
            <FormControl fullWidth>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                {/* End Date */}
                <MobileDateTimePicker
                  disabled={Boolean(
                    !(
                      values.event &&
                      values.event.schedule &&
                      values.event.schedule.startTime
                    )
                  )}
                  minDate={
                    values.event &&
                    values.event.schedule &&
                    values.event.schedule.startTime != null
                      ? dayjs(values.event.schedule.startTime).add(1, "day")
                      : undefined
                  }
                  value={
                    values.event &&
                    values.event?.schedule &&
                    values.event?.schedule?.endTime
                      ? dayjs(values.event.schedule.endTime)
                      : null
                  }
                  onChange={(newValue) => {
                    if (newValue != null) {
                      setFieldValue(
                        "event.schedule.endTime",
                        formatDayJsToISO(newValue)
                      );
                    }
                  }}
                  onClose={() =>
                    setFieldTouched("event.schedule.endTime", true)
                  }
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      error: Boolean(
                        getIn(errors, "event.schedule.endTime") &&
                          getIn(touched, "event.schedule.endTime")
                      ),
                      helperText:
                        getIn(errors, "event.schedule.endTime") &&
                        getIn(touched, "event.schedule.endTime")
                          ? getIn(errors, "event.schedule.endTime")
                          : "",
                      InputProps: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <CalendarTodayIcon />
                          </InputAdornment>
                        ),
                      },
                    },
                  }}
                  sx={{ width: "100%" }}
                  label={"End Date"}
                />
              </Box>
            </FormControl>
          </Box>
        </Box>
      </LocalizationProvider>
    );
  };

  const OfferDateTimePickerAndFields = (props: {
    setFieldValue: any;
    values: IGoogleCreatePost;
    errors: FormikErrors<IGoogleCreatePost>;
    touched: FormikTouched<IGoogleCreatePost>;
    setFieldTouched: any;
  }) => {
    const formatDayJsToISO = (date: Dayjs): string => {
      return date.utc().format("YYYY-MM-DDTHH:mm:ss[Z]");
    };
    const { setFieldValue, values, errors, touched, setFieldTouched } = props;

    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Box sx={{ width: "100%", margin: "auto" }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            Offer Start & End Date - Time
          </Typography>

          <EventDateTimePicker
            setFieldValue={setFieldValue}
            errors={errors}
            touched={touched}
            values={values}
            setFieldTouched={setFieldTouched}
            showtitle={false}
          />
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 2,
              bgcolor: "#ffffff",
              p: 2,
              justifyContent: "space-evenly",
            }}
          >
            <FormControl fullWidth>
              <TextField
                fullWidth
                label="Coupon Code"
                placeholder="Enter Coupon Code (Optional)"
                variant="outlined"
                value={values.offer ? values.offer.couponCode : ""}
                sx={{ "& input": iconTextInputStyle }}
                onChange={(e) => {
                  setFieldValue(
                    "offer.couponCode",
                    e.target.value.toUpperCase()
                  );
                }}
                slotProps={{
                  input: {
                    startAdornment: <LocalActivityIcon />,
                  },
                }}
                error={Boolean(
                  getIn(errors, "offer.couponCode") &&
                    getIn(touched, "offer.couponCode")
                )}
                helperText={
                  getIn(errors, "offer.couponCode") &&
                  getIn(touched, "offer.couponCode")
                    ? getIn(errors, "offer.couponCode")
                    : ""
                }
              />
            </FormControl>
          </Box>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 2,
              bgcolor: "#ffffff",
              p: 2,
              justifyContent: "space-evenly",
            }}
          >
            <FormControl fullWidth>
              <TextField
                fullWidth
                label="Redeem Link"
                placeholder="Enter link to redeem coupon (Optional)"
                variant="outlined"
                value={values.offer ? values.offer.redeemOnlineUrl : ""}
                sx={{
                  "& input": iconTextInputStyle,
                }}
                onChange={(e) => {
                  setFieldValue("offer.redeemOnlineUrl", e.target.value);
                }}
                slotProps={{
                  input: {
                    startAdornment: <LinkIcon />,
                  },
                }}
                error={Boolean(
                  getIn(errors, "offer.redeemOnlineUrl") &&
                    getIn(touched, "offer.redeemOnlineUrl")
                )}
                helperText={
                  getIn(errors, "offer.redeemOnlineUrl") &&
                  getIn(touched, "offer.redeemOnlineUrl")
                    ? getIn(errors, "offer.redeemOnlineUrl")
                    : ""
                }
              />
            </FormControl>
          </Box>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 2,
              bgcolor: "#ffffff",
              p: 2,
              justifyContent: "space-evenly",
            }}
          >
            <FormControl fullWidth>
              <TextField
                fullWidth
                label="Terms & Conditions"
                placeholder="Terms & Conditions (optional)"
                variant="outlined"
                value={values.offer ? values.offer.termsConditions : ""}
                sx={{ "& input": iconTextInputStyle }}
                onChange={(e) => {
                  setFieldValue("offer.termsConditions", e.target.value);
                }}
                slotProps={{
                  input: {
                    startAdornment: <ThumbUpAltIcon />,
                  },
                }}
                error={Boolean(
                  getIn(errors, "offer.termsConditions") &&
                    getIn(touched, "offer.termsConditions")
                )}
                helperText={
                  getIn(errors, "offer.termsConditions") &&
                  getIn(touched, "offer.termsConditions")
                    ? getIn(errors, "offer.termsConditions")
                    : ""
                }
              />
            </FormControl>
          </Box>
        </Box>
      </LocalizationProvider>
    );
  };

  const PostForm = (props: {
    setFieldValue: any;
    values: IGoogleCreatePost;
    errors: FormikErrors<IGoogleCreatePost>;
    touched: FormikTouched<IGoogleCreatePost>;
    setFieldTouched: any;
    setErrors: any;
    setTouched: any;
  }) => {
    const {
      setFieldValue,
      values,
      errors,
      touched,
      setFieldTouched,
      setErrors,
      setTouched,
    } = props;
    const [utmCampaign, setUtmCampaign] = useState("");
    const [utmSource, setUtmSource] = useState("");
    const [utmMedium, setUtmMedium] = useState("");
    const [utmUrl, setUtmUrl] = useState("");
    const [showUtmparams, setShowUtmparams] = useState(false);
    const TopicTypes = [
      { key: TOPIC_TYPES.Offer, Value: "Offer" },
      { key: TOPIC_TYPES.WhatsNew, Value: "What's New" },
      { key: TOPIC_TYPES.Event, Value: "Event" },
      // { key: TOPIC_TYPES.Informative, Value: "Informative" },
      // { key: TOPIC_TYPES.Standard, Value: "Standard" },
    ];
    const textareaRef = useRef<HTMLDivElement | null>(null);

    // useEffect(() => {
    //   const topic = searchParams.get("topic");
    //   if (topic) {
    //     handleTopicTypeChange(topic);
    //   }
    // }, []);

    const generateUTMUrl = () => {
      if (values.callToAction == null || !values.callToAction.url.trim()) {
        setToastConfig(
          ToastSeverity.Error,
          `Please enter a valid Website URL.`,
          true
        );
        return;
      }

      if (values.callToAction) {
        const utmGenerated = `${values.callToAction.url}?utm_source=${utmSource}&utm_medium=${utmMedium}&utm_campaign=${utmCampaign}`;
        setUtmUrl(utmGenerated);
        setFieldValue("callToAction.url", utmGenerated);
      }
    };

    const handleTopicTypeChange = (value: string) => {
      setFieldValue("topicType", value);
      setFieldValue("event", null);
      setFieldValue("offer", null);

      if (value === TOPIC_TYPES.Offer) {
        setFieldValue("offer", {
          couponCode: "BOGO-JET-CODE",
          redeemOnlineUrl: "https://www.google.com/redeem",
          termsConditions:
            "Offer only valid if you can prove you are a time traveler",
        });
        setFieldValue("event", {
          title: null,
          schedule: {
            startTime: "",
            endTime: "",
          },
        });
      }

      if (value === TOPIC_TYPES.Event) {
        setFieldValue("event", {
          title: "",
          schedule: {
            startTime: "",
            endTime: "",
          },
        });
      }

      setErrors({});
      setTouched({});
    };

    const handleEventTypeChange = (value: string) => {
      setFieldValue("callToAction", {
        actionType: value,
        url: "",
      });
    };

    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle1">Select Post Type</Typography>
        <Box sx={{ display: "flex", gap: "8px", my: 2 }}>
          {TopicTypes.map((event) => (
            <Button
              variant={
                values.topicType.toUpperCase() === event.key
                  ? "contained"
                  : "outlined"
              }
              onClick={() => handleTopicTypeChange(event.key)}
            >
              {event.Value}
            </Button>
          ))}
        </Box>
        {(values.topicType === TOPIC_TYPES.Event ||
          values.topicType === TOPIC_TYPES.Offer) && (
          <Box className="commonFormPart">
            <FormControl fullWidth>
              <TextField
                id="event.title"
                label="What's New Title"
                variant="outlined"
                sx={{ mb: 2 }}
                value={values.event?.title || ""}
                onChange={(e) => setFieldValue("event.title", e.target.value)}
                error={Boolean(
                  getIn(errors, "event.title") && getIn(touched, "event.title")
                )}
                helperText={
                  getIn(errors, "event.title") && getIn(touched, "event.title")
                    ? getIn(errors, "event.title")
                    : ""
                }
              />
            </FormControl>
          </Box>
        )}

        <Box
          className=""
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: 2,
            mx: "auto",
            border: "1px solid #f4f4f4",
            borderRadius: 2,
            bgcolor: "#f4f4f4",
            marginBottom: "10px",
          }}
        >
          <Box className="commonFormPart">
            {/* Text Area */}
            {/* <TextField
              id="summary"
              inputRef={textareaRef}
              multiline
              rows={8}
              variant="outlined"
              placeholder="Write Details about What's New with your Business"
              value={values.summary}
              onChange={(e) => {
                setFieldValue("summary", e.target.value);
                if (textareaRef.current) {
                  textareaRef.current.scrollTop = textareaRef.current.scrollHeight;
                }
              }}
              fullWidth
              sx={{
                borderRadius: 1,
              }}
              error={
                values.summary.length > MAX_ALLOWED_CHARS ||
                values.summary.trim().length < MIN_ALLOWED_CHARS
              }
              helperText={
                values.summary.length > MAX_ALLOWED_CHARS
                  ? `Maximum characters exceeded. Please limit to ${MAX_ALLOWED_CHARS} characters.`
                  : values.summary.trim().length < MIN_ALLOWED_CHARS
                    ? "Summary is required."
                    : ""
              }
            /> */}
            <TextField
              id="summary"
              inputRef={textareaRef}
              multiline
              rows={8}
              variant="outlined"
              placeholder="Write Details about What's New with your Business"
              value={values.summary}
              onChange={(e) => {
                setFieldValue("summary", e.target.value);
                if (textareaRef.current) {
                  textareaRef.current.scrollTop =
                    textareaRef.current.scrollHeight;
                }
              }}
              fullWidth
              sx={{
                borderRadius: 1,
              }}
              error={
                values.summary.length > MAX_ALLOWED_CHARS ||
                (touched.summary &&
                  values.summary.trim().length < MIN_ALLOWED_CHARS)
              }
              helperText={
                values.summary.length > MAX_ALLOWED_CHARS
                  ? `Maximum characters exceeded. Please limit to ${MAX_ALLOWED_CHARS} characters.`
                  : touched.summary &&
                    values.summary.trim().length < MIN_ALLOWED_CHARS
                  ? "Summary is required."
                  : ""
              }
            />
            {/* Character Counter */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
              }}
            >
              {/* <Typography
                variant="body2"
                color={
                  values.summary.length < MIN_ALLOWED_CHARS ||
                  values.summary.length > MAX_ALLOWED_CHARS

                    ? "error"
                    : "textSecondary"
                }
              >
                {values.summary.length} / {MIN_ALLOWED_CHARS}
                {values.summary.length} / {MAX_ALLOWED_CHARS}
              </Typography> */}
              {/* <Typography
                variant="body2"
                color={
                  values.summary.length > MAX_ALLOWED_CHARS ||
                    values.summary.trim().length < MIN_ALLOWED_CHARS
                    ? "error"
                    : "textSecondary"
                }
              >
                {values.summary.length} / {MAX_ALLOWED_CHARS}
              </Typography> */}
              <Typography
                variant="body2"
                color={
                  values.summary.length > MAX_ALLOWED_CHARS ||
                  (touched.summary &&
                    values.summary.trim().length < MIN_ALLOWED_CHARS)
                    ? "error"
                    : "textSecondary"
                }
              >
                {values.summary.length} / {MAX_ALLOWED_CHARS}
              </Typography>
            </Box>
          </Box>

          {/* Action Buttons */}
          <Box
            sx={{
              display: "flex",
              gap: 1,
              flexWrap: "wrap",
            }}
          >
            {[
              {
                title: "Address",
                toolTip: "Address includes the busniness name & Address",
                value: "{{Address}}",
              },
              // { title: "State", toolTip: "State", value: "{{State}}" },
              { title: "Area", toolTip: "Area", value: "{{Area}}" },
              { title: "Pincode", toolTip: "Pincode", value: "{{Pincode}}" },
            ].map((label, index) => (
              <Tooltip title={label.toolTip}>
                <Button
                  key={index}
                  variant="outlined"
                  sx={{
                    textTransform: "none",
                    borderRadius: 2,
                    bgcolor: "#f8f8f8",
                  }}
                  onClick={() => {
                    setFieldValue(
                      "summary",
                      `${values.summary} ${label.value}`
                    );
                  }}
                >
                  {label.title}
                </Button>
              </Tooltip>
            ))}
          </Box>

          {values.topicType === TOPIC_TYPES.Event && (
            <EventDateTimePicker
              setFieldValue={setFieldValue}
              errors={errors}
              touched={touched}
              values={values}
              setFieldTouched={setFieldTouched}
              showtitle={true}
            />
          )}

          {values.topicType === TOPIC_TYPES.Offer && (
            <OfferDateTimePickerAndFields
              setFieldValue={setFieldValue}
              errors={errors}
              touched={touched}
              values={values}
              setFieldTouched={setFieldTouched}
            />
          )}
        </Box>

        <Paper
          elevation={2}
          sx={{
            height: 200,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            border:
              getIn(errors, "media") && getIn(touched, "media")
                ? "1px solid #d32f2f"
                : "2px dashed #cccccc",
            backgroundColor: "#ffffff",
            marginBottom: 2,
          }}
          onClick={handleClick}
        >
          <Typography variant="body1" color="textSecondary">
            Add/Edit Post Image
          </Typography>
          <Typography variant="caption" color="textSecondary">
            Maximum Size Photo – 35 MB. Format: JPG, PNG & Recommended Size -
            1200 X 900
          </Typography>
          <input
            type="file"
            ref={fileInputRef}
            accept="image/jpeg, image/png"
            style={{ display: "none" }}
            multiple
            onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
              if (event.currentTarget.files) {
                const filesArray = Array.from(event.currentTarget.files);
                handleFileChange(event);
                setFieldValue("media", filesArray);
              }
            }}
          />
          {getIn(errors, "media") && getIn(touched, "media") ? (
            <Typography color="error" variant="caption">
              {getIn(errors, "media")}
            </Typography>
          ) : (
            <></>
          )}
        </Paper>

        {/* Action Buttons */}
        <Box sx={{ display: "flex", gap: 1, marginBottom: 2 }}>
          {EventTypes.map((btn, index) => (
            <Button
              key={index}
              variant={
                values.callToAction && values.callToAction.actionType == btn.key
                  ? "contained"
                  : "outlined"
              }
              color={btn.color as "primary"}
              // startIcon={iconMap[btn.icon]}
              sx={{ textTransform: "none", borderRadius: 2 }}
              onClick={() => handleEventTypeChange(btn.key)}
            >
              {btn.label}
            </Button>
          ))}
        </Box>
        {/* Website Link */}
        {values.callToAction &&
          values.callToAction.actionType != EVENT_TYPES.Call && (
            <TextField
              fullWidth
              label="Website Link"
              placeholder="Provide Link to Website (https://domainname.com)"
              variant="outlined"
              value={values.callToAction ? values.callToAction.url : ""}
              sx={{ marginBottom: 2, "& input": iconTextInputStyle }}
              onChange={(e) => {
                setFieldValue("callToAction.url", e.target.value);
              }}
              slotProps={{
                input: {
                  startAdornment: <Web />,
                },
              }}
              error={Boolean(
                getIn(errors, "callToAction.url") &&
                  getIn(touched, "callToAction.url")
              )}
              helperText={
                getIn(errors, "callToAction.url") &&
                getIn(touched, "callToAction.url")
                  ? getIn(errors, "callToAction.url")
                  : ""
              }
            />
          )}

        {/* <FormControlLabel
          control={<Switch />}
          label="Same as Website URL"
          sx={{ marginBottom: 2 }}
        /> */}
        {/* Toggle Options */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            borderTop: "1px solid #ddd",
            paddingTop: 2,
          }}
        >
          <FormControlLabel
            control={
              <Switch
                checked={showUtmparams}
                onChange={(
                  event: React.ChangeEvent<HTMLInputElement>,
                  checked: boolean
                ) => setShowUtmparams(checked)}
              />
            }
            label="Add UTM Source & Medium"
          />

          <FormControlLabel
            control={<Switch disabled />}
            label="Enable Tracking"
          />
        </Box>
        {showUtmparams && (
          <Box sx={{ margin: "auto", mt: 2 }}>
            <Card variant="outlined" sx={{ p: 2, mb: 2 }}>
              <CardContent>
                <Box sx={{ display: "flex", margin: "auto", mt: 4, gap: 3 }}>
                  <TextField
                    fullWidth
                    label="UTM Source"
                    placeholder="utm_source"
                    value={utmSource}
                    onChange={(e) => setUtmSource(e.target.value)}
                    sx={{ mb: 2 }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Campaign />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <TextField
                    fullWidth
                    label="UTM Medium"
                    placeholder="organic"
                    value={utmMedium}
                    onChange={(e) => setUtmMedium(e.target.value)}
                    sx={{ mb: 2 }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Campaign />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <TextField
                    fullWidth
                    label="UTM Campaign Name"
                    placeholder="gmb_post"
                    value={utmCampaign}
                    onChange={(e) => setUtmCampaign(e.target.value)}
                    sx={{ mb: 2 }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Campaign />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Box>

                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  onClick={generateUTMUrl}
                  sx={{ textTransform: "none", borderRadius: 2 }}
                >
                  Generate UTM URL
                </Button>
              </CardContent>
            </Card>

            {utmUrl && (
              <Card variant="outlined" sx={{ p: 2, mt: 2 }}>
                <CardContent>
                  <Typography variant="subtitle1">
                    Generated UTM URL:
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center", mt: 1 }}>
                    <TextField
                      fullWidth
                      value={utmUrl}
                      InputProps={{ readOnly: true }}
                    />
                    <Button
                      variant="contained"
                      color="secondary"
                      sx={{ ml: 1, textTransform: "none", borderRadius: 2 }}
                      onClick={() => navigator.clipboard.writeText(utmUrl)}
                      startIcon={<Link />}
                    >
                      Copy
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            )}
          </Box>
        )}

        {/* <FormControlLabel
          control={
            <Switch
              checked={scheduleForLater}
              onChange={(
                event: React.ChangeEvent<HTMLInputElement>,
                checked: boolean
              ) => setScheduleForLater(checked)}
            />
          }
          label="Schedule for Later"
          sx={{ mb: 2 }}
        />
        {scheduleForLater && (
          <ScheduleLater
            googleCreatePOst={values}
            formikSchedulerRef={formikSchedulerRef}
          />
        )} */}
      </Box>
    );
  };

  const PostPreview = (props: { values: IGoogleCreatePost }) => {
    const [currentIndex, setCurrentIndex] = useState(0);

    const handleNext = () => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % uploadedImages.length);
    };

    const handlePrev = () => {
      setCurrentIndex(
        (prevIndex) =>
          (prevIndex - 1 + uploadedImages.length) % uploadedImages.length
      );
    };
    const { values } = props;
    return (
      <Box>
        <Card
          elevation={3}
          sx={{
            borderRadius: 2,
            p: 2,
            mb: 2,
            maxWidth: "100%",
            mx: "auto",
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              height: 200,
              backgroundColor: "#f9fafb",
              borderRadius: 2,
              mb: 2,
              mt: 2,
            }}
          >
            {uploadedImages &&
              uploadedImages.length === 0 &&
              createPostInitials.media &&
              createPostInitials.media.length > 0 && (
                <CardMedia
                  component="div"
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    position: "relative",
                  }}
                >
                  <img
                    src={createPostInitials.media[currentIndex].sourceUrl}
                    alt={`Image ${currentIndex + 1}`}
                    style={{
                      width: "100%",
                      height: "242px",
                      objectFit: "cover",
                      borderRadius: "8px",
                      transition: "opacity 0.5s ease-in-out",
                    }}
                    referrerPolicy="no-referrer"
                  />

                  {/* Previous Button */}
                  {createPostInitials.media.length > 1 && currentIndex > 0 && (
                    <IconButton
                      onClick={handlePrev}
                      sx={{
                        position: "absolute",
                        left: 10,
                        backgroundColor: "rgba(0,0,0,0.5)",
                        color: "white",
                        "&:hover": { backgroundColor: "rgba(0,0,0,0.7)" },
                      }}
                    >
                      <ArrowBackIos />
                    </IconButton>
                  )}

                  {/* Next Button */}
                  {createPostInitials.media.length > 1 &&
                    currentIndex < createPostInitials.media.length && (
                      <IconButton
                        onClick={handleNext}
                        sx={{
                          position: "absolute",
                          right: 10,
                          backgroundColor: "rgba(0,0,0,0.5)",
                          color: "white",
                          "&:hover": { backgroundColor: "rgba(0,0,0,0.7)" },
                        }}
                      >
                        <ArrowForwardIos />
                      </IconButton>
                    )}
                </CardMedia>
              )}

            {uploadedImages && uploadedImages.length > 0 && (
              <CardMedia
                component="div"
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  position: "relative",
                }}
              >
                <img
                  src={URL.createObjectURL(
                    uploadedImages[currentIndex] as unknown as MediaSource
                  )}
                  alt={`Image ${currentIndex + 1}`}
                  style={{
                    width: "100%",
                    height: "242px",
                    objectFit: "cover",
                    borderRadius: "8px",
                    transition: "opacity 0.5s ease-in-out",
                  }}
                  referrerPolicy="no-referrer"
                />

                {/* Previous Button */}
                {uploadedImages.length > 1 && currentIndex > 0 && (
                  <IconButton
                    onClick={handlePrev}
                    sx={{
                      position: "absolute",
                      left: 10,
                      backgroundColor: "rgba(0,0,0,0.5)",
                      color: "white",
                      "&:hover": { backgroundColor: "rgba(0,0,0,0.7)" },
                    }}
                  >
                    <ArrowBackIos />
                  </IconButton>
                )}

                {/* Next Button */}
                {uploadedImages.length > 1 &&
                  currentIndex < uploadedImages.length && (
                    <IconButton
                      onClick={handleNext}
                      sx={{
                        position: "absolute",
                        right: 10,
                        backgroundColor: "rgba(0,0,0,0.5)",
                        color: "white",
                        "&:hover": { backgroundColor: "rgba(0,0,0,0.7)" },
                      }}
                    >
                      <ArrowForwardIos />
                    </IconButton>
                  )}
              </CardMedia>
            )}
            {uploadedImages &&
              uploadedImages.length === 0 &&
              createPostInitials.media &&
              createPostInitials.media.length === 0 && (
                <>
                  <ImageOutlinedIcon sx={{ fontSize: 50, color: "#c4c4c4" }} />
                  <Typography variant="body1" sx={{ mt: 1, color: "#6c757d" }}>
                    No Image Added
                  </Typography>
                </>
              )}
          </Box>
          <CardContent>
            {values.topicType === TOPIC_TYPES.Event && (
              <Typography variant="h6" sx={{ fontWeight: 600 }} gutterBottom>
                {values.event?.title || ""}
              </Typography>
            )}

            <Typography variant="body2" color="text.secondary">
              {values.summary}
            </Typography>
          </CardContent>
          <Box sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
            {values.callToAction != null &&
              EventTypes.filter(
                (x) => x.key === values.callToAction?.actionType
              ).map((btn) => (
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={iconMap[btn.icon]}
                  sx={{ textTransform: "none", borderRadius: 2 }}
                >
                  {btn.label}
                </Button>
              ))}
          </Box>
        </Card>
      </Box>
    );
  };

  const submitPost = async (
    createGooglePostList: ISelectionLocationWithPost[],
    values: IGoogleCreatePost
  ) => {
    if (createGooglePostList) {
      debugger;
      handleClosePost();
      setSelectedLocations(createGooglePostList);
      setShowCreatePostStatus(true);
      console.log(uploadedImages);
      const formData = new FormData();
      for (let i = 0; i < uploadedImages.length; i++) {
        formData.append("files", uploadedImages[i] as unknown as Blob);
      }
      setPostCreationProgress({
        percent: 20,
        status: "Uploading images",
      });

      // Generate bulk post ID if multiple locations are selected
      const isBulkPost = createGooglePostList.length > 1;
      const bulkPostId = isBulkPost ? generateBulkPostId() : undefined;
      var fileUploadResponse: IFileUploadResponseModel =
        await _postsService.uploadImagesToServer(formData);
      if (fileUploadResponse.files.length === uploadedImages.length) {
        let mediaObject = [];
        for (let index = 0; index < uploadedImages.length; index++) {
          const element = createGooglePostList[index];
          mediaObject.push({
            mediaFormat: "PHOTO",
            // sourceUrl: fileUploadResponse.files[index].fileUrl,
            sourceUrl: "https://dummyimage.com/2000X1000/000/fff.jpg",
          });
        }

        let postList = createGooglePostList;

        const percent = 80 / createGooglePostList.length;

        for (let index = 0; index < createGooglePostList.length; index++) {
          try {
            let element2 = createGooglePostList[index];
            const postRequest = {
              ...element2.createGooglePost,
              media: mediaObject,
            };

            setPostCreationProgress({
              percent: postCreationProgress.percent + percent / 2,
              status: `Posting ${element2.locationInfo.locationName}`,
            });

            console.log("Create post request: ", {
              ...element2,
              createGooglePost: postRequest,
            });

            try {
              console.log({
                ...element2,
                createGooglePost: postRequest,
                // scheduleForLater: scheduleForLater
                //   ? (formikSchedulerRef.current as unknown as any).values
                //   : null,
              });
              var createPostResponse: any = await _postsService.createPost(
                userInfo.id,
                {
                  ...element2,
                  createGooglePost: postRequest,
                  // scheduleForLater: scheduleForLater
                  //   ? (formikSchedulerRef.current as unknown as any).values
                  //   : null,
                },
                isBulkPost,
                bulkPostId
              );

              if (createPostResponse.isSuccess) {
                postList[index].locationInfo.viewUrl =
                  createPostResponse.data.searchUrl;
                postList[index].locationInfo.status =
                  createPostResponse.isSuccess;
                setSelectedLocations([...postList]);
              } else {
                postList[index].locationInfo.status = false;
                setSelectedLocations([...postList]);
              }

              setPostCreationProgress({
                percent: postCreationProgress.percent + percent / 2,
                status: `Posting ${element2.locationInfo.locationName}`,
              });

              setPostCreationProgress({
                percent: 100,
                status: createPostResponse.message,
              });
            } catch (error) {}
          } catch (error) {}
        }
      } else {
      }
    }
  };

  const BlinkingText = styled(Typography)`
    @keyframes blink {
      0% {
        opacity: 1;
      }
      50% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }
    animation: blink 1s infinite;
  `;

  const getProgressColor = () => {
    if (postCreationProgress.percent >= 100) {
      return "primary.main"; // Completed color (Green)
    }
    return "secondary.main"; // Processing color (Blue)
  };

  return (
    <Box>
      <LeftMenuComponent>
        <Box className="commonTableHeader">
          <h3 className="commonTitle pageTitle">Create Posts</h3>
        </Box>
        <Formik
          enableReinitialize
          initialValues={{ ...createPostInitials }}
          validationSchema={CreatePostSchema}
          onSubmit={(values, formikHelpers) => {
            _handlePostSubmission(values, formikHelpers);
          }}
        >
          {({
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
            handleSubmit,
            isSubmitting,
            isValid,
            setFieldValue,
            setFieldTouched,
            setTouched,
            setErrors,
            /* and other goodies */
          }) => (
            <form
              onSubmit={(e) => {
                e.preventDefault(); // Prevents page refresh
                handleSubmit();
              }}
            >
              <div className="height100">
                <Box className="height100">
                  <Box sx={{ display: "flex" }}>
                    {/* <Sidebar /> */}
                    <Box sx={{ width: "100%" }}>
                      <PostTypeTabs
                        value={tabValue}
                        onChange={(e: any, newValue: number) =>
                          setTabValue(newValue)
                        }
                      />
                      <Box sx={{ display: "flex", gap: "16px", mt: 2 }}>
                        <Box sx={{ width: "70%" }}>
                          {tabValue === 1 && (
                            <PostForm
                              setFieldValue={setFieldValue}
                              values={values}
                              errors={errors}
                              touched={touched}
                              setFieldTouched={setFieldTouched}
                              setErrors={setErrors}
                              setTouched={setTouched}
                            />
                          )}
                          {tabValue === 2 && <h1>TAB 2</h1>}
                          {tabValue === 3 && <h1>TAB 3</h1>}
                        </Box>
                        <Box sx={{ width: "30%" }}>
                          <PostPreview values={values} />
                          <InfoCard />
                        </Box>
                      </Box>
                      <Button
                        className="updatesShapeBtn"
                        type="submit"
                        variant="contained"
                        style={{ textTransform: "capitalize" }}
                        fullWidth
                      >
                        Select Business Locations to create post
                      </Button>
                    </Box>
                  </Box>
                </Box>

                <Drawer
                  anchor={"right"}
                  open={showLocationSelection.isShow}
                  onClose={() => console.log("Create Post modal closed")}
                  sx={{
                    "& .MuiDrawer-paper": {
                      maxWidth: "50vw", // Set the max width
                      width: "100%", // Ensure the drawer does not exceed the max width
                    },
                    zIndex: (theme) => {
                      return theme.zIndex.drawer;
                    },
                  }}
                >
                  <Box className="height100">
                    <SubmitPost
                      isShow={showLocationSelection.isShow}
                      closeModal={handleClosePost}
                      createPostModel={showLocationSelection.createPostModel}
                      savePosts={(
                        createGooglePostList: ISelectionLocationWithPost[]
                      ) => submitPost(createGooglePostList, values)}
                    />
                  </Box>
                </Drawer>
              </div>
              <FormErrorDebugger errors={errors} touched={touched} />
            </form>
          )}
        </Formik>
      </LeftMenuComponent>

      <Dialog
        fullWidth
        maxWidth={"md"}
        open={showCreatePostStatus}
        onClose={() => console.log("On Close")}
      >
        <DialogTitle>Upload Status</DialogTitle>
        <Box sx={{ position: "relative", width: "100%" }}>
          <LinearProgress
            variant="determinate"
            value={postCreationProgress.percent}
            color="secondary"
            sx={{
              height: "20px",
              backgroundColor: "#d3d3d3",
              "& .MuiLinearProgress-bar": {
                backgroundColor: getProgressColor(),
              },
            }}
          />
          <BlinkingText
            variant="body2"
            sx={{
              position: "absolute",
              top: 0,
              left: "13%",
              transform: "translateX(-50%)",
              fontWeight: "bold",
              color: "#ffffff",
            }}
          >
            {postCreationProgress.status}...
          </BlinkingText>
        </Box>
        <DialogContent>
          <DialogContentText>
            <Box
              noValidate
              component="form"
              sx={{
                display: "flex",
                flexDirection: "column",
                m: "auto",
              }}
            >
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>
                        <b>Business</b>
                      </TableCell>
                      <TableCell>
                        <b>Account</b>
                      </TableCell>
                      <TableCell>
                        <b>Location</b>
                      </TableCell>
                      <TableCell>
                        <b>Status</b>
                      </TableCell>
                      <TableCell>
                        <b></b>
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {selectedLocations &&
                      selectedLocations.map(
                        (x: ISelectionLocationWithPost, index: number) => (
                          <TableRow key={index}>
                            <TableCell>{x.locationInfo.businessName}</TableCell>
                            <TableCell>{x.locationInfo.accountName}</TableCell>
                            <TableCell>{x.locationInfo.locationName}</TableCell>
                            <TableCell>
                              {x.locationInfo.status == null ? (
                                <CircularProgress
                                  color="secondary"
                                  size="30px"
                                />
                              ) : x.locationInfo.status ? (
                                <CheckCircleOutlineIcon color="success" />
                              ) : (
                                <CancelIcon color="error" />
                              )}
                            </TableCell>
                            <TableCell>
                              {x.locationInfo.viewUrl ? (
                                <IconButton
                                  onClick={() =>
                                    window.open(
                                      x.locationInfo.viewUrl,
                                      "_blank"
                                    )
                                  }
                                  color="primary"
                                  size="small"
                                >
                                  <VisibilityIcon />
                                </IconButton>
                              ) : (
                                <BlockIcon color="error" />
                              )}
                            </TableCell>
                          </TableRow>
                        )
                      )}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            disabled={!(postCreationProgress.percent >= 100)}
            onClick={() => navigate("/post-management/posts")}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CreateSocialPost;
