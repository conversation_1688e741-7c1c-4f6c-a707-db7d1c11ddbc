{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\LinearProgressWithLabel\\\\LinearProgressWithLabel.component.tsx\";\nimport React from \"react\";\nimport LinearProgress from \"@mui/material/LinearProgress\";\nimport Typography from \"@mui/material/Typography\";\nimport Box from \"@mui/material/Box\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LinearProgressWithLabel = ({\n  value\n}) => {\n  const getColor = () => {\n    if (value >= 80) return \"#4caf50\"; // green\n    if (value >= 50) return \"#ff9800\"; // orange\n    return \"#f44336\"; // red\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: \"flex\",\n      alignItems: \"center\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: \"100%\",\n        mr: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n        variant: \"determinate\",\n        value: value,\n        sx: {\n          height: 15,\n          borderRadius: 5,\n          [`& .MuiLinearProgress-bar`]: {\n            backgroundColor: getColor()\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        minWidth: 35\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: `${Math.round(value)}%`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_c = LinearProgressWithLabel;\nexport default LinearProgressWithLabel;\nvar _c;\n$RefreshReg$(_c, \"LinearProgressWithLabel\");", "map": {"version": 3, "names": ["React", "LinearProgress", "Typography", "Box", "jsxDEV", "_jsxDEV", "LinearProgressWithLabel", "value", "getColor", "sx", "display", "alignItems", "children", "width", "mr", "variant", "height", "borderRadius", "backgroundColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "min<PERSON><PERSON><PERSON>", "color", "Math", "round", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/LinearProgressWithLabel/LinearProgressWithLabel.component.tsx"], "sourcesContent": ["import React from \"react\";\nimport LinearProgress from \"@mui/material/LinearProgress\";\nimport Typography from \"@mui/material/Typography\";\nimport Box from \"@mui/material/Box\";\n\ninterface LinearProgressWithLabelProps {\n  value: number;\n}\n\nconst LinearProgressWithLabel: React.FC<LinearProgressWithLabelProps> = ({\n  value,\n}) => {\n  const getColor = () => {\n    if (value >= 80) return \"#4caf50\"; // green\n    if (value >= 50) return \"#ff9800\"; // orange\n    return \"#f44336\"; // red\n  };\n  return (\n    <Box sx={{ display: \"flex\", alignItems: \"center\" }}>\n      <Box sx={{ width: \"100%\", mr: 1 }}>\n        <LinearProgress\n          variant=\"determinate\"\n          value={value}\n          sx={{\n            height: 15,\n            borderRadius: 5,\n            [`& .MuiLinearProgress-bar`]: {\n              backgroundColor: getColor(),\n            },\n          }}\n        />\n      </Box>\n      <Box sx={{ minWidth: 35 }}>\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          {`${Math.round(value)}%`}\n        </Typography>\n      </Box>\n    </Box>\n  );\n};\n\nexport default LinearProgressWithLabel;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,GAAG,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMpC,MAAMC,uBAA+D,GAAGA,CAAC;EACvEC;AACF,CAAC,KAAK;EACJ,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAID,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACnC,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;EACD,oBACEF,OAAA,CAACF,GAAG;IAACM,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACjDP,OAAA,CAACF,GAAG;MAACM,EAAE,EAAE;QAAEI,KAAK,EAAE,MAAM;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAChCP,OAAA,CAACJ,cAAc;QACbc,OAAO,EAAC,aAAa;QACrBR,KAAK,EAAEA,KAAM;QACbE,EAAE,EAAE;UACFO,MAAM,EAAE,EAAE;UACVC,YAAY,EAAE,CAAC;UACf,CAAC,0BAA0B,GAAG;YAC5BC,eAAe,EAAEV,QAAQ,CAAC;UAC5B;QACF;MAAE;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNjB,OAAA,CAACF,GAAG;MAACM,EAAE,EAAE;QAAEc,QAAQ,EAAE;MAAG,CAAE;MAAAX,QAAA,eACxBP,OAAA,CAACH,UAAU;QAACa,OAAO,EAAC,OAAO;QAACS,KAAK,EAAC,gBAAgB;QAAAZ,QAAA,EAC/C,GAAGa,IAAI,CAACC,KAAK,CAACnB,KAAK,CAAC;MAAG;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACK,EAAA,GA9BIrB,uBAA+D;AAgCrE,eAAeA,uBAAuB;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}