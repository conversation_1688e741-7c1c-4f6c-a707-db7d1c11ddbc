const AWS = require("aws-sdk");
require("dotenv").config({ path: ".env.development" });

/**
 * Setup S3 bucket for ManageAssets feature
 * This script will:
 * 1. Create the S3 bucket if it doesn't exist
 * 2. Configure bucket policy for public read access
 * 3. Configure CORS for web access
 * 4. Set up lifecycle rules (optional)
 */

async function setupS3Bucket() {
  try {
    console.log("🚀 Starting S3 bucket setup...");

    // Configure AWS SDK
    AWS.config.update({
      accessKeyId: process.env.APP_AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.APP_AWS_SECRET_ACCESS_KEY,
      region: process.env.APP_AWS_REGION,
    });

    const s3 = new AWS.S3();
    const bucketName = process.env.APP_AWS_S3_BUCKET;
    const region = process.env.APP_AWS_REGION;

    console.log(`📋 Bucket Name: ${bucketName}`);
    console.log(`📋 Region: ${region}`);

    // Check if bucket exists
    try {
      await s3.headBucket({ Bucket: bucketName }).promise();
      console.log("✅ Bucket already exists");
    } catch (error) {
      if (error.code === "NotFound") {
        console.log("📦 Creating bucket...");

        // Create bucket
        const createParams = {
          Bucket: bucketName,
          CreateBucketConfiguration:
            region !== "us-east-1"
              ? {
                  LocationConstraint: region,
                }
              : undefined,
        };

        await s3.createBucket(createParams).promise();
        console.log("✅ Bucket created successfully");
      } else {
        throw error;
      }
    }

    // Try to configure bucket policy for public read access (optional)
    console.log("🔧 Configuring bucket policy...");
    try {
      const bucketPolicy = {
        Version: "2012-10-17",
        Statement: [
          {
            Sid: "PublicReadGetObject",
            Effect: "Allow",
            Principal: "*",
            Action: "s3:GetObject",
            Resource: `arn:aws:s3:::${bucketName}/*`,
          },
        ],
      };

      await s3
        .putBucketPolicy({
          Bucket: bucketName,
          Policy: JSON.stringify(bucketPolicy),
        })
        .promise();
      console.log("✅ Bucket policy configured");
    } catch (error) {
      console.log(
        "⚠️  Could not set public bucket policy (this is okay for development)"
      );
      console.log("   Files will be accessible via signed URLs instead");
    }

    // Configure CORS
    console.log("🔧 Configuring CORS...");
    const corsConfiguration = {
      CORSRules: [
        {
          AllowedHeaders: ["*"],
          AllowedMethods: ["GET", "PUT", "POST", "DELETE"],
          AllowedOrigins: ["*"],
          ExposeHeaders: ["ETag"],
          MaxAgeSeconds: 3000,
        },
      ],
    };

    await s3
      .putBucketCors({
        Bucket: bucketName,
        CORSConfiguration: corsConfiguration,
      })
      .promise();
    console.log("✅ CORS configured");

    // Test upload
    console.log("🧪 Testing bucket access...");
    const testKey = "test/test-file.txt";
    const testContent = "This is a test file for ManageAssets feature";

    await s3
      .putObject({
        Bucket: bucketName,
        Key: testKey,
        Body: testContent,
        ContentType: "text/plain",
      })
      .promise();
    console.log("✅ Test upload successful");

    // Clean up test file
    await s3
      .deleteObject({
        Bucket: bucketName,
        Key: testKey,
      })
      .promise();
    console.log("✅ Test file cleaned up");

    console.log("🎉 S3 bucket setup completed successfully!");
    console.log("");
    console.log("📋 Summary:");
    console.log(`- Bucket Name: ${bucketName}`);
    console.log(`- Region: ${region}`);
    console.log(`- Public Read Access: Enabled`);
    console.log(`- CORS: Configured`);
    console.log(
      `- Base URL: https://${bucketName}.s3.${region}.amazonaws.com/`
    );
    console.log("");
    console.log("✅ ManageAssets feature is ready to use!");
  } catch (error) {
    console.error("❌ Error during S3 setup:", error.message);

    if (error.code === "InvalidAccessKeyId") {
      console.error(
        "🔑 Invalid AWS Access Key ID. Please check your credentials."
      );
    } else if (error.code === "SignatureDoesNotMatch") {
      console.error(
        "🔑 Invalid AWS Secret Access Key. Please check your credentials."
      );
    } else if (error.code === "BucketAlreadyExists") {
      console.error(
        "📦 Bucket name already exists globally. Please choose a different name."
      );
    } else if (error.code === "BucketAlreadyOwnedByYou") {
      console.log(
        "📦 Bucket already owned by you. Continuing with configuration..."
      );
    } else {
      console.error("Stack trace:", error.stack);
    }

    process.exit(1);
  }
}

// Run the setup
if (require.main === module) {
  setupS3Bucket();
}

module.exports = { setupS3Bucket };
