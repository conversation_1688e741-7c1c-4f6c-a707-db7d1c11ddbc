const logger = require("../utils/logger");

/**
 * Generate thumbnail from video buffer (minimal version - no processing)
 * @param {Buffer} videoBuffer - Video file buffer
 * @param {string} businessName - Business name for S3 key
 * @param {number} businessId - Business ID
 * @param {string} originalFileName - Original video file name
 * @returns {Promise<Object>} Thumbnail generation result
 */
const generateVideoThumbnail = async (videoBuffer, businessName, businessId, originalFileName) => {
  try {
    logger.info('Video thumbnail generation temporarily disabled', {
      businessId,
      originalFileName,
      videoSize: videoBuffer.length,
    });

    // TODO: Implement video thumbnail generation with ffmpeg
    // For now, return success without thumbnail to allow uploads to work
    return {
      success: false,
      error: 'Video thumbnail generation temporarily disabled - ffmpeg not available'
    };
  } catch (error) {
    logger.error("Error generating video thumbnail", {
      error: error.message,
      stack: error.stack,
      businessId,
      originalFileName,
    });

    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Generate thumbnail for image (minimal version - no processing)
 * @param {Buffer} imageBuffer - Image file buffer
 * @param {string} businessName - Business name for S3 key
 * @param {number} businessId - Business ID
 * @param {string} originalFileName - Original image file name
 * @param {string} mimeType - Image MIME type
 * @returns {Promise<Object>} Thumbnail generation result
 */
const generateImageThumbnail = async (imageBuffer, businessName, businessId, originalFileName, mimeType) => {
  try {
    logger.info('Image thumbnail generation temporarily disabled', {
      businessId,
      originalFileName,
      imageSize: imageBuffer.length
    });

    // TODO: Implement image thumbnail generation with sharp
    // For now, return success without thumbnail to allow uploads to work
    return {
      success: false,
      error: 'Image thumbnail generation temporarily disabled - sharp not available'
    };

  } catch (error) {
    logger.error('Error generating image thumbnail', {
      error: error.message,
      stack: error.stack,
      businessId,
      originalFileName
    });

    return {
      success: false,
      error: error.message
    };
  }
};

module.exports = {
  generateVideoThumbnail,
  generateImageThumbnail
};
