{"ast": null, "code": "import HttpHelperService from \"../httpHelper.service\";\nimport { MANAGE_ASSETS_UPLOAD, MANAGE_ASSETS_GET_BUSINESS_ASSETS, MANAGE_ASSETS_GET_ASSET, MANAGE_ASSETS_DELETE_ASSET, MA<PERSON>GE_ASSETS_UPDATE_MAX_SIZE } from \"../../constants/endPoints.constant\";\nclass ManageAssetsService {\n  constructor(dispatch) {\n    this._httpHelperService = void 0;\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  /**\n   * Upload assets for a business\n   * @param businessId - Business ID\n   * @param files - Files to upload\n   * @returns Promise with upload result\n   */\n  async uploadAssets(businessId, files) {\n    try {\n      const formData = new FormData();\n\n      // Add all files to FormData\n      Array.from(files).forEach(file => {\n        formData.append(\"files\", file);\n      });\n      return await this._httpHelperService.postFormData(MANAGE_ASSETS_UPLOAD(businessId), formData);\n    } catch (error) {\n      console.error(\"Error uploading assets:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get assets for a business with pagination\n   * @param businessId - Business ID\n   * @param page - Page number\n   * @param limit - Records per page\n   * @returns Promise with assets list\n   */\n  async getAssets(businessId, page = 1, limit = 12) {\n    try {\n      return await this._httpHelperService.get(MANAGE_ASSETS_GET_BUSINESS_ASSETS(businessId), {\n        page,\n        limit\n      });\n    } catch (error) {\n      console.error(\"Error fetching assets:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get single asset by ID\n   * @param assetId - Asset ID\n   * @returns Promise with asset data\n   */\n  async getAssetById(assetId) {\n    try {\n      return await this._httpHelperService.get(MANAGE_ASSETS_GET_ASSET(assetId));\n    } catch (error) {\n      console.error(\"Error fetching asset:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete an asset\n   * @param assetId - Asset ID\n   * @returns Promise with delete result\n   */\n  async deleteAsset(assetId) {\n    try {\n      return await this._httpHelperService.delete(MANAGE_ASSETS_DELETE_ASSET(assetId));\n    } catch (error) {\n      console.error(\"Error deleting asset:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update max upload size for a business\n   * @param businessId - Business ID\n   * @param maxSizeMB - Max size in MB\n   * @returns Promise with update result\n   */\n  async updateMaxUploadSize(businessId, maxSizeMB) {\n    try {\n      return await this._httpHelperService.put(MANAGE_ASSETS_UPDATE_MAX_SIZE(businessId), {\n        maxSizeMB\n      });\n    } catch (error) {\n      console.error(\"Error updating max upload size:\", error);\n      throw error;\n    }\n  }\n}\nexport default ManageAssetsService;", "map": {"version": 3, "names": ["HttpHelperService", "MANAGE_ASSETS_UPLOAD", "MANAGE_ASSETS_GET_BUSINESS_ASSETS", "MANAGE_ASSETS_GET_ASSET", "MANAGE_ASSETS_DELETE_ASSET", "MANAGE_ASSETS_UPDATE_MAX_SIZE", "ManageAssetsService", "constructor", "dispatch", "_httpHelperService", "uploadAssets", "businessId", "files", "formData", "FormData", "Array", "from", "for<PERSON>ach", "file", "append", "postFormData", "error", "console", "getAssets", "page", "limit", "get", "getAssetById", "assetId", "deleteAsset", "delete", "updateMaxUploadSize", "maxSizeMB", "put"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/manageAssets/manageAssets.service.ts"], "sourcesContent": ["import { Dispatch } from \"react\";\nimport HttpHelperService from \"../httpHelper.service\";\nimport { Action } from \"redux\";\nimport {\n  MANAGE_ASSETS_UPLOAD,\n  MANAGE_ASSETS_GET_BUSINESS_ASSETS,\n  MANAGE_ASSETS_GET_ASSET,\n  MANAGE_ASSETS_DELETE_ASSET,\n  MANAGE_ASSETS_UPDATE_MAX_SIZE,\n} from \"../../constants/endPoints.constant\";\n\nclass ManageAssetsService {\n  _httpHelperService: HttpHelperService;\n\n  constructor(dispatch: Dispatch<Action>) {\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  /**\n   * Upload assets for a business\n   * @param businessId - Business ID\n   * @param files - Files to upload\n   * @returns Promise with upload result\n   */\n  async uploadAssets(businessId: number, files: FileList): Promise<any> {\n    try {\n      const formData = new FormData();\n\n      // Add all files to FormData\n      Array.from(files).forEach((file) => {\n        formData.append(\"files\", file);\n      });\n\n      return await this._httpHelperService.postFormData(\n        MANAGE_ASSETS_UPLOAD(businessId),\n        formData\n      );\n    } catch (error: any) {\n      console.error(\"Error uploading assets:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get assets for a business with pagination\n   * @param businessId - Business ID\n   * @param page - Page number\n   * @param limit - Records per page\n   * @returns Promise with assets list\n   */\n  async getAssets(\n    businessId: number,\n    page: number = 1,\n    limit: number = 12\n  ): Promise<any> {\n    try {\n      return await this._httpHelperService.get(\n        MANAGE_ASSETS_GET_BUSINESS_ASSETS(businessId),\n        { page, limit }\n      );\n    } catch (error: any) {\n      console.error(\"Error fetching assets:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get single asset by ID\n   * @param assetId - Asset ID\n   * @returns Promise with asset data\n   */\n  async getAssetById(assetId: number): Promise<any> {\n    try {\n      return await this._httpHelperService.get(\n        MANAGE_ASSETS_GET_ASSET(assetId)\n      );\n    } catch (error: any) {\n      console.error(\"Error fetching asset:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete an asset\n   * @param assetId - Asset ID\n   * @returns Promise with delete result\n   */\n  async deleteAsset(assetId: number): Promise<any> {\n    try {\n      return await this._httpHelperService.delete(\n        MANAGE_ASSETS_DELETE_ASSET(assetId)\n      );\n    } catch (error: any) {\n      console.error(\"Error deleting asset:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update max upload size for a business\n   * @param businessId - Business ID\n   * @param maxSizeMB - Max size in MB\n   * @returns Promise with update result\n   */\n  async updateMaxUploadSize(\n    businessId: number,\n    maxSizeMB: number\n  ): Promise<any> {\n    try {\n      return await this._httpHelperService.put(\n        MANAGE_ASSETS_UPDATE_MAX_SIZE(businessId),\n        { maxSizeMB }\n      );\n    } catch (error: any) {\n      console.error(\"Error updating max upload size:\", error);\n      throw error;\n    }\n  }\n}\n\nexport default ManageAssetsService;\n"], "mappings": "AACA,OAAOA,iBAAiB,MAAM,uBAAuB;AAErD,SACEC,oBAAoB,EACpBC,iCAAiC,EACjCC,uBAAuB,EACvBC,0BAA0B,EAC1BC,6BAA6B,QACxB,oCAAoC;AAE3C,MAAMC,mBAAmB,CAAC;EAGxBC,WAAWA,CAACC,QAA0B,EAAE;IAAA,KAFxCC,kBAAkB;IAGhB,IAAI,CAACA,kBAAkB,GAAG,IAAIT,iBAAiB,CAACQ,QAAQ,CAAC;EAC3D;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAME,YAAYA,CAACC,UAAkB,EAAEC,KAAe,EAAgB;IACpE,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAE/B;MACAC,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC,CAACK,OAAO,CAAEC,IAAI,IAAK;QAClCL,QAAQ,CAACM,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;MAChC,CAAC,CAAC;MAEF,OAAO,MAAM,IAAI,CAACT,kBAAkB,CAACW,YAAY,CAC/CnB,oBAAoB,CAACU,UAAU,CAAC,EAChCE,QACF,CAAC;IACH,CAAC,CAAC,OAAOQ,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAME,SAASA,CACbZ,UAAkB,EAClBa,IAAY,GAAG,CAAC,EAChBC,KAAa,GAAG,EAAE,EACJ;IACd,IAAI;MACF,OAAO,MAAM,IAAI,CAAChB,kBAAkB,CAACiB,GAAG,CACtCxB,iCAAiC,CAACS,UAAU,CAAC,EAC7C;QAAEa,IAAI;QAAEC;MAAM,CAChB,CAAC;IACH,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMM,YAAYA,CAACC,OAAe,EAAgB;IAChD,IAAI;MACF,OAAO,MAAM,IAAI,CAACnB,kBAAkB,CAACiB,GAAG,CACtCvB,uBAAuB,CAACyB,OAAO,CACjC,CAAC;IACH,CAAC,CAAC,OAAOP,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMQ,WAAWA,CAACD,OAAe,EAAgB;IAC/C,IAAI;MACF,OAAO,MAAM,IAAI,CAACnB,kBAAkB,CAACqB,MAAM,CACzC1B,0BAA0B,CAACwB,OAAO,CACpC,CAAC;IACH,CAAC,CAAC,OAAOP,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMU,mBAAmBA,CACvBpB,UAAkB,EAClBqB,SAAiB,EACH;IACd,IAAI;MACF,OAAO,MAAM,IAAI,CAACvB,kBAAkB,CAACwB,GAAG,CACtC5B,6BAA6B,CAACM,UAAU,CAAC,EACzC;QAAEqB;MAAU,CACd,CAAC;IACH,CAAC,CAAC,OAAOX,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF;AACF;AAEA,eAAef,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}