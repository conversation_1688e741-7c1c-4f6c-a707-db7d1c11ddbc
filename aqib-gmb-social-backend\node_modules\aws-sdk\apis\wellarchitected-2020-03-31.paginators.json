{"pagination": {"GetConsolidatedReport": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListAnswers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListCheckDetails": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListCheckSummaries": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListLensReviewImprovements": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListLensReviews": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListLensShares": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListLenses": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListMilestones": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListNotifications": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListProfileNotifications": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListProfileShares": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListProfiles": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListReviewTemplateAnswers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListReviewTemplates": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListShareInvitations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListTemplateShares": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListWorkloadShares": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListWorkloads": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}}}