{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\createPost\\\\createPostTemplates\\\\cards\\\\testimonialCard3\\\\testimonialCard3.component.tsx\";\nimport React from \"react\";\nimport { Box, Typography, Container, CssBaseline } from \"@mui/material\";\nimport UserAvatar from \"../../../../userAvatar/userAvatar.component\";\nimport RatingsStar from \"../../../../ratingsStar/ratingsStar.component\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestimonialCard3 = props => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    ref: props.divRef,\n    sx: {\n      backgroundImage: `url(${require(\"../../../../../assets/feedbackBackgrouns/1.jpg\")})`,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\",\n      backgroundRepeat: \"no-repeat\",\n      borderRadius: 2,\n      color: \"#fff\",\n      padding: 4,\n      textAlign: \"center\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      height: \"100%\",\n      width: \"100%\",\n      boxShadow: \"0px 4px 10px rgba(0, 0, 0, 0.3)\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: \"relative\",\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"center\",\n          padding: 4,\n          borderRadius: 3,\n          boxShadow: \"0px 8px 20px rgba(0, 0, 0, 0.2)\",\n          backgroundColor: \"#fff\",\n          width: 250,\n          height: 250,\n          margin: \"auto\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            zIndex: 2,\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"flex-start\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              alignItems: \"center\",\n              mb: 2\n            },\n            children: [props.templateConfig.showAvatar && /*#__PURE__*/_jsxDEV(UserAvatar, {\n              profileImage: props.templateConfig.reviewerImage,\n              fullname: props.templateConfig.reviewerName,\n              style: {\n                width: 60,\n                height: 60,\n                margin: \"0 auto 10px\",\n                background: \"linear-gradient(45deg, #42A5F5, #64B5F6)\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                children: props.templateConfig.reviewerName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), props.templateConfig.showRating && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              justifyContent: \"center\",\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(RatingsStar, {\n              starRating: props.templateConfig.starRating\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            mb: 2,\n            children: props.templateConfig.comment\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontSize: \"16px\",\n              fontWeight: 600,\n              marginTop: \"20px\",\n              color: \"#0056A6\"\n            },\n            children: [\"\\u2014 \", props.templateConfig.reviewerName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: \"absolute\",\n            top: \"-20px\",\n            right: \"-20px\",\n            width: 50,\n            height: 50,\n            backgroundColor: \"#4caf50\",\n            borderRadius: \"50%\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            color: \"#fff\",\n            boxShadow: \"0px 4px 10px rgba(0, 0, 0, 0.2)\"\n          },\n          children: \"\\u2764\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_c = TestimonialCard3;\nexport default TestimonialCard3;\nvar _c;\n$RefreshReg$(_c, \"TestimonialCard3\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Container", "CssBaseline", "UserAvatar", "RatingsStar", "jsxDEV", "_jsxDEV", "TestimonialCard3", "props", "ref", "divRef", "sx", "backgroundImage", "require", "backgroundSize", "backgroundPosition", "backgroundRepeat", "borderRadius", "color", "padding", "textAlign", "display", "flexDirection", "justifyContent", "alignItems", "height", "width", "boxShadow", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "backgroundColor", "margin", "zIndex", "mb", "templateConfig", "showAvatar", "profileImage", "reviewerImage", "fullname", "reviewerName", "style", "background", "variant", "fontWeight", "showRating", "starRating", "comment", "fontSize", "marginTop", "top", "right", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/createPost/createPostTemplates/cards/testimonialCard3/testimonialCard3.component.tsx"], "sourcesContent": ["import React from \"react\";\nimport {\n  Box,\n  Typography,\n  Avatar,\n  Rating,\n  Container,\n  CssBaseline,\n} from \"@mui/material\";\nimport { IPostTemplateConfig } from \"../../../../../types/IPostTemplateConfig\";\nimport { ref } from \"yup\";\nimport StarIcon from \"@mui/icons-material/Star\";\nimport UserAvatar from \"../../../../userAvatar/userAvatar.component\";\nimport RatingsStar from \"../../../../ratingsStar/ratingsStar.component\";\n\nconst TestimonialCard3 = (props: {\n  templateConfig: IPostTemplateConfig;\n  divRef: any;\n}) => {\n  return (\n    <Box\n      ref={props.divRef}\n      sx={{\n        backgroundImage: `url(${require(\"../../../../../assets/feedbackBackgrouns/1.jpg\")})`,\n        backgroundSize: \"cover\",\n        backgroundPosition: \"center\",\n        backgroundRepeat: \"no-repeat\",\n        borderRadius: 2,\n        color: \"#fff\",\n        padding: 4,\n        textAlign: \"center\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        height: \"100%\",\n        width: \"100%\",\n        boxShadow: \"0px 4px 10px rgba(0, 0, 0, 0.3)\",\n      }}\n    >\n      <Container>\n        <CssBaseline />\n        <Box\n          sx={{\n            position: \"relative\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            padding: 4,\n            borderRadius: 3,\n            boxShadow: \"0px 8px 20px rgba(0, 0, 0, 0.2)\",\n            backgroundColor: \"#fff\",\n            width: 250,\n            height: 250,\n            margin: \"auto\",\n          }}\n        >\n          {/* Background Text */}\n          {/* <Typography\n        variant=\"h1\"\n        sx={{\n          position: \"absolute\",\n          top: \"-60px\",\n          left: \"-20px\",\n          fontSize: \"8rem\",\n          fontWeight: \"bold\",\n          color: \"rgba(0, 0, 0, 0.1)\",\n          zIndex: 1,\n        }}\n      >\n        feedback\n      </Typography> */}\n\n          {/* Main Content */}\n          <Box\n            sx={{\n              zIndex: 2,\n              display: \"flex\",\n              flexDirection: \"column\",\n              alignItems: \"flex-start\",\n            }}\n          >\n            {/* Avatar and Name */}\n            <Box sx={{ display: \"flex\", alignItems: \"center\", mb: 2 }}>\n              {/* Avatar */}\n              {props.templateConfig.showAvatar && (\n                <UserAvatar\n                  profileImage={props.templateConfig.reviewerImage}\n                  fullname={props.templateConfig.reviewerName}\n                  style={{\n                    width: 60,\n                    height: 60,\n                    margin: \"0 auto 10px\",\n                    background: \"linear-gradient(45deg, #42A5F5, #64B5F6)\",\n                  }}\n                />\n              )}\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\">\n                  {props.templateConfig.reviewerName}\n                </Typography>\n              </Box>\n            </Box>\n\n            {/* Star Rating */}\n            {props.templateConfig.showRating && (\n              <Box\n                sx={{\n                  display: \"flex\",\n                  justifyContent: \"center\",\n                  mb: 2,\n                }}\n              >\n                <RatingsStar starRating={props.templateConfig.starRating} />\n              </Box>\n            )}\n\n            {/* Review Text */}\n            <Typography variant=\"body2\" color=\"text.secondary\" mb={2}>\n              {props.templateConfig.comment}\n            </Typography>\n\n            {/* Author Section */}\n            <Typography\n              variant=\"h6\"\n              sx={{\n                fontSize: \"16px\",\n                fontWeight: 600,\n                marginTop: \"20px\",\n                color: \"#0056A6\",\n              }}\n            >\n              — {props.templateConfig.reviewerName}\n            </Typography>\n          </Box>\n\n          {/* Floating Heart Icon */}\n          <Box\n            sx={{\n              position: \"absolute\",\n              top: \"-20px\",\n              right: \"-20px\",\n              width: 50,\n              height: 50,\n              backgroundColor: \"#4caf50\",\n              borderRadius: \"50%\",\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              color: \"#fff\",\n              boxShadow: \"0px 4px 10px rgba(0, 0, 0, 0.2)\",\n            }}\n          >\n            ❤️\n          </Box>\n        </Box>\n      </Container>\n    </Box>\n  );\n};\n\nexport default TestimonialCard3;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EAGVC,SAAS,EACTC,WAAW,QACN,eAAe;AAItB,OAAOC,UAAU,MAAM,6CAA6C;AACpE,OAAOC,WAAW,MAAM,+CAA+C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExE,MAAMC,gBAAgB,GAAIC,KAGzB,IAAK;EACJ,oBACEF,OAAA,CAACP,GAAG;IACFU,GAAG,EAAED,KAAK,CAACE,MAAO;IAClBC,EAAE,EAAE;MACFC,eAAe,EAAE,OAAOC,OAAO,CAAC,gDAAgD,CAAC,GAAG;MACpFC,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE,WAAW;MAC7BC,YAAY,EAAE,CAAC;MACfC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,QAAQ;MACnBC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE;IACb,CAAE;IAAAC,QAAA,eAEFtB,OAAA,CAACL,SAAS;MAAA2B,QAAA,gBACRtB,OAAA,CAACJ,WAAW;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACf1B,OAAA,CAACP,GAAG;QACFY,EAAE,EAAE;UACFsB,QAAQ,EAAE,UAAU;UACpBZ,OAAO,EAAE,MAAM;UACfG,UAAU,EAAE,QAAQ;UACpBD,cAAc,EAAE,QAAQ;UACxBJ,OAAO,EAAE,CAAC;UACVF,YAAY,EAAE,CAAC;UACfU,SAAS,EAAE,iCAAiC;UAC5CO,eAAe,EAAE,MAAM;UACvBR,KAAK,EAAE,GAAG;UACVD,MAAM,EAAE,GAAG;UACXU,MAAM,EAAE;QACV,CAAE;QAAAP,QAAA,gBAmBFtB,OAAA,CAACP,GAAG;UACFY,EAAE,EAAE;YACFyB,MAAM,EAAE,CAAC;YACTf,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBE,UAAU,EAAE;UACd,CAAE;UAAAI,QAAA,gBAGFtB,OAAA,CAACP,GAAG;YAACY,EAAE,EAAE;cAAEU,OAAO,EAAE,MAAM;cAAEG,UAAU,EAAE,QAAQ;cAAEa,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,GAEvDpB,KAAK,CAAC8B,cAAc,CAACC,UAAU,iBAC9BjC,OAAA,CAACH,UAAU;cACTqC,YAAY,EAAEhC,KAAK,CAAC8B,cAAc,CAACG,aAAc;cACjDC,QAAQ,EAAElC,KAAK,CAAC8B,cAAc,CAACK,YAAa;cAC5CC,KAAK,EAAE;gBACLlB,KAAK,EAAE,EAAE;gBACTD,MAAM,EAAE,EAAE;gBACVU,MAAM,EAAE,aAAa;gBACrBU,UAAU,EAAE;cACd;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACF,eACD1B,OAAA,CAACP,GAAG;cAAA6B,QAAA,eACFtB,OAAA,CAACN,UAAU;gBAAC8C,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAAAnB,QAAA,EACvCpB,KAAK,CAAC8B,cAAc,CAACK;cAAY;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLxB,KAAK,CAAC8B,cAAc,CAACU,UAAU,iBAC9B1C,OAAA,CAACP,GAAG;YACFY,EAAE,EAAE;cACFU,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,QAAQ;cACxBc,EAAE,EAAE;YACN,CAAE;YAAAT,QAAA,eAEFtB,OAAA,CAACF,WAAW;cAAC6C,UAAU,EAAEzC,KAAK,CAAC8B,cAAc,CAACW;YAAW;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CACN,eAGD1B,OAAA,CAACN,UAAU;YAAC8C,OAAO,EAAC,OAAO;YAAC5B,KAAK,EAAC,gBAAgB;YAACmB,EAAE,EAAE,CAAE;YAAAT,QAAA,EACtDpB,KAAK,CAAC8B,cAAc,CAACY;UAAO;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAGb1B,OAAA,CAACN,UAAU;YACT8C,OAAO,EAAC,IAAI;YACZnC,EAAE,EAAE;cACFwC,QAAQ,EAAE,MAAM;cAChBJ,UAAU,EAAE,GAAG;cACfK,SAAS,EAAE,MAAM;cACjBlC,KAAK,EAAE;YACT,CAAE;YAAAU,QAAA,GACH,SACG,EAACpB,KAAK,CAAC8B,cAAc,CAACK,YAAY;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGN1B,OAAA,CAACP,GAAG;UACFY,EAAE,EAAE;YACFsB,QAAQ,EAAE,UAAU;YACpBoB,GAAG,EAAE,OAAO;YACZC,KAAK,EAAE,OAAO;YACd5B,KAAK,EAAE,EAAE;YACTD,MAAM,EAAE,EAAE;YACVS,eAAe,EAAE,SAAS;YAC1BjB,YAAY,EAAE,KAAK;YACnBI,OAAO,EAAE,MAAM;YACfG,UAAU,EAAE,QAAQ;YACpBD,cAAc,EAAE,QAAQ;YACxBL,KAAK,EAAE,MAAM;YACbS,SAAS,EAAE;UACb,CAAE;UAAAC,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACuB,EAAA,GAhJIhD,gBAAgB;AAkJtB,eAAeA,gBAAgB;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}