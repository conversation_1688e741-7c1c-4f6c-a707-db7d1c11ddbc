import React, { useContext, useEffect, useState } from "react";
import {
  Button,
  Modal,
  MenuItem,
  FormControl,
  InputLabel,
  ListItemText,
  OutlinedInput,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  FormControlLabel,
  Box,
  Dialog,
  AppBar,
  IconButton,
  Toolbar,
} from "@mui/material";
import Select, { SelectChangeEvent } from "@mui/material/Select";
import {
  IBusinessGroup,
  IBusinessGroupsResponseModel,
} from "../../../interfaces/response/IBusinessGroupsResponseModel";
import BusinessService from "../../../services/business/business.service";
import {
  ILocation,
  ILocationsListResponseModel,
} from "../../../interfaces/response/ILocationsListResponseModel";
import { LoadingContext } from "../../../context/loading.context";
import {
  IBusiness,
  IBusinessListResponseModel,
} from "../../../interfaces/response/IBusinessListResponseModel";
import { useDispatch, useSelector } from "react-redux";
import { Formik } from "formik";
import * as yup from "yup";
import { RoleType } from "../../../constants/dbConstant.constant";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CloseIcon from "@mui/icons-material/Close";
import { IGoogleCreatePost } from "../../../interfaces/request/IGoogleCreatePost";
import Card from "@mui/material/Card";
import { CardContent } from "@mui/material";
import { CardMedia, Grid } from "@mui/material";
import { ArrowBackIos, ArrowForwardIos } from "@mui/icons-material";
import LinearProgressWithLabel from "../../../components/LinearProgressWithLabel/LinearProgressWithLabel.component";
import { FormHelperText, InputAdornment } from "@mui/material";
import { TOPIC_TYPES } from "../../../constants/application.constant";
import { ISchedulePostRequestModel } from "../../../interfaces/request/ISchedulePostRequestModel";
import Stack from "@mui/material/Stack";

//Css Import
// import "../createSocialPost/components/submitPost.component.style.css";

type CreatePostData = {
  googleRequest: IGoogleCreatePost;
  schedule?: any;
  images: any;
};

export interface ICreatePostLocation {
  businessId: number;
  accountId: string[];
  locationId: ILocation[];
}

export interface IModalWithSelect {
  isShow: boolean;
  closeModal?: () => void | undefined;
  createPostModel?: CreatePostData;
  savePosts?: (
    createGooglePostList: ISelectionLocationWithPost[]
  ) => Promise<void> | undefined;
}

export interface ISelectedLocations {
  businessId: number;
  businessName: string;
  accountId: number;
  accountName: string;
  locationId: number;
  locationName: string;
  status?: boolean;
  viewUrl?: string;
}

export interface ISelectionLocationWithPost {
  locationInfo: ISelectedLocations;
  createGooglePost: IGoogleCreatePost;
  scheduleLater: ISchedulePostRequestModel | null;
}

const SubmitPost = (props: IModalWithSelect) => {
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const { isShow, closeModal, createPostModel, savePosts } = props;
  const [open, setOpen] = useState(isShow);
  const [selectedValue, setSelectedValue] = useState(""); // Single select
  const [selectedOptions, setSelectedOptions] = useState([]); // Multi-select
  const _businessService = new BusinessService(dispatch);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  const [businessGroups, setBusinessGroups] = useState<IBusinessGroup[]>([]);
  const [locations, setLocations] = useState<ILocation[]>([]);
  const [businessList, setBusinessList] = useState<IBusiness[]>([]);
  const { setLoading } = useContext(LoadingContext);
  const handleDropdownChange = (event: any) => {
    setSelectedValue(event.target.value);
  };

  const [selectedLocationData, setSelectedLocationData] = useState<
    ISelectionLocationWithPost[]
  >([]);

  const handleMultiSelectChange = (event: any) => {
    setSelectedOptions(event.target.value);
  };

  const [selectAllLocations, setSelectAllLocations] = useState(false);
  const [expandedAccordions, setExpandedAccordions] = useState<string[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleNext = () => {
    if (createPostModel) {
      setCurrentIndex(
        (prevIndex) =>
          (prevIndex + 1) % createPostModel?.googleRequest.media.length
      );
    }
  };

  const handlePrev = () => {
    if (createPostModel) {
      setCurrentIndex(
        (prevIndex) =>
          (prevIndex - 1 + createPostModel?.googleRequest.media.length) %
          createPostModel?.googleRequest.media.length
      );
    }
  };

  const DEBUG_MODE = !(process.env.NODE_ENV === "development");

  const SubmitPostSchema = yup.object().shape({
    businessId: yup
      .number()
      .typeError("Business ID must be a number") // Ensure it's a number
      .moreThan(0, "Business ID is required")
      .required("Business ID is required"), // Ensure > 0.required("Business ID is required"),
    accountId: yup
      .array()
      .of(yup.string().required())
      .min(1, "At least one account ID is required")
      .required(),
    locationId: yup
      .array()
      .of(yup.object().shape({})) // Any object type allowed
      .min(1, "At least one location is required")
      .required("Location array is required"),
  });

  const INITIAL_VALUES = {
    businessId: 0,
    accountId: [],
    locationId: [],
  };
  const [initialValues, setInitialValues] =
    useState<ICreatePostLocation>(INITIAL_VALUES);

  const getBusiness = async () => {
    try {
      setLoading(true);
      let businessListResp: IBusinessListResponseModel =
        await _businessService.getBusiness(userInfo.id);
      if (businessListResp.list.length > 0) {
        setBusinessList(businessListResp.list);
      }
    } catch (error) {}

    setLoading(false);
  };

  const getBusinessGroups = async () => {
    try {
      setLoading(true);
      let businessGroups: IBusinessGroupsResponseModel =
        await _businessService.getBusinessGroups(userInfo.id);
      if (businessGroups.data.length > 0) {
        setBusinessGroups(businessGroups.data);
      }
    } catch (error) {}

    setLoading(false);
  };

  const getLocationsList = async () => {
    try {
      setLoading(true);
      let locationsList: ILocationsListResponseModel =
        await _businessService.getLocations(userInfo.id);
      if (locationsList.list.length > 0) {
        setLocations(locationsList.list);
      }
    } catch (error) {}

    setLoading(false);
  };

  useEffect(() => {
    getBusiness();
    getLocationsList();
  }, []);

  // Effect to update selectAllLocations state when initialValues changes
  useEffect(() => {
    if (initialValues && initialValues.locationId && initialValues.accountId) {
      const filteredLocations = locations.filter((x) =>
        initialValues.accountId.includes(x.gmbAccountId)
      );

      if (
        filteredLocations.length > 0 &&
        initialValues.locationId.length === filteredLocations.length
      ) {
        setSelectAllLocations(true);

        // Open all location accordions when all are selected
        const locationIds = filteredLocations.map((loc) => loc.gmbLocationId);
        setExpandedAccordions(locationIds);
      } else {
        setSelectAllLocations(false);
      }
    }
  }, [initialValues.locationId, initialValues.accountId, locations]);

  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };

  const _handleCreatePosts = async (
    values: ICreatePostLocation,
    formikHelpers: any
  ) => {
    if (!DEBUG_MODE) {
      const isValid = await SubmitPostSchema.isValid(values);
      console.log(props);
      if (isValid) {
        savePosts && savePosts(selectedLocationData);
      }
    }
  };

  const handleValidation = async (values: ICreatePostLocation) => {
    try {
      // Validate form using Yup schema
      await SubmitPostSchema.validate(values, { abortEarly: false });
      console.log("Form Submitted Successfully ✅", values);
    } catch (validationError: any) {
      // Convert Yup validation error into Formik-compatible errors
      const formattedErrors = validationError.inner.reduce(
        (acc: any, err: any) => {
          acc[err.path] = err.message;
          return acc;
        },
        {}
      );

      return formattedErrors;
    }
  };

  return (
    <Formik
      enableReinitialize
      initialValues={{ ...initialValues }}
      validationSchema={SubmitPostSchema}
      onSubmit={(values, formikHelpers) => {
        _handleCreatePosts(values, formikHelpers);
      }}
      validate={handleValidation}
    >
      {({
        values,
        errors,
        touched,
        handleChange,
        handleBlur,
        handleSubmit,
        setFieldValue,
        handleReset,
        isSubmitting,
        isValid,
        setSubmitting,
        /* and other goodies */
      }) => (
        <form
          onSubmit={handleSubmit}
          onReset={handleReset}
          className="commonModal"
        >
          <Box className="height100">
            <Typography
              id="modal-modal-title"
              variant="h6"
              component="h2"
              className="modal-modal-title"
            >
              Select Locations to Create Post
            </Typography>

            <Box
              id="modal-modal-description"
              className="modal-modal-description"
            >
              <Box>
                <Box className="commonInput qwe">
                  {" "}
                  <FormControl variant="outlined" fullWidth>
                    <InputLabel id="outlined-country-dropdown-label">
                      Business
                    </InputLabel>

                    <Select
                      fullWidth
                      id="businessId"
                      label="Business"
                      value={values.businessId.toString()}
                      onChange={(evt: SelectChangeEvent) => {
                        getBusinessGroups();
                        setFieldValue("accountId", []);
                        setFieldValue("locationId", []);
                        setTimeout(() => {
                          setFieldValue("businessId", +evt.target.value);
                        }, 1000);
                      }}
                      sx={{
                        backgroundColor: "var(--whiteColor)",
                        borderRadius: "5px",
                      }}
                    >
                      <MenuItem value={0}>Select</MenuItem>
                      {businessList &&
                        businessList.map((business: IBusiness) => (
                          <MenuItem
                            key={business.id}
                            value={business.id.toString()}
                          >
                            {business.businessName}
                          </MenuItem>
                        ))}
                    </Select>
                    {errors.businessId && touched.businessId && (
                      <FormHelperText className="errorMessage">
                        {errors.businessId}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box>
                <Box className="commonInput">
                  <FormControl variant="outlined" fullWidth>
                    <InputLabel id="demo-multiple-name-label">
                      Groups
                    </InputLabel>
                    <Select
                      id="accountId"
                      multiple={true}
                      value={values.accountId}
                      onChange={(event: SelectChangeEvent<string[]>) => {
                        const {
                          target: { value },
                        } = event;
                        setFieldValue("accountId", value);
                      }}
                      input={<OutlinedInput label="Business" />}
                      MenuProps={MenuProps}
                      sx={{
                        backgroundColor: "var(--whiteColor)",
                        borderRadius: "5px",
                      }}
                    >
                      {businessGroups
                        .filter((x) => x.businessId === values.businessId)
                        .map((businessGroup: IBusinessGroup) => (
                          <MenuItem
                            key={businessGroup.accountId}
                            value={businessGroup.accountId}
                          >
                            {businessGroup.accountName}
                          </MenuItem>
                        ))}
                    </Select>
                    <Box sx={{ display: "flex" }}>
                      {values.accountId.map((value: string) => (
                        <Chip
                          key={value}
                          label={
                            businessGroups.filter(
                              (x) => x.accountId === value
                            )[0].accountName || ""
                          }
                          clickable
                          style={{
                            margin: 2,
                            backgroundColor: "#FFF",
                          }}
                          onDelete={(e) => {
                            setFieldValue(
                              "accountId",
                              values.accountId.filter((x) => x != value)
                            );

                            setFieldValue(
                              "locationId",
                              values.locationId.filter(
                                (x: ILocation) => x.gmbAccountId != value
                              )
                            );
                          }}
                          onClick={() => console.log("clicked chip")}
                          variant="outlined"
                          color="primary"
                        />
                      ))}
                    </Box>
                    {errors.accountId && touched.accountId && (
                      <FormHelperText className="errorMessage">
                        {errors.accountId}
                      </FormHelperText>
                    )}
                    {errors &&
                      errors.locationId &&
                      Object.keys(errors).length > 0 &&
                      Object.keys(errors).length == 1 &&
                      Object.entries(errors).map(
                        ([field, errorMsg]) =>
                          field === "locationId" && (
                            <FormHelperText
                              className="errorMessage"
                              key={field}
                            >{`${errorMsg}`}</FormHelperText>
                          )
                      )}
                    {/* Select All Accordion */}
                    {locations.filter((x) =>
                      values.accountId.includes(x.gmbAccountId)
                    ).length > 0 && (
                      <Accordion
                        expanded={false}
                        sx={{
                          "&.MuiAccordion-root": {
                            "&:before": {
                              display: "none",
                            },
                          },
                          "& .MuiAccordionSummary-root": {
                            minHeight: "48px",
                            "&.Mui-expanded": {
                              minHeight: "48px",
                            },
                          },
                        }}
                      >
                        <AccordionSummary
                          expandIcon={null}
                          onClick={(e) => {
                            // Prevent accordion from expanding/collapsing
                            e.stopPropagation();
                          }}
                        >
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={
                                  selectAllLocations ||
                                  (values.locationId.length > 0 &&
                                    values.locationId.length ===
                                      locations.filter((x) =>
                                        values.accountId.includes(
                                          x.gmbAccountId
                                        )
                                      ).length)
                                }
                                onChange={(
                                  event: React.ChangeEvent<HTMLInputElement>,
                                  checked: boolean
                                ) => {
                                  event.stopPropagation(); // Prevent accordion from toggling when checkbox is clicked
                                  setSelectAllLocations(checked);

                                  // Get all location IDs
                                  const allLocations = locations.filter((x) =>
                                    values.accountId.includes(x.gmbAccountId)
                                  );

                                  if (checked) {
                                    // Select all locations
                                    setFieldValue("locationId", allLocations);

                                    // Open all location accordions
                                    const locationIds = allLocations.map(
                                      (loc) => loc.gmbLocationId
                                    );
                                    setExpandedAccordions([...locationIds]);

                                    // Add all locations to selectedLocationData
                                    if (createPostModel) {
                                      const newSelectedLocationData: ISelectionLocationWithPost[] =
                                        [];

                                      allLocations.forEach((location) => {
                                        let postData = {
                                          ...createPostModel.googleRequest,
                                        };

                                        if (
                                          postData &&
                                          postData.topicType ===
                                            TOPIC_TYPES.Event
                                        ) {
                                          postData.summary = postData.summary
                                            .split("{{Address}}")
                                            .join(location.gmbLocationName);
                                          postData.summary = postData.summary
                                            .split("{{Area}}")
                                            .join(location.locality);
                                          postData.summary = postData.summary
                                            .split("{{Pincode}}")
                                            .join(location.postalCode);
                                        }

                                        const accountInfo =
                                          businessGroups.filter(
                                            (x: IBusinessGroup) =>
                                              x.accountId ===
                                              location.gmbAccountId
                                          )[0];

                                        const business = businessList.filter(
                                          (x: IBusiness) =>
                                            x.id === accountInfo.businessId
                                        )[0];

                                        newSelectedLocationData.push({
                                          locationInfo: {
                                            businessId: business.id,
                                            businessName: business.businessName,
                                            accountId: accountInfo.id,
                                            accountName:
                                              accountInfo.accountName,
                                            locationId: location.id,
                                            locationName:
                                              location.gmbLocationName,
                                          },
                                          createGooglePost: postData,
                                          scheduleLater: null,
                                        });
                                      });

                                      setSelectedLocationData(
                                        newSelectedLocationData
                                      );
                                    }
                                  } else {
                                    // Unselect all locations
                                    setFieldValue("locationId", []);
                                    setSelectedLocationData([]);

                                    // Close all accordions
                                    setExpandedAccordions([]);
                                  }
                                }}
                                onClick={(e) => e.stopPropagation()} // Prevent accordion from toggling when checkbox is clicked
                              />
                            }
                            label="Select All Locations"
                            sx={{ flexGrow: 1 }}
                            onClick={(e) => e.stopPropagation()} // Prevent accordion from toggling when label is clicked
                          />
                        </AccordionSummary>
                      </Accordion>
                    )}

                    {/* Individual Location Accordions */}
                    {locations
                      .filter((x) => values.accountId.includes(x.gmbAccountId))
                      .map((location: ILocation, index: number) => (
                        <Accordion
                          key={location.gmbLocationId}
                          expanded={expandedAccordions.includes(
                            location.gmbLocationId
                          )}
                        >
                          <AccordionSummary
                            expandIcon={<ExpandMoreIcon />}
                            onClick={() => {
                              // Toggle the expansion state of this accordion
                              if (
                                expandedAccordions.includes(
                                  location.gmbLocationId
                                )
                              ) {
                                setExpandedAccordions(
                                  expandedAccordions.filter(
                                    (id) => id !== location.gmbLocationId
                                  )
                                );
                              } else {
                                setExpandedAccordions([
                                  ...expandedAccordions,
                                  location.gmbLocationId,
                                ]);
                              }
                            }}
                          >
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={
                                    values.locationId.filter(
                                      (x: ILocation) =>
                                        x.gmbLocationId ===
                                        location.gmbLocationId
                                    ).length > 0
                                  }
                                  onChange={(
                                    event: React.ChangeEvent<HTMLInputElement>,
                                    checked: boolean
                                  ) => {
                                    event.stopPropagation(); // Prevent accordion from toggling when checkbox is clicked
                                    if (checked) {
                                      // Add this location to the selected locations
                                      const newLocationIds = [
                                        ...values.locationId,
                                        location,
                                      ];
                                      setFieldValue(
                                        "locationId",
                                        newLocationIds
                                      );

                                      // Open this accordion when selected
                                      if (
                                        !expandedAccordions.includes(
                                          location.gmbLocationId
                                        )
                                      ) {
                                        setExpandedAccordions([
                                          ...expandedAccordions,
                                          location.gmbLocationId,
                                        ]);
                                      }

                                      // Check if all locations are now selected
                                      const filteredLocations =
                                        locations.filter((x) =>
                                          values.accountId.includes(
                                            x.gmbAccountId
                                          )
                                        );
                                      if (
                                        newLocationIds.length ===
                                        filteredLocations.length
                                      ) {
                                        setSelectAllLocations(true);
                                      }

                                      if (createPostModel) {
                                        let postData = {
                                          ...createPostModel.googleRequest,
                                        };

                                        if (
                                          postData &&
                                          postData.topicType ===
                                            TOPIC_TYPES.Event
                                        ) {
                                          postData.summary = postData.summary
                                            .split("{{Address}}")
                                            .join(location.gmbLocationName);
                                          postData.summary = postData.summary
                                            .split("{{Area}}")
                                            .join(location.locality);
                                          postData.summary = postData.summary
                                            .split("{{Pincode}}")
                                            .join(location.postalCode);
                                        }

                                        const accountInfo =
                                          businessGroups.filter(
                                            (x: IBusinessGroup) =>
                                              x.accountId ===
                                              location.gmbAccountId
                                          )[0];
                                        const business = businessList.filter(
                                          (x: IBusiness) =>
                                            x.id === accountInfo.businessId
                                        )[0];

                                        const selectedLocationsDataInfo: ISelectionLocationWithPost =
                                          {
                                            locationInfo: {
                                              businessId: business.id,
                                              businessName:
                                                business.businessName,
                                              accountId: accountInfo.id,
                                              accountName:
                                                accountInfo.accountName,
                                              locationId: location.id,
                                              locationName:
                                                location.gmbLocationName,
                                            },
                                            createGooglePost: postData,
                                            scheduleLater: null,
                                          };

                                        setSelectedLocationData([
                                          ...selectedLocationData,
                                          selectedLocationsDataInfo,
                                        ]);
                                      }
                                    } else {
                                      // Remove this location from the selected locations
                                      const newLocationIds =
                                        values.locationId.filter(
                                          (x: ILocation) =>
                                            x.gmbLocationId !=
                                            location.gmbLocationId
                                        );
                                      setFieldValue(
                                        "locationId",
                                        newLocationIds
                                      );

                                      // Close this accordion when unselected
                                      setExpandedAccordions(
                                        expandedAccordions.filter(
                                          (id) => id !== location.gmbLocationId
                                        )
                                      );

                                      // If any location is unselected, uncheck the "Select All" checkbox
                                      setSelectAllLocations(false);

                                      // Also remove from selectedLocationData
                                      setSelectedLocationData(
                                        selectedLocationData.filter(
                                          (item) =>
                                            item.locationInfo.locationId !==
                                            location.id
                                        )
                                      );
                                    }

                                    // Prevent the click from propagating to the accordion
                                    event.stopPropagation();
                                  }}
                                />
                              }
                              label={location.gmbLocationName}
                              sx={{ flexGrow: 1 }}
                              onClick={(e) => e.stopPropagation()} // Prevent accordion from toggling when label is clicked
                            />
                          </AccordionSummary>
                          <AccordionDetails>
                            <TableContainer component={Paper}>
                              <Table>
                                <TableHead>
                                  <TableRow>
                                    <TableCell>
                                      <b>Locality</b>
                                    </TableCell>
                                    <TableCell>
                                      <b>Zip Code</b>
                                    </TableCell>
                                    <TableCell>
                                      <b>Address</b>
                                    </TableCell>
                                    <TableCell>
                                      <b>Primary Phone</b>
                                    </TableCell>
                                  </TableRow>
                                </TableHead>
                                <TableBody>
                                  <TableRow key={index}>
                                    <TableCell>{location.locality}</TableCell>
                                    <TableCell>{location.postalCode}</TableCell>
                                    <TableCell>
                                      {location.gmbLocationName}
                                    </TableCell>
                                    <TableCell>
                                      {location.primaryPhone}
                                    </TableCell>
                                  </TableRow>
                                </TableBody>
                              </Table>
                            </TableContainer>
                          </AccordionDetails>
                        </Accordion>
                      ))}
                    {/* <Grid xs={6} className="height100">
                  <Box className="lceRight">
                    <Grid container spacing={2}>
                      {selectedLocationData &&
                        selectedLocationData.map(
                          (
                            createPostModele: ISelectionLocationWithPost,
                            index: number
                          ) => {
                            return (
                              <Grid xs={12} md={6}>
                                <Card
                                  sx={{
                                    boxShadow: 3,
                                    minHeight: 450,
                                    overflow: "hidden",
                                    borderRadius: 2,
                                    margin: 1,
                                  }}
                                >
                                  {createPostModele && (
                                    <CardMedia
                                      component="div"
                                      sx={{
                                        display: "flex",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        position: "relative",
                                      }}
                                    >
                                      <img
                                        src={URL.createObjectURL(
                                          createPostModele.createGooglePost
                                            .media[
                                            currentIndex
                                          ] as unknown as MediaSource
                                        )}
                                        alt={`Image ${currentIndex + 1}`}
                                        style={{
                                          width: "100%",
                                          height: "300px",
                                          objectFit: "fill",
                                          borderRadius: "8px",
                                          transition:
                                            "opacity 0.5s ease-in-out",
                                        }}
                                        referrerPolicy="no-referrer"
                                      />

                                      {createPostModele.createGooglePost.media
                                        .length > 1 &&
                                        currentIndex > 0 && (
                                          <IconButton
                                            onClick={handlePrev}
                                            sx={{
                                              position: "absolute",
                                              left: 10,
                                              backgroundColor:
                                                "rgba(0,0,0,0.5)",
                                              color: "white",
                                              "&:hover": {
                                                backgroundColor:
                                                  "rgba(0,0,0,0.7)",
                                              },
                                            }}
                                          >
                                            <ArrowBackIos />
                                          </IconButton>
                                        )}

                                      {createPostModele.createGooglePost.media
                                        .length > 1 &&
                                        currentIndex <
                                          createPostModele.createGooglePost
                                            .media.length && (
                                          <IconButton
                                            onClick={handleNext}
                                            sx={{
                                              position: "absolute",
                                              right: 10,
                                              backgroundColor:
                                                "rgba(0,0,0,0.5)",
                                              color: "white",
                                              "&:hover": {
                                                backgroundColor:
                                                  "rgba(0,0,0,0.7)",
                                              },
                                            }}
                                          >
                                            <ArrowForwardIos />
                                          </IconButton>
                                        )}
                                    </CardMedia>
                                  )}

                                  <CardContent>
                                    {createPostModele.createGooglePost
                                      .event && (
                                      <Typography
                                        variant="subtitle1"
                                        fontWeight="bold"
                                      >
                                        {
                                          createPostModele.createGooglePost
                                            .event.title
                                        }
                                      </Typography>
                                    )}

                                    {createPostModele && (
                                      <Typography
                                        variant="body2"
                                        color="textSecondary"
                                      >
                                        {
                                          createPostModele.createGooglePost
                                            .summary
                                        }
                                      </Typography>
                                    )}

                                    <Typography
                                      variant="caption"
                                      display="block"
                                      sx={{ mt: 1 }}
                                    >
                                      Available On: 1 Location
                                    </Typography>
                                  </CardContent>
                                </Card>
                              </Grid>
                            );
                          }
                        )}
                    </Grid>
                  </Box>
                </Grid> */}
                  </FormControl>
                </Box>
              </Box>
            </Box>

            <Box className="">
              <Stack direction="row" className="commonFooter">
                <Button
                  variant="outlined"
                  className="secondaryOutlineBtn"
                  onClick={closeModal}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  className="primaryFillBtn"
                >
                  Save
                </Button>
              </Stack>
            </Box>
          </Box>
        </form>
      )}
    </Formik>
  );
};

export default SubmitPost;
