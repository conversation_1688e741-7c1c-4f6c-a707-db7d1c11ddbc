{"ast": null, "code": "import HttpHelperService from \"../httpHelper.service\";\nimport { REVIEW_SETTINGS_TEMPLATES, REVIEW_SETTINGS_TEMPLATE_BY_ID, REVIEW_SETTINGS_MAP_TEMPLATE, REVIEW_SETTINGS_AUTO_REPLY, REVIEW_SETTINGS_AUTO_REPLY_TEMPLATE } from \"../../constants/endPoints.constant\";\n\n// Interfaces\n\nclass ReviewSettingsService {\n  constructor(dispatch) {\n    this._httpHelperService = void 0;\n    // Get all reply templates for a user\n    this.getReplyTemplates = async (userId, businessId) => {\n      const url = businessId ? `${REVIEW_SETTINGS_TEMPLATES(userId)}?businessId=${businessId}` : REVIEW_SETTINGS_TEMPLATES(userId);\n      return await this._httpHelperService.get(url);\n    };\n    // Create a new reply template\n    this.createReplyTemplate = async (userId, templateData) => {\n      return await this._httpHelperService.post(REVIEW_SETTINGS_TEMPLATES(userId), templateData);\n    };\n    // Update a reply template\n    this.updateReplyTemplate = async (userId, templateId, templateData) => {\n      return await this._httpHelperService.put(REVIEW_SETTINGS_TEMPLATE_BY_ID(userId, templateId), templateData);\n    };\n    // Delete a reply template\n    this.deleteReplyTemplate = async (userId, templateId) => {\n      return await this._httpHelperService.delete(REVIEW_SETTINGS_TEMPLATE_BY_ID(userId, templateId));\n    };\n    // Map template to businesses\n    this.mapTemplateToBusinesses = async (userId, templateId, businessIds) => {\n      return await this._httpHelperService.post(REVIEW_SETTINGS_MAP_TEMPLATE(userId, templateId), {\n        businessIds\n      });\n    };\n    // Get auto-reply settings for a business\n    this.getAutoReplySettings = async businessId => {\n      return await this._httpHelperService.get(REVIEW_SETTINGS_AUTO_REPLY(businessId));\n    };\n    // Update auto-reply settings\n    this.updateAutoReplySettings = async (businessId, settings) => {\n      return await this._httpHelperService.put(REVIEW_SETTINGS_AUTO_REPLY(businessId), settings);\n    };\n    // Get template for auto-reply (used by auto-reply system)\n    this.getTemplateForAutoReply = async (businessId, starRating) => {\n      return await this._httpHelperService.get(REVIEW_SETTINGS_AUTO_REPLY_TEMPLATE(businessId, starRating));\n    };\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n}\nexport default ReviewSettingsService;", "map": {"version": 3, "names": ["HttpHelperService", "REVIEW_SETTINGS_TEMPLATES", "REVIEW_SETTINGS_TEMPLATE_BY_ID", "REVIEW_SETTINGS_MAP_TEMPLATE", "REVIEW_SETTINGS_AUTO_REPLY", "REVIEW_SETTINGS_AUTO_REPLY_TEMPLATE", "ReviewSettingsService", "constructor", "dispatch", "_httpHelperService", "getReplyTemplates", "userId", "businessId", "url", "get", "createReplyTemplate", "templateData", "post", "updateReplyTemplate", "templateId", "put", "deleteReplyTemplate", "delete", "mapTemplateToBusinesses", "businessIds", "getAutoReplySettings", "updateAutoReplySettings", "settings", "getTemplateForAutoReply", "starRating"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/reviewSettings/reviewSettings.service.tsx"], "sourcesContent": ["import { Dispatch } from \"react\";\nimport HttpHelperService from \"../httpHelper.service\";\nimport { Action } from \"redux\";\nimport {\n  REVIEW_SETTINGS_TEMPLATES,\n  REVIEW_SETTINGS_TEMPLATE_BY_ID,\n  REVIEW_SETTINGS_MAP_TEMPLATE,\n  REVIEW_SETTINGS_AUTO_REPLY,\n  REVIEW_SETTINGS_AUTO_REPLY_TEMPLATE,\n} from \"../../constants/endPoints.constant\";\n\n// Interfaces\nexport interface IReplyTemplate {\n  id?: number;\n  created_by: number;\n  star_rating: number;\n  template_name: string;\n  template_content: string;\n  is_default: boolean;\n  business_id?: number;\n  business_template_active?: boolean;\n  created_at?: string;\n  updated_at?: string;\n}\n\nexport interface ICreateReplyTemplateRequest {\n  starRating: number;\n  templateName: string;\n  templateContent: string;\n  isDefault: boolean;\n  businessId?: number;\n}\n\nexport interface IUpdateReplyTemplateRequest {\n  starRating: number;\n  templateName: string;\n  templateContent: string;\n  isDefault: boolean;\n}\n\nexport interface IAutoReplySettings {\n  id?: number;\n  business_id: number;\n  is_enabled: boolean;\n  enabled_star_ratings: number[];\n  delay_minutes: number;\n  only_business_hours: boolean;\n  business_hours_start: string;\n  business_hours_end: string;\n  created_at?: string;\n  updated_at?: string;\n}\n\nexport interface IMapTemplateToBusinessesRequest {\n  businessIds: number[];\n}\n\nexport interface IReplyTemplatesResponse {\n  message: string;\n  data: IReplyTemplate[];\n}\n\nexport interface IAutoReplySettingsResponse {\n  message: string;\n  data: IAutoReplySettings | null;\n}\n\nexport interface IApiResponse {\n  message: string;\n  data?: any;\n}\n\nclass ReviewSettingsService {\n  _httpHelperService;\n\n  constructor(dispatch: Dispatch<Action>) {\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  // Get all reply templates for a user\n  getReplyTemplates = async (\n    userId: number,\n    businessId?: number\n  ): Promise<IReplyTemplatesResponse> => {\n    const url = businessId\n      ? `${REVIEW_SETTINGS_TEMPLATES(userId)}?businessId=${businessId}`\n      : REVIEW_SETTINGS_TEMPLATES(userId);\n    return await this._httpHelperService.get(url);\n  };\n\n  // Create a new reply template\n  createReplyTemplate = async (\n    userId: number,\n    templateData: ICreateReplyTemplateRequest\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.post(\n      REVIEW_SETTINGS_TEMPLATES(userId),\n      templateData\n    );\n  };\n\n  // Update a reply template\n  updateReplyTemplate = async (\n    userId: number,\n    templateId: number,\n    templateData: IUpdateReplyTemplateRequest\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.put(\n      REVIEW_SETTINGS_TEMPLATE_BY_ID(userId, templateId),\n      templateData\n    );\n  };\n\n  // Delete a reply template\n  deleteReplyTemplate = async (\n    userId: number,\n    templateId: number\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.delete(\n      REVIEW_SETTINGS_TEMPLATE_BY_ID(userId, templateId)\n    );\n  };\n\n  // Map template to businesses\n  mapTemplateToBusinesses = async (\n    userId: number,\n    templateId: number,\n    businessIds: number[]\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.post(\n      REVIEW_SETTINGS_MAP_TEMPLATE(userId, templateId),\n      { businessIds }\n    );\n  };\n\n  // Get auto-reply settings for a business\n  getAutoReplySettings = async (\n    businessId: number\n  ): Promise<IAutoReplySettingsResponse> => {\n    return await this._httpHelperService.get(\n      REVIEW_SETTINGS_AUTO_REPLY(businessId)\n    );\n  };\n\n  // Update auto-reply settings\n  updateAutoReplySettings = async (\n    businessId: number,\n    settings: Omit<\n      IAutoReplySettings,\n      \"id\" | \"business_id\" | \"created_at\" | \"updated_at\"\n    >\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.put(\n      REVIEW_SETTINGS_AUTO_REPLY(businessId),\n      settings\n    );\n  };\n\n  // Get template for auto-reply (used by auto-reply system)\n  getTemplateForAutoReply = async (\n    businessId: number,\n    starRating: number\n  ): Promise<IApiResponse> => {\n    return await this._httpHelperService.get(\n      REVIEW_SETTINGS_AUTO_REPLY_TEMPLATE(businessId, starRating)\n    );\n  };\n}\n\nexport default ReviewSettingsService;\n"], "mappings": "AACA,OAAOA,iBAAiB,MAAM,uBAAuB;AAErD,SACEC,yBAAyB,EACzBC,8BAA8B,EAC9BC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,mCAAmC,QAC9B,oCAAoC;;AAE3C;;AA6DA,MAAMC,qBAAqB,CAAC;EAG1BC,WAAWA,CAACC,QAA0B,EAAE;IAAA,KAFxCC,kBAAkB;IAMlB;IAAA,KACAC,iBAAiB,GAAG,OAClBC,MAAc,EACdC,UAAmB,KACkB;MACrC,MAAMC,GAAG,GAAGD,UAAU,GAClB,GAAGX,yBAAyB,CAACU,MAAM,CAAC,eAAeC,UAAU,EAAE,GAC/DX,yBAAyB,CAACU,MAAM,CAAC;MACrC,OAAO,MAAM,IAAI,CAACF,kBAAkB,CAACK,GAAG,CAACD,GAAG,CAAC;IAC/C,CAAC;IAED;IAAA,KACAE,mBAAmB,GAAG,OACpBJ,MAAc,EACdK,YAAyC,KACf;MAC1B,OAAO,MAAM,IAAI,CAACP,kBAAkB,CAACQ,IAAI,CACvChB,yBAAyB,CAACU,MAAM,CAAC,EACjCK,YACF,CAAC;IACH,CAAC;IAED;IAAA,KACAE,mBAAmB,GAAG,OACpBP,MAAc,EACdQ,UAAkB,EAClBH,YAAyC,KACf;MAC1B,OAAO,MAAM,IAAI,CAACP,kBAAkB,CAACW,GAAG,CACtClB,8BAA8B,CAACS,MAAM,EAAEQ,UAAU,CAAC,EAClDH,YACF,CAAC;IACH,CAAC;IAED;IAAA,KACAK,mBAAmB,GAAG,OACpBV,MAAc,EACdQ,UAAkB,KACQ;MAC1B,OAAO,MAAM,IAAI,CAACV,kBAAkB,CAACa,MAAM,CACzCpB,8BAA8B,CAACS,MAAM,EAAEQ,UAAU,CACnD,CAAC;IACH,CAAC;IAED;IAAA,KACAI,uBAAuB,GAAG,OACxBZ,MAAc,EACdQ,UAAkB,EAClBK,WAAqB,KACK;MAC1B,OAAO,MAAM,IAAI,CAACf,kBAAkB,CAACQ,IAAI,CACvCd,4BAA4B,CAACQ,MAAM,EAAEQ,UAAU,CAAC,EAChD;QAAEK;MAAY,CAChB,CAAC;IACH,CAAC;IAED;IAAA,KACAC,oBAAoB,GAAG,MACrBb,UAAkB,IACsB;MACxC,OAAO,MAAM,IAAI,CAACH,kBAAkB,CAACK,GAAG,CACtCV,0BAA0B,CAACQ,UAAU,CACvC,CAAC;IACH,CAAC;IAED;IAAA,KACAc,uBAAuB,GAAG,OACxBd,UAAkB,EAClBe,QAGC,KACyB;MAC1B,OAAO,MAAM,IAAI,CAAClB,kBAAkB,CAACW,GAAG,CACtChB,0BAA0B,CAACQ,UAAU,CAAC,EACtCe,QACF,CAAC;IACH,CAAC;IAED;IAAA,KACAC,uBAAuB,GAAG,OACxBhB,UAAkB,EAClBiB,UAAkB,KACQ;MAC1B,OAAO,MAAM,IAAI,CAACpB,kBAAkB,CAACK,GAAG,CACtCT,mCAAmC,CAACO,UAAU,EAAEiB,UAAU,CAC5D,CAAC;IACH,CAAC;IA1FC,IAAI,CAACpB,kBAAkB,GAAG,IAAIT,iBAAiB,CAACQ,QAAQ,CAAC;EAC3D;AA0FF;AAEA,eAAeF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}