const GMB_ACTIONS = require("../constants/gmb-actions");
const logger = require("../utils/logger");
const QandA = require("../models/QandA.models");
const { reqGMBApi } = require("../services/gmb.service");

const welcome = async (req, res) => {
  try {
    logger.logControllerAction("QandA", "welcome", req.requestId);
    logger.info("QandA welcome endpoint accessed", {
      requestId: req.requestId,
    });
    res.send({ message: "QandA Home Page" });
  } catch (error) {
    logger.error("Error in QandA welcome", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res
      .status(500)
      .json({ message: "Internal server error", error: error.message });
  }
};

const QandAList = (req, res) => {
  const userId = req.params.userId;
  const gmbLocationId = req.headers["x-gmb-location-id"];
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const offset = (page - 1) * limit;

  QandA.fetchAll(userId, gmbLocationId, limit, offset)
    .then((response) => {
      res.status(201).json({ message: "QandAs List!", list: response });
    })
    .catch((error) => {
      res.status(404).json({ message: "QandA Not Found!", error: error });
    });
};

const refreshQandA = async (req, res) => {
  const accountId = req.headers["x-gmb-account-id"];
  const locationId = req.headers["x-gmb-location-id"];
  try {
    const result = await reqGMBApi({
      req,
      action: GMB_ACTIONS.get_QandA,
      reqBodyData: { accountId, locationId },
    });
    if (result.success) {
      let QandA_Data = [];
      result.data.questions.forEach((question) => {
        QandA_Data.push({
          userId: 52,
          userName: question.author.displayName,
          accountId,
          locationId,
          gmbQuestionId: question.name.split("/").pop(),
          author: question.author.displayName,
          authorProfilePic: question.author.profilePhotoUri,
          question: question.text,
          createTime: question.createTime,
          updateTime: question.updateTime,
          answer: question.topAnswers ? question.topAnswers[0].text : "",
          statusId: 1,
        });
      });
      QandA.postQandA(QandA_Data);
      res
        .status(200)
        .json({ message: "QandA fetched", data: result.data.questions });
    } else {
      res
        .status(result.status)
        .json({ message: "Failed to fetch QandA", data: result.data });
    }
  } catch (error) {
    res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
};

const replyToQuestions = async (req, res) => {
  const { answer } = req.body;
  const questionId = req.headers["x-gmb-question-id"];
  const locationId = req.headers["x-gmb-location-id"];

  try {
    const result = await reqGMBApi({
      req,
      action: GMB_ACTIONS.upsert_QandAReply,
      reqBodyData: { locationId, questionId, answer },
    });
    const replyData = {
      answer: answer,
      gmbQuestionId: questionId,
      gmbLocationId: locationId,
    };
    const dbResult = await QandA.replyQandA(replyData);
    res.status(200).json({ message: "QandA reply successful!", data: result });
  } catch (error) {
    console.error("Error in replyToQuestions:", error);
    res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
};

module.exports = {
  welcome,
  QandAList,
  refreshQandA,
  replyToQuestions,
};
