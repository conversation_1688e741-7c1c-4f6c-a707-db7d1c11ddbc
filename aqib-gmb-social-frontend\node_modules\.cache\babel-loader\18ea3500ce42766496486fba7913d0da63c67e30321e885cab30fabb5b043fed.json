{"ast": null, "code": "import { createContext } from 'react';\nexport const LoadingContext = /*#__PURE__*/createContext({\n  setLoading: loading => {},\n  loading: false\n});", "map": {"version": 3, "names": ["createContext", "LoadingContext", "setLoading", "loading"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/context/loading.context.tsx"], "sourcesContent": ["import { createContext } from 'react';\n\nexport const LoadingContext = createContext({\n    setLoading: (loading: boolean) => { },\n    loading: false,\n});"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AAErC,OAAO,MAAMC,cAAc,gBAAGD,aAAa,CAAC;EACxCE,UAAU,EAAGC,OAAgB,IAAK,CAAE,CAAC;EACrCA,OAAO,EAAE;AACb,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}