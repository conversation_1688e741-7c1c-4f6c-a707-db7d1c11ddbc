const GMB_ACTIONS = require("../constants/gmb-actions");
const Location = require("../models/location.models");
const { reqGMBApi } = require("../services/gmb.service");
const { OAuth2Client } = require("google-auth-library");
const keys = require("../config/OAuthKey.json");
const gmbToken = require("../models/gmb.models");
const Auth = require("../models/auth.models");
const axios = require("axios");
const logger = require("../utils/logger");

const welcome = async (req, res) => {
  try {
    logger.logControllerAction("locations", "welcome", req.requestId);

    const response = {
      message: "Locations Home Page",
    };

    logger.info("Locations welcome endpoint accessed", {
      requestId: req.requestId,
    });

    res.send(response);
  } catch (error) {
    logger.error("Error in locations welcome", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res
      .status(500)
      .json({ message: "Internal server error", error: error.message });
  }
};

const createLocation = async (req, res, next) => {
  const id = req.body.id;
  const userId = req.body.userId;
  const gmbLocationId = req.body.gmbLocationId;
  const statusId = req.body.statusId;

  const newLocation = {
    id: id,
    userId: userId,
    gmbLocationId: gmbLocationId,
    statusId: statusId,
  };

  Location.Insert(newLocation)
    .then((result) => {
      res.status(201).json({ message: "Location Created!", response: result });
    })
    .catch((error) => {
      res.status(404).json({ message: "Location Not Created!", error: error });
    });
};

const getLocationSummary = async (req, res) => {
  const locationId = req.headers["x-gmb-location-id"];
  const accountId = req.headers["x-gmb-account-id"];
  try {
    const result = await reqGMBApi({
      req,
      action: GMB_ACTIONS.get_location_summary,
      reqBodyData: { accountId, locationId },
    });
    if (result.success) {
      // if (result.data.mediaInfo.mediaItems.length > 0) {
      //   for (let index = 0; index < result.data.mediaInfo.mediaItems.length; index++) {
      //     const element = result.data.mediaInfo.mediaItems[index];
      //     const imageResponse = await axios.get(element.thumbnailUrl, {
      //       responseType: "arraybuffer",
      //     });
      //     const base64 = Buffer.from(imageResponse.data, "binary").toString("base64");
      //     const mimeType = imageResponse.headers["content-type"];
      //     res.send(`data:${mimeType};base64,${base64}`);

      //   }
      // }
      res.status(200).json({
        message: "Location summary fetched.",
        data: result.data,
        success: result.success,
      });
    } else {
      res.status(result.status).json({
        message: "Failed to fetch Location summary",
        data: result.data,
      });
    }
    // console.log(req)
  } catch (error) {
    res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
};

const locationsList = (req, res) => {
  const userId = req.params.userId;
  Location.fetchAll(userId)
    .then((response) => {
      res.status(201).json({ message: "Locations List!", list: response });
    })
    .catch((error) => {
      res.status(404).json({ message: "Locations Not Found!", error: error });
    });
};

const locationsListPaginated = (req, res) => {
  const userId = req.params.userId;
  const pageNo = req.query.pageNo;
  const offset = req.query.offset;
  const businessId = req.query.businessId;
  const businessGroupId = req.query.businessGroupId;
  const search = req.query.search;
  Location.fetchAllPaginated(
    userId,
    pageNo,
    offset,
    businessId,
    businessGroupId,
    search
  )
    .then((response) => {
      res.status(200).json({ message: "Locations List!", ...response });
    })
    .catch((error) => {
      res.status(404).json({ message: "Locations Not Found!", error: error });
    });
};

const getAccountsCount = async (req, res) => {
  try {
    const userId = req.params.userId;
    var result = await Location.getAccountsCount(userId);
    res.status(200).json({ message: "Account Count fetched", result });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
};

const getImageBase64 = async (req, res) => {
  const imageUrl = req.query.url;
  try {
    const response = await axios.get(imageUrl, {
      responseType: "arraybuffer",
    });
    const base64 = Buffer.from(response.data, "binary").toString("base64");
    const mimeType = response.headers["content-type"];
    res.status(200).json({ base64: `data:${mimeType};base64,${base64}` });
  } catch (error) {
    res.status(500).send("Failed to fetch image");
  }
};

const isTokenExpired = (tokenInfo) => {
  const currentTime = Math.floor(Date.now() / 1000);
  return currentTime >= tokenInfo.expiry_date;
};

const refreshLocations = async (req, res) => {
  try {
    logger.logControllerAction("locations", "refreshLocations", req.requestId, {
      userId: req.params.userId,
    });

    logger.info("Starting location refresh process", {
      requestId: req.requestId,
      userId: req.params.userId,
    });

    const oAuthTokens = await Location.getAllOAuthTokens(req.params.userId);

    if (oAuthTokens.length > 0) {
      logger.info("Found OAuth tokens for location refresh", {
        requestId: req.requestId,
        userId: req.params.userId,
        tokenCount: oAuthTokens.length,
      });

      for (let index = 0; index < oAuthTokens.length; index++) {
        const element = oAuthTokens[index];

        logger.debug("Processing OAuth token", {
          requestId: req.requestId,
          tokenIndex: index + 1,
          totalTokens: oAuthTokens.length,
        });

        var responseData = await Auth.authenticateGoogle(element);
        const accountId = responseData.gmbAccountId;
        req["user"]["gmbToken"] = responseData.accessToken;

        logger.info("Syncing location data for account", {
          requestId: req.requestId,
          accountId: accountId,
        });

        await syncLocationData(req, accountId, null);
      }

      logger.info("Location refresh completed successfully", {
        requestId: req.requestId,
        userId: req.params.userId,
        processedAccounts: oAuthTokens.length,
      });

      res.status(200).json({
        message: "Locations synced successfully.",
        isSuccess: true,
      });
    } else {
      logger.warn("No OAuth tokens found for user", {
        requestId: req.requestId,
        userId: req.params.userId,
      });

      res.status(200).json({ message: "No Accounts found.", isSuccess: false });
    }
  } catch (error) {
    logger.error("Error in refreshLocations", {
      requestId: req.requestId,
      userId: req.params.userId,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

const syncLocationData = async (req, accountId, refreshToken) => {
  try {
    const result = await reqGMBApi({
      req,
      action: GMB_ACTIONS.get_locations,
      reqBodyData: { accountId, refreshToken },
    });
    console.log(JSON.stringify(result.data));
    if (result.success && result.data.locations) {
      let locationsData = [];
      result.data.locations.forEach(async (location) => {
        console.log(location);
        const locationId = location.name.split("/")[1];
        locationsData.push({
          accountId,
          locationId: locationId,
          title:
            location.title +
            " " +
            (location.storefrontAddress &&
            location.storefrontAddress.addressLines
              ? location.storefrontAddress.addressLines.join(", ")
              : ""),
          locality:
            location.storefrontAddress && location.storefrontAddress.locality
              ? location.storefrontAddress.locality
              : "",
          postalCode:
            location.storefrontAddress && location.storefrontAddress.postalCode
              ? location.storefrontAddress.postalCode
              : "",
          businessStream: location.categories.primaryCategory.displayName,
          statusId: 1,
          websiteUri: location.websiteUri ? location.websiteUri : "",
          description:
            location.profile && location.profile.description
              ? location.profile.description
              : "",
          placeId: location.metadata.placeId,
          mapsUri: location.metadata.mapsUri,
          newReviewUri: location.metadata.newReviewUri,
          latitude: location.latlng ? location.latlng.latitude : "",
          longitude: location.latlng ? location.latlng.longitude : "",
          primaryPhone: location.phoneNumbers.primaryPhone,
          regularHours: location.regularHours
            ? JSON.stringify(location.regularHours)
            : null,
          reviewsStatusId: 2,
          createdBy: 33,
          updatedBy: req.params.userId,
        });

        const newLocation = {
          userId: req.params.userId,
          gmbLocationId: locationId,
          statusId: 1,
        };

        await Location.InsertOrUpdateLocation(newLocation);
      });

      Location.postLocations(locationsData);
    }

    if (result.data.nextPageToken) {
      await syncLocationData(req, accountId, result.data.nextPageToken);
    }
  } catch (error) {}
};

module.exports = {
  welcome,
  createLocation,
  refreshLocations,
  locationsList,
  locationsListPaginated,
  getAccountsCount,
  getLocationSummary,
  getImageBase64,
};
