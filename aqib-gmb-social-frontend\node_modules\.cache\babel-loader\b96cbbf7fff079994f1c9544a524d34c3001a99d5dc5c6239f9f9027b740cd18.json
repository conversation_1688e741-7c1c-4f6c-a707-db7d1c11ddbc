{"ast": null, "code": "import { createContext } from 'react';\nexport const PreferencesContext = /*#__PURE__*/createContext({\n  setActiveMenuItem: item => {},\n  activeMenuItem: ''\n});", "map": {"version": 3, "names": ["createContext", "PreferencesContext", "setActiveMenuItem", "item", "activeMenuItem"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/context/preferences.context.tsx"], "sourcesContent": ["import { createContext } from 'react';\n\nexport const PreferencesContext = createContext({\n    setActiveMenuItem: (item: string) => { },\n    activeMenuItem: '',\n});"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AAErC,OAAO,MAAMC,kBAAkB,gBAAGD,aAAa,CAAC;EAC5CE,iBAAiB,EAAGC,IAAY,IAAK,CAAE,CAAC;EACxCC,cAAc,EAAE;AACpB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}