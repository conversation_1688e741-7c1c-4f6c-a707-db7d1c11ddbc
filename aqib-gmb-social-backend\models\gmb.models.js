const pool = require("../config/db");

module.exports = class gmbToken {
  constructor(accessToken, refreshToken, userId, gmbAccountId, statusId) {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    this.userId = userId;
    this.gmbAccountId = gmbAccountId;
    this.statusId = statusId;
  }

  static async resetAccountStatus(businessId) {
    try {
      const results = await pool.query(
        "Update gmb_accounts SET statusId = 0 WHERE businessId = ?",
        [businessId]
      );
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async updateGoogleSyncComplete(businessesMasterId, userId) {
    try {
      const results = await pool.query(
        "Update user_business SET isGoogleSyncComplete = 1, ModifiedOn = CURRENT_TIMESTAMP WHERE UserId = ? AND BusinessId = ?",
        [userId, businessesMasterId]
      );
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async InsertAccount(account_data) {
    try {
      const results = await pool.query(
        "INSERT INTO gmb_accounts (businessId, accountId, accountName, accountType, statusId)VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE accountName = VALUES(accountName), accountType = VALUES(accountType), statusId = VALUES(statusId), updatedAt = CURRENT_TIMESTAMP",
        [
          account_data.businessId,
          account_data.account_id,
          account_data.account_Name,
          account_data.account_Type,
          1,
        ]
      );
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async resetOAuth(userId) {
    try {
      const results = await pool.query(
        "UPDATE gmb_oauth_tokens set statusId = 0 WHERE userId = ?",
        [userId]
      );
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async InsertOAuth(account_data) {
    try {
      const results = await pool.query(
        "INSERT INTO gmb_oauth_tokens (accessToken, refreshToken, userId, gmbAccountId, statusId) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE accessToken = VALUES(accessToken),refreshToken = VALUES(refreshToken),statusId = VALUES(statusId), updatedAt = CURRENT_TIMESTAMP",
        [
          account_data.access_token,
          account_data.refresh_token,
          account_data.userId,
          account_data.account_id,
          1,
        ]
      );
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async gmb_oauth_tokens(accountId) {
    try {
      const results = await pool.query(
        "SELECT * FROM gmb_oauth_tokens WHERE gmbAccountId= ?",
        [accountId]
      );
      // console.log("results",results)
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }
};
