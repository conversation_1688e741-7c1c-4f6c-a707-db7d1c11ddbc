.accountLeft 
{
    background-image: url(../../assets/login/loginBg.png);
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0% 12%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}
.accountRight
{
    height: 100%;
    padding: 0px 8%;
    text-align: left;
    display: flex;
    justify-content: center;
    flex-direction: column;
}
.accountLogo
{
    width: 280px;
    margin-bottom: 36px;
}
.welcomeTitle
{
    font-size: 28px;
    font-weight: 600;
    color: #2F2F2F;
    margin-bottom: 20px;
}
.MuiFilledInput-root::before,
.MuiFilledInput-root::after
{
    border: 0px !important;
}
.accountRight .commonInput
{
    margin-bottom: 4px;
    background-color: #ECECEC;
    border-radius: 8px;
    padding: 0px 16px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.accountRight .primaryFillBtn
{
    margin-top: 8px;
    font-size: 18px !important;
    padding: 24px !important;
    font-weight: 600;
}
/* .MuiGrid-item
{
    padding-left: 0px !important;
} */