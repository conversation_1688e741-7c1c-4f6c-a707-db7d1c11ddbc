{"ast": null, "code": "import { createContext } from \"react\";\nexport const ToastContext = /*#__PURE__*/createContext({\n  setOpen: show => {},\n  open: false,\n  setToastMessage: message => {},\n  message: \"\",\n  toastConfig: {\n    severity: \"\",\n    message: \"\",\n    open: false\n  },\n  setToastConfig: (severity, message, open) => {}\n});", "map": {"version": 3, "names": ["createContext", "ToastContext", "<PERSON><PERSON><PERSON>", "show", "open", "setToastMessage", "message", "toastConfig", "severity", "setToastConfig"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/context/toast.context.tsx"], "sourcesContent": ["import { createContext } from \"react\";\nimport { ToastSeverity } from \"../constants/toastSeverity.constant\";\n\nexport const ToastContext = createContext({\n  setOpen: (show: boolean) => {},\n  open: false,\n  setToastMessage: (message: string) => {},\n  message: \"\",\n  toastConfig: {\n    severity: \"\",\n    message: \"\",\n    open: false,\n  },\n  setToastConfig: (\n    severity: ToastSeverity,\n    message: string,\n    open: boolean\n  ) => {},\n});\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AAGrC,OAAO,MAAMC,YAAY,gBAAGD,aAAa,CAAC;EACxCE,OAAO,EAAGC,IAAa,IAAK,CAAC,CAAC;EAC9BC,IAAI,EAAE,KAAK;EACXC,eAAe,EAAGC,OAAe,IAAK,CAAC,CAAC;EACxCA,OAAO,EAAE,EAAE;EACXC,WAAW,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZF,OAAO,EAAE,EAAE;IACXF,IAAI,EAAE;EACR,CAAC;EACDK,cAAc,EAAEA,CACdD,QAAuB,EACvBF,OAAe,EACfF,IAAa,KACV,CAAC;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}