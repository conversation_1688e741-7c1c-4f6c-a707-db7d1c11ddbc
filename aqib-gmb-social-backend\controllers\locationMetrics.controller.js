const GMB_ACTIONS = require("../constants/gmb-actions");
const logger = require("../utils/logger");
const { reqGMBApi } = require("../services/gmb.service");

const refreshLocationMetrics = async (req, res) => {
  const accountId = req.headers["x-gmb-account-id"];
  const locationId = req.headers["x-gmb-location-id"];
  const { startDate, endDate } = req.body;

  try {
    const result = await reqGMBApi({
      req,
      action: GMB_ACTIONS.get_locationMetrics,
      reqBodyData: {
        locationId: locationId,
        accountId: accountId,
        startDateRange: {
          year: startDate.split("-")[0],
          month: startDate.split("-")[1],
          day: startDate.split("-")[2],
        },
        endDateRange: {
          year: endDate.split("-")[0],
          month: endDate.split("-")[1],
          day: endDate.split("-")[2],
        },
      },
    });
    if (result.success) {
      // let webSiteClicksData = [];
      // result.data.multiDailyMetricTimeSeries.forEach((metric) => {
      //   console.log(metric);
      //   webSiteClicksData.push({
      //     locationId,

      //     // dailyMetricTimeSeries: metric.dailyMetricTimeSeries,
      //     dailyMetric: metric.dailyMetricTimeSeries[1].dailyMetric,
      //     // timeSeries: metric.timeSeries,
      //     datedValues: metric.dailyMetricTimeSeries[1].timeSeries.datedValues,
      //     // date: metric.date,
      //     // year: metric.year,
      //     // month: metric.month,
      //     // day: metric.day,
      //     // value: metric.datedValues ? metric.datedValues.value : ""
      //     // date:metric.datedValues.date.year.month.day,
      //     // value:metric.datedValues.date.year.month.day ? metric.datedValues.value : " ",
      //     // dailySubEntityType: metric.dailySubEntityType,
      //     // timeSeries:metric.timeSeries,
      //     // datedValues:metric.datedValues.date,
      //     // datedValues: metric.datedValues ? metric.datedValues.value : "",
      //   });
      // });
      res.status(200).json({
        message: "Locations Metric fetched",
        data: { ...result.data },
      });
    } else {
      res.status(result.status).json({
        message: "Failed to fetch Locations Metric",
        data: result.data,
      });
    }
  } catch (error) {
    res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
};

const getSearchkeywords = async (req, res) => {
  const accountId = req.headers["x-gmb-account-id"];
  const locationId = req.headers["x-gmb-location-id"];
  const { startDate, endDate, pageToken } = req.body;

  try {
    const result = await reqGMBApi({
      req,
      action: GMB_ACTIONS.get_searchkeywords,
      reqBodyData: {
        locationId: locationId,
        accountId: accountId,
        startDateRange: {
          year: startDate.split("-")[0],
          month: startDate.split("-")[1],
          day: startDate.split("-")[2],
        },
        endDateRange: {
          year: endDate.split("-")[0],
          month: endDate.split("-")[1],
          day: endDate.split("-")[2],
        },
        pageToken: pageToken,
      },
    });
    if (result.success) {
      res.status(200).json({
        message: "Search Keywords fetched",
        data: { ...result.data },
      });
    } else {
      res.status(result.status).json({
        message: "Failed to fetch Locations Metric",
        data: result.data,
      });
    }
  } catch (error) {
    res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
};

module.exports = { refreshLocationMetrics, getSearchkeywords };
