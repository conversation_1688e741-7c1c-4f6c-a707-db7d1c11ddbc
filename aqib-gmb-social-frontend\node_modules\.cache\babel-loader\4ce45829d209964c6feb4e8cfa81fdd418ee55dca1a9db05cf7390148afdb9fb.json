{"ast": null, "code": "import HttpHelperService from \"../httpHelper.service\";\nimport { ACCOUNTS_COUNT, IMAGE_PROXY, LIST_OF_LOCATIONS, LOCATION_SUMMARY, REFRESH_LOCATIONS } from \"../../constants/endPoints.constant\";\nclass LocationService {\n  constructor(dispatch) {\n    this._httpHelperService = void 0;\n    this.getLocations = async () => {\n      return await this._httpHelperService.get(LIST_OF_LOCATIONS);\n    };\n    this.getLocationsPaginated = async (userId, paginationModel, businessId, businessGroupId, search) => {\n      return await this._httpHelperService.get(`${LIST_OF_LOCATIONS}/userId/${userId}?pageNo=${paginationModel.pageNo}&offset=${paginationModel.offset}&businessId=${businessId}&businessGroupId=${businessGroupId}&search=${search}`);\n    };\n    this.getAccountsCount = async userId => {\n      return await this._httpHelperService.get(`${ACCOUNTS_COUNT}/userId/${userId}`);\n    };\n    this.syncAllLocations = async userId => {\n      return await this._httpHelperService.get(`${REFRESH_LOCATIONS}/userId/${userId}`);\n    };\n    this.getLocationSummary = async headerObject => {\n      return await this._httpHelperService.get(LOCATION_SUMMARY, headerObject);\n    };\n    this.getGoogleImageBase64 = async url => {\n      return await this._httpHelperService.get(IMAGE_PROXY(url));\n    };\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n}\nexport default LocationService;", "map": {"version": 3, "names": ["HttpHelperService", "ACCOUNTS_COUNT", "IMAGE_PROXY", "LIST_OF_LOCATIONS", "LOCATION_SUMMARY", "REFRESH_LOCATIONS", "LocationService", "constructor", "dispatch", "_httpHelperService", "getLocations", "get", "getLocationsPaginated", "userId", "paginationModel", "businessId", "businessGroupId", "search", "pageNo", "offset", "getAccountsCount", "syncAllLocations", "getLocationSummary", "headerObject", "getGoogleImageBase64", "url"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/location/location.service.tsx"], "sourcesContent": ["import { Component, Dispatch } from \"react\";\nimport HttpHelperService from \"../httpHelper.service\";\nimport {\n  ACCOUNTS_COUNT,\n  IMAGE_PROXY,\n  LIST_OF_LOCATIONS,\n  LOCATION_SUMMARY,\n  REFRESH_LOCATIONS,\n} from \"../../constants/endPoints.constant\";\nimport { IPaginationModel } from \"../../interfaces/IPaginationModel\";\nimport { Action } from \"redux\";\n\nclass LocationService {\n  _httpHelperService;\n  constructor(dispatch: Dispatch<Action>) {\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  getLocations = async () => {\n    return await this._httpHelperService.get(LIST_OF_LOCATIONS);\n  };\n\n  getLocationsPaginated = async (\n    userId: number,\n    paginationModel: IPaginationModel,\n    businessId: number,\n    businessGroupId: number,\n    search: string\n  ) => {\n    return await this._httpHelperService.get(\n      `${LIST_OF_LOCATIONS}/userId/${userId}?pageNo=${paginationModel.pageNo}&offset=${paginationModel.offset}&businessId=${businessId}&businessGroupId=${businessGroupId}&search=${search}`\n    );\n  };\n\n  getAccountsCount = async (userId: number) => {\n    return await this._httpHelperService.get(\n      `${ACCOUNTS_COUNT}/userId/${userId}`\n    );\n  };\n\n  syncAllLocations = async (userId: number) => {\n    return await this._httpHelperService.get(\n      `${REFRESH_LOCATIONS}/userId/${userId}`\n    );\n  };\n\n  getLocationSummary = async (headerObject: any) => {\n    return await this._httpHelperService.get(LOCATION_SUMMARY, headerObject);\n  };\n\n  getGoogleImageBase64 = async (url: string) => {\n    return await this._httpHelperService.get(IMAGE_PROXY(url));\n  };\n}\n\nexport default LocationService;\n"], "mappings": "AACA,OAAOA,iBAAiB,MAAM,uBAAuB;AACrD,SACEC,cAAc,EACdC,WAAW,EACXC,iBAAiB,EACjBC,gBAAgB,EAChBC,iBAAiB,QACZ,oCAAoC;AAI3C,MAAMC,eAAe,CAAC;EAEpBC,WAAWA,CAACC,QAA0B,EAAE;IAAA,KADxCC,kBAAkB;IAAA,KAKlBC,YAAY,GAAG,YAAY;MACzB,OAAO,MAAM,IAAI,CAACD,kBAAkB,CAACE,GAAG,CAACR,iBAAiB,CAAC;IAC7D,CAAC;IAAA,KAEDS,qBAAqB,GAAG,OACtBC,MAAc,EACdC,eAAiC,EACjCC,UAAkB,EAClBC,eAAuB,EACvBC,MAAc,KACX;MACH,OAAO,MAAM,IAAI,CAACR,kBAAkB,CAACE,GAAG,CACtC,GAAGR,iBAAiB,WAAWU,MAAM,WAAWC,eAAe,CAACI,MAAM,WAAWJ,eAAe,CAACK,MAAM,eAAeJ,UAAU,oBAAoBC,eAAe,WAAWC,MAAM,EACtL,CAAC;IACH,CAAC;IAAA,KAEDG,gBAAgB,GAAG,MAAOP,MAAc,IAAK;MAC3C,OAAO,MAAM,IAAI,CAACJ,kBAAkB,CAACE,GAAG,CACtC,GAAGV,cAAc,WAAWY,MAAM,EACpC,CAAC;IACH,CAAC;IAAA,KAEDQ,gBAAgB,GAAG,MAAOR,MAAc,IAAK;MAC3C,OAAO,MAAM,IAAI,CAACJ,kBAAkB,CAACE,GAAG,CACtC,GAAGN,iBAAiB,WAAWQ,MAAM,EACvC,CAAC;IACH,CAAC;IAAA,KAEDS,kBAAkB,GAAG,MAAOC,YAAiB,IAAK;MAChD,OAAO,MAAM,IAAI,CAACd,kBAAkB,CAACE,GAAG,CAACP,gBAAgB,EAAEmB,YAAY,CAAC;IAC1E,CAAC;IAAA,KAEDC,oBAAoB,GAAG,MAAOC,GAAW,IAAK;MAC5C,OAAO,MAAM,IAAI,CAAChB,kBAAkB,CAACE,GAAG,CAACT,WAAW,CAACuB,GAAG,CAAC,CAAC;IAC5D,CAAC;IArCC,IAAI,CAAChB,kBAAkB,GAAG,IAAIT,iBAAiB,CAACQ,QAAQ,CAAC;EAC3D;AAqCF;AAEA,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}