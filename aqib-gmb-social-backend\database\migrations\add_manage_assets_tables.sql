-- Migration: Add Manage Assets functionality
-- Date: 2025-01-02
-- Description: Add max_upload_size_mb column to user_business table and create business_assets table

-- Add max_upload_size_mb column to gmb_businesses_master table
ALTER TABLE gmb_businesses_master
ADD COLUMN max_upload_size_mb INT DEFAULT 1024 COMMENT 'Maximum upload size in MB for business assets (default 1GB)';

-- Create business_assets table to track uploaded files
CREATE TABLE IF NOT EXISTS business_assets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    business_id INT NOT NULL,
    user_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    original_file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL COMMENT 'File size in bytes',
    s3_key VARCHAR(500) NOT NULL COMMENT 'S3 object key/path',
    s3_url VARCHAR(1000) NOT NULL COMMENT 'S3 public URL',
    mime_type VARCHAR(100) NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign key constraints
    FOREIGN KEY (business_id) REFERENCES gmb_businesses_master(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    -- Indexes for performance
    INDEX idx_business_assets_business_id (business_id),
    INDEX idx_business_assets_user_id (user_id),
    INDEX idx_business_assets_status (status),
    INDEX idx_business_assets_upload_date (upload_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Update existing gmb_businesses_master records to have default max_upload_size_mb if NULL
UPDATE gmb_businesses_master
SET max_upload_size_mb = 1024
WHERE max_upload_size_mb IS NULL;
