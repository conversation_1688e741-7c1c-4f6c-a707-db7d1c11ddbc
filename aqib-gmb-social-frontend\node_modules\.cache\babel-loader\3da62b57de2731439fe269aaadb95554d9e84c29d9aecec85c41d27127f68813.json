{"ast": null, "code": "import HttpHelperService from \"../httpHelper.service\";\nimport { GMB_CALLBACK, GOOGLE_AUTHENTICATION } from \"../../constants/endPoints.constant\";\nclass AuthService {\n  constructor(dispatch) {\n    this._httpHelperService = void 0;\n    this.googleAuthenticate = async formData => {\n      return await this._httpHelperService.post(GOOGLE_AUTHENTICATION, formData);\n    };\n    this.gmbcallback = async (code, state, userId) => {\n      try {\n        const result = await this._httpHelperService.post(GMB_CALLBACK, {\n          code,\n          state,\n          userId\n        });\n        return result;\n      } catch (error) {\n        return error;\n      }\n    };\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n}\nexport default AuthService;", "map": {"version": 3, "names": ["HttpHelperService", "GMB_CALLBACK", "GOOGLE_AUTHENTICATION", "AuthService", "constructor", "dispatch", "_httpHelperService", "googleAuthenticate", "formData", "post", "gmbcallback", "code", "state", "userId", "result", "error"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/auth/auth.service.tsx"], "sourcesContent": ["import { Component, Dispatch } from \"react\";\nimport HttpHelperService from \"../httpHelper.service\";\nimport {\n  GMB_CALLBACK,\n  GOOGLE_AUTHENTICATION,\n  LIST_OF_BUSINESS,\n} from \"../../constants/endPoints.constant\";\nimport { Action } from \"redux\";\n\nclass AuthService {\n  _httpHelperService;\n  constructor(dispatch: Dispatch<Action>) {\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  googleAuthenticate = async (formData: any) => {\n    return await this._httpHelperService.post(GOOGLE_AUTHENTICATION, formData);\n  };\n\n  gmbcallback = async (code: any, state: any, userId: number) => {\n    try {\n      const result = await this._httpHelperService.post(GMB_CALLBACK, {\n        code,\n        state,\n        userId,\n      });\n      return result;\n    } catch (error) {\n      return error;\n    }\n  };\n}\n\nexport default AuthService;\n"], "mappings": "AACA,OAAOA,iBAAiB,MAAM,uBAAuB;AACrD,SACEC,YAAY,EACZC,qBAAqB,QAEhB,oCAAoC;AAG3C,MAAMC,WAAW,CAAC;EAEhBC,WAAWA,CAACC,QAA0B,EAAE;IAAA,KADxCC,kBAAkB;IAAA,KAKlBC,kBAAkB,GAAG,MAAOC,QAAa,IAAK;MAC5C,OAAO,MAAM,IAAI,CAACF,kBAAkB,CAACG,IAAI,CAACP,qBAAqB,EAAEM,QAAQ,CAAC;IAC5E,CAAC;IAAA,KAEDE,WAAW,GAAG,OAAOC,IAAS,EAAEC,KAAU,EAAEC,MAAc,KAAK;MAC7D,IAAI;QACF,MAAMC,MAAM,GAAG,MAAM,IAAI,CAACR,kBAAkB,CAACG,IAAI,CAACR,YAAY,EAAE;UAC9DU,IAAI;UACJC,KAAK;UACLC;QACF,CAAC,CAAC;QACF,OAAOC,MAAM;MACf,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd,OAAOA,KAAK;MACd;IACF,CAAC;IAlBC,IAAI,CAACT,kBAAkB,GAAG,IAAIN,iBAAiB,CAACK,QAAQ,CAAC;EAC3D;AAkBF;AAEA,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}