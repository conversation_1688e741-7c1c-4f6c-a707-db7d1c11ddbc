{"ast": null, "code": "import { Component } from \"react\";\nimport moment from \"moment\";\nimport { STARRATINGMAP } from \"../constants/dbConstant.constant\";\nimport dayjs from \"dayjs\";\nclass ApplicationHelperService extends Component {\n  constructor(...args) {\n    super(...args);\n    this.isValidateEmail = async text => {\n      try {\n        let reg = /^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w\\w+)+$/;\n        if (reg.test(text) === false) {\n          console.log(\"In-Valid Email in Application Helper Service\");\n          return false;\n        } else {\n          console.log(\"Valid Email in Application Helper Service\");\n          return true;\n        }\n      } catch (error) {\n        return false;\n      }\n    };\n    this.getUserDateTimeFormat = timestamp => {\n      return moment(new Date(timestamp)).format(\"DD-MM-YYYY hh:mm A\"); // 12H clock (AM/PM)\n    };\n    this.getExpandedDateTimeFormat = timestamp => {\n      return moment(new Date(timestamp)).format(\"MMMM DD, YYYY hh:mm A\"); // 12H clock (AM/PM)\n    };\n    this.getFormatedDate = (date = new Date(), format = \"YYYY/MM/DD\") => moment(date).format(format);\n    this.getExpandedFormatedDate = (date = new Date(), format = \"DD-MMM-YYYY\") => moment(date).format(format);\n    this.getPaginationText = (pageNo, offset, totalRecords) => `Showing ${(pageNo - 1) * offset + 1} - ${offset * pageNo > totalRecords ? totalRecords : offset * pageNo} of ${totalRecords} Records`;\n    this.getRatingNumberFromText = rating => {\n      return STARRATINGMAP[rating];\n    };\n    this.getAvatarText = fullname => {\n      var _nameParts$, _nameParts;\n      const nameParts = fullname.trim().split(/\\s+/);\n      const firstInitial = ((_nameParts$ = nameParts[0]) === null || _nameParts$ === void 0 ? void 0 : _nameParts$.charAt(0).toUpperCase()) || \"\";\n      const lastInitial = ((_nameParts = nameParts[nameParts.length - 1]) === null || _nameParts === void 0 ? void 0 : _nameParts.charAt(0).toUpperCase()) || \"\";\n      return `${firstInitial}${lastInitial}` || \"NA\";\n    };\n    this.transformClicksData = (rawData, clickType) => {\n      const clicksSeries = rawData.multiDailyMetricTimeSeries[0].dailyMetricTimeSeries.find(series => series.dailyMetric === clickType);\n      if (!clicksSeries) return {\n        data: [],\n        labels: []\n      };\n      const monthlyAggregates = {};\n      for (const entry of clicksSeries.timeSeries.datedValues) {\n        var _entry$value;\n        const {\n          year,\n          month\n        } = entry.date;\n        const key = dayjs(`${year}-${month}-01`).format(\"MMM YYYY\");\n        const value = parseInt((_entry$value = entry.value) !== null && _entry$value !== void 0 ? _entry$value : \"0\", 10);\n        if (!monthlyAggregates[key]) monthlyAggregates[key] = 0;\n        monthlyAggregates[key] += value;\n      }\n      const labels = Object.keys(monthlyAggregates);\n      const data = labels.map(label => monthlyAggregates[label]);\n      return {\n        data,\n        labels\n      };\n    };\n  }\n  toTitleCase(str) {\n    return str && str.length > 0 ? str.toLowerCase().split(\" \").map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(\" \") : \"\";\n  }\n  getDaysDifference(startDate, endDate) {\n    const start = dayjs(startDate);\n    const end = dayjs(endDate);\n    return end.diff(start, \"day\");\n  }\n}\nexport default ApplicationHelperService;", "map": {"version": 3, "names": ["Component", "moment", "STARRATINGMAP", "dayjs", "ApplicationHelperService", "constructor", "args", "isValidateEmail", "text", "reg", "test", "console", "log", "error", "getUserDateTimeFormat", "timestamp", "Date", "format", "getExpandedDateTimeFormat", "getFormatedDate", "date", "getExpandedFormatedDate", "getPaginationText", "pageNo", "offset", "totalRecords", "getRatingNumberFromText", "rating", "getAvatarText", "fullname", "_nameParts$", "_nameParts", "nameParts", "trim", "split", "firstInitial", "char<PERSON>t", "toUpperCase", "lastInitial", "length", "transformClicksData", "rawData", "clickType", "clicksSeries", "multiDailyMetricTimeSeries", "dailyMetricTimeSeries", "find", "series", "dailyMetric", "data", "labels", "monthlyAggregates", "entry", "timeSeries", "dated<PERSON><PERSON><PERSON>", "_entry$value", "year", "month", "key", "value", "parseInt", "Object", "keys", "map", "label", "toTitleCase", "str", "toLowerCase", "word", "slice", "join", "getDaysDifference", "startDate", "endDate", "start", "end", "diff"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/ApplicationHelperService.tsx"], "sourcesContent": ["import { Component } from \"react\";\nimport moment from \"moment\";\nimport { STARRATINGMAP } from \"../constants/dbConstant.constant\";\nimport dayjs from \"dayjs\";\n\nclass ApplicationHelperService extends Component {\n  isValidateEmail = async (text: string) => {\n    try {\n      let reg = /^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w\\w+)+$/;\n      if (reg.test(text) === false) {\n        console.log(\"In-Valid Email in Application Helper Service\");\n        return false;\n      } else {\n        console.log(\"Valid Email in Application Helper Service\");\n        return true;\n      }\n    } catch (error) {\n      return false;\n    }\n  };\n\n  getUserDateTimeFormat = (timestamp: any) => {\n    return moment(new Date(timestamp)).format(\"DD-MM-YYYY hh:mm A\"); // 12H clock (AM/PM)\n  };\n\n  getExpandedDateTimeFormat = (timestamp: any) => {\n    return moment(new Date(timestamp)).format(\"MMMM DD, YYYY hh:mm A\"); // 12H clock (AM/PM)\n  };\n\n  getFormatedDate = (date = new Date(), format = \"YYYY/MM/DD\") =>\n    moment(date).format(format);\n\n  getExpandedFormatedDate = (date = new Date(), format = \"DD-MMM-YYYY\") =>\n    moment(date).format(format);\n\n  getPaginationText = (pageNo: number, offset: number, totalRecords: number) =>\n    `Showing ${(pageNo - 1) * offset + 1} - ${\n      offset * pageNo > totalRecords ? totalRecords : offset * pageNo\n    } of ${totalRecords} Records`;\n\n  getRatingNumberFromText = (rating: string) => {\n    return STARRATINGMAP[rating as keyof typeof STARRATINGMAP];\n  };\n\n  getAvatarText = (fullname: string): string => {\n    const nameParts = fullname.trim().split(/\\s+/);\n    const firstInitial = nameParts[0]?.charAt(0).toUpperCase() || \"\";\n    const lastInitial =\n      nameParts[nameParts.length - 1]?.charAt(0).toUpperCase() || \"\";\n    return `${firstInitial}${lastInitial}` || \"NA\";\n  };\n\n  toTitleCase(str: string): string {\n    return str && str.length > 0\n      ? str\n          .toLowerCase()\n          .split(\" \")\n          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))\n          .join(\" \")\n      : \"\";\n  }\n\n  transformClicksData = (rawData: any, clickType: string) => {\n    const clicksSeries =\n      rawData.multiDailyMetricTimeSeries[0].dailyMetricTimeSeries.find(\n        (series: any) => series.dailyMetric === clickType\n      );\n\n    if (!clicksSeries) return { data: [], labels: [] };\n\n    const monthlyAggregates: Record<string, number> = {};\n\n    for (const entry of clicksSeries.timeSeries.datedValues) {\n      const { year, month } = entry.date;\n      const key = dayjs(`${year}-${month}-01`).format(\"MMM YYYY\");\n\n      const value = parseInt(entry.value ?? \"0\", 10);\n      if (!monthlyAggregates[key]) monthlyAggregates[key] = 0;\n      monthlyAggregates[key] += value;\n    }\n\n    const labels = Object.keys(monthlyAggregates);\n    const data = labels.map((label) => monthlyAggregates[label]);\n\n    return { data, labels };\n  };\n\n  getDaysDifference(startDate: string, endDate: string): number {\n    const start = dayjs(startDate);\n    const end = dayjs(endDate);\n\n    return end.diff(start, \"day\");\n  }\n}\n\nexport default ApplicationHelperService;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,aAAa,QAAQ,kCAAkC;AAChE,OAAOC,KAAK,MAAM,OAAO;AAEzB,MAAMC,wBAAwB,SAASJ,SAAS,CAAC;EAAAK,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA,KAC/CC,eAAe,GAAG,MAAOC,IAAY,IAAK;MACxC,IAAI;QACF,IAAIC,GAAG,GAAG,6CAA6C;QACvD,IAAIA,GAAG,CAACC,IAAI,CAACF,IAAI,CAAC,KAAK,KAAK,EAAE;UAC5BG,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3D,OAAO,KAAK;QACd,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;UACxD,OAAO,IAAI;QACb;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd,OAAO,KAAK;MACd;IACF,CAAC;IAAA,KAEDC,qBAAqB,GAAIC,SAAc,IAAK;MAC1C,OAAOd,MAAM,CAAC,IAAIe,IAAI,CAACD,SAAS,CAAC,CAAC,CAACE,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC;IACnE,CAAC;IAAA,KAEDC,yBAAyB,GAAIH,SAAc,IAAK;MAC9C,OAAOd,MAAM,CAAC,IAAIe,IAAI,CAACD,SAAS,CAAC,CAAC,CAACE,MAAM,CAAC,uBAAuB,CAAC,CAAC,CAAC;IACtE,CAAC;IAAA,KAEDE,eAAe,GAAG,CAACC,IAAI,GAAG,IAAIJ,IAAI,CAAC,CAAC,EAAEC,MAAM,GAAG,YAAY,KACzDhB,MAAM,CAACmB,IAAI,CAAC,CAACH,MAAM,CAACA,MAAM,CAAC;IAAA,KAE7BI,uBAAuB,GAAG,CAACD,IAAI,GAAG,IAAIJ,IAAI,CAAC,CAAC,EAAEC,MAAM,GAAG,aAAa,KAClEhB,MAAM,CAACmB,IAAI,CAAC,CAACH,MAAM,CAACA,MAAM,CAAC;IAAA,KAE7BK,iBAAiB,GAAG,CAACC,MAAc,EAAEC,MAAc,EAAEC,YAAoB,KACvE,WAAW,CAACF,MAAM,GAAG,CAAC,IAAIC,MAAM,GAAG,CAAC,MAClCA,MAAM,GAAGD,MAAM,GAAGE,YAAY,GAAGA,YAAY,GAAGD,MAAM,GAAGD,MAAM,OAC1DE,YAAY,UAAU;IAAA,KAE/BC,uBAAuB,GAAIC,MAAc,IAAK;MAC5C,OAAOzB,aAAa,CAACyB,MAAM,CAA+B;IAC5D,CAAC;IAAA,KAEDC,aAAa,GAAIC,QAAgB,IAAa;MAAA,IAAAC,WAAA,EAAAC,UAAA;MAC5C,MAAMC,SAAS,GAAGH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC;MAC9C,MAAMC,YAAY,GAAG,EAAAL,WAAA,GAAAE,SAAS,CAAC,CAAC,CAAC,cAAAF,WAAA,uBAAZA,WAAA,CAAcM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI,EAAE;MAChE,MAAMC,WAAW,GACf,EAAAP,UAAA,GAAAC,SAAS,CAACA,SAAS,CAACO,MAAM,GAAG,CAAC,CAAC,cAAAR,UAAA,uBAA/BA,UAAA,CAAiCK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI,EAAE;MAChE,OAAO,GAAGF,YAAY,GAAGG,WAAW,EAAE,IAAI,IAAI;IAChD,CAAC;IAAA,KAYDE,mBAAmB,GAAG,CAACC,OAAY,EAAEC,SAAiB,KAAK;MACzD,MAAMC,YAAY,GAChBF,OAAO,CAACG,0BAA0B,CAAC,CAAC,CAAC,CAACC,qBAAqB,CAACC,IAAI,CAC7DC,MAAW,IAAKA,MAAM,CAACC,WAAW,KAAKN,SAC1C,CAAC;MAEH,IAAI,CAACC,YAAY,EAAE,OAAO;QAAEM,IAAI,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAG,CAAC;MAElD,MAAMC,iBAAyC,GAAG,CAAC,CAAC;MAEpD,KAAK,MAAMC,KAAK,IAAIT,YAAY,CAACU,UAAU,CAACC,WAAW,EAAE;QAAA,IAAAC,YAAA;QACvD,MAAM;UAAEC,IAAI;UAAEC;QAAM,CAAC,GAAGL,KAAK,CAAChC,IAAI;QAClC,MAAMsC,GAAG,GAAGvD,KAAK,CAAC,GAAGqD,IAAI,IAAIC,KAAK,KAAK,CAAC,CAACxC,MAAM,CAAC,UAAU,CAAC;QAE3D,MAAM0C,KAAK,GAAGC,QAAQ,EAAAL,YAAA,GAACH,KAAK,CAACO,KAAK,cAAAJ,YAAA,cAAAA,YAAA,GAAI,GAAG,EAAE,EAAE,CAAC;QAC9C,IAAI,CAACJ,iBAAiB,CAACO,GAAG,CAAC,EAAEP,iBAAiB,CAACO,GAAG,CAAC,GAAG,CAAC;QACvDP,iBAAiB,CAACO,GAAG,CAAC,IAAIC,KAAK;MACjC;MAEA,MAAMT,MAAM,GAAGW,MAAM,CAACC,IAAI,CAACX,iBAAiB,CAAC;MAC7C,MAAMF,IAAI,GAAGC,MAAM,CAACa,GAAG,CAAEC,KAAK,IAAKb,iBAAiB,CAACa,KAAK,CAAC,CAAC;MAE5D,OAAO;QAAEf,IAAI;QAAEC;MAAO,CAAC;IACzB,CAAC;EAAA;EAjCDe,WAAWA,CAACC,GAAW,EAAU;IAC/B,OAAOA,GAAG,IAAIA,GAAG,CAAC3B,MAAM,GAAG,CAAC,GACxB2B,GAAG,CACAC,WAAW,CAAC,CAAC,CACbjC,KAAK,CAAC,GAAG,CAAC,CACV6B,GAAG,CAAEK,IAAI,IAAKA,IAAI,CAAChC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG+B,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAC3DC,IAAI,CAAC,GAAG,CAAC,GACZ,EAAE;EACR;EA2BAC,iBAAiBA,CAACC,SAAiB,EAAEC,OAAe,EAAU;IAC5D,MAAMC,KAAK,GAAGvE,KAAK,CAACqE,SAAS,CAAC;IAC9B,MAAMG,GAAG,GAAGxE,KAAK,CAACsE,OAAO,CAAC;IAE1B,OAAOE,GAAG,CAACC,IAAI,CAACF,KAAK,EAAE,KAAK,CAAC;EAC/B;AACF;AAEA,eAAetE,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}