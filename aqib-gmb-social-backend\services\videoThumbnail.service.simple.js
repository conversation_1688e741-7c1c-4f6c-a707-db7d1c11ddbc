const sharp = require("sharp");
const logger = require("../utils/logger");
const s3Service = require("./s3.service");

/**
 * Generate thumbnail from video buffer (simplified version)
 * @param {Buffer} videoBuffer - Video file buffer
 * @param {string} businessName - Business name for S3 key
 * @param {number} businessId - Business ID
 * @param {string} originalFileName - Original video file name
 * @returns {Promise<Object>} Thumbnail generation result
 */
const generateVideoThumbnail = async (videoBuffer, businessName, businessId, originalFileName) => {
  try {
    logger.info('Video thumbnail generation temporarily disabled', {
      businessId,
      originalFileName,
      videoSize: videoBuffer.length,
    });

    // TODO: Implement video thumbnail generation with ffmpeg
    // For now, return success without thumbnail to allow uploads to work
    return {
      success: false,
      error: 'Video thumbnail generation temporarily disabled - ffmpeg not available'
    };
  } catch (error) {
    logger.error("Error generating video thumbnail", {
      error: error.message,
      stack: error.stack,
      businessId,
      originalFileName,
    });

    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Generate thumbnail for image (resize and optimize)
 * @param {Buffer} imageBuffer - Image file buffer
 * @param {string} businessName - Business name for S3 key
 * @param {number} businessId - Business ID
 * @param {string} originalFileName - Original image file name
 * @param {string} mimeType - Image MIME type
 * @returns {Promise<Object>} Thumbnail generation result
 */
const generateImageThumbnail = async (imageBuffer, businessName, businessId, originalFileName, mimeType) => {
  try {
    logger.info('Starting image thumbnail generation', {
      businessId,
      originalFileName,
      imageSize: imageBuffer.length
    });

    // Generate thumbnail using sharp
    const thumbnailBuffer = await sharp(imageBuffer)
      .resize(320, 240, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({
        quality: 80,
        progressive: true
      })
      .toBuffer();

    // Generate S3 key for thumbnail
    const thumbnailFileName = `${originalFileName.split('.')[0]}_thumbnail.jpg`;
    const thumbnailS3Key = s3Service.generateS3Key(businessName, businessId, thumbnailFileName);

    // Upload thumbnail to S3
    const uploadResult = await s3Service.uploadFileToS3(
      thumbnailBuffer,
      thumbnailS3Key,
      'image/jpeg'
    );

    if (uploadResult.success) {
      logger.info('Image thumbnail uploaded successfully', {
        thumbnailS3Key,
        thumbnailUrl: uploadResult.data.s3Url
      });

      return {
        success: true,
        thumbnail: {
          s3Key: thumbnailS3Key,
          s3Url: uploadResult.data.s3Url,
          fileName: thumbnailFileName,
          size: thumbnailBuffer.length
        }
      };
    } else {
      throw new Error(`Failed to upload thumbnail: ${uploadResult.error}`);
    }

  } catch (error) {
    logger.error('Error generating image thumbnail', {
      error: error.message,
      stack: error.stack,
      businessId,
      originalFileName
    });

    return {
      success: false,
      error: error.message
    };
  }
};

module.exports = {
  generateVideoThumbnail,
  generateImageThumbnail
};
