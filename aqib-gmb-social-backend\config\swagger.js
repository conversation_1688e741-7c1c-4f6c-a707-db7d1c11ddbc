const swaggerJsdoc = require("swagger-jsdoc");
const path = require("path");

/**
 * @swagger
 * tags:
 *   - name: Auth
 *     description: Authentication endpoints
 *   - name: User
 *     description: User management endpoints
 *   - name: GMB
 *     description: Google My Business related endpoints
 *   - name: Business
 *     description: Business management endpoints
 *   - name: Locations
 *     description: Location management endpoints
 *   - name: Reviews
 *     description: Review management endpoints
 *   - name: Q&A
 *     description: Questions and Answers management endpoints
 *   - name: Role
 *     description: Role management endpoints
 *   - name: Performance
 *     description: Performance metrics endpoints
 *   - name: Post
 *     description: Post management endpoints
 *
 * components:
 *   schemas:
 *     Error:
 *       type: object
 *       properties:
 *         error:
 *           type: string
 *           description: Error message
 *         details:
 *           type: array
 *           items:
 *             type: object
 *           description: Detailed error information
 */

// Swagger definition
const swaggerDefinition = {
  openapi: "3.0.0",
  info: {
    title: "GMB Social Backend API",
    version: process.env.APP_VER_PREFIX || "v1",
    description: "API documentation for GMB Social Backend",
    // license: {
    //   name: "GPL-3.0",
    //   url: "https://www.gnu.org/licenses/gpl-3.0.en.html",
    // },
    // contact: {
    //   name: "Support",
    //   url: "https://github.com/anveshadicherla001/express-js-aws-lambda-claudia-boilerplate",
    // },
  },
  servers: [
    {
      url: `http://localhost:${process.env.APP_PORT || 3000}/${
        process.env.APP_VER_PREFIX || "v1"
      }`,
      description: "Development server",
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
      },
    },
  },
  security: [
    {
      bearerAuth: [],
    },
  ],
};

// Options for the swagger docs
const options = {
  swaggerDefinition,
  // Path to the API docs
  apis: [
    path.join(__dirname, "../routes/*.js"),
    path.join(__dirname, "../controllers/*.js"),
    path.join(__dirname, "../models/*.js"),
  ],
};

// Initialize swagger-jsdoc
const swaggerSpec = swaggerJsdoc(options);

module.exports = swaggerSpec;
