{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\screens\\\\manageAssets\\\\components\\\\confirmDelete.component.tsx\";\nimport React from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box } from '@mui/material';\nimport { Warning as WarningIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConfirmDeleteComponent = ({\n  open,\n  title,\n  message,\n  onConfirm,\n  onCancel\n}) => {\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onCancel,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 1,\n        children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          component: \"span\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        justifyContent: 'space-between',\n        padding: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onCancel,\n        variant: \"outlined\",\n        color: \"primary\",\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: onConfirm,\n        variant: \"contained\",\n        color: \"error\",\n        startIcon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 22\n        }, this),\n        children: \"Delete\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_c = ConfirmDeleteComponent;\nexport default ConfirmDeleteComponent;\nvar _c;\n$RefreshReg$(_c, \"ConfirmDeleteComponent\");", "map": {"version": 3, "names": ["React", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "Box", "Warning", "WarningIcon", "jsxDEV", "_jsxDEV", "ConfirmDeleteComponent", "open", "title", "message", "onConfirm", "onCancel", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "display", "alignItems", "gap", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "sx", "justifyContent", "padding", "onClick", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/screens/manageAssets/components/confirmDelete.component.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Typography,\n  Box,\n} from '@mui/material';\nimport {\n  Warning as WarningIcon,\n} from '@mui/icons-material';\n\ninterface ConfirmDeleteComponentProps {\n  open: boolean;\n  title: string;\n  message: string;\n  onConfirm: () => void;\n  onCancel: () => void;\n}\n\nconst ConfirmDeleteComponent: React.FC<ConfirmDeleteComponentProps> = ({\n  open,\n  title,\n  message,\n  onConfirm,\n  onCancel,\n}) => {\n  return (\n    <Dialog\n      open={open}\n      onClose={onCancel}\n      maxWidth=\"sm\"\n      fullWidth\n    >\n      <DialogTitle>\n        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n          <WarningIcon color=\"warning\" />\n          <Typography variant=\"h6\" component=\"span\">\n            {title}\n          </Typography>\n        </Box>\n      </DialogTitle>\n\n      <DialogContent>\n        <Typography variant=\"body1\">\n          {message}\n        </Typography>\n      </DialogContent>\n\n      <DialogActions sx={{ justifyContent: 'space-between', padding: 2 }}>\n        <Button\n          onClick={onCancel}\n          variant=\"outlined\"\n          color=\"primary\"\n        >\n          Cancel\n        </Button>\n        <Button\n          onClick={onConfirm}\n          variant=\"contained\"\n          color=\"error\"\n          startIcon={<WarningIcon />}\n        >\n          Delete\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default ConfirmDeleteComponent;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,GAAG,QACE,eAAe;AACtB,SACEC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU7B,MAAMC,sBAA6D,GAAGA,CAAC;EACrEC,IAAI;EACJC,KAAK;EACLC,OAAO;EACPC,SAAS;EACTC;AACF,CAAC,KAAK;EACJ,oBACEN,OAAA,CAACV,MAAM;IACLY,IAAI,EAAEA,IAAK;IACXK,OAAO,EAAED,QAAS;IAClBE,QAAQ,EAAC,IAAI;IACbC,SAAS;IAAAC,QAAA,gBAETV,OAAA,CAACT,WAAW;MAAAmB,QAAA,eACVV,OAAA,CAACJ,GAAG;QAACe,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAAAH,QAAA,gBAC7CV,OAAA,CAACF,WAAW;UAACgB,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/BlB,OAAA,CAACL,UAAU;UAACwB,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,MAAM;UAAAV,QAAA,EACtCP;QAAK;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdlB,OAAA,CAACR,aAAa;MAAAkB,QAAA,eACZV,OAAA,CAACL,UAAU;QAACwB,OAAO,EAAC,OAAO;QAAAT,QAAA,EACxBN;MAAO;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEhBlB,OAAA,CAACP,aAAa;MAAC4B,EAAE,EAAE;QAAEC,cAAc,EAAE,eAAe;QAAEC,OAAO,EAAE;MAAE,CAAE;MAAAb,QAAA,gBACjEV,OAAA,CAACN,MAAM;QACL8B,OAAO,EAAElB,QAAS;QAClBa,OAAO,EAAC,UAAU;QAClBL,KAAK,EAAC,SAAS;QAAAJ,QAAA,EAChB;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlB,OAAA,CAACN,MAAM;QACL8B,OAAO,EAAEnB,SAAU;QACnBc,OAAO,EAAC,WAAW;QACnBL,KAAK,EAAC,OAAO;QACbW,SAAS,eAAEzB,OAAA,CAACF,WAAW;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAR,QAAA,EAC5B;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACQ,EAAA,GAhDIzB,sBAA6D;AAkDnE,eAAeA,sBAAsB;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}