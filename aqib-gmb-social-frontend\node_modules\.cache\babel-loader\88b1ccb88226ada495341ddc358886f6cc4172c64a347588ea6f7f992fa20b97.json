{"ast": null, "code": "export let ToastSeverity = /*#__PURE__*/function (ToastSeverity) {\n  ToastSeverity[\"Error\"] = \"error\";\n  ToastSeverity[\"Warning\"] = \"warning\";\n  ToastSeverity[\"Info\"] = \"info\";\n  ToastSeverity[\"Success\"] = \"success\";\n  return ToastSeverity;\n}({});", "map": {"version": 3, "names": ["ToastSeverity"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/constants/toastSeverity.constant.tsx"], "sourcesContent": ["export enum ToastSeverity {\n  Error = \"error\",\n  Warning = \"warning\",\n  Info = \"info\",\n  Success = \"success\",\n}\n"], "mappings": "AAAA,WAAYA,aAAa,0BAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAA,OAAbA,aAAa;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}