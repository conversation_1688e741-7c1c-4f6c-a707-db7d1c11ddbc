{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\screens\\\\forgotPassword\\\\forgotPassword.screen.tsx\";\n//Widgets\nimport Box from \"@mui/material/Box\";\nimport Grid from \"@mui/material/Grid\";\nimport TextField from \"@mui/material/TextField\";\nimport Link from \"@mui/material/Link\";\nimport Button from \"@mui/material/Button\";\nimport { Typography } from \"@mui/material\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ForgotPassword = ({\n  title\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"height100\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      className: \"height100\",\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        className: \"height100\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"accountLeft\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              alt: \"MyLocoBiz - Login\",\n              className: \"width100\",\n              src: require(\"../../assets/login/loginLeftSlider.png\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"accountRight\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"accountTopPart\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"commonTitle\",\n                children: \"Welcome to\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                className: \"accountLogo\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  alt: \"MyLocoBiz - Logo\",\n                  className: \"width100\",\n                  src: require(\"../../assets/common/Logo.png\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"accountBodyPart\",\n              children: /*#__PURE__*/_jsxDEV(\"form\", {\n                children: /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 12,\n                    lg: 12,\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      className: \"commonInput\",\n                      children: /*#__PURE__*/_jsxDEV(TextField, {\n                        id: \"filled-basic\",\n                        className: \"width100\",\n                        label: \"Email\",\n                        type: \"email\",\n                        variant: \"filled\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 44,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 43,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 42,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    xs: 6,\n                    md: 6,\n                    lg: 6,\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      href: \"/\",\n                      className: \"floatR\",\n                      children: \"Continue with Login.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 55,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 54,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    xs: 12,\n                    md: 12,\n                    lg: 12,\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"contained\",\n                      className: \"primaryFillBtn\",\n                      children: \"Reset My Password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 60,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 59,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 41,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_c = ForgotPassword;\nexport default ForgotPassword;\nvar _c;\n$RefreshReg$(_c, \"ForgotPassword\");", "map": {"version": 3, "names": ["Box", "Grid", "TextField", "Link", "<PERSON><PERSON>", "Typography", "jsxDEV", "_jsxDEV", "ForgotPassword", "title", "className", "children", "container", "spacing", "item", "xs", "md", "lg", "alt", "src", "require", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "label", "type", "variant", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/screens/forgotPassword/forgotPassword.screen.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\nimport PageProps from \"../../models/PageProps.interface\";\n\n//Widgets\nimport Box from \"@mui/material/Box\";\nimport Grid from \"@mui/material/Grid\";\nimport TextField from \"@mui/material/TextField\";\nimport Link from \"@mui/material/Link\";\nimport Button from \"@mui/material/Button\";\nimport { Typography } from \"@mui/material\";\nimport * as yup from \"yup\";\n\nconst ForgotPassword: FunctionComponent<PageProps> = ({ title }) => {\n  return (\n    <div className=\"height100\">\n      <Box className=\"height100\">\n        <Grid container spacing={2} className=\"height100\">\n          <Grid item xs={12} md={6} lg={6}>\n            <Box className=\"accountLeft\">\n              <img\n                alt=\"MyLocoBiz - Login\"\n                className=\"width100\"\n                src={require(\"../../assets/login/loginLeftSlider.png\")}\n              />\n            </Box>\n          </Grid>\n          <Grid item xs={12} md={6} lg={6}>\n            <Box className=\"accountRight\">\n              <Box className=\"accountTopPart\">\n                <Typography className=\"commonTitle\">Welcome to</Typography>\n                <Box className=\"accountLogo\">\n                  <img\n                    alt=\"MyLocoBiz - Logo\"\n                    className=\"width100\"\n                    src={require(\"../../assets/common/Logo.png\")}\n                  />\n                </Box>\n              </Box>\n              <Box className=\"accountBodyPart\">\n                <form>\n                  <Grid container spacing={2}>\n                    <Grid item xs={12} md={12} lg={12}>\n                      <Box className=\"commonInput\">\n                        <TextField\n                          id=\"filled-basic\"\n                          className=\"width100\"\n                          label=\"Email\"\n                          type=\"email\"\n                          variant=\"filled\"\n                        />\n                      </Box>\n                    </Grid>\n\n                    <Grid xs={6} md={6} lg={6}>\n                      <Link href=\"/\" className=\"floatR\">\n                        Continue with Login.\n                      </Link>\n                    </Grid>\n                    <Grid xs={12} md={12} lg={12}>\n                      <Button variant=\"contained\" className=\"primaryFillBtn\">\n                        Reset My Password\n                      </Button>\n                    </Grid>\n                  </Grid>\n                </form>\n              </Box>\n            </Box>\n          </Grid>\n        </Grid>\n      </Box>\n    </div>\n  );\n};\n\nexport default ForgotPassword;\n"], "mappings": ";AAGA;AACA,OAAOA,GAAG,MAAM,mBAAmB;AACnC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,UAAU,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3C,MAAMC,cAA4C,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAClE,oBACEF,OAAA;IAAKG,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBJ,OAAA,CAACP,GAAG;MAACU,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBJ,OAAA,CAACN,IAAI;QAACW,SAAS;QAACC,OAAO,EAAE,CAAE;QAACH,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAC/CJ,OAAA,CAACN,IAAI;UAACa,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAN,QAAA,eAC9BJ,OAAA,CAACP,GAAG;YAACU,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BJ,OAAA;cACEW,GAAG,EAAC,mBAAmB;cACvBR,SAAS,EAAC,UAAU;cACpBS,GAAG,EAAEC,OAAO,CAAC,wCAAwC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPjB,OAAA,CAACN,IAAI;UAACa,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAN,QAAA,eAC9BJ,OAAA,CAACP,GAAG;YAACU,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BJ,OAAA,CAACP,GAAG;cAACU,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BJ,OAAA,CAACF,UAAU;gBAACK,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAU;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC3DjB,OAAA,CAACP,GAAG;gBAACU,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BJ,OAAA;kBACEW,GAAG,EAAC,kBAAkB;kBACtBR,SAAS,EAAC,UAAU;kBACpBS,GAAG,EAAEC,OAAO,CAAC,8BAA8B;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjB,OAAA,CAACP,GAAG;cAACU,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BJ,OAAA;gBAAAI,QAAA,eACEJ,OAAA,CAACN,IAAI;kBAACW,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACzBJ,OAAA,CAACN,IAAI;oBAACa,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACC,EAAE,EAAE,EAAG;oBAACC,EAAE,EAAE,EAAG;oBAAAN,QAAA,eAChCJ,OAAA,CAACP,GAAG;sBAACU,SAAS,EAAC,aAAa;sBAAAC,QAAA,eAC1BJ,OAAA,CAACL,SAAS;wBACRuB,EAAE,EAAC,cAAc;wBACjBf,SAAS,EAAC,UAAU;wBACpBgB,KAAK,EAAC,OAAO;wBACbC,IAAI,EAAC,OAAO;wBACZC,OAAO,EAAC;sBAAQ;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAEPjB,OAAA,CAACN,IAAI;oBAACc,EAAE,EAAE,CAAE;oBAACC,EAAE,EAAE,CAAE;oBAACC,EAAE,EAAE,CAAE;oBAAAN,QAAA,eACxBJ,OAAA,CAACJ,IAAI;sBAAC0B,IAAI,EAAC,GAAG;sBAACnB,SAAS,EAAC,QAAQ;sBAAAC,QAAA,EAAC;oBAElC;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACPjB,OAAA,CAACN,IAAI;oBAACc,EAAE,EAAE,EAAG;oBAACC,EAAE,EAAE,EAAG;oBAACC,EAAE,EAAE,EAAG;oBAAAN,QAAA,eAC3BJ,OAAA,CAACH,MAAM;sBAACwB,OAAO,EAAC,WAAW;sBAAClB,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,EAAC;oBAEvD;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACM,EAAA,GA5DItB,cAA4C;AA8DlD,eAAeA,cAAc;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}