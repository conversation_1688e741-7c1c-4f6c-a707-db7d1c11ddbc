{"ast": null, "code": "import { AUTH_REQUESTED, AUTH_SUCCESS, AUTH_LOGOUT, AUTH_ERROR, AUTH_UNAUTHORIZED } from \"../constants/reducer.constant\";\nconst initialState = {\n  userInfo: null,\n  rbAccess: null,\n  isLoading: false,\n  success: false,\n  loginMessage: \"\",\n  token: null,\n  isUnauthorized: false\n};\nconst authReducer = (prevState = initialState, action) => {\n  switch (action.type) {\n    case AUTH_REQUESTED:\n      return {\n        ...prevState,\n        isLoading: true,\n        success: true,\n        loginMessage: null,\n        isUnauthorized: null\n      };\n    case AUTH_SUCCESS:\n      return {\n        ...prevState,\n        userInfo: action.payload.result,\n        token: action.payload.token,\n        rbAccess: action.payload.rbAccess,\n        isLoading: false\n      };\n    case AUTH_LOGOUT:\n      return {\n        ...initialState\n      };\n    case AUTH_ERROR:\n      return {\n        ...initialState,\n        userInfo: null,\n        isLoading: false,\n        success: false,\n        loginMessage: action.payload\n      };\n    case AUTH_UNAUTHORIZED:\n      return {\n        ...prevState,\n        userInfo: null,\n        isLoading: false,\n        success: false,\n        isUnauthorized: true\n      };\n    default:\n      return prevState;\n  }\n};\nexport default authReducer;", "map": {"version": 3, "names": ["AUTH_REQUESTED", "AUTH_SUCCESS", "AUTH_LOGOUT", "AUTH_ERROR", "AUTH_UNAUTHORIZED", "initialState", "userInfo", "rbAccess", "isLoading", "success", "loginMessage", "token", "isUnauthorized", "authReducer", "prevState", "action", "type", "payload", "result"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/reducers/auth.reducer.tsx"], "sourcesContent": ["import {\n  AUTH_REQUESTED,\n  AUTH_SUCCESS,\n  AUTH_LOGOUT,\n  AUTH_ERROR,\n  AUTH_UNAUTHORIZED,\n} from \"../constants/reducer.constant\";\nimport { IRoleBasedAccessResponseModel } from \"../interfaces/response/IRoleBasedAccessResponseModel\";\nimport { ILoggedInUserResponseModel } from \"../interfaces/response/ISignInResponseModel\";\n\ninterface AuthState {\n  userInfo: ILoggedInUserResponseModel | null;\n  rbAccess: IRoleBasedAccessResponseModel | null;\n  isLoading: boolean;\n  success: boolean | null;\n  loginMessage: string | null;\n  token: string | null;\n  isUnauthorized: boolean | null;\n}\n\nconst initialState: AuthState = {\n  userInfo: null,\n  rbAccess: null,\n  isLoading: false,\n  success: false,\n  loginMessage: \"\",\n  token: null,\n  isUnauthorized: false,\n};\n\nconst authReducer = (prevState = initialState, action: any) => {\n  switch (action.type) {\n    case AUTH_REQUESTED:\n      return {\n        ...prevState,\n        isLoading: true,\n        success: true,\n        loginMessage: null,\n        isUnauthorized: null,\n      };\n    case AUTH_SUCCESS:\n      return {\n        ...prevState,\n        userInfo: action.payload.result,\n        token: action.payload.token,\n        rbAccess: action.payload.rbAccess,\n        isLoading: false,\n      };\n    case AUTH_LOGOUT:\n      return { ...initialState };\n    case AUTH_ERROR:\n      return {\n        ...initialState,\n        userInfo: null,\n        isLoading: false,\n        success: false,\n        loginMessage: action.payload,\n      };\n    case AUTH_UNAUTHORIZED:\n      return {\n        ...prevState,\n        userInfo: null,\n        isLoading: false,\n        success: false,\n        isUnauthorized: true,\n      };\n    default:\n      return prevState;\n  }\n};\n\nexport default authReducer;\n"], "mappings": "AAAA,SACEA,cAAc,EACdC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,iBAAiB,QACZ,+BAA+B;AActC,MAAMC,YAAuB,GAAG;EAC9BC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,KAAK;EAChBC,OAAO,EAAE,KAAK;EACdC,YAAY,EAAE,EAAE;EAChBC,KAAK,EAAE,IAAI;EACXC,cAAc,EAAE;AAClB,CAAC;AAED,MAAMC,WAAW,GAAGA,CAACC,SAAS,GAAGT,YAAY,EAAEU,MAAW,KAAK;EAC7D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKhB,cAAc;MACjB,OAAO;QACL,GAAGc,SAAS;QACZN,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,IAAI;QACbC,YAAY,EAAE,IAAI;QAClBE,cAAc,EAAE;MAClB,CAAC;IACH,KAAKX,YAAY;MACf,OAAO;QACL,GAAGa,SAAS;QACZR,QAAQ,EAAES,MAAM,CAACE,OAAO,CAACC,MAAM;QAC/BP,KAAK,EAAEI,MAAM,CAACE,OAAO,CAACN,KAAK;QAC3BJ,QAAQ,EAAEQ,MAAM,CAACE,OAAO,CAACV,QAAQ;QACjCC,SAAS,EAAE;MACb,CAAC;IACH,KAAKN,WAAW;MACd,OAAO;QAAE,GAAGG;MAAa,CAAC;IAC5B,KAAKF,UAAU;MACb,OAAO;QACL,GAAGE,YAAY;QACfC,QAAQ,EAAE,IAAI;QACdE,SAAS,EAAE,KAAK;QAChBC,OAAO,EAAE,KAAK;QACdC,YAAY,EAAEK,MAAM,CAACE;MACvB,CAAC;IACH,KAAKb,iBAAiB;MACpB,OAAO;QACL,GAAGU,SAAS;QACZR,QAAQ,EAAE,IAAI;QACdE,SAAS,EAAE,KAAK;QAChBC,OAAO,EAAE,KAAK;QACdG,cAAc,EAAE;MAClB,CAAC;IACH;MACE,OAAOE,SAAS;EACpB;AACF,CAAC;AAED,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}