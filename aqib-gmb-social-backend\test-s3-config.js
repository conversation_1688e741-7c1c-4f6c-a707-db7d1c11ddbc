require('dotenv').config({ path: '.env.development' });

console.log('🔧 Testing S3 Configuration...');
console.log();

// Check environment variables
console.log('📋 Environment Variables:');
console.log(`   APP_AWS_ACCESS_KEY_ID: ${process.env.APP_AWS_ACCESS_KEY_ID ? 'SET' : 'NOT SET'}`);
console.log(`   APP_AWS_SECRET_ACCESS_KEY: ${process.env.APP_AWS_SECRET_ACCESS_KEY ? 'SET' : 'NOT SET'}`);
console.log(`   APP_AWS_REGION: ${process.env.APP_AWS_REGION || 'NOT SET'}`);
console.log(`   APP_AWS_S3_BUCKET: ${process.env.APP_AWS_S3_BUCKET || 'NOT SET'}`);
console.log();

// Test AWS SDK configuration
try {
  const AWS = require('aws-sdk');
  
  // Configure AWS SDK
  AWS.config.update({
    accessKeyId: process.env.APP_AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.APP_AWS_SECRET_ACCESS_KEY,
    region: process.env.APP_AWS_REGION,
  });

  const s3 = new AWS.S3();
  
  console.log('✅ AWS SDK loaded successfully');
  console.log(`🌍 AWS Region: ${AWS.config.region || 'NOT SET'}`);
  console.log(`🔑 Access Key ID: ${AWS.config.credentials?.accessKeyId ? 'SET' : 'NOT SET'}`);
  console.log();
  
  // Test S3 connection (list buckets)
  console.log('🧪 Testing S3 connection...');
  
  if (!process.env.APP_AWS_ACCESS_KEY_ID || !process.env.APP_AWS_SECRET_ACCESS_KEY) {
    console.log('⚠️ AWS credentials not configured. Please set:');
    console.log('   - APP_AWS_ACCESS_KEY_ID');
    console.log('   - APP_AWS_SECRET_ACCESS_KEY');
    console.log('   - APP_AWS_REGION');
    console.log('   - APP_AWS_S3_BUCKET');
    console.log();
    console.log('💡 Update .env.development with your AWS credentials');
    process.exit(1);
  }
  
  // Test bucket access
  s3.headBucket({ Bucket: process.env.APP_AWS_S3_BUCKET }, (err, data) => {
    if (err) {
      console.log('❌ S3 bucket access failed:', err.message);
      console.log();
      console.log('🔧 Possible issues:');
      console.log('   1. Invalid AWS credentials');
      console.log('   2. Bucket does not exist');
      console.log('   3. Insufficient permissions');
      console.log('   4. Incorrect region');
      console.log();
      console.log('💡 Please verify your AWS configuration');
    } else {
      console.log('✅ S3 bucket access successful!');
      console.log(`📦 Bucket: ${process.env.APP_AWS_S3_BUCKET}`);
      console.log();
      console.log('🎉 S3 configuration is working correctly!');
    }
  });
  
} catch (error) {
  console.log('❌ AWS SDK error:', error.message);
  console.log();
  console.log('🔧 Please install AWS SDK: npm install aws-sdk');
}

// Keep the script running for async operations
setTimeout(() => {
  console.log('⏰ Test completed');
  process.exit(0);
}, 5000);
