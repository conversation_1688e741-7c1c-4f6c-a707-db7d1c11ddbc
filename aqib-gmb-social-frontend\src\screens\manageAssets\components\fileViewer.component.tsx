import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  Chip,
  Grid,
} from "@mui/material";
import {
  Close as CloseIcon,
  Download as DownloadIcon,
  Image as ImageIcon,
  VideoFile as VideoIcon,
} from "@mui/icons-material";

interface IAsset {
  id: number;
  business_id: number;
  user_id: number;
  file_name: string;
  original_file_name: string;
  file_type: "image" | "video";
  file_size: number;
  s3_key: string;
  s3_url: string;
  mime_type: string;
  upload_date: string;
  status: string;
  uploaded_by_name?: string;
  thumbnail_s3_url?: string;
}

interface FileViewerComponentProps {
  asset: IAsset | null;
  open: boolean;
  onClose: () => void;
}

const FileViewerComponent: React.FC<FileViewerComponentProps> = ({
  asset,
  open,
  onClose,
}) => {
  if (!asset) return null;

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleDownload = () => {
    const link = document.createElement("a");
    link.href = asset.s3_url;
    link.download = asset.original_file_name;
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getFileTypeIcon = () => {
    return asset.file_type === "image" ? <ImageIcon /> : <VideoIcon />;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          minHeight: "80vh",
          maxHeight: "90vh",
        },
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center" gap={1}>
            {getFileTypeIcon()}
            <Typography variant="h6" component="span">
              {asset.original_file_name}
            </Typography>
            <Chip
              label={asset.file_type.toUpperCase()}
              size="small"
              color="primary"
              variant="outlined"
            />
          </Box>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={3}>
          {/* Media Preview */}
          <Grid item xs={12} md={8}>
            <Box
              sx={{
                width: "100%",
                height: "60vh",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "#f5f5f5",
                borderRadius: 1,
                overflow: "hidden",
              }}
            >
              {asset.file_type === "image" ? (
                <img
                  src={asset.s3_url}
                  alt={asset.original_file_name}
                  style={{
                    maxWidth: "100%",
                    maxHeight: "100%",
                    objectFit: "contain",
                  }}
                />
              ) : (
                <video
                  controls
                  style={{
                    maxWidth: "100%",
                    maxHeight: "100%",
                  }}
                  poster={asset.thumbnail_s3_url}
                >
                  <source src={asset.s3_url} type={asset.mime_type} />
                  Your browser does not support the video tag.
                </video>
              )}
            </Box>
          </Grid>

          {/* Asset Details */}
          <Grid item xs={12} md={4}>
            <Box>
              <Typography variant="h6" gutterBottom>
                Asset Details
              </Typography>

              <Box marginBottom={2}>
                <Typography variant="body2" color="text.secondary">
                  File Name
                </Typography>
                <Typography variant="body1" sx={{ wordBreak: "break-all" }}>
                  {asset.original_file_name}
                </Typography>
              </Box>

              <Box marginBottom={2}>
                <Typography variant="body2" color="text.secondary">
                  File Type
                </Typography>
                <Typography variant="body1">{asset.mime_type}</Typography>
              </Box>

              <Box marginBottom={2}>
                <Typography variant="body2" color="text.secondary">
                  File Size
                </Typography>
                <Typography variant="body1">
                  {formatFileSize(asset.file_size)}
                </Typography>
              </Box>

              <Box marginBottom={2}>
                <Typography variant="body2" color="text.secondary">
                  Upload Date
                </Typography>
                <Typography variant="body1">
                  {formatDate(asset.upload_date)}
                </Typography>
              </Box>

              {asset.uploaded_by_name && (
                <Box marginBottom={2}>
                  <Typography variant="body2" color="text.secondary">
                    Uploaded By
                  </Typography>
                  <Typography variant="body1">
                    {asset.uploaded_by_name}
                  </Typography>
                </Box>
              )}

              {asset.thumbnail_s3_url && asset.file_type === "video" && (
                <Box marginBottom={2}>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    gutterBottom
                  >
                    Video Thumbnail
                  </Typography>
                  <Box
                    sx={{
                      width: "100%",
                      height: 120,
                      backgroundColor: "#f5f5f5",
                      borderRadius: 1,
                      overflow: "hidden",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <img
                      src={asset.thumbnail_s3_url}
                      alt="Video thumbnail"
                      style={{
                        maxWidth: "100%",
                        maxHeight: "100%",
                        objectFit: "cover",
                      }}
                    />
                  </Box>
                </Box>
              )}
            </Box>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button
          startIcon={<DownloadIcon />}
          onClick={handleDownload}
          variant="outlined"
        >
          Download
        </Button>
        <Button onClick={onClose} variant="contained">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default FileViewerComponent;
