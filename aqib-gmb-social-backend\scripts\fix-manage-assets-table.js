const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.development' });

/**
 * Fix script for Manage Assets table location
 * This script will:
 * 1. Remove max_upload_size_mb column from user_business table
 * 2. Add max_upload_size_mb column to gmb_businesses_master table
 * 3. Update existing records with default values
 */

async function fixManageAssetsTable() {
  let connection;
  
  try {
    console.log('🚀 Starting Manage Assets table fix...');
    
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.APP_DB_HOST,
      user: process.env.APP_DB_USER,
      password: process.env.APP_DB_PASSWORD,
      database: process.env.APP_DB_NAME,
      multipleStatements: true
    });

    console.log('✅ Database connection established');

    // Check current state
    console.log('🔍 Checking current table structure...');
    
    // Check if column exists in user_business
    const [userBusinessColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'user_business' AND COLUMN_NAME = 'max_upload_size_mb'
    `, [process.env.APP_DB_NAME]);

    // Check if column exists in gmb_businesses_master
    const [businessColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'gmb_businesses_master' AND COLUMN_NAME = 'max_upload_size_mb'
    `, [process.env.APP_DB_NAME]);

    console.log(`📋 max_upload_size_mb in user_business: ${userBusinessColumns.length > 0 ? 'EXISTS' : 'NOT EXISTS'}`);
    console.log(`📋 max_upload_size_mb in gmb_businesses_master: ${businessColumns.length > 0 ? 'EXISTS' : 'NOT EXISTS'}`);

    // Execute the fix statements
    console.log('🔄 Executing fix...');
    
    const statements = [
      // Remove from user_business if exists
      userBusinessColumns.length > 0 ? 
        'ALTER TABLE user_business DROP COLUMN max_upload_size_mb' : null,
      
      // Add to gmb_businesses_master if doesn't exist
      businessColumns.length === 0 ? 
        'ALTER TABLE gmb_businesses_master ADD COLUMN max_upload_size_mb INT DEFAULT 1024 COMMENT "Maximum upload size in MB for business assets (default 1GB)"' : null,
      
      // Update existing records
      'UPDATE gmb_businesses_master SET max_upload_size_mb = 1024 WHERE max_upload_size_mb IS NULL'
    ].filter(stmt => stmt !== null);

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      try {
        console.log(`📝 Executing statement ${i + 1}/${statements.length}...`);
        await connection.execute(statement);
        console.log(`✅ Statement ${i + 1} executed successfully`);
      } catch (error) {
        console.log(`❌ Error in statement ${i + 1}:`, error.message);
        // Continue with other statements even if one fails
      }
    }

    // Verify the fix
    console.log('🔍 Verifying fix...');
    
    // Check final state
    const [finalUserBusinessColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'user_business' AND COLUMN_NAME = 'max_upload_size_mb'
    `, [process.env.APP_DB_NAME]);

    const [finalBusinessColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'gmb_businesses_master' AND COLUMN_NAME = 'max_upload_size_mb'
    `, [process.env.APP_DB_NAME]);

    if (finalUserBusinessColumns.length === 0) {
      console.log('✅ max_upload_size_mb column removed from user_business table');
    } else {
      console.log('❌ max_upload_size_mb column still exists in user_business table');
    }

    if (finalBusinessColumns.length > 0) {
      console.log('✅ max_upload_size_mb column exists in gmb_businesses_master table');
      
      // Check business records
      const [businessRecords] = await connection.execute(`
        SELECT COUNT(*) as count,
               COUNT(CASE WHEN max_upload_size_mb IS NOT NULL THEN 1 END) as with_max_size
        FROM gmb_businesses_master
      `);

      const totalRecords = businessRecords[0].count;
      const recordsWithMaxSize = businessRecords[0].with_max_size;

      console.log(`📊 Business Records: ${totalRecords} total, ${recordsWithMaxSize} with max_upload_size_mb set`);
    } else {
      console.log('❌ max_upload_size_mb column missing from gmb_businesses_master table');
    }

    console.log('🎉 Manage Assets table fix completed successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log('- Moved max_upload_size_mb column from user_business to gmb_businesses_master table');
    console.log('- Storage limits are now per business (not per user-business relationship)');
    console.log('- All businesses have default 1GB (1024 MB) storage limit');

  } catch (error) {
    console.error('❌ Error during fix:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the fix
if (require.main === module) {
  fixManageAssetsTable();
}

module.exports = { fixManageAssetsTable };
