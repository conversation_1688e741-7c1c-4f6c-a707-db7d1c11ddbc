{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\createPost\\\\createPostTemplates\\\\cards\\\\testimonialCard6\\\\testimonialCard6.component.tsx\";\nimport React from \"react\";\nimport { Box, Typography, Container, CssBaseline } from \"@mui/material\";\nimport UserAvatar from \"../../../../userAvatar/userAvatar.component\";\nimport RatingsStar from \"../../../../ratingsStar/ratingsStar.component\";\n\n//Css Import\nimport \"../testimonialCard6/testimonialCard6.component.style.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestimonialCard6 = props => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    ref: props.divRef,\n    sx: {\n      background: `${props.templateConfig.backgroundColor} url(${require(\"../../../../../assets/feedbackBackgrouns/6.png\")}) no-repeat center/cover`,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\",\n      backgroundRepeat: \"no-repeat\",\n      color: \"#fff\",\n      textAlign: \"center\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      height: \"100%\",\n      width: \"100%\",\n      border: 1,\n      borderColor: \"black\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: \"marB20\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              className: \"marB20\",\n              children: props.templateConfig.showRating && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  justifyContent: \"center\",\n                  mb: 2\n                },\n                children: /*#__PURE__*/_jsxDEV(RatingsStar, {\n                  starRating: props.templateConfig.starRating\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            mb: 2,\n            children: props.templateConfig.comment\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"testmonialUserInfo\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: props.templateConfig.showAvatar && /*#__PURE__*/_jsxDEV(UserAvatar, {\n                profileImage: props.templateConfig.reviewerImage,\n                fullname: props.templateConfig.reviewerName,\n                style: {\n                  width: 60,\n                  height: 60,\n                  margin: \"0 auto 10px\",\n                  background: \"linear-gradient(45deg, #42A5F5, #64B5F6)\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                className: \"testmonialUserName\",\n                children: props.templateConfig.reviewerName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_c = TestimonialCard6;\nexport default TestimonialCard6;\nvar _c;\n$RefreshReg$(_c, \"TestimonialCard6\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Container", "CssBaseline", "UserAvatar", "RatingsStar", "jsxDEV", "_jsxDEV", "TestimonialCard6", "props", "ref", "divRef", "sx", "background", "templateConfig", "backgroundColor", "require", "backgroundSize", "backgroundPosition", "backgroundRepeat", "color", "textAlign", "display", "flexDirection", "justifyContent", "alignItems", "height", "width", "border", "borderColor", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "showRating", "mb", "starRating", "variant", "comment", "showAvatar", "profileImage", "reviewerImage", "fullname", "reviewerName", "style", "margin", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/createPost/createPostTemplates/cards/testimonialCard6/testimonialCard6.component.tsx"], "sourcesContent": ["import React from \"react\";\nimport {\n  Box,\n  Typography,\n  Avatar,\n  Rating,\n  Container,\n  CssBaseline,\n} from \"@mui/material\";\nimport { IPostTemplateConfig } from \"../../../../../types/IPostTemplateConfig\";\nimport { ref } from \"yup\";\nimport StarIcon from \"@mui/icons-material/Star\";\nimport UserAvatar from \"../../../../userAvatar/userAvatar.component\";\nimport RatingsStar from \"../../../../ratingsStar/ratingsStar.component\";\n\n//Css Import\nimport \"../testimonialCard6/testimonialCard6.component.style.css\";\n\nconst TestimonialCard6 = (props: {\n  templateConfig: IPostTemplateConfig;\n  divRef: any;\n}) => {\n  return (\n    <Box\n      ref={props.divRef}\n      sx={{\n        background: `${\n          props.templateConfig.backgroundColor\n        } url(${require(\"../../../../../assets/feedbackBackgrouns/6.png\")}) no-repeat center/cover`,\n        backgroundSize: \"cover\",\n        backgroundPosition: \"center\",\n        backgroundRepeat: \"no-repeat\",\n        color: \"#fff\",\n        textAlign: \"center\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        height: \"100%\",\n        width: \"100%\",\n        border: 1,\n        borderColor: \"black\",\n      }}\n    >\n      <Container>\n        <CssBaseline />\n        <Box>\n          {/* Main Content */}\n          {/* Avatar and Name */}\n          <Box>\n            {/* Rating */}\n            <Box className=\"marB20\">\n              {/* Rating */}\n              <Box className=\"marB20\">\n                {props.templateConfig.showRating && (\n                  <Box\n                    sx={{\n                      display: \"flex\",\n                      justifyContent: \"center\",\n                      mb: 2,\n                    }}\n                  >\n                    <RatingsStar starRating={props.templateConfig.starRating} />\n                  </Box>\n                )}\n              </Box>\n            </Box>\n            {/* Review Text */}\n            <Typography variant=\"body2\" color=\"text.secondary\" mb={2}>\n              {props.templateConfig.comment}\n            </Typography>\n            <Box className=\"testmonialUserInfo\">\n              <Box>\n                {props.templateConfig.showAvatar && (\n                  <UserAvatar\n                    profileImage={props.templateConfig.reviewerImage}\n                    fullname={props.templateConfig.reviewerName}\n                    style={{\n                      width: 60,\n                      height: 60,\n                      margin: \"0 auto 10px\",\n                      background: \"linear-gradient(45deg, #42A5F5, #64B5F6)\",\n                    }}\n                  />\n                )}\n              </Box>\n              <Box>\n                <Typography className=\"testmonialUserName\">\n                  {props.templateConfig.reviewerName}\n                </Typography>\n              </Box>\n            </Box>\n          </Box>\n        </Box>\n      </Container>\n    </Box>\n  );\n};\n\nexport default TestimonialCard6;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EAGVC,SAAS,EACTC,WAAW,QACN,eAAe;AAItB,OAAOC,UAAU,MAAM,6CAA6C;AACpE,OAAOC,WAAW,MAAM,+CAA+C;;AAEvE;AACA,OAAO,0DAA0D;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,gBAAgB,GAAIC,KAGzB,IAAK;EACJ,oBACEF,OAAA,CAACP,GAAG;IACFU,GAAG,EAAED,KAAK,CAACE,MAAO;IAClBC,EAAE,EAAE;MACFC,UAAU,EAAE,GACVJ,KAAK,CAACK,cAAc,CAACC,eAAe,QAC9BC,OAAO,CAAC,gDAAgD,CAAC,0BAA0B;MAC3FC,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE,QAAQ;MAC5BC,gBAAgB,EAAE,WAAW;MAC7BC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,QAAQ;MACnBC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,CAAC;MACTC,WAAW,EAAE;IACf,CAAE;IAAAC,QAAA,eAEFvB,OAAA,CAACL,SAAS;MAAA4B,QAAA,gBACRvB,OAAA,CAACJ,WAAW;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACf3B,OAAA,CAACP,GAAG;QAAA8B,QAAA,eAGFvB,OAAA,CAACP,GAAG;UAAA8B,QAAA,gBAEFvB,OAAA,CAACP,GAAG;YAACmC,SAAS,EAAC,QAAQ;YAAAL,QAAA,eAErBvB,OAAA,CAACP,GAAG;cAACmC,SAAS,EAAC,QAAQ;cAAAL,QAAA,EACpBrB,KAAK,CAACK,cAAc,CAACsB,UAAU,iBAC9B7B,OAAA,CAACP,GAAG;gBACFY,EAAE,EAAE;kBACFU,OAAO,EAAE,MAAM;kBACfE,cAAc,EAAE,QAAQ;kBACxBa,EAAE,EAAE;gBACN,CAAE;gBAAAP,QAAA,eAEFvB,OAAA,CAACF,WAAW;kBAACiC,UAAU,EAAE7B,KAAK,CAACK,cAAc,CAACwB;gBAAW;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3B,OAAA,CAACN,UAAU;YAACsC,OAAO,EAAC,OAAO;YAACnB,KAAK,EAAC,gBAAgB;YAACiB,EAAE,EAAE,CAAE;YAAAP,QAAA,EACtDrB,KAAK,CAACK,cAAc,CAAC0B;UAAO;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACb3B,OAAA,CAACP,GAAG;YAACmC,SAAS,EAAC,oBAAoB;YAAAL,QAAA,gBACjCvB,OAAA,CAACP,GAAG;cAAA8B,QAAA,EACDrB,KAAK,CAACK,cAAc,CAAC2B,UAAU,iBAC9BlC,OAAA,CAACH,UAAU;gBACTsC,YAAY,EAAEjC,KAAK,CAACK,cAAc,CAAC6B,aAAc;gBACjDC,QAAQ,EAAEnC,KAAK,CAACK,cAAc,CAAC+B,YAAa;gBAC5CC,KAAK,EAAE;kBACLnB,KAAK,EAAE,EAAE;kBACTD,MAAM,EAAE,EAAE;kBACVqB,MAAM,EAAE,aAAa;kBACrBlC,UAAU,EAAE;gBACd;cAAE;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN3B,OAAA,CAACP,GAAG;cAAA8B,QAAA,eACFvB,OAAA,CAACN,UAAU;gBAACkC,SAAS,EAAC,oBAAoB;gBAAAL,QAAA,EACvCrB,KAAK,CAACK,cAAc,CAAC+B;cAAY;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACc,EAAA,GA/EIxC,gBAAgB;AAiFtB,eAAeA,gBAAgB;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}