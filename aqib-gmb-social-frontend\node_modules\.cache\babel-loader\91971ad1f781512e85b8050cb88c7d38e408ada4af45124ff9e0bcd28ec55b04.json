{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\screens\\\\manageAssets\\\\manageAssets.screen.tsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useEffect, useState } from \"react\";\nimport { Box, Typography, Grid, Card, CardContent, Button, LinearProgress, Pagination, Alert } from \"@mui/material\";\nimport LeftMenuComponent from \"../../components/leftMenu/leftMenu.component\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { LoadingContext } from \"../../context/loading.context\";\nimport { ToastContext } from \"../../context/toast.context\";\nimport { ToastSeverity } from \"../../constants/toastSeverity.constant\";\nimport StorageIcon from \"@mui/icons-material/Storage\";\nimport FileUploadComponent from \"../../components/fileUpload/fileUpload.component\";\nimport FileGalleryComponent from \"../../components/fileGallery/fileGallery.component\";\nimport FileViewerComponent from \"../../components/fileViewer/fileViewer.component\";\nimport ConfirmDeleteComponent from \"../../components/confirmDelete/confirmDelete.component\";\nimport ManageAssetsService from \"../../services/manageAssets/manageAssets.service\";\nimport BusinessService from \"../../services/business/business.service\";\n\n// CSS Import\nimport \"./manageAssets.screen.style.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ManageAssets = ({\n  title\n}) => {\n  _s();\n  const {\n    userInfo\n  } = useSelector(state => state.authReducer);\n  const {\n    setLoading\n  } = useContext(LoadingContext);\n  const {\n    showToast\n  } = useContext(ToastContext);\n  const dispatch = useDispatch();\n\n  // Initialize services\n  const manageAssetsService = new ManageAssetsService(dispatch);\n  const businessService = new BusinessService(dispatch);\n\n  // State management\n  const [assets, setAssets] = useState([]);\n  const [businesses, setBusinesses] = useState([]);\n  const [selectedBusinessId, setSelectedBusinessId] = useState(null);\n  const [storageInfo, setStorageInfo] = useState(null);\n  const [pagination, setPagination] = useState({\n    totalRecords: 0,\n    pageCount: 0,\n    currentPage: 1,\n    recordsPerPage: 12\n  });\n  const [uploading, setUploading] = useState(false);\n  const [selectedAsset, setSelectedAsset] = useState(null);\n  const [viewerOpen, setViewerOpen] = useState(false);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [assetToDelete, setAssetToDelete] = useState(null);\n\n  // Load businesses on component mount\n  useEffect(() => {\n    loadBusinesses();\n  }, []);\n\n  // Load assets when business is selected\n  useEffect(() => {\n    if (selectedBusinessId) {\n      loadAssets(1);\n    }\n  }, [selectedBusinessId]);\n  const loadBusinesses = async () => {\n    try {\n      setLoading(true);\n      const response = await businessService.getBusinessList(userInfo.id);\n      if (response.data && response.data.length > 0) {\n        setBusinesses(response.data);\n        // Auto-select first business if only one exists\n        if (response.data.length === 1) {\n          setSelectedBusinessId(response.data[0].id);\n        }\n      }\n    } catch (error) {\n      showToast(\"Failed to load businesses\", ToastSeverity.Error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadAssets = async (page = 1) => {\n    if (!selectedBusinessId) return;\n    try {\n      setLoading(true);\n      const response = await ManageAssetsService.getAssets(selectedBusinessId, page, pagination.recordsPerPage);\n      if (response.success) {\n        setAssets(response.data || []);\n        setStorageInfo(response.storageInfo);\n        setPagination({\n          ...response.pagination,\n          currentPage: page\n        });\n      } else {\n        setAssets([]);\n        setStorageInfo(null);\n      }\n    } catch (error) {\n      showToast(\"Failed to load assets\", ToastSeverity.Error);\n      setAssets([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleFileUpload = async files => {\n    if (!selectedBusinessId) {\n      showToast(\"Please select a business first\", ToastSeverity.Warning);\n      return;\n    }\n    try {\n      setUploading(true);\n      const response = await ManageAssetsService.uploadAssets(selectedBusinessId, files);\n      if (response.success) {\n        var _response$uploadedAss;\n        showToast(`${((_response$uploadedAss = response.uploadedAssets) === null || _response$uploadedAss === void 0 ? void 0 : _response$uploadedAss.length) || 0} files uploaded successfully`, ToastSeverity.Success);\n        if (response.errors && response.errors.length > 0) {\n          response.errors.forEach(error => {\n            showToast(`${error.fileName}: ${error.error}`, ToastSeverity.Warning);\n          });\n        }\n\n        // Reload assets\n        loadAssets(pagination.currentPage);\n      } else {\n        showToast(response.message || \"Upload failed\", ToastSeverity.Error);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      showToast(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || \"Upload failed\", ToastSeverity.Error);\n    } finally {\n      setUploading(false);\n    }\n  };\n  const handleDeleteAsset = async asset => {\n    setAssetToDelete(asset);\n    setDeleteDialogOpen(true);\n  };\n  const confirmDelete = async () => {\n    if (!assetToDelete) return;\n    try {\n      setLoading(true);\n      const response = await ManageAssetsService.deleteAsset(assetToDelete.id);\n      if (response.success) {\n        showToast(\"Asset deleted successfully\", ToastSeverity.Success);\n        loadAssets(pagination.currentPage);\n      } else {\n        showToast(response.message || \"Delete failed\", ToastSeverity.Error);\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      showToast(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Delete failed\", ToastSeverity.Error);\n    } finally {\n      setLoading(false);\n      setDeleteDialogOpen(false);\n      setAssetToDelete(null);\n    }\n  };\n  const handleViewAsset = asset => {\n    setSelectedAsset(asset);\n    setViewerOpen(true);\n  };\n  const handlePageChange = (event, page) => {\n    loadAssets(page);\n  };\n  const formatFileSize = bytes => {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n  };\n  const getStorageUsageColor = percentage => {\n    if (percentage < 70) return \"success\";\n    if (percentage < 90) return \"warning\";\n    return \"error\";\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(LeftMenuComponent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              marginBottom: \"20px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"pageTitle\",\n              children: \"Manage Assets\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              className: \"subtitle2\",\n              children: \"Upload, view, and manage your business assets (images and videos)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), businesses.length > 1 && /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              marginBottom: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Select Business\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: businesses.map(business => /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    variant: selectedBusinessId === business.id ? \"contained\" : \"outlined\",\n                    onClick: () => setSelectedBusinessId(business.id),\n                    children: business.businessName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 25\n                  }, this)\n                }, business.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this), selectedBusinessId && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [storageInfo && /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                marginBottom: 3\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 2,\n                  marginBottom: 2,\n                  children: [/*#__PURE__*/_jsxDEV(StorageIcon, {\n                    color: \"primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    children: \"Storage Usage\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  marginBottom: 2,\n                  children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                    variant: \"determinate\",\n                    value: Math.min(parseFloat(storageInfo.usagePercentage), 100),\n                    color: getStorageUsageColor(parseFloat(storageInfo.usagePercentage)),\n                    sx: {\n                      height: 10,\n                      borderRadius: 5\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [storageInfo.totalSizeMB, \" MB used of\", \" \", storageInfo.maxSizeMB, \" MB (\", storageInfo.usagePercentage, \"%)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(FileUploadComponent, {\n              onFileUpload: handleFileUpload,\n              uploading: uploading,\n              maxSizeMB: (storageInfo === null || storageInfo === void 0 ? void 0 : storageInfo.maxSizeMB) || 1024,\n              currentUsageMB: parseFloat((storageInfo === null || storageInfo === void 0 ? void 0 : storageInfo.totalSizeMB) || \"0\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FileGalleryComponent, {\n              assets: assets,\n              onViewAsset: handleViewAsset,\n              onDeleteAsset: handleDeleteAsset,\n              formatFileSize: formatFileSize\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this), pagination.pageCount > 1 && /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"center\",\n              marginTop: 3,\n              children: /*#__PURE__*/_jsxDEV(Pagination, {\n                count: pagination.pageCount,\n                page: pagination.currentPage,\n                onChange: handlePageChange,\n                color: \"primary\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true), !selectedBusinessId && businesses.length === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"No businesses found. Please add a business first.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FileViewerComponent, {\n            asset: selectedAsset,\n            open: viewerOpen,\n            onClose: () => {\n              setViewerOpen(false);\n              setSelectedAsset(null);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ConfirmDeleteComponent, {\n            open: deleteDialogOpen,\n            title: \"Delete Asset\",\n            message: `Are you sure you want to delete \"${assetToDelete === null || assetToDelete === void 0 ? void 0 : assetToDelete.original_file_name}\"? This action cannot be undone.`,\n            onConfirm: confirmDelete,\n            onCancel: () => {\n              setDeleteDialogOpen(false);\n              setAssetToDelete(null);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 267,\n    columnNumber: 5\n  }, this);\n};\n_s(ManageAssets, \"XyfBJyEAVVkMRJkEoVYiH/U5tiw=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = ManageAssets;\nexport default ManageAssets;\nvar _c;\n$RefreshReg$(_c, \"ManageAssets\");", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useState", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "LinearProgress", "Pagination", "<PERSON><PERSON>", "LeftMenuComponent", "useSelector", "useDispatch", "LoadingContext", "ToastContext", "ToastSeverity", "StorageIcon", "FileUploadComponent", "FileGalleryComponent", "FileViewerComponent", "ConfirmDeleteComponent", "ManageAssetsService", "BusinessService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ManageAssets", "title", "_s", "userInfo", "state", "authReducer", "setLoading", "showToast", "dispatch", "manageAssetsService", "businessService", "assets", "setAssets", "businesses", "setBusinesses", "selectedBusinessId", "setSelectedBusinessId", "storageInfo", "setStorageInfo", "pagination", "setPagination", "totalRecords", "pageCount", "currentPage", "recordsPerPage", "uploading", "setUploading", "selectedAsset", "setSelectedAsset", "viewerOpen", "setViewerOpen", "deleteDialogOpen", "setDeleteDialogOpen", "assetToDelete", "setAssetToDelete", "loadBusinesses", "loadAssets", "response", "getBusinessList", "id", "data", "length", "error", "Error", "page", "getAssets", "success", "handleFileUpload", "files", "Warning", "uploadAssets", "_response$uploadedAss", "uploadedAssets", "Success", "errors", "for<PERSON>ach", "fileName", "message", "_error$response", "_error$response$data", "handleDeleteAsset", "asset", "confirmDelete", "deleteAsset", "_error$response2", "_error$response2$data", "handleViewAsset", "handlePageChange", "event", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "getStorageUsageColor", "percentage", "children", "sx", "marginBottom", "className", "_jsxFileName", "lineNumber", "columnNumber", "variant", "gutterBottom", "container", "spacing", "map", "business", "item", "onClick", "businessName", "display", "alignItems", "gap", "color", "value", "min", "usagePercentage", "height", "borderRadius", "totalSizeMB", "maxSizeMB", "onFileUpload", "currentUsageMB", "onViewAsset", "onDeleteAsset", "justifyContent", "marginTop", "count", "onChange", "size", "severity", "open", "onClose", "original_file_name", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/screens/manageAssets/manageAssets.screen.tsx"], "sourcesContent": ["import React, {\n  FunctionComponent,\n  useContext,\n  useEffect,\n  useState,\n} from \"react\";\nimport PageProps from \"../../models/PageProps.interface\";\nimport {\n  Box,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  Button,\n  LinearProgress,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Pagination,\n  Alert,\n  CircularProgress,\n} from \"@mui/material\";\nimport LeftMenuComponent from \"../../components/leftMenu/leftMenu.component\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { LoadingContext } from \"../../context/loading.context\";\nimport { ToastContext } from \"../../context/toast.context\";\nimport { ToastSeverity } from \"../../constants/toastSeverity.constant\";\nimport CloudUploadIcon from \"@mui/icons-material/CloudUpload\";\nimport DeleteIcon from \"@mui/icons-material/Delete\";\nimport VisibilityIcon from \"@mui/icons-material/Visibility\";\nimport ImageIcon from \"@mui/icons-material/Image\";\nimport VideoLibraryIcon from \"@mui/icons-material/VideoLibrary\";\nimport StorageIcon from \"@mui/icons-material/Storage\";\nimport FileUploadComponent from \"../../components/fileUpload/fileUpload.component\";\nimport FileGalleryComponent from \"../../components/fileGallery/fileGallery.component\";\nimport FileViewerComponent from \"../../components/fileViewer/fileViewer.component\";\nimport ConfirmDeleteComponent from \"../../components/confirmDelete/confirmDelete.component\";\nimport ManageAssetsService from \"../../services/manageAssets/manageAssets.service\";\nimport BusinessService from \"../../services/business/business.service\";\n\n// CSS Import\nimport \"./manageAssets.screen.style.css\";\n\ninterface IAsset {\n  id: number;\n  business_id: number;\n  user_id: number;\n  file_name: string;\n  original_file_name: string;\n  file_type: \"image\" | \"video\";\n  file_size: number;\n  s3_key: string;\n  s3_url: string;\n  mime_type: string;\n  upload_date: string;\n  status: string;\n  uploaded_by_name?: string;\n}\n\ninterface IStorageInfo {\n  totalSizeMB: string;\n  maxSizeMB: number;\n  usagePercentage: string;\n}\n\ninterface IPagination {\n  totalRecords: number;\n  pageCount: number;\n  currentPage: number;\n  recordsPerPage: number;\n}\n\nconst ManageAssets: FunctionComponent<PageProps> = ({ title }) => {\n  const { userInfo } = useSelector((state: any) => state.authReducer);\n  const { setLoading } = useContext(LoadingContext);\n  const { showToast } = useContext(ToastContext);\n  const dispatch = useDispatch();\n\n  // Initialize services\n  const manageAssetsService = new ManageAssetsService(dispatch);\n  const businessService = new BusinessService(dispatch);\n\n  // State management\n  const [assets, setAssets] = useState<IAsset[]>([]);\n  const [businesses, setBusinesses] = useState<any[]>([]);\n  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(\n    null\n  );\n  const [storageInfo, setStorageInfo] = useState<IStorageInfo | null>(null);\n  const [pagination, setPagination] = useState<IPagination>({\n    totalRecords: 0,\n    pageCount: 0,\n    currentPage: 1,\n    recordsPerPage: 12,\n  });\n  const [uploading, setUploading] = useState(false);\n  const [selectedAsset, setSelectedAsset] = useState<IAsset | null>(null);\n  const [viewerOpen, setViewerOpen] = useState(false);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [assetToDelete, setAssetToDelete] = useState<IAsset | null>(null);\n\n  // Load businesses on component mount\n  useEffect(() => {\n    loadBusinesses();\n  }, []);\n\n  // Load assets when business is selected\n  useEffect(() => {\n    if (selectedBusinessId) {\n      loadAssets(1);\n    }\n  }, [selectedBusinessId]);\n\n  const loadBusinesses = async () => {\n    try {\n      setLoading(true);\n      const response = await businessService.getBusinessList(userInfo.id);\n      if (response.data && response.data.length > 0) {\n        setBusinesses(response.data);\n        // Auto-select first business if only one exists\n        if (response.data.length === 1) {\n          setSelectedBusinessId(response.data[0].id);\n        }\n      }\n    } catch (error: any) {\n      showToast(\"Failed to load businesses\", ToastSeverity.Error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadAssets = async (page: number = 1) => {\n    if (!selectedBusinessId) return;\n\n    try {\n      setLoading(true);\n      const response = await ManageAssetsService.getAssets(\n        selectedBusinessId,\n        page,\n        pagination.recordsPerPage\n      );\n\n      if (response.success) {\n        setAssets(response.data || []);\n        setStorageInfo(response.storageInfo);\n        setPagination({\n          ...response.pagination,\n          currentPage: page,\n        });\n      } else {\n        setAssets([]);\n        setStorageInfo(null);\n      }\n    } catch (error: any) {\n      showToast(\"Failed to load assets\", ToastSeverity.Error);\n      setAssets([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFileUpload = async (files: FileList) => {\n    if (!selectedBusinessId) {\n      showToast(\"Please select a business first\", ToastSeverity.Warning);\n      return;\n    }\n\n    try {\n      setUploading(true);\n      const response = await ManageAssetsService.uploadAssets(\n        selectedBusinessId,\n        files\n      );\n\n      if (response.success) {\n        showToast(\n          `${response.uploadedAssets?.length || 0} files uploaded successfully`,\n          ToastSeverity.Success\n        );\n\n        if (response.errors && response.errors.length > 0) {\n          response.errors.forEach((error: any) => {\n            showToast(\n              `${error.fileName}: ${error.error}`,\n              ToastSeverity.Warning\n            );\n          });\n        }\n\n        // Reload assets\n        loadAssets(pagination.currentPage);\n      } else {\n        showToast(response.message || \"Upload failed\", ToastSeverity.Error);\n      }\n    } catch (error: any) {\n      showToast(\n        error.response?.data?.message || \"Upload failed\",\n        ToastSeverity.Error\n      );\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  const handleDeleteAsset = async (asset: IAsset) => {\n    setAssetToDelete(asset);\n    setDeleteDialogOpen(true);\n  };\n\n  const confirmDelete = async () => {\n    if (!assetToDelete) return;\n\n    try {\n      setLoading(true);\n      const response = await ManageAssetsService.deleteAsset(assetToDelete.id);\n\n      if (response.success) {\n        showToast(\"Asset deleted successfully\", ToastSeverity.Success);\n        loadAssets(pagination.currentPage);\n      } else {\n        showToast(response.message || \"Delete failed\", ToastSeverity.Error);\n      }\n    } catch (error: any) {\n      showToast(\n        error.response?.data?.message || \"Delete failed\",\n        ToastSeverity.Error\n      );\n    } finally {\n      setLoading(false);\n      setDeleteDialogOpen(false);\n      setAssetToDelete(null);\n    }\n  };\n\n  const handleViewAsset = (asset: IAsset) => {\n    setSelectedAsset(asset);\n    setViewerOpen(true);\n  };\n\n  const handlePageChange = (\n    event: React.ChangeEvent<unknown>,\n    page: number\n  ) => {\n    loadAssets(page);\n  };\n\n  const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n  };\n\n  const getStorageUsageColor = (\n    percentage: number\n  ): \"success\" | \"warning\" | \"error\" => {\n    if (percentage < 70) return \"success\";\n    if (percentage < 90) return \"warning\";\n    return \"error\";\n  };\n\n  return (\n    <div>\n      <Box>\n        <LeftMenuComponent>\n          <Box>\n            <Box sx={{ marginBottom: \"20px\" }}>\n              <h3 className=\"pageTitle\">Manage Assets</h3>\n              <Typography variant=\"subtitle2\" className=\"subtitle2\">\n                Upload, view, and manage your business assets (images and\n                videos)\n              </Typography>\n            </Box>\n\n            {/* Business Selection */}\n            {businesses.length > 1 && (\n              <Card sx={{ marginBottom: 3 }}>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Select Business\n                  </Typography>\n                  <Grid container spacing={2}>\n                    {businesses.map((business) => (\n                      <Grid item key={business.id}>\n                        <Button\n                          variant={\n                            selectedBusinessId === business.id\n                              ? \"contained\"\n                              : \"outlined\"\n                          }\n                          onClick={() => setSelectedBusinessId(business.id)}\n                        >\n                          {business.businessName}\n                        </Button>\n                      </Grid>\n                    ))}\n                  </Grid>\n                </CardContent>\n              </Card>\n            )}\n\n            {selectedBusinessId && (\n              <>\n                {/* Storage Info */}\n                {storageInfo && (\n                  <Card sx={{ marginBottom: 3 }}>\n                    <CardContent>\n                      <Box\n                        display=\"flex\"\n                        alignItems=\"center\"\n                        gap={2}\n                        marginBottom={2}\n                      >\n                        <StorageIcon color=\"primary\" />\n                        <Typography variant=\"h6\">Storage Usage</Typography>\n                      </Box>\n                      <Box marginBottom={2}>\n                        <LinearProgress\n                          variant=\"determinate\"\n                          value={Math.min(\n                            parseFloat(storageInfo.usagePercentage),\n                            100\n                          )}\n                          color={getStorageUsageColor(\n                            parseFloat(storageInfo.usagePercentage)\n                          )}\n                          sx={{ height: 10, borderRadius: 5 }}\n                        />\n                      </Box>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        {storageInfo.totalSizeMB} MB used of{\" \"}\n                        {storageInfo.maxSizeMB} MB (\n                        {storageInfo.usagePercentage}%)\n                      </Typography>\n                    </CardContent>\n                  </Card>\n                )}\n\n                {/* File Upload */}\n                <FileUploadComponent\n                  onFileUpload={handleFileUpload}\n                  uploading={uploading}\n                  maxSizeMB={storageInfo?.maxSizeMB || 1024}\n                  currentUsageMB={parseFloat(storageInfo?.totalSizeMB || \"0\")}\n                />\n\n                {/* Assets Gallery */}\n                <FileGalleryComponent\n                  assets={assets}\n                  onViewAsset={handleViewAsset}\n                  onDeleteAsset={handleDeleteAsset}\n                  formatFileSize={formatFileSize}\n                />\n\n                {/* Pagination */}\n                {pagination.pageCount > 1 && (\n                  <Box display=\"flex\" justifyContent=\"center\" marginTop={3}>\n                    <Pagination\n                      count={pagination.pageCount}\n                      page={pagination.currentPage}\n                      onChange={handlePageChange}\n                      color=\"primary\"\n                      size=\"large\"\n                    />\n                  </Box>\n                )}\n              </>\n            )}\n\n            {!selectedBusinessId && businesses.length === 0 && (\n              <Alert severity=\"info\">\n                No businesses found. Please add a business first.\n              </Alert>\n            )}\n\n            {/* File Viewer Dialog */}\n            <FileViewerComponent\n              asset={selectedAsset}\n              open={viewerOpen}\n              onClose={() => {\n                setViewerOpen(false);\n                setSelectedAsset(null);\n              }}\n            />\n\n            {/* Delete Confirmation Dialog */}\n            <ConfirmDeleteComponent\n              open={deleteDialogOpen}\n              title=\"Delete Asset\"\n              message={`Are you sure you want to delete \"${assetToDelete?.original_file_name}\"? This action cannot be undone.`}\n              onConfirm={confirmDelete}\n              onCancel={() => {\n                setDeleteDialogOpen(false);\n                setAssetToDelete(null);\n              }}\n            />\n          </Box>\n        </LeftMenuComponent>\n      </Box>\n    </div>\n  );\n};\n\nexport default ManageAssets;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAEVC,UAAU,EACVC,SAAS,EACTC,QAAQ,QACH,OAAO;AAEd,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,cAAc,EAOdC,UAAU,EACVC,KAAK,QAEA,eAAe;AACtB,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,aAAa,QAAQ,wCAAwC;AAMtE,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,mBAAmB,MAAM,kDAAkD;AAClF,OAAOC,oBAAoB,MAAM,oDAAoD;AACrF,OAAOC,mBAAmB,MAAM,kDAAkD;AAClF,OAAOC,sBAAsB,MAAM,wDAAwD;AAC3F,OAAOC,mBAAmB,MAAM,kDAAkD;AAClF,OAAOC,eAAe,MAAM,0CAA0C;;AAEtE;AACA,OAAO,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA+BzC,MAAMC,YAA0C,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAChE,MAAM;IAAEC;EAAS,CAAC,GAAGnB,WAAW,CAAEoB,KAAU,IAAKA,KAAK,CAACC,WAAW,CAAC;EACnE,MAAM;IAAEC;EAAW,CAAC,GAAGnC,UAAU,CAACe,cAAc,CAAC;EACjD,MAAM;IAAEqB;EAAU,CAAC,GAAGpC,UAAU,CAACgB,YAAY,CAAC;EAC9C,MAAMqB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMwB,mBAAmB,GAAG,IAAIf,mBAAmB,CAACc,QAAQ,CAAC;EAC7D,MAAME,eAAe,GAAG,IAAIf,eAAe,CAACa,QAAQ,CAAC;;EAErD;EACA,MAAM,CAACG,MAAM,EAAEC,SAAS,CAAC,GAAGvC,QAAQ,CAAW,EAAE,CAAC;EAClD,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAQ,EAAE,CAAC;EACvD,MAAM,CAAC0C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3C,QAAQ,CAC1D,IACF,CAAC;EACD,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAsB,IAAI,CAAC;EACzE,MAAM,CAAC8C,UAAU,EAAEC,aAAa,CAAC,GAAG/C,QAAQ,CAAc;IACxDgD,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,WAAW,EAAE,CAAC;IACdC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAgB,IAAI,CAAC;;EAEvE;EACAD,SAAS,CAAC,MAAM;IACd+D,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/D,SAAS,CAAC,MAAM;IACd,IAAI2C,kBAAkB,EAAE;MACtBqB,UAAU,CAAC,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACrB,kBAAkB,CAAC,CAAC;EAExB,MAAMoB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF7B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM+B,QAAQ,GAAG,MAAM3B,eAAe,CAAC4B,eAAe,CAACnC,QAAQ,CAACoC,EAAE,CAAC;MACnE,IAAIF,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7C3B,aAAa,CAACuB,QAAQ,CAACG,IAAI,CAAC;QAC5B;QACA,IAAIH,QAAQ,CAACG,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;UAC9BzB,qBAAqB,CAACqB,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC,CAACD,EAAE,CAAC;QAC5C;MACF;IACF,CAAC,CAAC,OAAOG,KAAU,EAAE;MACnBnC,SAAS,CAAC,2BAA2B,EAAEnB,aAAa,CAACuD,KAAK,CAAC;IAC7D,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,UAAU,GAAG,MAAAA,CAAOQ,IAAY,GAAG,CAAC,KAAK;IAC7C,IAAI,CAAC7B,kBAAkB,EAAE;IAEzB,IAAI;MACFT,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM+B,QAAQ,GAAG,MAAM3C,mBAAmB,CAACmD,SAAS,CAClD9B,kBAAkB,EAClB6B,IAAI,EACJzB,UAAU,CAACK,cACb,CAAC;MAED,IAAIa,QAAQ,CAACS,OAAO,EAAE;QACpBlC,SAAS,CAACyB,QAAQ,CAACG,IAAI,IAAI,EAAE,CAAC;QAC9BtB,cAAc,CAACmB,QAAQ,CAACpB,WAAW,CAAC;QACpCG,aAAa,CAAC;UACZ,GAAGiB,QAAQ,CAAClB,UAAU;UACtBI,WAAW,EAAEqB;QACf,CAAC,CAAC;MACJ,CAAC,MAAM;QACLhC,SAAS,CAAC,EAAE,CAAC;QACbM,cAAc,CAAC,IAAI,CAAC;MACtB;IACF,CAAC,CAAC,OAAOwB,KAAU,EAAE;MACnBnC,SAAS,CAAC,uBAAuB,EAAEnB,aAAa,CAACuD,KAAK,CAAC;MACvD/B,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyC,gBAAgB,GAAG,MAAOC,KAAe,IAAK;IAClD,IAAI,CAACjC,kBAAkB,EAAE;MACvBR,SAAS,CAAC,gCAAgC,EAAEnB,aAAa,CAAC6D,OAAO,CAAC;MAClE;IACF;IAEA,IAAI;MACFvB,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMW,QAAQ,GAAG,MAAM3C,mBAAmB,CAACwD,YAAY,CACrDnC,kBAAkB,EAClBiC,KACF,CAAC;MAED,IAAIX,QAAQ,CAACS,OAAO,EAAE;QAAA,IAAAK,qBAAA;QACpB5C,SAAS,CACP,GAAG,EAAA4C,qBAAA,GAAAd,QAAQ,CAACe,cAAc,cAAAD,qBAAA,uBAAvBA,qBAAA,CAAyBV,MAAM,KAAI,CAAC,8BAA8B,EACrErD,aAAa,CAACiE,OAChB,CAAC;QAED,IAAIhB,QAAQ,CAACiB,MAAM,IAAIjB,QAAQ,CAACiB,MAAM,CAACb,MAAM,GAAG,CAAC,EAAE;UACjDJ,QAAQ,CAACiB,MAAM,CAACC,OAAO,CAAEb,KAAU,IAAK;YACtCnC,SAAS,CACP,GAAGmC,KAAK,CAACc,QAAQ,KAAKd,KAAK,CAACA,KAAK,EAAE,EACnCtD,aAAa,CAAC6D,OAChB,CAAC;UACH,CAAC,CAAC;QACJ;;QAEA;QACAb,UAAU,CAACjB,UAAU,CAACI,WAAW,CAAC;MACpC,CAAC,MAAM;QACLhB,SAAS,CAAC8B,QAAQ,CAACoB,OAAO,IAAI,eAAe,EAAErE,aAAa,CAACuD,KAAK,CAAC;MACrE;IACF,CAAC,CAAC,OAAOD,KAAU,EAAE;MAAA,IAAAgB,eAAA,EAAAC,oBAAA;MACnBpD,SAAS,CACP,EAAAmD,eAAA,GAAAhB,KAAK,CAACL,QAAQ,cAAAqB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBlB,IAAI,cAAAmB,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI,eAAe,EAChDrE,aAAa,CAACuD,KAChB,CAAC;IACH,CAAC,SAAS;MACRjB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMkC,iBAAiB,GAAG,MAAOC,KAAa,IAAK;IACjD3B,gBAAgB,CAAC2B,KAAK,CAAC;IACvB7B,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAM8B,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAC7B,aAAa,EAAE;IAEpB,IAAI;MACF3B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM+B,QAAQ,GAAG,MAAM3C,mBAAmB,CAACqE,WAAW,CAAC9B,aAAa,CAACM,EAAE,CAAC;MAExE,IAAIF,QAAQ,CAACS,OAAO,EAAE;QACpBvC,SAAS,CAAC,4BAA4B,EAAEnB,aAAa,CAACiE,OAAO,CAAC;QAC9DjB,UAAU,CAACjB,UAAU,CAACI,WAAW,CAAC;MACpC,CAAC,MAAM;QACLhB,SAAS,CAAC8B,QAAQ,CAACoB,OAAO,IAAI,eAAe,EAAErE,aAAa,CAACuD,KAAK,CAAC;MACrE;IACF,CAAC,CAAC,OAAOD,KAAU,EAAE;MAAA,IAAAsB,gBAAA,EAAAC,qBAAA;MACnB1D,SAAS,CACP,EAAAyD,gBAAA,GAAAtB,KAAK,CAACL,QAAQ,cAAA2B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxB,IAAI,cAAAyB,qBAAA,uBAApBA,qBAAA,CAAsBR,OAAO,KAAI,eAAe,EAChDrE,aAAa,CAACuD,KAChB,CAAC;IACH,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;MACjB0B,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAMgC,eAAe,GAAIL,KAAa,IAAK;IACzCjC,gBAAgB,CAACiC,KAAK,CAAC;IACvB/B,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMqC,gBAAgB,GAAGA,CACvBC,KAAiC,EACjCxB,IAAY,KACT;IACHR,UAAU,CAACQ,IAAI,CAAC;EAClB,CAAC;EAED,MAAMyB,cAAc,GAAIC,KAAa,IAAa;IAChD,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMO,oBAAoB,GACxBC,UAAkB,IACkB;IACpC,IAAIA,UAAU,GAAG,EAAE,EAAE,OAAO,SAAS;IACrC,IAAIA,UAAU,GAAG,EAAE,EAAE,OAAO,SAAS;IACrC,OAAO,OAAO;EAChB,CAAC;EAED,oBACEpF,OAAA;IAAAqF,QAAA,eACErF,OAAA,CAACvB,GAAG;MAAA4G,QAAA,eACFrF,OAAA,CAACd,iBAAiB;QAAAmG,QAAA,eAChBrF,OAAA,CAACvB,GAAG;UAAA4G,QAAA,gBACFrF,OAAA,CAACvB,GAAG;YAAC6G,EAAE,EAAE;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,gBAChCrF,OAAA;cAAIwF,SAAS,EAAC,WAAW;cAAAH,QAAA,EAAC;YAAa;cAAA1B,QAAA,EAAA8B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C3F,OAAA,CAACtB,UAAU;cAACkH,OAAO,EAAC,WAAW;cAACJ,SAAS,EAAC,WAAW;cAAAH,QAAA,EAAC;YAGtD;cAAA1B,QAAA,EAAA8B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAhC,QAAA,EAAA8B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EAGL3E,UAAU,CAAC4B,MAAM,GAAG,CAAC,iBACpB5C,OAAA,CAACpB,IAAI;YAAC0G,EAAE,EAAE;cAAEC,YAAY,EAAE;YAAE,CAAE;YAAAF,QAAA,eAC5BrF,OAAA,CAACnB,WAAW;cAAAwG,QAAA,gBACVrF,OAAA,CAACtB,UAAU;gBAACkH,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAR,QAAA,EAAC;cAEtC;gBAAA1B,QAAA,EAAA8B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3F,OAAA,CAACrB,IAAI;gBAACmH,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAV,QAAA,EACxBrE,UAAU,CAACgF,GAAG,CAAEC,QAAQ,iBACvBjG,OAAA,CAACrB,IAAI;kBAACuH,IAAI;kBAAAb,QAAA,eACRrF,OAAA,CAAClB,MAAM;oBACL8G,OAAO,EACL1E,kBAAkB,KAAK+E,QAAQ,CAACvD,EAAE,GAC9B,WAAW,GACX,UACL;oBACDyD,OAAO,EAAEA,CAAA,KAAMhF,qBAAqB,CAAC8E,QAAQ,CAACvD,EAAE,CAAE;oBAAA2C,QAAA,EAEjDY,QAAQ,CAACG;kBAAY;oBAAAzC,QAAA,EAAA8B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB;gBAAC,GAVKM,QAAQ,CAACvD,EAAE;kBAAAiB,QAAA,EAAA8B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWrB,CACP;cAAC;gBAAAhC,QAAA,EAAA8B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAhC,QAAA,EAAA8B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAhC,QAAA,EAAA8B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACP,EAEAzE,kBAAkB,iBACjBlB,OAAA,CAAAE,SAAA;YAAAmF,QAAA,GAEGjE,WAAW,iBACVpB,OAAA,CAACpB,IAAI;cAAC0G,EAAE,EAAE;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAF,QAAA,eAC5BrF,OAAA,CAACnB,WAAW;gBAAAwG,QAAA,gBACVrF,OAAA,CAACvB,GAAG;kBACF4H,OAAO,EAAC,MAAM;kBACdC,UAAU,EAAC,QAAQ;kBACnBC,GAAG,EAAE,CAAE;kBACPhB,YAAY,EAAE,CAAE;kBAAAF,QAAA,gBAEhBrF,OAAA,CAACR,WAAW;oBAACgH,KAAK,EAAC;kBAAS;oBAAA7C,QAAA,EAAA8B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/B3F,OAAA,CAACtB,UAAU;oBAACkH,OAAO,EAAC,IAAI;oBAAAP,QAAA,EAAC;kBAAa;oBAAA1B,QAAA,EAAA8B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAhC,QAAA,EAAA8B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACN3F,OAAA,CAACvB,GAAG;kBAAC8G,YAAY,EAAE,CAAE;kBAAAF,QAAA,eACnBrF,OAAA,CAACjB,cAAc;oBACb6G,OAAO,EAAC,aAAa;oBACrBa,KAAK,EAAE5B,IAAI,CAAC6B,GAAG,CACb1B,UAAU,CAAC5D,WAAW,CAACuF,eAAe,CAAC,EACvC,GACF,CAAE;oBACFH,KAAK,EAAErB,oBAAoB,CACzBH,UAAU,CAAC5D,WAAW,CAACuF,eAAe,CACxC,CAAE;oBACFrB,EAAE,EAAE;sBAAEsB,MAAM,EAAE,EAAE;sBAAEC,YAAY,EAAE;oBAAE;kBAAE;oBAAAlD,QAAA,EAAA8B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC;gBAAC;kBAAAhC,QAAA,EAAA8B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN3F,OAAA,CAACtB,UAAU;kBAACkH,OAAO,EAAC,OAAO;kBAACY,KAAK,EAAC,gBAAgB;kBAAAnB,QAAA,GAC/CjE,WAAW,CAAC0F,WAAW,EAAC,aAAW,EAAC,GAAG,EACvC1F,WAAW,CAAC2F,SAAS,EAAC,OACvB,EAAC3F,WAAW,CAACuF,eAAe,EAAC,IAC/B;gBAAA;kBAAAhD,QAAA,EAAA8B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAhC,QAAA,EAAA8B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAhC,QAAA,EAAA8B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACP,eAGD3F,OAAA,CAACP,mBAAmB;cAClBuH,YAAY,EAAE9D,gBAAiB;cAC/BtB,SAAS,EAAEA,SAAU;cACrBmF,SAAS,EAAE,CAAA3F,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2F,SAAS,KAAI,IAAK;cAC1CE,cAAc,EAAEjC,UAAU,CAAC,CAAA5D,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE0F,WAAW,KAAI,GAAG;YAAE;cAAAnD,QAAA,EAAA8B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eAGF3F,OAAA,CAACN,oBAAoB;cACnBoB,MAAM,EAAEA,MAAO;cACfoG,WAAW,EAAE7C,eAAgB;cAC7B8C,aAAa,EAAEpD,iBAAkB;cACjCS,cAAc,EAAEA;YAAe;cAAAb,QAAA,EAAA8B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,EAGDrE,UAAU,CAACG,SAAS,GAAG,CAAC,iBACvBzB,OAAA,CAACvB,GAAG;cAAC4H,OAAO,EAAC,MAAM;cAACe,cAAc,EAAC,QAAQ;cAACC,SAAS,EAAE,CAAE;cAAAhC,QAAA,eACvDrF,OAAA,CAAChB,UAAU;gBACTsI,KAAK,EAAEhG,UAAU,CAACG,SAAU;gBAC5BsB,IAAI,EAAEzB,UAAU,CAACI,WAAY;gBAC7B6F,QAAQ,EAAEjD,gBAAiB;gBAC3BkC,KAAK,EAAC,SAAS;gBACfgB,IAAI,EAAC;cAAO;gBAAA7D,QAAA,EAAA8B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAhC,QAAA,EAAA8B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA,eACD,CACH,EAEA,CAACzE,kBAAkB,IAAIF,UAAU,CAAC4B,MAAM,KAAK,CAAC,iBAC7C5C,OAAA,CAACf,KAAK;YAACwI,QAAQ,EAAC,MAAM;YAAApC,QAAA,EAAC;UAEvB;YAAA1B,QAAA,EAAA8B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR,eAGD3F,OAAA,CAACL,mBAAmB;YAClBqE,KAAK,EAAElC,aAAc;YACrB4F,IAAI,EAAE1F,UAAW;YACjB2F,OAAO,EAAEA,CAAA,KAAM;cACb1F,aAAa,CAAC,KAAK,CAAC;cACpBF,gBAAgB,CAAC,IAAI,CAAC;YACxB;UAAE;YAAA4B,QAAA,EAAA8B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGF3F,OAAA,CAACJ,sBAAsB;YACrB8H,IAAI,EAAExF,gBAAiB;YACvB9B,KAAK,EAAC,cAAc;YACpBwD,OAAO,EAAE,oCAAoCxB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwF,kBAAkB,kCAAmC;YACjHC,SAAS,EAAE5D,aAAc;YACzB6D,QAAQ,EAAEA,CAAA,KAAM;cACd3F,mBAAmB,CAAC,KAAK,CAAC;cAC1BE,gBAAgB,CAAC,IAAI,CAAC;YACxB;UAAE;YAAAsB,QAAA,EAAA8B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAhC,QAAA,EAAA8B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAhC,QAAA,EAAA8B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW;IAAC;MAAAhC,QAAA,EAAA8B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB;EAAC;IAAAhC,QAAA,EAAA8B,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtF,EAAA,CA1UIF,YAA0C;EAAA,QACzBhB,WAAW,EAGfC,WAAW;AAAA;AAAA2I,EAAA,GAJxB5H,YAA0C;AA4UhD,eAAeA,YAAY;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}