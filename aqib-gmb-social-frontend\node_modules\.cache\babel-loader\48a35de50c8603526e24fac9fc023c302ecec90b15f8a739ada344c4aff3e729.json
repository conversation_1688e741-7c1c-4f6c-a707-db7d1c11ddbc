{"ast": null, "code": "import HttpHelperService from \"../httpHelper.service\";\nimport { CREATE_REVIEW_TAGS, GET_ALL_TAGS, LIST_OF_QANDA, REFRESH_QandA, REPLY_TO_QandA, REPLY_WITH_AI } from \"../../constants/endPoints.constant\";\nclass QandAService {\n  constructor(dispatch) {\n    this._httpHelperService = void 0;\n    this.getQA = async (userId, locationId) => {\n      return await this._httpHelperService.get(LIST_OF_QANDA(userId), {\n        \"x-gmb-location-id\": locationId\n      });\n    };\n    this.getReviewReplyFromAI = async (comment, rating) => {\n      return await this._httpHelperService.post(REPLY_WITH_AI, {\n        comment,\n        rating\n      });\n    };\n    this.createReviewTags = async (tagName, userId) => {\n      return await this._httpHelperService.post(CREATE_REVIEW_TAGS, {\n        tagName,\n        createdBy: userId\n      });\n    };\n    this.getAllTags = async () => {\n      return await this._httpHelperService.get(GET_ALL_TAGS);\n    };\n    this.refreshQA = async headerObject => {\n      return await this._httpHelperService.get(REFRESH_QandA, headerObject);\n    };\n    this.replyQandA = async (headers, reqBody) => {\n      return await this._httpHelperService.post(REPLY_TO_QandA, reqBody, headers);\n    };\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n}\nexport default QandAService;", "map": {"version": 3, "names": ["HttpHelperService", "CREATE_REVIEW_TAGS", "GET_ALL_TAGS", "LIST_OF_QANDA", "REFRESH_QandA", "REPLY_TO_QandA", "REPLY_WITH_AI", "QandAService", "constructor", "dispatch", "_httpHelperService", "getQA", "userId", "locationId", "get", "getReviewReplyFromAI", "comment", "rating", "post", "createReviewTags", "tagName", "created<PERSON>y", "getAllTags", "refreshQA", "headerObject", "replyQandA", "headers", "reqBody"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/qanda/qanda.service.tsx"], "sourcesContent": ["import { Component, Dispatch } from \"react\";\nimport HttpHelperService from \"../httpHelper.service\";\nimport {\n  CREATE_REVIEW_TAGS,\n  GET_ALL_TAGS,\n  LIST_OF_QANDA,\n  REFRESH_QandA,\n  REPLY_TO_QandA,\n  REPLY_WITH_AI,\n} from \"../../constants/endPoints.constant\";\nimport { Action } from \"redux\";\n\nclass QandAService {\n  _httpHelperService;\n  constructor(dispatch: Dispatch<Action>) {\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  getQA = async (userId: number, locationId: string) => {\n    return await this._httpHelperService.get(LIST_OF_QANDA(userId), {\n      \"x-gmb-location-id\": locationId,\n    });\n  };\n\n  getReviewReplyFromAI = async (comment: string, rating: number) => {\n    return await this._httpHelperService.post(REPLY_WITH_AI, {\n      comment,\n      rating,\n    });\n  };\n\n  createReviewTags = async (tagName: string, userId: number) => {\n    return await this._httpHelperService.post(CREATE_REVIEW_TAGS, {\n      tagName,\n      createdBy: userId,\n    });\n  };\n\n  getAllTags = async () => {\n    return await this._httpHelperService.get(GET_ALL_TAGS);\n  };\n\n  refreshQA = async (headerObject: any) => {\n    return await this._httpHelperService.get(REFRESH_QandA, headerObject);\n  };\n\n  replyQandA = async (headers: any, reqBody: any) => {\n    return await this._httpHelperService.post(REPLY_TO_QandA, reqBody, headers);\n  };\n}\n\nexport default QandAService;\n"], "mappings": "AACA,OAAOA,iBAAiB,MAAM,uBAAuB;AACrD,SACEC,kBAAkB,EAClBC,YAAY,EACZC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,aAAa,QACR,oCAAoC;AAG3C,MAAMC,YAAY,CAAC;EAEjBC,WAAWA,CAACC,QAA0B,EAAE;IAAA,KADxCC,kBAAkB;IAAA,KAKlBC,KAAK,GAAG,OAAOC,MAAc,EAAEC,UAAkB,KAAK;MACpD,OAAO,MAAM,IAAI,CAACH,kBAAkB,CAACI,GAAG,CAACX,aAAa,CAACS,MAAM,CAAC,EAAE;QAC9D,mBAAmB,EAAEC;MACvB,CAAC,CAAC;IACJ,CAAC;IAAA,KAEDE,oBAAoB,GAAG,OAAOC,OAAe,EAAEC,MAAc,KAAK;MAChE,OAAO,MAAM,IAAI,CAACP,kBAAkB,CAACQ,IAAI,CAACZ,aAAa,EAAE;QACvDU,OAAO;QACPC;MACF,CAAC,CAAC;IACJ,CAAC;IAAA,KAEDE,gBAAgB,GAAG,OAAOC,OAAe,EAAER,MAAc,KAAK;MAC5D,OAAO,MAAM,IAAI,CAACF,kBAAkB,CAACQ,IAAI,CAACjB,kBAAkB,EAAE;QAC5DmB,OAAO;QACPC,SAAS,EAAET;MACb,CAAC,CAAC;IACJ,CAAC;IAAA,KAEDU,UAAU,GAAG,YAAY;MACvB,OAAO,MAAM,IAAI,CAACZ,kBAAkB,CAACI,GAAG,CAACZ,YAAY,CAAC;IACxD,CAAC;IAAA,KAEDqB,SAAS,GAAG,MAAOC,YAAiB,IAAK;MACvC,OAAO,MAAM,IAAI,CAACd,kBAAkB,CAACI,GAAG,CAACV,aAAa,EAAEoB,YAAY,CAAC;IACvE,CAAC;IAAA,KAEDC,UAAU,GAAG,OAAOC,OAAY,EAAEC,OAAY,KAAK;MACjD,OAAO,MAAM,IAAI,CAACjB,kBAAkB,CAACQ,IAAI,CAACb,cAAc,EAAEsB,OAAO,EAAED,OAAO,CAAC;IAC7E,CAAC;IAjCC,IAAI,CAAChB,kBAAkB,GAAG,IAAIV,iBAAiB,CAACS,QAAQ,CAAC;EAC3D;AAiCF;AAEA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}