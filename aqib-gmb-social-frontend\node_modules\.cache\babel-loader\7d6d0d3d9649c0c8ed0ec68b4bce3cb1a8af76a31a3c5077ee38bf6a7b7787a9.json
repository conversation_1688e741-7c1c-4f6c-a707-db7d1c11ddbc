{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\iconOnAvailability\\\\iconOnAvailability.component.tsx\";\nimport CheckCircleIcon from \"@mui/icons-material/CheckCircle\";\nimport CancelOutlinedIcon from \"@mui/icons-material/CancelOutlined\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst IconOnAvailability = props => {\n  return props.isAvailable ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n    sx: {\n      color: \"var(--positive)\",\n      fontSize: 36\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this) : /*#__PURE__*/_jsxDEV(CancelOutlinedIcon, {\n    sx: {\n      color: \"var(--negative)\",\n      fontSize: 36\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_c = IconOnAvailability;\nexport default IconOnAvailability;\nvar _c;\n$RefreshReg$(_c, \"IconOnAvailability\");", "map": {"version": 3, "names": ["CheckCircleIcon", "CancelOutlinedIcon", "jsxDEV", "_jsxDEV", "IconOnAvailability", "props", "isAvailable", "sx", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/iconOnAvailability/iconOnAvailability.component.tsx"], "sourcesContent": ["import Avatar from \"@mui/material/Avatar\";\nimport { useEffect, useState } from \"react\";\nimport CheckCircleIcon from \"@mui/icons-material/CheckCircle\";\nimport CancelOutlinedIcon from \"@mui/icons-material/CancelOutlined\";\n\nconst IconOnAvailability = (props: { isAvailable: boolean }) => {\n  return props.isAvailable ? (\n    <CheckCircleIcon\n      sx={{\n        color: \"var(--positive)\",\n        fontSize: 36,\n      }}\n    />\n  ) : (\n    <CancelOutlinedIcon\n      sx={{\n        color: \"var(--negative)\",\n        fontSize: 36,\n      }}\n    />\n  );\n};\n\nexport default IconOnAvailability;\n"], "mappings": ";AAEA,OAAOA,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,kBAAkB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAMC,kBAAkB,GAAIC,KAA+B,IAAK;EAC9D,OAAOA,KAAK,CAACC,WAAW,gBACtBH,OAAA,CAACH,eAAe;IACdO,EAAE,EAAE;MACFC,KAAK,EAAE,iBAAiB;MACxBC,QAAQ,EAAE;IACZ;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC,gBAEFV,OAAA,CAACF,kBAAkB;IACjBM,EAAE,EAAE;MACFC,KAAK,EAAE,iBAAiB;MACxBC,QAAQ,EAAE;IACZ;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;AACH,CAAC;AAACC,EAAA,GAhBIV,kBAAkB;AAkBxB,eAAeA,kBAAkB;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}