{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\screens\\\\help\\\\help.screen.tsx\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Help = ({\n  title\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Help\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 10\n  }, this);\n};\n_c = Help;\nexport default Help;\nvar _c;\n$RefreshReg$(_c, \"Help\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Help", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/screens/help/help.screen.tsx"], "sourcesContent": ["import React, { FunctionComponent } from \"react\";\nimport PageProps from \"../../models/PageProps.interface\";\n\nconst Help: FunctionComponent<PageProps> = ({ title }) => {\n  return <div>Help</div>;\n};\n\nexport default Help;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAA6B,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGjD,MAAMC,IAAkC,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EACxD,oBAAOF,OAAA;IAAAG,QAAA,EAAK;EAAI;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AACxB,CAAC;AAACC,EAAA,GAFIP,IAAkC;AAIxC,eAAeA,IAAI;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}