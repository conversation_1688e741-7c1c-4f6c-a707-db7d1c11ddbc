const pool = require("../config/db");
const bcrypt = require("bcryptjs");
const RoleType = require("../constants/dbConstants");
const { error } = require("ajv/dist/vocabularies/applicator/dependencies");
const async = require("async");

module.exports = class User {
  constructor(
    roleId,
    name,
    mobile,
    email,
    mobileVerified,
    emailVerified,
    password,
    statusId
  ) {
    this.roleId = roleId;
    this.name = name;
    this.mobile = mobile;
    this.email = email;
    this.mobileVerified = mobileVerified;
    this.emailVerified = emailVerified;
    this.password = password;
    this.statusId = statusId;
  }

  static async Insert(user) {
    try {
      const hashedPassword = await bcrypt.hash(user.password, 12);
      const results = await pool.query(
        "INSERT INTO users(roleId, name, mobile, email, mobileVerified, emailVerified, password, statusId) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
        [
          user.roleId,
          user.name,
          user.mobile,
          user.email,
          user.mobileVerified,
          user.emailVerified,
          hashedPassword,
          user.statusId,
        ]
      );

      if (user.roleId === RoleType.Manager) {
        await pool.query(
          "INSERT INTO user_business(UserId, BusinessId) VALUES (?, ?)",
          [results.insertId, user.businessId]
        );
      }

      return { results, userId: results.insertId };
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async fetchAll(userId) {
    try {
      const userData = await pool.query(
        "SELECT u.*, r.role FROM users u INNER Join roles r ON u.roleId = r.Id AND r.IsActive = 1 WHERE u.id = ? order by u.name",
        [userId]
      );
      if (userData[0].roleId === RoleType.Admin) {
        const results = await pool.query(
          "SELECT u.*, r.role FROM users u INNER Join roles r ON u.roleId = r.Id AND r.IsActive = 1 order by u.name"
        );
        return results;
      } else {
        const results = await pool.query(
          "SELECT DISTINCT u.* FROM users u JOIN users_gmb_locations ul ON u.id = ul.userId JOIN gmb_locations gl ON ul.gmbLocationId = gl.gmbLocationId JOIN gmb_accounts a ON gl.gmbAccountId = a.accountId JOIN gmb_businesses_master b ON a.businessId = b.id JOIN user_business ub ON  ub.businessId = b.id WHERE ub.userId = ?",
          [userId]
        );
        return results;
      }
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async fetchUsersPaginated(userId, pageNo, offset) {
    var results = [];
    var totalRecords = 0;
    try {
      const userData = await pool.query(
        "SELECT u.*, r.role FROM users u INNER Join roles r ON u.roleId = r.Id AND r.IsActive = 1 WHERE u.id = ? order by u.name",
        [userId]
      );
      if (userData[0].roleId === RoleType.Admin) {
        const rows = await pool.query(
          "SELECT COUNT(DISTINCT u.id) AS Count FROM users u INNER Join roles r ON u.roleId = r.Id AND r.IsActive = 1 order by u.name;"
        );
        totalRecords = rows[0].Count;
        if (totalRecords > 0) {
          results = await pool.query(
            "SELECT u.*, r.role FROM users u INNER Join roles r ON u.roleId = r.Id AND r.IsActive = 1 order by u.name LIMIT ? , ?;",
            [(+pageNo - 1) * +offset, +offset]
          );
        }
      } else {
        const rows = await pool.query(
          "SELECT Count(DISTINCT u.id) AS Count FROM users u JOIN users_gmb_locations ul ON u.id = ul.userId JOIN gmb_locations gl ON ul.gmbLocationId = gl.gmbLocationId JOIN gmb_accounts a ON gl.gmbAccountId = a.accountId JOIN gmb_businesses_master b ON a.businessId = b.id JOIN user_business ub ON  ub.businessId = b.id WHERE ub.userId = ?",
          [userId]
        );

        totalRecords = rows[0].Count;

        results = await pool.query(
          "SELECT DISTINCT u.* FROM users u JOIN users_gmb_locations ul ON u.id = ul.userId JOIN gmb_locations gl ON ul.gmbLocationId = gl.gmbLocationId JOIN gmb_accounts a ON gl.gmbAccountId = a.accountId JOIN gmb_businesses_master b ON a.businessId = b.id JOIN user_business ub ON  ub.businessId = b.id WHERE ub.userId = ? LIMIT ? , ?",
          [userId, (+pageNo - 1) * +offset, +offset]
        );
      }

      return {
        pagination: {
          totalRecords: totalRecords,
          pageCount: Math.ceil(totalRecords / offset),
        },
        results: results,
      };
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async deleteById(id) {
    try {
      const userData = await pool.query(
        "SELECT u.*, r.role FROM users u INNER Join roles r ON u.roleId = r.Id AND r.IsActive = 1 WHERE u.id = ? order by u.name",
        [id]
      );
      if (userData[0].roleId === RoleType.Manager) {
        const managerResults = await pool.query(
          "DELETE FROM user_business WHERE UserId = ?",
          [id]
        );
        await pool.query("DELETE FROM users_gmb_locations WHERE UserId = ?", [
          id,
        ]);

        if (managerResults !== null) {
          await pool.query("DELETE FROM gmb_oauth_tokens WHERE userId = ?", [
            id,
          ]);
          const managerDeleteResult = await pool.query(
            "DELETE FROM users WHERE id = ?",
            [id]
          );
          return managerDeleteResult;
        }
        return managerResults;
      } else {
        const results = await pool.query(
          "DELETE FROM users_gmb_locations WHERE userId = ?",
          [id]
        );
        await pool.query("DELETE FROM users_gmb_locations WHERE UserId = ?", [
          id,
        ]);
        if (results !== null) {
          const deleteResult = await pool.query(
            "DELETE FROM users WHERE id =?",
            [id]
          );
          return deleteResult;
        }
        return results;
      }
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async updateById(newData) {
    const {
      id,
      roleId,
      name,
      mobile,
      email,
      mobileVerified,
      emailVerified,
      password,
      statusId,
    } = newData;

    debugger;
    // Start building the SQL query and parameters array
    let query =
      "UPDATE users SET roleId = ?, mobile = ?, mobileVerified = ?, emailVerified = ?, statusId = ? WHERE id = ?";
    let params = [roleId, mobile, mobileVerified, emailVerified, statusId, id];

    // If password is provided and not empty, hash it and include it in the query
    if (password) {
      const hashedPassword = await bcrypt.hash(password, 10);
      query = "UPDATE users SET password = ? WHERE id = ?";
      params = [hashedPassword, id];
    }

    try {
      const results = await pool.query(query, params);
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async updatePasswordById(newData) {
    const { id, password } = newData;
    // If password is provided and not empty, hash it and include it in the query
    if (password) {
      const hashedPassword = await bcrypt.hash(password, 10);
      try {
        const results = await pool.query(
          "UPDATE users SET  password = ? WHERE id = ?",
          [hashedPassword, id]
        );
        return results;
      } catch (error) {
        console.error("Database query failed:", error);
        throw error;
      }
    }
  }

  static async enableDisableUser(newData) {
    // Start building the SQL query and parameters array
    let query = "UPDATE users SET isActive = ? WHERE id = ?";
    let params = [newData.isActive, newData.id];

    try {
      const results = await pool.query(query, params);
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async updateByLocationId(newData) {
    const { id, userId, gmbLocationId, statusId } = newData;
    try {
      const results = await pool.query(
        "UPDATE users_gmb_locations SET userId = ? ,gmbLocationId = ?, statusId = ? WHERE id = ?",
        [userId, gmbLocationId, statusId, id]
      );
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async AssignUserLocation(locationData) {
    var queries = [];
    async.forEachOf(
      locationData,
      async function (value, key, callback) {
        const user = {
          userId: value.userId,
          gmbLocationId: value.gmbLocationId,
          statusId: value.statusId,
        };

        var result = await pool.query(
          `SELECT COUNT(1) FROM users_gmb_locations WHERE userId = ? AND gmbLocationId = ?;`,
          [user.userId, user.gmbLocationId]
        );

        console.log("Result: ", result);

        if (result[0]["COUNT(1)"] == 0) {
          queries.push(
            `INSERT INTO users_gmb_locations (userId,gmbLocationId,statusId) VALUES (${user.userId}, '${user.gmbLocationId}', ${user.statusId});`
          );
        } else {
          queries.push(
            `UPDATE users_gmb_locations SET statusId=${user.statusId} WHERE userId=${user.userId} AND gmbLocationId='${user.gmbLocationId}';`
          );
        }
      },
      async function (err) {
        if (err) console.log(err.message);
        else {
          for (let index = 0; index < queries.length; index++) {
            const element = queries[index];
            var result3 = await pool.query(element);
          }
        }
      }
    );
  }

  static async fetchUserLocation(userId) {
    try {
      const userData = await pool.query("SELECT * FROM users WHERE id = ?", [
        userId,
      ]);
      if (userData[0].roleId === RoleType.Admin) {
        const userBusiness = await pool.query(
          `Select id, businessId from user_business ub`
        );
        console.log("ul", userBusiness, userId);

        return userBusiness;
      } else if (userData[0].roleId === RoleType.Manager) {
        const userBusiness = await pool.query(
          `Select id, businessId from user_business ub where ub.userId = ?`,
          [userId]
        );
        console.log("ul", userBusiness, userId);

        return userBusiness;
      } else if (userData[0].roleId === RoleType.User) {
        const userLocation = await pool.query(
          `SELECT 
                u.id as id,
                u.userId, 
                u.gmbLocationId, 
                g.gmbAccountId, 
              a.businessId
            FROM 
                users_gmb_locations u
            JOIN 
                gmb_locations g ON u.gmbLocationId = g.gmbLocationId
            JOIN
                gmb_accounts a ON g.gmbAccountId = a.accountId
            WHERE 
                u.userId = ?
            `,
          [userId]
        );
        console.log("ul", userLocation, userId);

        return userLocation;
      }
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async findAssignUserLocation(userId, gmbLocationId) {
    try {
      const [rows] = await pool.query(
        `SELECT * FROM UserLocationAssignments
        WHERE userId = ? AND gmbLocationId = ?`,
        [userId, gmbLocationId]
      );
      if (rows.length > 0) {
        return rows[0];
      } else {
        return null;
      }
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async updateAssignUserLocation(userId, gmbLocationIds, { statusId }) {
    try {
      // Check if the location already exists for the user
      const results = await pool.query(
        "DELETE FROM users_gmb_locations WHERE userId = ?",
        [userId]
      );
      const result = [];
      if (results !== null) {
        for (let locationId of gmbLocationIds) {
          const updateResult = await pool.query(
            " INSERT INTO users_gmb_locations(userId,gmbLocationId,statusId) VALUES (?, ?, ?)",
            [userId, locationId, statusId]
          );
          result.push(updateResult);
        }
        return result;
      }
      return results;
    } catch (error) {
      console.error("Error processing location assignment:", error);
      throw error;
    }
  }
};
