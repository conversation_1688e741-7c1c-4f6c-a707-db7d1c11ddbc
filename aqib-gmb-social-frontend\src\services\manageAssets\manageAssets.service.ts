import { Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import { Action } from "redux";
import {
  MANAGE_ASSETS_UPLOAD,
  MANAGE_ASSETS_GET_BUSINESS_ASSETS,
  MANAGE_ASSETS_GET_ASSET,
  MANAGE_ASSETS_DELETE_ASSET,
  MANAGE_ASSETS_UPDATE_MAX_SIZE,
} from "../../constants/endPoints.constant";

class ManageAssetsService {
  _httpHelperService: HttpHelperService;

  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  /**
   * Upload assets for a business
   * @param businessId - Business ID
   * @param files - Files to upload
   * @returns Promise with upload result
   */
  async uploadAssets(businessId: number, files: FileList): Promise<any> {
    try {
      const formData = new FormData();

      // Add all files to FormData
      Array.from(files).forEach((file) => {
        formData.append("files", file);
      });

      return await this._httpHelperService.postFormData(
        MANAGE_ASSETS_UPLOAD(businessId),
        formData
      );
    } catch (error: any) {
      console.error("Error uploading assets:", error);
      throw error;
    }
  }

  /**
   * Get assets for a business with pagination
   * @param businessId - Business ID
   * @param page - Page number
   * @param limit - Records per page
   * @returns Promise with assets list
   */
  async getAssets(
    businessId: number,
    page: number = 1,
    limit: number = 12
  ): Promise<any> {
    try {
      return await this._httpHelperService.get(
        MANAGE_ASSETS_GET_BUSINESS_ASSETS(businessId),
        { page, limit }
      );
    } catch (error: any) {
      console.error("Error fetching assets:", error);
      throw error;
    }
  }

  /**
   * Get single asset by ID
   * @param assetId - Asset ID
   * @returns Promise with asset data
   */
  async getAssetById(assetId: number): Promise<any> {
    try {
      return await this._httpHelperService.get(
        MANAGE_ASSETS_GET_ASSET(assetId)
      );
    } catch (error: any) {
      console.error("Error fetching asset:", error);
      throw error;
    }
  }

  /**
   * Delete an asset
   * @param assetId - Asset ID
   * @returns Promise with delete result
   */
  async deleteAsset(assetId: number): Promise<any> {
    try {
      return await this._httpHelperService.delete(
        MANAGE_ASSETS_DELETE_ASSET(assetId)
      );
    } catch (error: any) {
      console.error("Error deleting asset:", error);
      throw error;
    }
  }

  /**
   * Update max upload size for a business
   * @param businessId - Business ID
   * @param maxSizeMB - Max size in MB
   * @returns Promise with update result
   */
  async updateMaxUploadSize(
    businessId: number,
    maxSizeMB: number
  ): Promise<any> {
    try {
      return await this._httpHelperService.put(
        MANAGE_ASSETS_UPDATE_MAX_SIZE(businessId),
        { maxSizeMB }
      );
    } catch (error: any) {
      console.error("Error updating max upload size:", error);
      throw error;
    }
  }
}

export default ManageAssetsService;
